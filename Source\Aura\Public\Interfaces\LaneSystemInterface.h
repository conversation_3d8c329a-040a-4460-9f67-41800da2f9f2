#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "Engine/Engine.h"
#include "LaneSystemInterface.generated.h"

UINTERFACE(MinimalAPI, BlueprintType)
class ULaneSystemInterface : public UInterface
{
    GENERATED_BODY()
};

/**
 * Interface moderna do UE 5.6 para sistemas de lanes
 * Implementa padrões robustos de configuração de lanes
 */
class AURA_API ILaneSystemInterface
{
    GENERATED_BODY()

public:
    /**
     * Configura lanes usando pontos de spline
     * @param LanePoints - Array de pontos que definem a lane
     * @param LaneName - Nome identificador da lane
     * @return true se configurado com sucesso
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "Lane System")
    bool ConfigureLanes(const TArray<FVector>& LanePoints, const FString& LaneName);

    /**
     * Obtém informações de uma lane específica
     * @param LaneName - Nome da lane
     * @return Array de pontos da lane
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "Lane System")
    TArray<FVector> GetLanePoints(const FString& LaneName);

    /**
     * Valida se uma lane está configurada corretamente
     * @param LaneName - Nome da lane para validar
     * @return true se válida
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "Lane System")
    bool ValidateLane(const FString& LaneName);

    /**
     * Remove uma lane do sistema
     * @param LaneName - Nome da lane para remover
     * @return true se removida com sucesso
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "Lane System")
    bool RemoveLane(const FString& LaneName);

    /**
     * Obtém todas as lanes configuradas
     * @return Array com nomes de todas as lanes
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "Lane System")
    TArray<FString> GetAllLaneNames();

    /**
     * Configura conexões entre lanes
     * @param FromLane - Nome da lane de origem
     * @param ToLane - Nome da lane de destino
     * @param ConnectionPoints - Pontos de conexão
     * @return true se conexão criada com sucesso
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "Lane System")
    bool CreateLaneConnection(const FString& FromLane, const FString& ToLane, const TArray<FVector>& ConnectionPoints);

    /**
     * Obtém o ponto mais próximo em uma lane
     * @param Position - Posição de referência
     * @param LaneName - Nome da lane
     * @return Ponto mais próximo na lane
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "Lane System")
    FVector GetClosestPointOnLane(const FVector& Position, const FString& LaneName);

    /**
     * Verifica se uma posição está dentro de uma lane
     * @param Position - Posição para verificar
     * @param LaneName - Nome da lane
     * @param Tolerance - Tolerância em unidades
     * @return true se está dentro da lane
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "Lane System")
    bool IsPositionInLane(const FVector& Position, const FString& LaneName, float Tolerance = 100.0f);

    /**
     * Obtém a largura de uma lane em um ponto específico
     * @param Position - Posição na lane
     * @param LaneName - Nome da lane
     * @return Largura da lane no ponto
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "Lane System")
    float GetLaneWidthAtPosition(const FVector& Position, const FString& LaneName);

    /**
     * Configura propriedades avançadas de uma lane
     * @param LaneName - Nome da lane
     * @param Width - Largura da lane
     * @param SpeedLimit - Limite de velocidade
     * @param bBidirectional - Se é bidirecional
     * @return true se configurado com sucesso
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "Lane System")
    bool SetLaneProperties(const FString& LaneName, float Width, float SpeedLimit, bool bBidirectional);
};
