// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "UPCGQualityValidator.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUPCGQualityValidator() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_APCGCacheManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGChaosIntegrator_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGLumenIntegrator_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGNaniteOptimizer_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGStreamingManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGWorldPartitionManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister();
AURA_API UClass* Z_Construct_UClass_UPCGQualityValidator();
AURA_API UClass* Z_Construct_UClass_UPCGQualityValidator_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGQualityLevel();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGValidationCategory();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGValidationMode();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGValidationResult();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGValidationSeverity();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGQualityMetrics();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGValidationConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGValidationIssue();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGValidationReport();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGGraph_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPCGValidationSeverity ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGValidationSeverity;
static UEnum* EPCGValidationSeverity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGValidationSeverity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGValidationSeverity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGValidationSeverity, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGValidationSeverity"));
	}
	return Z_Registration_Info_UEnum_EPCGValidationSeverity.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGValidationSeverity>()
{
	return EPCGValidationSeverity_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGValidationSeverity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums\n" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EPCGValidationSeverity::Critical" },
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EPCGValidationSeverity::Error" },
		{ "Info.DisplayName", "Info" },
		{ "Info.Name", "EPCGValidationSeverity::Info" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums" },
#endif
		{ "Warning.DisplayName", "Warning" },
		{ "Warning.Name", "EPCGValidationSeverity::Warning" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGValidationSeverity::Info", (int64)EPCGValidationSeverity::Info },
		{ "EPCGValidationSeverity::Warning", (int64)EPCGValidationSeverity::Warning },
		{ "EPCGValidationSeverity::Error", (int64)EPCGValidationSeverity::Error },
		{ "EPCGValidationSeverity::Critical", (int64)EPCGValidationSeverity::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGValidationSeverity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGValidationSeverity",
	"EPCGValidationSeverity",
	Z_Construct_UEnum_Aura_EPCGValidationSeverity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGValidationSeverity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGValidationSeverity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGValidationSeverity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGValidationSeverity()
{
	if (!Z_Registration_Info_UEnum_EPCGValidationSeverity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGValidationSeverity.InnerSingleton, Z_Construct_UEnum_Aura_EPCGValidationSeverity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGValidationSeverity.InnerSingleton;
}
// ********** End Enum EPCGValidationSeverity ******************************************************

// ********** Begin Enum EPCGValidationCategory ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGValidationCategory;
static UEnum* EPCGValidationCategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGValidationCategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGValidationCategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGValidationCategory, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGValidationCategory"));
	}
	return Z_Registration_Info_UEnum_EPCGValidationCategory.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGValidationCategory>()
{
	return EPCGValidationCategory_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGValidationCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cache.DisplayName", "Cache" },
		{ "Cache.Name", "EPCGValidationCategory::Cache" },
		{ "Compliance.DisplayName", "Compliance" },
		{ "Compliance.Name", "EPCGValidationCategory::Compliance" },
		{ "Geometry.DisplayName", "Geometry" },
		{ "Geometry.Name", "EPCGValidationCategory::Geometry" },
		{ "Memory.DisplayName", "Memory" },
		{ "Memory.Name", "EPCGValidationCategory::Memory" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
		{ "Performance.DisplayName", "Performance" },
		{ "Performance.Name", "EPCGValidationCategory::Performance" },
		{ "Physics.DisplayName", "Physics" },
		{ "Physics.Name", "EPCGValidationCategory::Physics" },
		{ "Quality.DisplayName", "Quality" },
		{ "Quality.Name", "EPCGValidationCategory::Quality" },
		{ "Rendering.DisplayName", "Rendering" },
		{ "Rendering.Name", "EPCGValidationCategory::Rendering" },
		{ "Streaming.DisplayName", "Streaming" },
		{ "Streaming.Name", "EPCGValidationCategory::Streaming" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGValidationCategory::Geometry", (int64)EPCGValidationCategory::Geometry },
		{ "EPCGValidationCategory::Performance", (int64)EPCGValidationCategory::Performance },
		{ "EPCGValidationCategory::Memory", (int64)EPCGValidationCategory::Memory },
		{ "EPCGValidationCategory::Rendering", (int64)EPCGValidationCategory::Rendering },
		{ "EPCGValidationCategory::Physics", (int64)EPCGValidationCategory::Physics },
		{ "EPCGValidationCategory::Streaming", (int64)EPCGValidationCategory::Streaming },
		{ "EPCGValidationCategory::Cache", (int64)EPCGValidationCategory::Cache },
		{ "EPCGValidationCategory::Quality", (int64)EPCGValidationCategory::Quality },
		{ "EPCGValidationCategory::Compliance", (int64)EPCGValidationCategory::Compliance },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGValidationCategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGValidationCategory",
	"EPCGValidationCategory",
	Z_Construct_UEnum_Aura_EPCGValidationCategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGValidationCategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGValidationCategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGValidationCategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGValidationCategory()
{
	if (!Z_Registration_Info_UEnum_EPCGValidationCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGValidationCategory.InnerSingleton, Z_Construct_UEnum_Aura_EPCGValidationCategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGValidationCategory.InnerSingleton;
}
// ********** End Enum EPCGValidationCategory ******************************************************

// ********** Begin Enum EPCGValidationMode ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGValidationMode;
static UEnum* EPCGValidationMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGValidationMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGValidationMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGValidationMode, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGValidationMode"));
	}
	return Z_Registration_Info_UEnum_EPCGValidationMode.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGValidationMode>()
{
	return EPCGValidationMode_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGValidationMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Automatic.DisplayName", "Automatic" },
		{ "Automatic.Name", "EPCGValidationMode::Automatic" },
		{ "BlueprintType", "true" },
		{ "Manual.DisplayName", "Manual" },
		{ "Manual.Name", "EPCGValidationMode::Manual" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
		{ "RealTime.DisplayName", "Real Time" },
		{ "RealTime.Name", "EPCGValidationMode::RealTime" },
		{ "Scheduled.DisplayName", "Scheduled" },
		{ "Scheduled.Name", "EPCGValidationMode::Scheduled" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGValidationMode::Manual", (int64)EPCGValidationMode::Manual },
		{ "EPCGValidationMode::Automatic", (int64)EPCGValidationMode::Automatic },
		{ "EPCGValidationMode::RealTime", (int64)EPCGValidationMode::RealTime },
		{ "EPCGValidationMode::Scheduled", (int64)EPCGValidationMode::Scheduled },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGValidationMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGValidationMode",
	"EPCGValidationMode",
	Z_Construct_UEnum_Aura_EPCGValidationMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGValidationMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGValidationMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGValidationMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGValidationMode()
{
	if (!Z_Registration_Info_UEnum_EPCGValidationMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGValidationMode.InnerSingleton, Z_Construct_UEnum_Aura_EPCGValidationMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGValidationMode.InnerSingleton;
}
// ********** End Enum EPCGValidationMode **********************************************************

// ********** Begin Enum EPCGValidationResult ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGValidationResult;
static UEnum* EPCGValidationResult_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGValidationResult.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGValidationResult.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGValidationResult, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGValidationResult"));
	}
	return Z_Registration_Info_UEnum_EPCGValidationResult.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGValidationResult>()
{
	return EPCGValidationResult_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGValidationResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EPCGValidationResult::Failed" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
		{ "Passed.DisplayName", "Passed" },
		{ "Passed.Name", "EPCGValidationResult::Passed" },
		{ "Pending.DisplayName", "Pending" },
		{ "Pending.Name", "EPCGValidationResult::Pending" },
		{ "Skipped.DisplayName", "Skipped" },
		{ "Skipped.Name", "EPCGValidationResult::Skipped" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGValidationResult::Passed", (int64)EPCGValidationResult::Passed },
		{ "EPCGValidationResult::Failed", (int64)EPCGValidationResult::Failed },
		{ "EPCGValidationResult::Skipped", (int64)EPCGValidationResult::Skipped },
		{ "EPCGValidationResult::Pending", (int64)EPCGValidationResult::Pending },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGValidationResult_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGValidationResult",
	"EPCGValidationResult",
	Z_Construct_UEnum_Aura_EPCGValidationResult_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGValidationResult_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGValidationResult_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGValidationResult_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGValidationResult()
{
	if (!Z_Registration_Info_UEnum_EPCGValidationResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGValidationResult.InnerSingleton, Z_Construct_UEnum_Aura_EPCGValidationResult_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGValidationResult.InnerSingleton;
}
// ********** End Enum EPCGValidationResult ********************************************************

// ********** Begin Enum EPCGQualityLevel **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGQualityLevel;
static UEnum* EPCGQualityLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGQualityLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGQualityLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGQualityLevel, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGQualityLevel"));
	}
	return Z_Registration_Info_UEnum_EPCGQualityLevel.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGQualityLevel>()
{
	return EPCGQualityLevel_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGQualityLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cinematic.DisplayName", "Cinematic" },
		{ "Cinematic.Name", "EPCGQualityLevel::Cinematic" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EPCGQualityLevel::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EPCGQualityLevel::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EPCGQualityLevel::Medium" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
		{ "Ultra.DisplayName", "Ultra" },
		{ "Ultra.Name", "EPCGQualityLevel::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGQualityLevel::Low", (int64)EPCGQualityLevel::Low },
		{ "EPCGQualityLevel::Medium", (int64)EPCGQualityLevel::Medium },
		{ "EPCGQualityLevel::High", (int64)EPCGQualityLevel::High },
		{ "EPCGQualityLevel::Ultra", (int64)EPCGQualityLevel::Ultra },
		{ "EPCGQualityLevel::Cinematic", (int64)EPCGQualityLevel::Cinematic },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGQualityLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGQualityLevel",
	"EPCGQualityLevel",
	Z_Construct_UEnum_Aura_EPCGQualityLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGQualityLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGQualityLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGQualityLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGQualityLevel()
{
	if (!Z_Registration_Info_UEnum_EPCGQualityLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGQualityLevel.InnerSingleton, Z_Construct_UEnum_Aura_EPCGQualityLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGQualityLevel.InnerSingleton;
}
// ********** End Enum EPCGQualityLevel ************************************************************

// ********** Begin ScriptStruct FPCGValidationConfig **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGValidationConfig;
class UScriptStruct* FPCGValidationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGValidationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGValidationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGValidationConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGValidationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGValidationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGValidationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structs\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structs" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationMode_MetaData[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnabledCategories_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumSeverity_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetQualityLevel_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxFrameTime_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance Thresholds\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Thresholds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMemoryUsage_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 60 FPS\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "60 FPS" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDrawCalls_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// MB\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxTriangles_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinLODDistance_MetaData[] = {
		{ "Category", "Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Quality Thresholds\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quality Thresholds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxTexelDensity_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableNaniteValidation_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLumenValidation_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationInterval_MetaData[] = {
		{ "Category", "Automation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Automation Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Automation Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoFixIssues_MetaData[] = {
		{ "Category", "Automation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// seconds\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "seconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateReports_MetaData[] = {
		{ "Category", "Automation" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ValidationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ValidationMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnabledCategories_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnabledCategories_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EnabledCategories;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinimumSeverity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinimumSeverity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetQualityLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetQualityLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxFrameTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxMemoryUsage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxDrawCalls;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxTriangles;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinLODDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxTexelDensity;
	static void NewProp_bEnableNaniteValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableNaniteValidation;
	static void NewProp_bEnableLumenValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLumenValidation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValidationInterval;
	static void NewProp_bAutoFixIssues_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoFixIssues;
	static void NewProp_bGenerateReports_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateReports;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGValidationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_ValidationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_ValidationMode = { "ValidationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationConfig, ValidationMode), Z_Construct_UEnum_Aura_EPCGValidationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationMode_MetaData), NewProp_ValidationMode_MetaData) }; // 3841298128
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_EnabledCategories_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_EnabledCategories_Inner = { "EnabledCategories", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Aura_EPCGValidationCategory, METADATA_PARAMS(0, nullptr) }; // 760008476
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_EnabledCategories = { "EnabledCategories", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationConfig, EnabledCategories), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnabledCategories_MetaData), NewProp_EnabledCategories_MetaData) }; // 760008476
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MinimumSeverity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MinimumSeverity = { "MinimumSeverity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationConfig, MinimumSeverity), Z_Construct_UEnum_Aura_EPCGValidationSeverity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumSeverity_MetaData), NewProp_MinimumSeverity_MetaData) }; // 1120267668
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_TargetQualityLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_TargetQualityLevel = { "TargetQualityLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationConfig, TargetQualityLevel), Z_Construct_UEnum_Aura_EPCGQualityLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetQualityLevel_MetaData), NewProp_TargetQualityLevel_MetaData) }; // 1582851793
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MaxFrameTime = { "MaxFrameTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationConfig, MaxFrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxFrameTime_MetaData), NewProp_MaxFrameTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MaxMemoryUsage = { "MaxMemoryUsage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationConfig, MaxMemoryUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMemoryUsage_MetaData), NewProp_MaxMemoryUsage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MaxDrawCalls = { "MaxDrawCalls", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationConfig, MaxDrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDrawCalls_MetaData), NewProp_MaxDrawCalls_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MaxTriangles = { "MaxTriangles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationConfig, MaxTriangles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxTriangles_MetaData), NewProp_MaxTriangles_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MinLODDistance = { "MinLODDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationConfig, MinLODDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinLODDistance_MetaData), NewProp_MinLODDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MaxTexelDensity = { "MaxTexelDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationConfig, MaxTexelDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxTexelDensity_MetaData), NewProp_MaxTexelDensity_MetaData) };
void Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bEnableNaniteValidation_SetBit(void* Obj)
{
	((FPCGValidationConfig*)Obj)->bEnableNaniteValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bEnableNaniteValidation = { "bEnableNaniteValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGValidationConfig), &Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bEnableNaniteValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableNaniteValidation_MetaData), NewProp_bEnableNaniteValidation_MetaData) };
void Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bEnableLumenValidation_SetBit(void* Obj)
{
	((FPCGValidationConfig*)Obj)->bEnableLumenValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bEnableLumenValidation = { "bEnableLumenValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGValidationConfig), &Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bEnableLumenValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLumenValidation_MetaData), NewProp_bEnableLumenValidation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_ValidationInterval = { "ValidationInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationConfig, ValidationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationInterval_MetaData), NewProp_ValidationInterval_MetaData) };
void Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bAutoFixIssues_SetBit(void* Obj)
{
	((FPCGValidationConfig*)Obj)->bAutoFixIssues = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bAutoFixIssues = { "bAutoFixIssues", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGValidationConfig), &Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bAutoFixIssues_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoFixIssues_MetaData), NewProp_bAutoFixIssues_MetaData) };
void Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bGenerateReports_SetBit(void* Obj)
{
	((FPCGValidationConfig*)Obj)->bGenerateReports = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bGenerateReports = { "bGenerateReports", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGValidationConfig), &Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bGenerateReports_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateReports_MetaData), NewProp_bGenerateReports_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_ValidationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_ValidationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_EnabledCategories_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_EnabledCategories_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_EnabledCategories,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MinimumSeverity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MinimumSeverity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_TargetQualityLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_TargetQualityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MaxFrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MaxMemoryUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MaxDrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MaxTriangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MinLODDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_MaxTexelDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bEnableNaniteValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bEnableLumenValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_ValidationInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bAutoFixIssues,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewProp_bGenerateReports,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGValidationConfig",
	Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::PropPointers),
	sizeof(FPCGValidationConfig),
	alignof(FPCGValidationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGValidationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGValidationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGValidationConfig.InnerSingleton, Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGValidationConfig.InnerSingleton;
}
// ********** End ScriptStruct FPCGValidationConfig ************************************************

// ********** Begin ScriptStruct FPCGValidationIssue ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGValidationIssue;
class UScriptStruct* FPCGValidationIssue::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGValidationIssue.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGValidationIssue.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGValidationIssue, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGValidationIssue"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGValidationIssue.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGValidationIssue_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IssueID_MetaData[] = {
		{ "Category", "Issue" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Severity_MetaData[] = {
		{ "Category", "Issue" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "Issue" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Title_MetaData[] = {
		{ "Category", "Issue" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Issue" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Recommendation_MetaData[] = {
		{ "Category", "Issue" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AffectedObject_MetaData[] = {
		{ "Category", "Issue" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Issue" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Issue" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanAutoFix_MetaData[] = {
		{ "Category", "Issue" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsFixed_MetaData[] = {
		{ "Category", "Issue" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_IssueID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Severity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Title;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Recommendation;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_AffectedObject;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Timestamp;
	static void NewProp_bCanAutoFix_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanAutoFix;
	static void NewProp_bIsFixed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsFixed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGValidationIssue>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_IssueID = { "IssueID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationIssue, IssueID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IssueID_MetaData), NewProp_IssueID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Severity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationIssue, Severity), Z_Construct_UEnum_Aura_EPCGValidationSeverity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Severity_MetaData), NewProp_Severity_MetaData) }; // 1120267668
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationIssue, Category), Z_Construct_UEnum_Aura_EPCGValidationCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) }; // 760008476
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Title = { "Title", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationIssue, Title), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Title_MetaData), NewProp_Title_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationIssue, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Recommendation = { "Recommendation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationIssue, Recommendation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Recommendation_MetaData), NewProp_Recommendation_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_AffectedObject = { "AffectedObject", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationIssue, AffectedObject), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AffectedObject_MetaData), NewProp_AffectedObject_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationIssue, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationIssue, Timestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
void Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_bCanAutoFix_SetBit(void* Obj)
{
	((FPCGValidationIssue*)Obj)->bCanAutoFix = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_bCanAutoFix = { "bCanAutoFix", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGValidationIssue), &Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_bCanAutoFix_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanAutoFix_MetaData), NewProp_bCanAutoFix_MetaData) };
void Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_bIsFixed_SetBit(void* Obj)
{
	((FPCGValidationIssue*)Obj)->bIsFixed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_bIsFixed = { "bIsFixed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGValidationIssue), &Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_bIsFixed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsFixed_MetaData), NewProp_bIsFixed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_IssueID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Severity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Severity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Title,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Recommendation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_AffectedObject,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_bCanAutoFix,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewProp_bIsFixed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGValidationIssue",
	Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::PropPointers),
	sizeof(FPCGValidationIssue),
	alignof(FPCGValidationIssue),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGValidationIssue()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGValidationIssue.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGValidationIssue.InnerSingleton, Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGValidationIssue.InnerSingleton;
}
// ********** End ScriptStruct FPCGValidationIssue *************************************************

// ********** Begin ScriptStruct FPCGValidationReport **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGValidationReport;
class UScriptStruct* FPCGValidationReport::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGValidationReport.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGValidationReport.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGValidationReport, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGValidationReport"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGValidationReport.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGValidationReport_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReportID_MetaData[] = {
		{ "Category", "Report" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationTime_MetaData[] = {
		{ "Category", "Report" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationDuration_MetaData[] = {
		{ "Category", "Report" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Issues_MetaData[] = {
		{ "Category", "Report" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalChecks_MetaData[] = {
		{ "Category", "Report" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PassedChecks_MetaData[] = {
		{ "Category", "Report" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedChecks_MetaData[] = {
		{ "Category", "Report" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkippedChecks_MetaData[] = {
		{ "Category", "Report" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverallQuality_MetaData[] = {
		{ "Category", "Report" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityScore_MetaData[] = {
		{ "Category", "Report" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReportID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValidationDuration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Issues_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Issues;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalChecks;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PassedChecks;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FailedChecks;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SkippedChecks;
	static const UECodeGen_Private::FBytePropertyParams NewProp_OverallQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OverallQuality;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QualityScore;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGValidationReport>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_ReportID = { "ReportID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationReport, ReportID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReportID_MetaData), NewProp_ReportID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_GenerationTime = { "GenerationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationReport, GenerationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationTime_MetaData), NewProp_GenerationTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_ValidationDuration = { "ValidationDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationReport, ValidationDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationDuration_MetaData), NewProp_ValidationDuration_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_Issues_Inner = { "Issues", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGValidationIssue, METADATA_PARAMS(0, nullptr) }; // 2081596474
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_Issues = { "Issues", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationReport, Issues), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Issues_MetaData), NewProp_Issues_MetaData) }; // 2081596474
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_TotalChecks = { "TotalChecks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationReport, TotalChecks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalChecks_MetaData), NewProp_TotalChecks_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_PassedChecks = { "PassedChecks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationReport, PassedChecks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PassedChecks_MetaData), NewProp_PassedChecks_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_FailedChecks = { "FailedChecks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationReport, FailedChecks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedChecks_MetaData), NewProp_FailedChecks_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_SkippedChecks = { "SkippedChecks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationReport, SkippedChecks), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkippedChecks_MetaData), NewProp_SkippedChecks_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_OverallQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_OverallQuality = { "OverallQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationReport, OverallQuality), Z_Construct_UEnum_Aura_EPCGQualityLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverallQuality_MetaData), NewProp_OverallQuality_MetaData) }; // 1582851793
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_QualityScore = { "QualityScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGValidationReport, QualityScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityScore_MetaData), NewProp_QualityScore_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGValidationReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_ReportID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_GenerationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_ValidationDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_Issues_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_Issues,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_TotalChecks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_PassedChecks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_FailedChecks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_SkippedChecks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_OverallQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_OverallQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewProp_QualityScore,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGValidationReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGValidationReport_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGValidationReport",
	Z_Construct_UScriptStruct_FPCGValidationReport_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGValidationReport_Statics::PropPointers),
	sizeof(FPCGValidationReport),
	alignof(FPCGValidationReport),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGValidationReport_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGValidationReport_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGValidationReport()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGValidationReport.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGValidationReport.InnerSingleton, Z_Construct_UScriptStruct_FPCGValidationReport_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGValidationReport.InnerSingleton;
}
// ********** End ScriptStruct FPCGValidationReport ************************************************

// ********** Begin ScriptStruct FPCGQualityMetrics ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGQualityMetrics;
class UScriptStruct* FPCGQualityMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGQualityMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGQualityMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGQualityMetrics, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGQualityMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGQualityMetrics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameTime_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance Metrics\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsage_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DrawCalls_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriangleCount_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODCoverage_MetaData[] = {
		{ "Category", "Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Quality Metrics\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quality Metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureQuality_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeometryComplexity_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingQuality_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingEfficiency_MetaData[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming Metrics\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming Metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheHitRate_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrameTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DrawCalls;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TriangleCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODCoverage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TextureQuality;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GeometryComplexity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LightingQuality;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingEfficiency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CacheHitRate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGQualityMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_FrameTime = { "FrameTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGQualityMetrics, FrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameTime_MetaData), NewProp_FrameTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_MemoryUsage = { "MemoryUsage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGQualityMetrics, MemoryUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsage_MetaData), NewProp_MemoryUsage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_DrawCalls = { "DrawCalls", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGQualityMetrics, DrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DrawCalls_MetaData), NewProp_DrawCalls_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_TriangleCount = { "TriangleCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGQualityMetrics, TriangleCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriangleCount_MetaData), NewProp_TriangleCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_LODCoverage = { "LODCoverage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGQualityMetrics, LODCoverage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODCoverage_MetaData), NewProp_LODCoverage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_TextureQuality = { "TextureQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGQualityMetrics, TextureQuality), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureQuality_MetaData), NewProp_TextureQuality_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_GeometryComplexity = { "GeometryComplexity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGQualityMetrics, GeometryComplexity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeometryComplexity_MetaData), NewProp_GeometryComplexity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_LightingQuality = { "LightingQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGQualityMetrics, LightingQuality), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingQuality_MetaData), NewProp_LightingQuality_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_StreamingEfficiency = { "StreamingEfficiency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGQualityMetrics, StreamingEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingEfficiency_MetaData), NewProp_StreamingEfficiency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_CacheHitRate = { "CacheHitRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGQualityMetrics, CacheHitRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheHitRate_MetaData), NewProp_CacheHitRate_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_FrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_MemoryUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_DrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_TriangleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_LODCoverage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_TextureQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_GeometryComplexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_LightingQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_StreamingEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewProp_CacheHitRate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGQualityMetrics",
	Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::PropPointers),
	sizeof(FPCGQualityMetrics),
	alignof(FPCGQualityMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGQualityMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGQualityMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGQualityMetrics.InnerSingleton, Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGQualityMetrics.InnerSingleton;
}
// ********** End ScriptStruct FPCGQualityMetrics **************************************************

// ********** Begin Delegate FOnValidationStarted **************************************************
struct Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnValidationStarted_Parms
	{
		FString ValidationID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegates\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature_Statics::NewProp_ValidationID = { "ValidationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnValidationStarted_Parms, ValidationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationID_MetaData), NewProp_ValidationID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature_Statics::NewProp_ValidationID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnValidationStarted__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature_Statics::_Script_Aura_eventOnValidationStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature_Statics::_Script_Aura_eventOnValidationStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnValidationStarted_DelegateWrapper(const FMulticastScriptDelegate& OnValidationStarted, const FString& ValidationID)
{
	struct _Script_Aura_eventOnValidationStarted_Parms
	{
		FString ValidationID;
	};
	_Script_Aura_eventOnValidationStarted_Parms Parms;
	Parms.ValidationID=ValidationID;
	OnValidationStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnValidationStarted ****************************************************

// ********** Begin Delegate FOnValidationCompleted ************************************************
struct Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnValidationCompleted_Parms
	{
		FString ValidationID;
		FPCGValidationReport Report;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Report_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Report;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::NewProp_ValidationID = { "ValidationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnValidationCompleted_Parms, ValidationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationID_MetaData), NewProp_ValidationID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::NewProp_Report = { "Report", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnValidationCompleted_Parms, Report), Z_Construct_UScriptStruct_FPCGValidationReport, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Report_MetaData), NewProp_Report_MetaData) }; // 2761269386
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::NewProp_ValidationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::NewProp_Report,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnValidationCompleted__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::_Script_Aura_eventOnValidationCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::_Script_Aura_eventOnValidationCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnValidationCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnValidationCompleted, const FString& ValidationID, FPCGValidationReport const& Report)
{
	struct _Script_Aura_eventOnValidationCompleted_Parms
	{
		FString ValidationID;
		FPCGValidationReport Report;
	};
	_Script_Aura_eventOnValidationCompleted_Parms Parms;
	Parms.ValidationID=ValidationID;
	Parms.Report=Report;
	OnValidationCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnValidationCompleted **************************************************

// ********** Begin Delegate FOnIssueDetected ******************************************************
struct Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnIssueDetected_Parms
	{
		FString ValidationID;
		FPCGValidationIssue Issue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Issue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Issue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::NewProp_ValidationID = { "ValidationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnIssueDetected_Parms, ValidationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationID_MetaData), NewProp_ValidationID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::NewProp_Issue = { "Issue", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnIssueDetected_Parms, Issue), Z_Construct_UScriptStruct_FPCGValidationIssue, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Issue_MetaData), NewProp_Issue_MetaData) }; // 2081596474
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::NewProp_ValidationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::NewProp_Issue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnIssueDetected__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::_Script_Aura_eventOnIssueDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::_Script_Aura_eventOnIssueDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnIssueDetected_DelegateWrapper(const FMulticastScriptDelegate& OnIssueDetected, const FString& ValidationID, FPCGValidationIssue const& Issue)
{
	struct _Script_Aura_eventOnIssueDetected_Parms
	{
		FString ValidationID;
		FPCGValidationIssue Issue;
	};
	_Script_Aura_eventOnIssueDetected_Parms Parms;
	Parms.ValidationID=ValidationID;
	Parms.Issue=Issue;
	OnIssueDetected.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnIssueDetected ********************************************************

// ********** Begin Delegate FOnIssueFixed *********************************************************
struct Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnIssueFixed_Parms
	{
		FString ValidationID;
		FPCGValidationIssue Issue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Issue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Issue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::NewProp_ValidationID = { "ValidationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnIssueFixed_Parms, ValidationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationID_MetaData), NewProp_ValidationID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::NewProp_Issue = { "Issue", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnIssueFixed_Parms, Issue), Z_Construct_UScriptStruct_FPCGValidationIssue, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Issue_MetaData), NewProp_Issue_MetaData) }; // 2081596474
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::NewProp_ValidationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::NewProp_Issue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnIssueFixed__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::_Script_Aura_eventOnIssueFixed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::_Script_Aura_eventOnIssueFixed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnIssueFixed_DelegateWrapper(const FMulticastScriptDelegate& OnIssueFixed, const FString& ValidationID, FPCGValidationIssue const& Issue)
{
	struct _Script_Aura_eventOnIssueFixed_Parms
	{
		FString ValidationID;
		FPCGValidationIssue Issue;
	};
	_Script_Aura_eventOnIssueFixed_Parms Parms;
	Parms.ValidationID=ValidationID;
	Parms.Issue=Issue;
	OnIssueFixed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnIssueFixed ***********************************************************

// ********** Begin Delegate FOnQualityChanged *****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnQualityChanged_Parms
	{
		EPCGQualityLevel OldLevel;
		EPCGQualityLevel NewLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldLevel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::NewProp_OldLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::NewProp_OldLevel = { "OldLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnQualityChanged_Parms, OldLevel), Z_Construct_UEnum_Aura_EPCGQualityLevel, METADATA_PARAMS(0, nullptr) }; // 1582851793
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::NewProp_NewLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::NewProp_NewLevel = { "NewLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnQualityChanged_Parms, NewLevel), Z_Construct_UEnum_Aura_EPCGQualityLevel, METADATA_PARAMS(0, nullptr) }; // 1582851793
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::NewProp_OldLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::NewProp_OldLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::NewProp_NewLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::NewProp_NewLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnQualityChanged__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::_Script_Aura_eventOnQualityChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::_Script_Aura_eventOnQualityChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnQualityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnQualityChanged, EPCGQualityLevel OldLevel, EPCGQualityLevel NewLevel)
{
	struct _Script_Aura_eventOnQualityChanged_Parms
	{
		EPCGQualityLevel OldLevel;
		EPCGQualityLevel NewLevel;
	};
	_Script_Aura_eventOnQualityChanged_Parms Parms;
	Parms.OldLevel=OldLevel;
	Parms.NewLevel=NewLevel;
	OnQualityChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnQualityChanged *******************************************************

// ********** Begin Class UPCGQualityValidator Function AssessOverallQuality ***********************
struct Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics
{
	struct PCGQualityValidator_eventAssessOverallQuality_Parms
	{
		EPCGQualityLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Assessment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Quality Assessment\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quality Assessment" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventAssessOverallQuality_Parms, ReturnValue), Z_Construct_UEnum_Aura_EPCGQualityLevel, METADATA_PARAMS(0, nullptr) }; // 1582851793
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "AssessOverallQuality", Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::PCGQualityValidator_eventAssessOverallQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::PCGQualityValidator_eventAssessOverallQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execAssessOverallQuality)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EPCGQualityLevel*)Z_Param__Result=P_THIS->AssessOverallQuality();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function AssessOverallQuality *************************

// ********** Begin Class UPCGQualityValidator Function CalculateGeometryComplexity ****************
struct Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity_Statics
{
	struct PCGQualityValidator_eventCalculateGeometryComplexity_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Quality" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventCalculateGeometryComplexity_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "CalculateGeometryComplexity", Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity_Statics::PCGQualityValidator_eventCalculateGeometryComplexity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity_Statics::PCGQualityValidator_eventCalculateGeometryComplexity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execCalculateGeometryComplexity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateGeometryComplexity();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function CalculateGeometryComplexity ******************

// ********** Begin Class UPCGQualityValidator Function CalculateLightingQuality *******************
struct Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality_Statics
{
	struct PCGQualityValidator_eventCalculateLightingQuality_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Quality" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventCalculateLightingQuality_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "CalculateLightingQuality", Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality_Statics::PCGQualityValidator_eventCalculateLightingQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality_Statics::PCGQualityValidator_eventCalculateLightingQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execCalculateLightingQuality)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateLightingQuality();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function CalculateLightingQuality *********************

// ********** Begin Class UPCGQualityValidator Function CalculateLODCoverage ***********************
struct Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage_Statics
{
	struct PCGQualityValidator_eventCalculateLODCoverage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Quality" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventCalculateLODCoverage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "CalculateLODCoverage", Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage_Statics::PCGQualityValidator_eventCalculateLODCoverage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage_Statics::PCGQualityValidator_eventCalculateLODCoverage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execCalculateLODCoverage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateLODCoverage();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function CalculateLODCoverage *************************

// ********** Begin Class UPCGQualityValidator Function CalculateQualityScore **********************
struct Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore_Statics
{
	struct PCGQualityValidator_eventCalculateQualityScore_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Assessment" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventCalculateQualityScore_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "CalculateQualityScore", Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore_Statics::PCGQualityValidator_eventCalculateQualityScore_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore_Statics::PCGQualityValidator_eventCalculateQualityScore_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execCalculateQualityScore)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateQualityScore();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function CalculateQualityScore ************************

// ********** Begin Class UPCGQualityValidator Function CalculateTextureQuality ********************
struct Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality_Statics
{
	struct PCGQualityValidator_eventCalculateTextureQuality_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de c\xc3\xa1lculo de qualidade\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de c\xc3\xa1lculo de qualidade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventCalculateTextureQuality_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "CalculateTextureQuality", Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality_Statics::PCGQualityValidator_eventCalculateTextureQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality_Statics::PCGQualityValidator_eventCalculateTextureQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execCalculateTextureQuality)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateTextureQuality();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function CalculateTextureQuality **********************

// ********** Begin Class UPCGQualityValidator Function DisableCategory ****************************
struct Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics
{
	struct PCGQualityValidator_eventDisableCategory_Parms
	{
		EPCGValidationCategory Category;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Config" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventDisableCategory_Parms, Category), Z_Construct_UEnum_Aura_EPCGValidationCategory, METADATA_PARAMS(0, nullptr) }; // 760008476
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::NewProp_Category,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "DisableCategory", Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::PCGQualityValidator_eventDisableCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::PCGQualityValidator_eventDisableCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_DisableCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_DisableCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execDisableCategory)
{
	P_GET_ENUM(EPCGValidationCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DisableCategory(EPCGValidationCategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function DisableCategory ******************************

// ********** Begin Class UPCGQualityValidator Function EnableCategory *****************************
struct Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics
{
	struct PCGQualityValidator_eventEnableCategory_Parms
	{
		EPCGValidationCategory Category;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Config" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventEnableCategory_Parms, Category), Z_Construct_UEnum_Aura_EPCGValidationCategory, METADATA_PARAMS(0, nullptr) }; // 760008476
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::NewProp_Category,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "EnableCategory", Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::PCGQualityValidator_eventEnableCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::PCGQualityValidator_eventEnableCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_EnableCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_EnableCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execEnableCategory)
{
	P_GET_ENUM(EPCGValidationCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableCategory(EPCGValidationCategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function EnableCategory *******************************

// ********** Begin Class UPCGQualityValidator Function ExportReport *******************************
struct Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics
{
	struct PCGQualityValidator_eventExportReport_Parms
	{
		FString ValidationID;
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Reporting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Reporting\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reporting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::NewProp_ValidationID = { "ValidationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventExportReport_Parms, ValidationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationID_MetaData), NewProp_ValidationID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventExportReport_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGQualityValidator_eventExportReport_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGQualityValidator_eventExportReport_Parms), &Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::NewProp_ValidationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "ExportReport", Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::PCGQualityValidator_eventExportReport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::PCGQualityValidator_eventExportReport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_ExportReport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_ExportReport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execExportReport)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ValidationID);
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExportReport(Z_Param_ValidationID,Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function ExportReport *********************************

// ********** Begin Class UPCGQualityValidator Function FixAllAutoFixableIssues ********************
struct Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues_Statics
{
	struct PCGQualityValidator_eventFixAllAutoFixableIssues_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Issues" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventFixAllAutoFixableIssues_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "FixAllAutoFixableIssues", Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues_Statics::PCGQualityValidator_eventFixAllAutoFixableIssues_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues_Statics::PCGQualityValidator_eventFixAllAutoFixableIssues_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execFixAllAutoFixableIssues)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->FixAllAutoFixableIssues();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function FixAllAutoFixableIssues **********************

// ********** Begin Class UPCGQualityValidator Function FixIssue ***********************************
struct Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics
{
	struct PCGQualityValidator_eventFixIssue_Parms
	{
		FString IssueID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Issues" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IssueID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_IssueID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::NewProp_IssueID = { "IssueID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventFixIssue_Parms, IssueID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IssueID_MetaData), NewProp_IssueID_MetaData) };
void Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGQualityValidator_eventFixIssue_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGQualityValidator_eventFixIssue_Parms), &Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::NewProp_IssueID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "FixIssue", Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::PCGQualityValidator_eventFixIssue_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::PCGQualityValidator_eventFixIssue_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_FixIssue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_FixIssue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execFixIssue)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_IssueID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->FixIssue(Z_Param_IssueID);
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function FixIssue *************************************

// ********** Begin Class UPCGQualityValidator Function GenerateHTMLReport *************************
struct Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics
{
	struct PCGQualityValidator_eventGenerateHTMLReport_Parms
	{
		FString ValidationID;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Reporting" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::NewProp_ValidationID = { "ValidationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventGenerateHTMLReport_Parms, ValidationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationID_MetaData), NewProp_ValidationID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventGenerateHTMLReport_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::NewProp_ValidationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "GenerateHTMLReport", Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::PCGQualityValidator_eventGenerateHTMLReport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::PCGQualityValidator_eventGenerateHTMLReport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execGenerateHTMLReport)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ValidationID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GenerateHTMLReport(Z_Param_ValidationID);
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function GenerateHTMLReport ***************************

// ********** Begin Class UPCGQualityValidator Function GenerateJSONReport *************************
struct Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics
{
	struct PCGQualityValidator_eventGenerateJSONReport_Parms
	{
		FString ValidationID;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Reporting" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::NewProp_ValidationID = { "ValidationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventGenerateJSONReport_Parms, ValidationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationID_MetaData), NewProp_ValidationID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventGenerateJSONReport_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::NewProp_ValidationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "GenerateJSONReport", Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::PCGQualityValidator_eventGenerateJSONReport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::PCGQualityValidator_eventGenerateJSONReport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execGenerateJSONReport)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ValidationID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GenerateJSONReport(Z_Param_ValidationID);
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function GenerateJSONReport ***************************

// ********** Begin Class UPCGQualityValidator Function GetAllReports ******************************
struct Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics
{
	struct PCGQualityValidator_eventGetAllReports_Parms
	{
		TArray<FPCGValidationReport> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Validation" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGValidationReport, METADATA_PARAMS(0, nullptr) }; // 2761269386
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventGetAllReports_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2761269386
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "GetAllReports", Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::PCGQualityValidator_eventGetAllReports_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::PCGQualityValidator_eventGetAllReports_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_GetAllReports()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_GetAllReports_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execGetAllReports)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGValidationReport>*)Z_Param__Result=P_THIS->GetAllReports();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function GetAllReports ********************************

// ********** Begin Class UPCGQualityValidator Function GetCurrentMetrics **************************
struct Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics_Statics
{
	struct PCGQualityValidator_eventGetCurrentMetrics_Parms
	{
		FPCGQualityMetrics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Assessment" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventGetCurrentMetrics_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGQualityMetrics, METADATA_PARAMS(0, nullptr) }; // 565725014
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "GetCurrentMetrics", Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics_Statics::PCGQualityValidator_eventGetCurrentMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics_Statics::PCGQualityValidator_eventGetCurrentMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execGetCurrentMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGQualityMetrics*)Z_Param__Result=P_THIS->GetCurrentMetrics();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function GetCurrentMetrics ****************************

// ********** Begin Class UPCGQualityValidator Function GetIssuesByCategory ************************
struct Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics
{
	struct PCGQualityValidator_eventGetIssuesByCategory_Parms
	{
		EPCGValidationCategory Category;
		TArray<FPCGValidationIssue> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Issues" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Issue Management\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Issue Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventGetIssuesByCategory_Parms, Category), Z_Construct_UEnum_Aura_EPCGValidationCategory, METADATA_PARAMS(0, nullptr) }; // 760008476
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGValidationIssue, METADATA_PARAMS(0, nullptr) }; // 2081596474
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventGetIssuesByCategory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2081596474
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "GetIssuesByCategory", Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::PCGQualityValidator_eventGetIssuesByCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::PCGQualityValidator_eventGetIssuesByCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execGetIssuesByCategory)
{
	P_GET_ENUM(EPCGValidationCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGValidationIssue>*)Z_Param__Result=P_THIS->GetIssuesByCategory(EPCGValidationCategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function GetIssuesByCategory **************************

// ********** Begin Class UPCGQualityValidator Function GetIssuesBySeverity ************************
struct Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics
{
	struct PCGQualityValidator_eventGetIssuesBySeverity_Parms
	{
		EPCGValidationSeverity Severity;
		TArray<FPCGValidationIssue> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Issues" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Severity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::NewProp_Severity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventGetIssuesBySeverity_Parms, Severity), Z_Construct_UEnum_Aura_EPCGValidationSeverity, METADATA_PARAMS(0, nullptr) }; // 1120267668
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGValidationIssue, METADATA_PARAMS(0, nullptr) }; // 2081596474
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventGetIssuesBySeverity_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2081596474
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::NewProp_Severity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::NewProp_Severity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "GetIssuesBySeverity", Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::PCGQualityValidator_eventGetIssuesBySeverity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::PCGQualityValidator_eventGetIssuesBySeverity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execGetIssuesBySeverity)
{
	P_GET_ENUM(EPCGValidationSeverity,Z_Param_Severity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGValidationIssue>*)Z_Param__Result=P_THIS->GetIssuesBySeverity(EPCGValidationSeverity(Z_Param_Severity));
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function GetIssuesBySeverity **************************

// ********** Begin Class UPCGQualityValidator Function GetValidationConfig ************************
struct Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig_Statics
{
	struct PCGQualityValidator_eventGetValidationConfig_Parms
	{
		FPCGValidationConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Config" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventGetValidationConfig_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGValidationConfig, METADATA_PARAMS(0, nullptr) }; // 2696257098
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "GetValidationConfig", Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig_Statics::PCGQualityValidator_eventGetValidationConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig_Statics::PCGQualityValidator_eventGetValidationConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execGetValidationConfig)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGValidationConfig*)Z_Param__Result=P_THIS->GetValidationConfig();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function GetValidationConfig **************************

// ********** Begin Class UPCGQualityValidator Function GetValidationReport ************************
struct Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics
{
	struct PCGQualityValidator_eventGetValidationReport_Parms
	{
		FString ValidationID;
		FPCGValidationReport ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Validation" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::NewProp_ValidationID = { "ValidationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventGetValidationReport_Parms, ValidationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationID_MetaData), NewProp_ValidationID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventGetValidationReport_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGValidationReport, METADATA_PARAMS(0, nullptr) }; // 2761269386
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::NewProp_ValidationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "GetValidationReport", Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::PCGQualityValidator_eventGetValidationReport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::PCGQualityValidator_eventGetValidationReport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execGetValidationReport)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ValidationID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGValidationReport*)Z_Param__Result=P_THIS->GetValidationReport(Z_Param_ValidationID);
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function GetValidationReport **************************

// ********** Begin Class UPCGQualityValidator Function IsAutomaticValidationRunning ***************
struct Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics
{
	struct PCGQualityValidator_eventIsAutomaticValidationRunning_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Automation" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGQualityValidator_eventIsAutomaticValidationRunning_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGQualityValidator_eventIsAutomaticValidationRunning_Parms), &Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "IsAutomaticValidationRunning", Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::PCGQualityValidator_eventIsAutomaticValidationRunning_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::PCGQualityValidator_eventIsAutomaticValidationRunning_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execIsAutomaticValidationRunning)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAutomaticValidationRunning();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function IsAutomaticValidationRunning *****************

// ********** Begin Class UPCGQualityValidator Function MeetsQualityStandards **********************
struct Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics
{
	struct PCGQualityValidator_eventMeetsQualityStandards_Parms
	{
		EPCGQualityLevel RequiredLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Assessment" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RequiredLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RequiredLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::NewProp_RequiredLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::NewProp_RequiredLevel = { "RequiredLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventMeetsQualityStandards_Parms, RequiredLevel), Z_Construct_UEnum_Aura_EPCGQualityLevel, METADATA_PARAMS(0, nullptr) }; // 1582851793
void Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGQualityValidator_eventMeetsQualityStandards_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGQualityValidator_eventMeetsQualityStandards_Parms), &Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::NewProp_RequiredLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::NewProp_RequiredLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "MeetsQualityStandards", Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::PCGQualityValidator_eventMeetsQualityStandards_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::PCGQualityValidator_eventMeetsQualityStandards_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execMeetsQualityStandards)
{
	P_GET_ENUM(EPCGQualityLevel,Z_Param_RequiredLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MeetsQualityStandards(EPCGQualityLevel(Z_Param_RequiredLevel));
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function MeetsQualityStandards ************************

// ********** Begin Class UPCGQualityValidator Function SetValidationConfig ************************
struct Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig_Statics
{
	struct PCGQualityValidator_eventSetValidationConfig_Parms
	{
		FPCGValidationConfig NewConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig_Statics::NewProp_NewConfig = { "NewConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventSetValidationConfig_Parms, NewConfig), Z_Construct_UScriptStruct_FPCGValidationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewConfig_MetaData), NewProp_NewConfig_MetaData) }; // 2696257098
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig_Statics::NewProp_NewConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "SetValidationConfig", Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig_Statics::PCGQualityValidator_eventSetValidationConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig_Statics::PCGQualityValidator_eventSetValidationConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execSetValidationConfig)
{
	P_GET_STRUCT_REF(FPCGValidationConfig,Z_Param_Out_NewConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetValidationConfig(Z_Param_Out_NewConfig);
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function SetValidationConfig **************************

// ********** Begin Class UPCGQualityValidator Function StartAutomaticValidation *******************
struct Z_Construct_UFunction_UPCGQualityValidator_StartAutomaticValidation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Automation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Automation\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Automation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_StartAutomaticValidation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "StartAutomaticValidation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_StartAutomaticValidation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_StartAutomaticValidation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGQualityValidator_StartAutomaticValidation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_StartAutomaticValidation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execStartAutomaticValidation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartAutomaticValidation();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function StartAutomaticValidation *********************

// ********** Begin Class UPCGQualityValidator Function StartValidation ****************************
struct Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics
{
	struct PCGQualityValidator_eventStartValidation_Parms
	{
		FPCGValidationConfig Config;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core Validation Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core Validation Functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventStartValidation_Parms, Config), Z_Construct_UScriptStruct_FPCGValidationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 2696257098
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventStartValidation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "StartValidation", Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::PCGQualityValidator_eventStartValidation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::PCGQualityValidator_eventStartValidation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_StartValidation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_StartValidation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execStartValidation)
{
	P_GET_STRUCT_REF(FPCGValidationConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->StartValidation(Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function StartValidation ******************************

// ********** Begin Class UPCGQualityValidator Function StopAutomaticValidation ********************
struct Z_Construct_UFunction_UPCGQualityValidator_StopAutomaticValidation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Automation" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_StopAutomaticValidation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "StopAutomaticValidation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_StopAutomaticValidation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_StopAutomaticValidation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGQualityValidator_StopAutomaticValidation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_StopAutomaticValidation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execStopAutomaticValidation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopAutomaticValidation();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function StopAutomaticValidation **********************

// ********** Begin Class UPCGQualityValidator Function StopValidation *****************************
struct Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics
{
	struct PCGQualityValidator_eventStopValidation_Parms
	{
		FString ValidationID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Validation" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::NewProp_ValidationID = { "ValidationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventStopValidation_Parms, ValidationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationID_MetaData), NewProp_ValidationID_MetaData) };
void Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGQualityValidator_eventStopValidation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGQualityValidator_eventStopValidation_Parms), &Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::NewProp_ValidationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "StopValidation", Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::PCGQualityValidator_eventStopValidation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::PCGQualityValidator_eventStopValidation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_StopValidation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_StopValidation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execStopValidation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ValidationID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopValidation(Z_Param_ValidationID);
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function StopValidation *******************************

// ********** Begin Class UPCGQualityValidator Function ValidateLumenSetup *************************
struct Z_Construct_UFunction_UPCGQualityValidator_ValidateLumenSetup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Integration" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_ValidateLumenSetup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "ValidateLumenSetup", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_ValidateLumenSetup_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_ValidateLumenSetup_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGQualityValidator_ValidateLumenSetup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_ValidateLumenSetup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execValidateLumenSetup)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ValidateLumenSetup();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function ValidateLumenSetup ***************************

// ********** Begin Class UPCGQualityValidator Function ValidateNaniteContent **********************
struct Z_Construct_UFunction_UPCGQualityValidator_ValidateNaniteContent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Integration" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_ValidateNaniteContent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "ValidateNaniteContent", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_ValidateNaniteContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_ValidateNaniteContent_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGQualityValidator_ValidateNaniteContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_ValidateNaniteContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execValidateNaniteContent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ValidateNaniteContent();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function ValidateNaniteContent ************************

// ********** Begin Class UPCGQualityValidator Function ValidatePCGComponent ***********************
struct Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent_Statics
{
	struct PCGQualityValidator_eventValidatePCGComponent_Parms
	{
		UPCGComponent* Component;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with PCG Systems\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with PCG Systems" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Component_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventValidatePCGComponent_Parms, Component), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Component_MetaData), NewProp_Component_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent_Statics::NewProp_Component,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "ValidatePCGComponent", Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent_Statics::PCGQualityValidator_eventValidatePCGComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent_Statics::PCGQualityValidator_eventValidatePCGComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execValidatePCGComponent)
{
	P_GET_OBJECT(UPCGComponent,Z_Param_Component);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ValidatePCGComponent(Z_Param_Component);
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function ValidatePCGComponent *************************

// ********** Begin Class UPCGQualityValidator Function ValidatePCGGraph ***************************
struct Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph_Statics
{
	struct PCGQualityValidator_eventValidatePCGGraph_Parms
	{
		UPCGGraph* Graph;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Integration" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Graph;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph_Statics::NewProp_Graph = { "Graph", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGQualityValidator_eventValidatePCGGraph_Parms, Graph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph_Statics::NewProp_Graph,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "ValidatePCGGraph", Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph_Statics::PCGQualityValidator_eventValidatePCGGraph_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph_Statics::PCGQualityValidator_eventValidatePCGGraph_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execValidatePCGGraph)
{
	P_GET_OBJECT(UPCGGraph,Z_Param_Graph);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ValidatePCGGraph(Z_Param_Graph);
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function ValidatePCGGraph *****************************

// ********** Begin Class UPCGQualityValidator Function ValidateWorldPartition *********************
struct Z_Construct_UFunction_UPCGQualityValidator_ValidateWorldPartition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG|Quality|Integration" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGQualityValidator_ValidateWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGQualityValidator, nullptr, "ValidateWorldPartition", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGQualityValidator_ValidateWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGQualityValidator_ValidateWorldPartition_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGQualityValidator_ValidateWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGQualityValidator_ValidateWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGQualityValidator::execValidateWorldPartition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ValidateWorldPartition();
	P_NATIVE_END;
}
// ********** End Class UPCGQualityValidator Function ValidateWorldPartition ***********************

// ********** Begin Class UPCGQualityValidator *****************************************************
void UPCGQualityValidator::StaticRegisterNativesUPCGQualityValidator()
{
	UClass* Class = UPCGQualityValidator::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AssessOverallQuality", &UPCGQualityValidator::execAssessOverallQuality },
		{ "CalculateGeometryComplexity", &UPCGQualityValidator::execCalculateGeometryComplexity },
		{ "CalculateLightingQuality", &UPCGQualityValidator::execCalculateLightingQuality },
		{ "CalculateLODCoverage", &UPCGQualityValidator::execCalculateLODCoverage },
		{ "CalculateQualityScore", &UPCGQualityValidator::execCalculateQualityScore },
		{ "CalculateTextureQuality", &UPCGQualityValidator::execCalculateTextureQuality },
		{ "DisableCategory", &UPCGQualityValidator::execDisableCategory },
		{ "EnableCategory", &UPCGQualityValidator::execEnableCategory },
		{ "ExportReport", &UPCGQualityValidator::execExportReport },
		{ "FixAllAutoFixableIssues", &UPCGQualityValidator::execFixAllAutoFixableIssues },
		{ "FixIssue", &UPCGQualityValidator::execFixIssue },
		{ "GenerateHTMLReport", &UPCGQualityValidator::execGenerateHTMLReport },
		{ "GenerateJSONReport", &UPCGQualityValidator::execGenerateJSONReport },
		{ "GetAllReports", &UPCGQualityValidator::execGetAllReports },
		{ "GetCurrentMetrics", &UPCGQualityValidator::execGetCurrentMetrics },
		{ "GetIssuesByCategory", &UPCGQualityValidator::execGetIssuesByCategory },
		{ "GetIssuesBySeverity", &UPCGQualityValidator::execGetIssuesBySeverity },
		{ "GetValidationConfig", &UPCGQualityValidator::execGetValidationConfig },
		{ "GetValidationReport", &UPCGQualityValidator::execGetValidationReport },
		{ "IsAutomaticValidationRunning", &UPCGQualityValidator::execIsAutomaticValidationRunning },
		{ "MeetsQualityStandards", &UPCGQualityValidator::execMeetsQualityStandards },
		{ "SetValidationConfig", &UPCGQualityValidator::execSetValidationConfig },
		{ "StartAutomaticValidation", &UPCGQualityValidator::execStartAutomaticValidation },
		{ "StartValidation", &UPCGQualityValidator::execStartValidation },
		{ "StopAutomaticValidation", &UPCGQualityValidator::execStopAutomaticValidation },
		{ "StopValidation", &UPCGQualityValidator::execStopValidation },
		{ "ValidateLumenSetup", &UPCGQualityValidator::execValidateLumenSetup },
		{ "ValidateNaniteContent", &UPCGQualityValidator::execValidateNaniteContent },
		{ "ValidatePCGComponent", &UPCGQualityValidator::execValidatePCGComponent },
		{ "ValidatePCGGraph", &UPCGQualityValidator::execValidatePCGGraph },
		{ "ValidateWorldPartition", &UPCGQualityValidator::execValidateWorldPartition },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPCGQualityValidator;
UClass* UPCGQualityValidator::GetPrivateStaticClass()
{
	using TClass = UPCGQualityValidator;
	if (!Z_Registration_Info_UClass_UPCGQualityValidator.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PCGQualityValidator"),
			Z_Registration_Info_UClass_UPCGQualityValidator.InnerSingleton,
			StaticRegisterNativesUPCGQualityValidator,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPCGQualityValidator.InnerSingleton;
}
UClass* Z_Construct_UClass_UPCGQualityValidator_NoRegister()
{
	return UPCGQualityValidator::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPCGQualityValidator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "PCG|Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * UPCGQualityValidator - Advanced quality validation system for PCG content\n * Provides automated quality assurance, performance validation, and compliance checking\n * Integrates with UE5.6 modern APIs for comprehensive content validation\n */" },
#endif
		{ "IncludePath", "UPCGQualityValidator.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UPCGQualityValidator - Advanced quality validation system for PCG content\nProvides automated quality assurance, performance validation, and compliance checking\nIntegrates with UE5.6 modern APIs for comprehensive content validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnValidationStarted_MetaData[] = {
		{ "Category", "PCG|Quality|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegates\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnValidationCompleted_MetaData[] = {
		{ "Category", "PCG|Quality|Events" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnIssueDetected_MetaData[] = {
		{ "Category", "PCG|Quality|Events" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnIssueFixed_MetaData[] = {
		{ "Category", "PCG|Quality|Events" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnQualityChanged_MetaData[] = {
		{ "Category", "PCG|Quality|Events" },
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationConfig_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveValidations_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Active Validations\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Active Validations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationHistory_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation History\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation History" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentIssues_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current Issues\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current Issues" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPartitionManager_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration References\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration References" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteOptimizer_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LumenIntegrator_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingManager_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceProfiler_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheManager_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosIntegrator_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGQualityValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnValidationStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnValidationCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnIssueDetected;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnIssueFixed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnQualityChanged;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ValidationConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveValidations_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActiveValidations_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveValidations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ValidationHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValidationHistory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentIssues_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CurrentIssues;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_WorldPartitionManager;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_NaniteOptimizer;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_LumenIntegrator;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_StreamingManager;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_PerformanceProfiler;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_CacheManager;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ChaosIntegrator;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPCGQualityValidator_AssessOverallQuality, "AssessOverallQuality" }, // 604661264
		{ &Z_Construct_UFunction_UPCGQualityValidator_CalculateGeometryComplexity, "CalculateGeometryComplexity" }, // 2222437821
		{ &Z_Construct_UFunction_UPCGQualityValidator_CalculateLightingQuality, "CalculateLightingQuality" }, // 4142138489
		{ &Z_Construct_UFunction_UPCGQualityValidator_CalculateLODCoverage, "CalculateLODCoverage" }, // 3654582183
		{ &Z_Construct_UFunction_UPCGQualityValidator_CalculateQualityScore, "CalculateQualityScore" }, // 1136857587
		{ &Z_Construct_UFunction_UPCGQualityValidator_CalculateTextureQuality, "CalculateTextureQuality" }, // 2853230471
		{ &Z_Construct_UFunction_UPCGQualityValidator_DisableCategory, "DisableCategory" }, // 2025514975
		{ &Z_Construct_UFunction_UPCGQualityValidator_EnableCategory, "EnableCategory" }, // 826760645
		{ &Z_Construct_UFunction_UPCGQualityValidator_ExportReport, "ExportReport" }, // 1042494568
		{ &Z_Construct_UFunction_UPCGQualityValidator_FixAllAutoFixableIssues, "FixAllAutoFixableIssues" }, // 2817228298
		{ &Z_Construct_UFunction_UPCGQualityValidator_FixIssue, "FixIssue" }, // 143965395
		{ &Z_Construct_UFunction_UPCGQualityValidator_GenerateHTMLReport, "GenerateHTMLReport" }, // 3835594688
		{ &Z_Construct_UFunction_UPCGQualityValidator_GenerateJSONReport, "GenerateJSONReport" }, // 1688558926
		{ &Z_Construct_UFunction_UPCGQualityValidator_GetAllReports, "GetAllReports" }, // 2338173810
		{ &Z_Construct_UFunction_UPCGQualityValidator_GetCurrentMetrics, "GetCurrentMetrics" }, // 3773201972
		{ &Z_Construct_UFunction_UPCGQualityValidator_GetIssuesByCategory, "GetIssuesByCategory" }, // 1574832595
		{ &Z_Construct_UFunction_UPCGQualityValidator_GetIssuesBySeverity, "GetIssuesBySeverity" }, // 3729015861
		{ &Z_Construct_UFunction_UPCGQualityValidator_GetValidationConfig, "GetValidationConfig" }, // 726005740
		{ &Z_Construct_UFunction_UPCGQualityValidator_GetValidationReport, "GetValidationReport" }, // 2795374696
		{ &Z_Construct_UFunction_UPCGQualityValidator_IsAutomaticValidationRunning, "IsAutomaticValidationRunning" }, // 3987692645
		{ &Z_Construct_UFunction_UPCGQualityValidator_MeetsQualityStandards, "MeetsQualityStandards" }, // 1163069746
		{ &Z_Construct_UFunction_UPCGQualityValidator_SetValidationConfig, "SetValidationConfig" }, // 432232829
		{ &Z_Construct_UFunction_UPCGQualityValidator_StartAutomaticValidation, "StartAutomaticValidation" }, // 2491736354
		{ &Z_Construct_UFunction_UPCGQualityValidator_StartValidation, "StartValidation" }, // 2442388420
		{ &Z_Construct_UFunction_UPCGQualityValidator_StopAutomaticValidation, "StopAutomaticValidation" }, // 1900040734
		{ &Z_Construct_UFunction_UPCGQualityValidator_StopValidation, "StopValidation" }, // 1239415250
		{ &Z_Construct_UFunction_UPCGQualityValidator_ValidateLumenSetup, "ValidateLumenSetup" }, // 4231537683
		{ &Z_Construct_UFunction_UPCGQualityValidator_ValidateNaniteContent, "ValidateNaniteContent" }, // 3559741532
		{ &Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGComponent, "ValidatePCGComponent" }, // 884971880
		{ &Z_Construct_UFunction_UPCGQualityValidator_ValidatePCGGraph, "ValidatePCGGraph" }, // 3910954708
		{ &Z_Construct_UFunction_UPCGQualityValidator_ValidateWorldPartition, "ValidateWorldPartition" }, // 2412105415
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPCGQualityValidator>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_OnValidationStarted = { "OnValidationStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, OnValidationStarted), Z_Construct_UDelegateFunction_Aura_OnValidationStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnValidationStarted_MetaData), NewProp_OnValidationStarted_MetaData) }; // 432463433
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_OnValidationCompleted = { "OnValidationCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, OnValidationCompleted), Z_Construct_UDelegateFunction_Aura_OnValidationCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnValidationCompleted_MetaData), NewProp_OnValidationCompleted_MetaData) }; // 796711486
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_OnIssueDetected = { "OnIssueDetected", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, OnIssueDetected), Z_Construct_UDelegateFunction_Aura_OnIssueDetected__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnIssueDetected_MetaData), NewProp_OnIssueDetected_MetaData) }; // 1921714505
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_OnIssueFixed = { "OnIssueFixed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, OnIssueFixed), Z_Construct_UDelegateFunction_Aura_OnIssueFixed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnIssueFixed_MetaData), NewProp_OnIssueFixed_MetaData) }; // 939017072
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_OnQualityChanged = { "OnQualityChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, OnQualityChanged), Z_Construct_UDelegateFunction_Aura_OnQualityChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnQualityChanged_MetaData), NewProp_OnQualityChanged_MetaData) }; // 3176663447
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ValidationConfig = { "ValidationConfig", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, ValidationConfig), Z_Construct_UScriptStruct_FPCGValidationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationConfig_MetaData), NewProp_ValidationConfig_MetaData) }; // 2696257098
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ActiveValidations_ValueProp = { "ActiveValidations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPCGValidationReport, METADATA_PARAMS(0, nullptr) }; // 2761269386
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ActiveValidations_Key_KeyProp = { "ActiveValidations_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ActiveValidations = { "ActiveValidations", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, ActiveValidations), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveValidations_MetaData), NewProp_ActiveValidations_MetaData) }; // 2761269386
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ValidationHistory_Inner = { "ValidationHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGValidationReport, METADATA_PARAMS(0, nullptr) }; // 2761269386
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ValidationHistory = { "ValidationHistory", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, ValidationHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationHistory_MetaData), NewProp_ValidationHistory_MetaData) }; // 2761269386
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_CurrentIssues_Inner = { "CurrentIssues", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGValidationIssue, METADATA_PARAMS(0, nullptr) }; // 2081596474
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_CurrentIssues = { "CurrentIssues", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, CurrentIssues), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentIssues_MetaData), NewProp_CurrentIssues_MetaData) }; // 2081596474
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_WorldPartitionManager = { "WorldPartitionManager", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, WorldPartitionManager), Z_Construct_UClass_APCGWorldPartitionManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPartitionManager_MetaData), NewProp_WorldPartitionManager_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_NaniteOptimizer = { "NaniteOptimizer", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, NaniteOptimizer), Z_Construct_UClass_APCGNaniteOptimizer_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteOptimizer_MetaData), NewProp_NaniteOptimizer_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_LumenIntegrator = { "LumenIntegrator", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, LumenIntegrator), Z_Construct_UClass_APCGLumenIntegrator_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LumenIntegrator_MetaData), NewProp_LumenIntegrator_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_StreamingManager = { "StreamingManager", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, StreamingManager), Z_Construct_UClass_APCGStreamingManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingManager_MetaData), NewProp_StreamingManager_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_PerformanceProfiler = { "PerformanceProfiler", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, PerformanceProfiler), Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceProfiler_MetaData), NewProp_PerformanceProfiler_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_CacheManager = { "CacheManager", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, CacheManager), Z_Construct_UClass_APCGCacheManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheManager_MetaData), NewProp_CacheManager_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ChaosIntegrator = { "ChaosIntegrator", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGQualityValidator, ChaosIntegrator), Z_Construct_UClass_APCGChaosIntegrator_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosIntegrator_MetaData), NewProp_ChaosIntegrator_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPCGQualityValidator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_OnValidationStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_OnValidationCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_OnIssueDetected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_OnIssueFixed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_OnQualityChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ValidationConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ActiveValidations_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ActiveValidations_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ActiveValidations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ValidationHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ValidationHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_CurrentIssues_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_CurrentIssues,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_WorldPartitionManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_NaniteOptimizer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_LumenIntegrator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_StreamingManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_PerformanceProfiler,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_CacheManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGQualityValidator_Statics::NewProp_ChaosIntegrator,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPCGQualityValidator_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPCGQualityValidator_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPCGQualityValidator_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPCGQualityValidator_Statics::ClassParams = {
	&UPCGQualityValidator::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UPCGQualityValidator_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UPCGQualityValidator_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPCGQualityValidator_Statics::Class_MetaDataParams), Z_Construct_UClass_UPCGQualityValidator_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPCGQualityValidator()
{
	if (!Z_Registration_Info_UClass_UPCGQualityValidator.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPCGQualityValidator.OuterSingleton, Z_Construct_UClass_UPCGQualityValidator_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPCGQualityValidator.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPCGQualityValidator);
UPCGQualityValidator::~UPCGQualityValidator() {}
// ********** End Class UPCGQualityValidator *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGQualityValidator_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPCGValidationSeverity_StaticEnum, TEXT("EPCGValidationSeverity"), &Z_Registration_Info_UEnum_EPCGValidationSeverity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1120267668U) },
		{ EPCGValidationCategory_StaticEnum, TEXT("EPCGValidationCategory"), &Z_Registration_Info_UEnum_EPCGValidationCategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 760008476U) },
		{ EPCGValidationMode_StaticEnum, TEXT("EPCGValidationMode"), &Z_Registration_Info_UEnum_EPCGValidationMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3841298128U) },
		{ EPCGValidationResult_StaticEnum, TEXT("EPCGValidationResult"), &Z_Registration_Info_UEnum_EPCGValidationResult, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3676686090U) },
		{ EPCGQualityLevel_StaticEnum, TEXT("EPCGQualityLevel"), &Z_Registration_Info_UEnum_EPCGQualityLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1582851793U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPCGValidationConfig::StaticStruct, Z_Construct_UScriptStruct_FPCGValidationConfig_Statics::NewStructOps, TEXT("PCGValidationConfig"), &Z_Registration_Info_UScriptStruct_FPCGValidationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGValidationConfig), 2696257098U) },
		{ FPCGValidationIssue::StaticStruct, Z_Construct_UScriptStruct_FPCGValidationIssue_Statics::NewStructOps, TEXT("PCGValidationIssue"), &Z_Registration_Info_UScriptStruct_FPCGValidationIssue, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGValidationIssue), 2081596474U) },
		{ FPCGValidationReport::StaticStruct, Z_Construct_UScriptStruct_FPCGValidationReport_Statics::NewStructOps, TEXT("PCGValidationReport"), &Z_Registration_Info_UScriptStruct_FPCGValidationReport, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGValidationReport), 2761269386U) },
		{ FPCGQualityMetrics::StaticStruct, Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics::NewStructOps, TEXT("PCGQualityMetrics"), &Z_Registration_Info_UScriptStruct_FPCGQualityMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGQualityMetrics), 565725014U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPCGQualityValidator, UPCGQualityValidator::StaticClass, TEXT("UPCGQualityValidator"), &Z_Registration_Info_UClass_UPCGQualityValidator, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPCGQualityValidator), 2040246857U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGQualityValidator_h__Script_Aura_1083619777(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGQualityValidator_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGQualityValidator_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGQualityValidator_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGQualityValidator_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGQualityValidator_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGQualityValidator_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
