// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Interfaces/RiverSystemInterface.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRiverSystemInterface() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_URiverSystemInterface();
AURA_API UClass* Z_Construct_UClass_URiverSystemInterface_NoRegister();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FRiverGenerationParams();
COREUOBJECT_API UClass* Z_Construct_UClass_UInterface();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FRiverGenerationParams ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRiverGenerationParams;
class UScriptStruct* FRiverGenerationParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRiverGenerationParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRiverGenerationParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRiverGenerationParams, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("RiverGenerationParams"));
	}
	return Z_Registration_Info_UScriptStruct_FRiverGenerationParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRiverGenerationParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPosition_MetaData[] = {
		{ "Category", "River" },
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPosition_MetaData[] = {
		{ "Category", "River" },
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "Category", "River" },
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Depth_MetaData[] = {
		{ "Category", "River" },
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpeed_MetaData[] = {
		{ "Category", "River" },
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateIsland_MetaData[] = {
		{ "Category", "River" },
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandRadius_MetaData[] = {
		{ "Category", "River" },
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Depth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeed;
	static void NewProp_bGenerateIsland_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateIsland;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_IslandRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRiverGenerationParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_StartPosition = { "StartPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverGenerationParams, StartPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPosition_MetaData), NewProp_StartPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_EndPosition = { "EndPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverGenerationParams, EndPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPosition_MetaData), NewProp_EndPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverGenerationParams, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_Depth = { "Depth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverGenerationParams, Depth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Depth_MetaData), NewProp_Depth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_FlowSpeed = { "FlowSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverGenerationParams, FlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpeed_MetaData), NewProp_FlowSpeed_MetaData) };
void Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_bGenerateIsland_SetBit(void* Obj)
{
	((FRiverGenerationParams*)Obj)->bGenerateIsland = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_bGenerateIsland = { "bGenerateIsland", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRiverGenerationParams), &Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_bGenerateIsland_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateIsland_MetaData), NewProp_bGenerateIsland_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_IslandRadius = { "IslandRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverGenerationParams, IslandRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandRadius_MetaData), NewProp_IslandRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_StartPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_EndPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_Depth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_FlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_bGenerateIsland,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewProp_IslandRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"RiverGenerationParams",
	Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::PropPointers),
	sizeof(FRiverGenerationParams),
	alignof(FRiverGenerationParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRiverGenerationParams()
{
	if (!Z_Registration_Info_UScriptStruct_FRiverGenerationParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRiverGenerationParams.InnerSingleton, Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRiverGenerationParams.InnerSingleton;
}
// ********** End ScriptStruct FRiverGenerationParams **********************************************

// ********** Begin Interface URiverSystemInterface Function ClearRiverData ************************
struct RiverSystemInterface_eventClearRiverData_Parms
{
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RiverSystemInterface_eventClearRiverData_Parms()
		: ReturnValue(false)
	{
	}
};
bool IRiverSystemInterface::ClearRiverData()
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_ClearRiverData instead.");
	RiverSystemInterface_eventClearRiverData_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_URiverSystemInterface_ClearRiverData = FName(TEXT("ClearRiverData"));
bool IRiverSystemInterface::Execute_ClearRiverData(UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URiverSystemInterface::StaticClass()));
	RiverSystemInterface_eventClearRiverData_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URiverSystemInterface_ClearRiverData);
	if (Func)
	{
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_URiverSystemInterface_ClearRiverData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Limpa todos os dados do rio\n     * @return true se limpeza bem-sucedida\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpa todos os dados do rio\n@return true se limpeza bem-sucedida" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URiverSystemInterface_ClearRiverData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverSystemInterface_eventClearRiverData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URiverSystemInterface_ClearRiverData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverSystemInterface_eventClearRiverData_Parms), &Z_Construct_UFunction_URiverSystemInterface_ClearRiverData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URiverSystemInterface_ClearRiverData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_ClearRiverData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_ClearRiverData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URiverSystemInterface_ClearRiverData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URiverSystemInterface, nullptr, "ClearRiverData", Z_Construct_UFunction_URiverSystemInterface_ClearRiverData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_ClearRiverData_Statics::PropPointers), sizeof(RiverSystemInterface_eventClearRiverData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_ClearRiverData_Statics::Function_MetaDataParams), Z_Construct_UFunction_URiverSystemInterface_ClearRiverData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RiverSystemInterface_eventClearRiverData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URiverSystemInterface_ClearRiverData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URiverSystemInterface_ClearRiverData_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URiverSystemInterface Function ClearRiverData **************************

// ********** Begin Interface URiverSystemInterface Function ConfigureRiver ************************
struct RiverSystemInterface_eventConfigureRiver_Parms
{
	FRiverGenerationParams Params;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RiverSystemInterface_eventConfigureRiver_Parms()
		: ReturnValue(false)
	{
	}
};
bool IRiverSystemInterface::ConfigureRiver(FRiverGenerationParams const& Params)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_ConfigureRiver instead.");
	RiverSystemInterface_eventConfigureRiver_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_URiverSystemInterface_ConfigureRiver = FName(TEXT("ConfigureRiver"));
bool IRiverSystemInterface::Execute_ConfigureRiver(UObject* O, FRiverGenerationParams const& Params)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URiverSystemInterface::StaticClass()));
	RiverSystemInterface_eventConfigureRiver_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URiverSystemInterface_ConfigureRiver);
	if (Func)
	{
		Parms.Params=Params;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configura o rio com par\xc3\xa2metros espec\xc3\xad""ficos\n     * @param Params - Par\xc3\xa2metros de configura\xc3\xa7\xc3\xa3o do rio\n     * @return true se configurado com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura o rio com par\xc3\xa2metros espec\xc3\xad""ficos\n@param Params - Par\xc3\xa2metros de configura\xc3\xa7\xc3\xa3o do rio\n@return true se configurado com sucesso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Params_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Params;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::NewProp_Params = { "Params", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventConfigureRiver_Parms, Params), Z_Construct_UScriptStruct_FRiverGenerationParams, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Params_MetaData), NewProp_Params_MetaData) }; // 1940653944
void Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverSystemInterface_eventConfigureRiver_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverSystemInterface_eventConfigureRiver_Parms), &Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::NewProp_Params,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URiverSystemInterface, nullptr, "ConfigureRiver", Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::PropPointers), sizeof(RiverSystemInterface_eventConfigureRiver_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::Function_MetaDataParams), Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RiverSystemInterface_eventConfigureRiver_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URiverSystemInterface Function ConfigureRiver **************************

// ********** Begin Interface URiverSystemInterface Function CreateBridge **************************
struct RiverSystemInterface_eventCreateBridge_Parms
{
	FVector BridgePosition;
	float BridgeLength;
	float BridgeWidth;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RiverSystemInterface_eventCreateBridge_Parms()
		: ReturnValue(false)
	{
	}
};
bool IRiverSystemInterface::CreateBridge(FVector const& BridgePosition, float BridgeLength, float BridgeWidth)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_CreateBridge instead.");
	RiverSystemInterface_eventCreateBridge_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_URiverSystemInterface_CreateBridge = FName(TEXT("CreateBridge"));
bool IRiverSystemInterface::Execute_CreateBridge(UObject* O, FVector const& BridgePosition, float BridgeLength, float BridgeWidth)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URiverSystemInterface::StaticClass()));
	RiverSystemInterface_eventCreateBridge_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URiverSystemInterface_CreateBridge);
	if (Func)
	{
		Parms.BridgePosition=BridgePosition;
		Parms.BridgeLength=BridgeLength;
		Parms.BridgeWidth=BridgeWidth;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configura uma ponte sobre o rio\n     * @param BridgePosition - Posi\xc3\xa7\xc3\xa3o da ponte\n     * @param BridgeLength - Comprimento da ponte\n     * @param BridgeWidth - Largura da ponte\n     * @return true se ponte criada com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura uma ponte sobre o rio\n@param BridgePosition - Posi\xc3\xa7\xc3\xa3o da ponte\n@param BridgeLength - Comprimento da ponte\n@param BridgeWidth - Largura da ponte\n@return true se ponte criada com sucesso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BridgePosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BridgePosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BridgeLength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BridgeWidth;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::NewProp_BridgePosition = { "BridgePosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventCreateBridge_Parms, BridgePosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BridgePosition_MetaData), NewProp_BridgePosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::NewProp_BridgeLength = { "BridgeLength", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventCreateBridge_Parms, BridgeLength), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::NewProp_BridgeWidth = { "BridgeWidth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventCreateBridge_Parms, BridgeWidth), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverSystemInterface_eventCreateBridge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverSystemInterface_eventCreateBridge_Parms), &Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::NewProp_BridgePosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::NewProp_BridgeLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::NewProp_BridgeWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URiverSystemInterface, nullptr, "CreateBridge", Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::PropPointers), sizeof(RiverSystemInterface_eventCreateBridge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C20800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::Function_MetaDataParams), Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RiverSystemInterface_eventCreateBridge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URiverSystemInterface_CreateBridge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URiverSystemInterface_CreateBridge_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URiverSystemInterface Function CreateBridge ****************************

// ********** Begin Interface URiverSystemInterface Function GenerateRiverMesh *********************
struct RiverSystemInterface_eventGenerateRiverMesh_Parms
{
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RiverSystemInterface_eventGenerateRiverMesh_Parms()
		: ReturnValue(false)
	{
	}
};
bool IRiverSystemInterface::GenerateRiverMesh()
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GenerateRiverMesh instead.");
	RiverSystemInterface_eventGenerateRiverMesh_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_URiverSystemInterface_GenerateRiverMesh = FName(TEXT("GenerateRiverMesh"));
bool IRiverSystemInterface::Execute_GenerateRiverMesh(UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URiverSystemInterface::StaticClass()));
	RiverSystemInterface_eventGenerateRiverMesh_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URiverSystemInterface_GenerateRiverMesh);
	if (Func)
	{
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Gera o mesh do rio baseado na configura\xc3\xa7\xc3\xa3o atual\n     * @return true se gerado com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gera o mesh do rio baseado na configura\xc3\xa7\xc3\xa3o atual\n@return true se gerado com sucesso" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverSystemInterface_eventGenerateRiverMesh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverSystemInterface_eventGenerateRiverMesh_Parms), &Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URiverSystemInterface, nullptr, "GenerateRiverMesh", Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh_Statics::PropPointers), sizeof(RiverSystemInterface_eventGenerateRiverMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RiverSystemInterface_eventGenerateRiverMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URiverSystemInterface Function GenerateRiverMesh ***********************

// ********** Begin Interface URiverSystemInterface Function GetAllBridgePositions *****************
struct RiverSystemInterface_eventGetAllBridgePositions_Parms
{
	TArray<FVector> ReturnValue;
};
TArray<FVector> IRiverSystemInterface::GetAllBridgePositions()
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetAllBridgePositions instead.");
	RiverSystemInterface_eventGetAllBridgePositions_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_URiverSystemInterface_GetAllBridgePositions = FName(TEXT("GetAllBridgePositions"));
TArray<FVector> IRiverSystemInterface::Execute_GetAllBridgePositions(UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URiverSystemInterface::StaticClass()));
	RiverSystemInterface_eventGetAllBridgePositions_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URiverSystemInterface_GetAllBridgePositions);
	if (Func)
	{
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m todas as pontes configuradas\n     * @return Array com posi\xc3\xa7\xc3\xb5""es das pontes\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m todas as pontes configuradas\n@return Array com posi\xc3\xa7\xc3\xb5""es das pontes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventGetAllBridgePositions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URiverSystemInterface, nullptr, "GetAllBridgePositions", Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions_Statics::PropPointers), sizeof(RiverSystemInterface_eventGetAllBridgePositions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions_Statics::Function_MetaDataParams), Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RiverSystemInterface_eventGetAllBridgePositions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URiverSystemInterface Function GetAllBridgePositions *******************

// ********** Begin Interface URiverSystemInterface Function GetFlowVelocityAtPosition *************
struct RiverSystemInterface_eventGetFlowVelocityAtPosition_Parms
{
	FVector Position;
	FVector ReturnValue;

	/** Constructor, initializes return property only **/
	RiverSystemInterface_eventGetFlowVelocityAtPosition_Parms()
		: ReturnValue(ForceInit)
	{
	}
};
FVector IRiverSystemInterface::GetFlowVelocityAtPosition(FVector const& Position)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetFlowVelocityAtPosition instead.");
	RiverSystemInterface_eventGetFlowVelocityAtPosition_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_URiverSystemInterface_GetFlowVelocityAtPosition = FName(TEXT("GetFlowVelocityAtPosition"));
FVector IRiverSystemInterface::Execute_GetFlowVelocityAtPosition(UObject* O, FVector const& Position)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URiverSystemInterface::StaticClass()));
	RiverSystemInterface_eventGetFlowVelocityAtPosition_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URiverSystemInterface_GetFlowVelocityAtPosition);
	if (Func)
	{
		Parms.Position=Position;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m a velocidade do fluxo em uma posi\xc3\xa7\xc3\xa3o\n     * @param Position - Posi\xc3\xa7\xc3\xa3o para consultar\n     * @return Vetor de velocidade do fluxo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m a velocidade do fluxo em uma posi\xc3\xa7\xc3\xa3o\n@param Position - Posi\xc3\xa7\xc3\xa3o para consultar\n@return Vetor de velocidade do fluxo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventGetFlowVelocityAtPosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventGetFlowVelocityAtPosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URiverSystemInterface, nullptr, "GetFlowVelocityAtPosition", Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition_Statics::PropPointers), sizeof(RiverSystemInterface_eventGetFlowVelocityAtPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C20800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RiverSystemInterface_eventGetFlowVelocityAtPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URiverSystemInterface Function GetFlowVelocityAtPosition ***************

// ********** Begin Interface URiverSystemInterface Function GetWaterDepthAtPosition ***************
struct RiverSystemInterface_eventGetWaterDepthAtPosition_Parms
{
	FVector Position;
	float ReturnValue;

	/** Constructor, initializes return property only **/
	RiverSystemInterface_eventGetWaterDepthAtPosition_Parms()
		: ReturnValue(0)
	{
	}
};
float IRiverSystemInterface::GetWaterDepthAtPosition(FVector const& Position)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetWaterDepthAtPosition instead.");
	RiverSystemInterface_eventGetWaterDepthAtPosition_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_URiverSystemInterface_GetWaterDepthAtPosition = FName(TEXT("GetWaterDepthAtPosition"));
float IRiverSystemInterface::Execute_GetWaterDepthAtPosition(UObject* O, FVector const& Position)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URiverSystemInterface::StaticClass()));
	RiverSystemInterface_eventGetWaterDepthAtPosition_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URiverSystemInterface_GetWaterDepthAtPosition);
	if (Func)
	{
		Parms.Position=Position;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m informa\xc3\xa7\xc3\xb5""es do rio em uma posi\xc3\xa7\xc3\xa3o espec\xc3\xad""fica\n     * @param Position - Posi\xc3\xa7\xc3\xa3o para consultar\n     * @return Profundidade da \xc3\xa1gua na posi\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m informa\xc3\xa7\xc3\xb5""es do rio em uma posi\xc3\xa7\xc3\xa3o espec\xc3\xad""fica\n@param Position - Posi\xc3\xa7\xc3\xa3o para consultar\n@return Profundidade da \xc3\xa1gua na posi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventGetWaterDepthAtPosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventGetWaterDepthAtPosition_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URiverSystemInterface, nullptr, "GetWaterDepthAtPosition", Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition_Statics::PropPointers), sizeof(RiverSystemInterface_eventGetWaterDepthAtPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C20800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RiverSystemInterface_eventGetWaterDepthAtPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URiverSystemInterface Function GetWaterDepthAtPosition *****************

// ********** Begin Interface URiverSystemInterface Function GetWaterSurfacePosition ***************
struct RiverSystemInterface_eventGetWaterSurfacePosition_Parms
{
	FVector Position;
	FVector ReturnValue;

	/** Constructor, initializes return property only **/
	RiverSystemInterface_eventGetWaterSurfacePosition_Parms()
		: ReturnValue(ForceInit)
	{
	}
};
FVector IRiverSystemInterface::GetWaterSurfacePosition(FVector const& Position)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetWaterSurfacePosition instead.");
	RiverSystemInterface_eventGetWaterSurfacePosition_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_URiverSystemInterface_GetWaterSurfacePosition = FName(TEXT("GetWaterSurfacePosition"));
FVector IRiverSystemInterface::Execute_GetWaterSurfacePosition(UObject* O, FVector const& Position)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URiverSystemInterface::StaticClass()));
	RiverSystemInterface_eventGetWaterSurfacePosition_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URiverSystemInterface_GetWaterSurfacePosition);
	if (Func)
	{
		Parms.Position=Position;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m a posi\xc3\xa7\xc3\xa3o da superf\xc3\xad""cie da \xc3\xa1gua\n     * @param Position - Posi\xc3\xa7\xc3\xa3o de refer\xc3\xaancia\n     * @return Posi\xc3\xa7\xc3\xa3o na superf\xc3\xad""cie da \xc3\xa1gua\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m a posi\xc3\xa7\xc3\xa3o da superf\xc3\xad""cie da \xc3\xa1gua\n@param Position - Posi\xc3\xa7\xc3\xa3o de refer\xc3\xaancia\n@return Posi\xc3\xa7\xc3\xa3o na superf\xc3\xad""cie da \xc3\xa1gua" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventGetWaterSurfacePosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventGetWaterSurfacePosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URiverSystemInterface, nullptr, "GetWaterSurfacePosition", Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition_Statics::PropPointers), sizeof(RiverSystemInterface_eventGetWaterSurfacePosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C20800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RiverSystemInterface_eventGetWaterSurfacePosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URiverSystemInterface Function GetWaterSurfacePosition *****************

// ********** Begin Interface URiverSystemInterface Function IsPositionInWater *********************
struct RiverSystemInterface_eventIsPositionInWater_Parms
{
	FVector Position;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RiverSystemInterface_eventIsPositionInWater_Parms()
		: ReturnValue(false)
	{
	}
};
bool IRiverSystemInterface::IsPositionInWater(FVector const& Position)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_IsPositionInWater instead.");
	RiverSystemInterface_eventIsPositionInWater_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_URiverSystemInterface_IsPositionInWater = FName(TEXT("IsPositionInWater"));
bool IRiverSystemInterface::Execute_IsPositionInWater(UObject* O, FVector const& Position)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URiverSystemInterface::StaticClass()));
	RiverSystemInterface_eventIsPositionInWater_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URiverSystemInterface_IsPositionInWater);
	if (Func)
	{
		Parms.Position=Position;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se uma posi\xc3\xa7\xc3\xa3o est\xc3\xa1 na \xc3\xa1gua\n     * @param Position - Posi\xc3\xa7\xc3\xa3o para verificar\n     * @return true se est\xc3\xa1 na \xc3\xa1gua\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se uma posi\xc3\xa7\xc3\xa3o est\xc3\xa1 na \xc3\xa1gua\n@param Position - Posi\xc3\xa7\xc3\xa3o para verificar\n@return true se est\xc3\xa1 na \xc3\xa1gua" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventIsPositionInWater_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
void Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverSystemInterface_eventIsPositionInWater_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverSystemInterface_eventIsPositionInWater_Parms), &Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URiverSystemInterface, nullptr, "IsPositionInWater", Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::PropPointers), sizeof(RiverSystemInterface_eventIsPositionInWater_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C20800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::Function_MetaDataParams), Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RiverSystemInterface_eventIsPositionInWater_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URiverSystemInterface Function IsPositionInWater ***********************

// ********** Begin Interface URiverSystemInterface Function RemoveBridge **************************
struct RiverSystemInterface_eventRemoveBridge_Parms
{
	int32 BridgeIndex;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RiverSystemInterface_eventRemoveBridge_Parms()
		: ReturnValue(false)
	{
	}
};
bool IRiverSystemInterface::RemoveBridge(int32 BridgeIndex)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_RemoveBridge instead.");
	RiverSystemInterface_eventRemoveBridge_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_URiverSystemInterface_RemoveBridge = FName(TEXT("RemoveBridge"));
bool IRiverSystemInterface::Execute_RemoveBridge(UObject* O, int32 BridgeIndex)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URiverSystemInterface::StaticClass()));
	RiverSystemInterface_eventRemoveBridge_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URiverSystemInterface_RemoveBridge);
	if (Func)
	{
		Parms.BridgeIndex=BridgeIndex;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remove uma ponte do rio\n     * @param BridgeIndex - \xc3\x8dndice da ponte para remover\n     * @return true se removida com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove uma ponte do rio\n@param BridgeIndex - \xc3\x8dndice da ponte para remover\n@return true se removida com sucesso" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_BridgeIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::NewProp_BridgeIndex = { "BridgeIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventRemoveBridge_Parms, BridgeIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverSystemInterface_eventRemoveBridge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverSystemInterface_eventRemoveBridge_Parms), &Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::NewProp_BridgeIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URiverSystemInterface, nullptr, "RemoveBridge", Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::PropPointers), sizeof(RiverSystemInterface_eventRemoveBridge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::Function_MetaDataParams), Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RiverSystemInterface_eventRemoveBridge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URiverSystemInterface_RemoveBridge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URiverSystemInterface_RemoveBridge_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URiverSystemInterface Function RemoveBridge ****************************

// ********** Begin Interface URiverSystemInterface Function SetSplinePoints ***********************
struct RiverSystemInterface_eventSetSplinePoints_Parms
{
	TArray<FVector> SplinePoints;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RiverSystemInterface_eventSetSplinePoints_Parms()
		: ReturnValue(false)
	{
	}
};
bool IRiverSystemInterface::SetSplinePoints(TArray<FVector> const& SplinePoints)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_SetSplinePoints instead.");
	RiverSystemInterface_eventSetSplinePoints_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_URiverSystemInterface_SetSplinePoints = FName(TEXT("SetSplinePoints"));
bool IRiverSystemInterface::Execute_SetSplinePoints(UObject* O, TArray<FVector> const& SplinePoints)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URiverSystemInterface::StaticClass()));
	RiverSystemInterface_eventSetSplinePoints_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URiverSystemInterface_SetSplinePoints);
	if (Func)
	{
		Parms.SplinePoints=SplinePoints;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Define pontos do spline do rio\n     * @param SplinePoints - Array de pontos do spline\n     * @return true se configurado com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define pontos do spline do rio\n@param SplinePoints - Array de pontos do spline\n@return true se configurado com sucesso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplinePoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SplinePoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SplinePoints;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::NewProp_SplinePoints_Inner = { "SplinePoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::NewProp_SplinePoints = { "SplinePoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverSystemInterface_eventSetSplinePoints_Parms, SplinePoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplinePoints_MetaData), NewProp_SplinePoints_MetaData) };
void Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverSystemInterface_eventSetSplinePoints_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverSystemInterface_eventSetSplinePoints_Parms), &Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::NewProp_SplinePoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::NewProp_SplinePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URiverSystemInterface, nullptr, "SetSplinePoints", Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::PropPointers), sizeof(RiverSystemInterface_eventSetSplinePoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RiverSystemInterface_eventSetSplinePoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URiverSystemInterface Function SetSplinePoints *************************

// ********** Begin Interface URiverSystemInterface Function ValidateRiverConfiguration ************
struct RiverSystemInterface_eventValidateRiverConfiguration_Parms
{
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RiverSystemInterface_eventValidateRiverConfiguration_Parms()
		: ReturnValue(false)
	{
	}
};
bool IRiverSystemInterface::ValidateRiverConfiguration()
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_ValidateRiverConfiguration instead.");
	RiverSystemInterface_eventValidateRiverConfiguration_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_URiverSystemInterface_ValidateRiverConfiguration = FName(TEXT("ValidateRiverConfiguration"));
bool IRiverSystemInterface::Execute_ValidateRiverConfiguration(UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URiverSystemInterface::StaticClass()));
	RiverSystemInterface_eventValidateRiverConfiguration_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URiverSystemInterface_ValidateRiverConfiguration);
	if (Func)
	{
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida a configura\xc3\xa7\xc3\xa3o atual do rio\n     * @return true se configura\xc3\xa7\xc3\xa3o \xc3\xa9 v\xc3\xa1lida\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida a configura\xc3\xa7\xc3\xa3o atual do rio\n@return true se configura\xc3\xa7\xc3\xa3o \xc3\xa9 v\xc3\xa1lida" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverSystemInterface_eventValidateRiverConfiguration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverSystemInterface_eventValidateRiverConfiguration_Parms), &Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URiverSystemInterface, nullptr, "ValidateRiverConfiguration", Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration_Statics::PropPointers), sizeof(RiverSystemInterface_eventValidateRiverConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RiverSystemInterface_eventValidateRiverConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URiverSystemInterface Function ValidateRiverConfiguration **************

// ********** Begin Interface URiverSystemInterface ************************************************
void URiverSystemInterface::StaticRegisterNativesURiverSystemInterface()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URiverSystemInterface;
UClass* URiverSystemInterface::GetPrivateStaticClass()
{
	using TClass = URiverSystemInterface;
	if (!Z_Registration_Info_UClass_URiverSystemInterface.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RiverSystemInterface"),
			Z_Registration_Info_UClass_URiverSystemInterface.InnerSingleton,
			StaticRegisterNativesURiverSystemInterface,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URiverSystemInterface.InnerSingleton;
}
UClass* Z_Construct_UClass_URiverSystemInterface_NoRegister()
{
	return URiverSystemInterface::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URiverSystemInterface_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Interfaces/RiverSystemInterface.h" },
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URiverSystemInterface_ClearRiverData, "ClearRiverData" }, // 105995537
		{ &Z_Construct_UFunction_URiverSystemInterface_ConfigureRiver, "ConfigureRiver" }, // 3086132909
		{ &Z_Construct_UFunction_URiverSystemInterface_CreateBridge, "CreateBridge" }, // 1224264698
		{ &Z_Construct_UFunction_URiverSystemInterface_GenerateRiverMesh, "GenerateRiverMesh" }, // 3889110127
		{ &Z_Construct_UFunction_URiverSystemInterface_GetAllBridgePositions, "GetAllBridgePositions" }, // 3669839916
		{ &Z_Construct_UFunction_URiverSystemInterface_GetFlowVelocityAtPosition, "GetFlowVelocityAtPosition" }, // 2001883346
		{ &Z_Construct_UFunction_URiverSystemInterface_GetWaterDepthAtPosition, "GetWaterDepthAtPosition" }, // 1457535033
		{ &Z_Construct_UFunction_URiverSystemInterface_GetWaterSurfacePosition, "GetWaterSurfacePosition" }, // 4148668279
		{ &Z_Construct_UFunction_URiverSystemInterface_IsPositionInWater, "IsPositionInWater" }, // 4241895665
		{ &Z_Construct_UFunction_URiverSystemInterface_RemoveBridge, "RemoveBridge" }, // 3546143343
		{ &Z_Construct_UFunction_URiverSystemInterface_SetSplinePoints, "SetSplinePoints" }, // 3176580007
		{ &Z_Construct_UFunction_URiverSystemInterface_ValidateRiverConfiguration, "ValidateRiverConfiguration" }, // 2388328525
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<IRiverSystemInterface>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_URiverSystemInterface_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UInterface,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URiverSystemInterface_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URiverSystemInterface_Statics::ClassParams = {
	&URiverSystemInterface::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x000840A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URiverSystemInterface_Statics::Class_MetaDataParams), Z_Construct_UClass_URiverSystemInterface_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URiverSystemInterface()
{
	if (!Z_Registration_Info_UClass_URiverSystemInterface.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URiverSystemInterface.OuterSingleton, Z_Construct_UClass_URiverSystemInterface_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URiverSystemInterface.OuterSingleton;
}
URiverSystemInterface::URiverSystemInterface(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(URiverSystemInterface);
// ********** End Interface URiverSystemInterface **************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h__Script_Aura_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRiverGenerationParams::StaticStruct, Z_Construct_UScriptStruct_FRiverGenerationParams_Statics::NewStructOps, TEXT("RiverGenerationParams"), &Z_Registration_Info_UScriptStruct_FRiverGenerationParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRiverGenerationParams), 1940653944U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URiverSystemInterface, URiverSystemInterface::StaticClass, TEXT("URiverSystemInterface"), &Z_Registration_Info_UClass_URiverSystemInterface, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URiverSystemInterface), 3033372319U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h__Script_Aura_1907108489(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h__Script_Aura_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
