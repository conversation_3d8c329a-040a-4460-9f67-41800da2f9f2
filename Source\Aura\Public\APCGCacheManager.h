#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/ActorComponent.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Containers/Map.h"
#include "Containers/Array.h"
#include "Containers/Queue.h"
#include "Templates/SharedPointer.h"
#include "Async/Future.h"
#include "Serialization/Archive.h"
#include "UObject/ObjectMacros.h"
#include "APCGCacheManager.generated.h"

// Forward declarations
class APCGWorldPartitionManager;
class APCGNaniteOptimizer;
class APCGLumenIntegrator;
class APCGStreamingManager;
class UPCGPerformanceProfiler;

// Cache priority levels
UENUM(BlueprintType)
enum class EPCGCachePriority : uint8
{
    VeryLow     UMETA(DisplayName = "Very Low"),
    Low         UMETA(DisplayName = "Low"),
    Medium      UMETA(DisplayName = "Medium"),
    High        UMETA(DisplayName = "High"),
    VeryHigh    UMETA(DisplayName = "Very High"),
    Critical    UMETA(DisplayName = "Critical")
};

// Cache entry states
UENUM(BlueprintType)
enum class EPCGCacheState : uint8
{
    Invalid     UMETA(DisplayName = "Invalid"),
    Loading     UMETA(DisplayName = "Loading"),
    Valid       UMETA(DisplayName = "Valid"),
    Dirty       UMETA(DisplayName = "Dirty"),
    Expired     UMETA(DisplayName = "Expired"),
    Evicted     UMETA(DisplayName = "Evicted")
};

// Cache storage types
UENUM(BlueprintType)
enum class EPCGCacheStorageType : uint8
{
    Memory      UMETA(DisplayName = "Memory"),
    Disk        UMETA(DisplayName = "Disk"),
    Hybrid      UMETA(DisplayName = "Hybrid"),
    Network     UMETA(DisplayName = "Network"),
    Compressed  UMETA(DisplayName = "Compressed")
};

// Cache eviction policies
UENUM(BlueprintType)
enum class EPCGCacheEvictionPolicy : uint8
{
    LRU         UMETA(DisplayName = "Least Recently Used"),
    LFU         UMETA(DisplayName = "Least Frequently Used"),
    FIFO        UMETA(DisplayName = "First In First Out"),
    Random      UMETA(DisplayName = "Random"),
    Priority    UMETA(DisplayName = "Priority Based"),
    Adaptive    UMETA(DisplayName = "Adaptive")
};

// Cache compression levels
UENUM(BlueprintType)
enum class EPCGCacheCompressionLevel : uint8
{
    None        UMETA(DisplayName = "None"),
    Fast        UMETA(DisplayName = "Fast"),
    Balanced    UMETA(DisplayName = "Balanced"),
    Maximum     UMETA(DisplayName = "Maximum"),
    Adaptive    UMETA(DisplayName = "Adaptive")
};

// Cache configuration structure
USTRUCT(BlueprintType)
struct AURA_API FPCGCacheConfig
{
    GENERATED_BODY()

    // Memory settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    int32 MaxMemoryCacheSizeMB = 512;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    int32 MaxMemoryEntries = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float MemoryEvictionThreshold = 0.8f;

    // Disk settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disk")
    FString DiskCachePath = TEXT("Cache/PCG");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disk")
    int32 MaxDiskCacheSizeMB = 2048;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disk")
    bool bEnableDiskCache = true;

    // CORRIGIDO: Campo faltante para flush no shutdown
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disk")
    bool bFlushOnShutdown = true;

    // Compression settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression")
    EPCGCacheCompressionLevel CompressionLevel = EPCGCacheCompressionLevel::Balanced;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression")
    bool bEnableCompression = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression")
    float CompressionRatio = 0.7f;

    // Eviction settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Eviction")
    EPCGCacheEvictionPolicy EvictionPolicy = EPCGCacheEvictionPolicy::LRU;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Eviction")
    float EvictionCheckInterval = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Eviction")
    float EntryExpirationTime = 3600.0f; // 1 hour

    // Performance settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAsyncLoading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnablePrefetching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxConcurrentOperations = 4;

    // Network settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Network")
    bool bEnableNetworkCache = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Network")
    FString NetworkCacheEndpoint;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Network")
    float NetworkTimeout = 10.0f;

    FPCGCacheConfig()
    {
        // Default constructor with sensible defaults
    }
};

// Cache entry metadata
USTRUCT(BlueprintType)
struct AURA_API FPCGCacheEntry
{
    GENERATED_BODY()

    // Entry identification
    UPROPERTY(BlueprintReadOnly, Category = "Entry")
    FString EntryKey;

    UPROPERTY(BlueprintReadOnly, Category = "Entry")
    FString EntryType;

    UPROPERTY(BlueprintReadOnly, Category = "Entry")
    EPCGCacheState State = EPCGCacheState::Invalid;

    // Priority and access
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority")
    EPCGCachePriority Priority = EPCGCachePriority::Medium;

    UPROPERTY(BlueprintReadOnly, Category = "Access")
    FDateTime CreationTime;

    UPROPERTY(BlueprintReadOnly, Category = "Access")
    FDateTime LastAccessTime;

    UPROPERTY(BlueprintReadOnly, Category = "Access")
    FDateTime LastModificationTime;

    UPROPERTY(BlueprintReadOnly, Category = "Access")
    int32 AccessCount = 0;

    // Size and storage
    UPROPERTY(BlueprintReadOnly, Category = "Storage")
    int32 SizeInBytes = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Storage")
    int32 CompressedSizeInBytes = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Storage")
    EPCGCacheStorageType StorageType = EPCGCacheStorageType::Memory;

    UPROPERTY(BlueprintReadOnly, Category = "Storage")
    FString DiskPath;

    // Validation and dependencies
    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    FString ChecksumHash;

    UPROPERTY(BlueprintReadOnly, Category = "Dependencies")
    TArray<FString> Dependencies;

    UPROPERTY(BlueprintReadOnly, Category = "Dependencies")
    TArray<FString> Dependents;

    // Performance metrics
    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float LoadTimeMs = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float CompressionTimeMs = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float DecompressionTimeMs = 0.0f;

    FPCGCacheEntry()
    {
        CreationTime = FDateTime::Now();
        LastAccessTime = CreationTime;
        LastModificationTime = CreationTime;
    }
};

// Cache statistics
USTRUCT(BlueprintType)
struct AURA_API FPCGCacheStatistics
{
    GENERATED_BODY()

    // Hit/Miss statistics
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalRequests = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 CacheHits = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 CacheMisses = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float HitRatio = 0.0f;

    // Memory statistics
    UPROPERTY(BlueprintReadOnly, Category = "Memory")
    int32 MemoryUsedMB = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Memory")
    int32 MemoryAvailableMB = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Memory")
    int32 MemoryEntryCount = 0;

    // Disk statistics
    UPROPERTY(BlueprintReadOnly, Category = "Disk")
    int32 DiskUsedMB = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Disk")
    int32 DiskAvailableMB = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Disk")
    int32 DiskEntryCount = 0;

    // Performance statistics
    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float AverageLoadTimeMs = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float AverageCompressionRatio = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 EvictedEntries = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 ExpiredEntries = 0;

    FPCGCacheStatistics()
    {
        // Default constructor
    }
};

// Cache operation result
USTRUCT(BlueprintType)
struct AURA_API FPCGCacheOperationResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ErrorMessage;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float OperationTimeMs = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 DataSizeBytes = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bFromCache = false;

    FPCGCacheOperationResult()
    {
        // Default constructor
    }
};

// Delegate declarations
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCacheEntryLoaded, const FString&, EntryKey, const FPCGCacheOperationResult&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCacheEntryEvicted, const FString&, EntryKey, EPCGCachePriority, Priority);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCacheStatisticsUpdated, const FPCGCacheStatistics&, Statistics);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCacheError, const FString&, ErrorMessage, const FString&, EntryKey);

/**
 * APCGCacheManager - Advanced cache management system for PCG content
 * 
 * Features:
 * - Multi-tier caching (Memory, Disk, Network)
 * - Intelligent eviction policies
 * - Compression and decompression
 * - Async loading and prefetching
 * - Dependency tracking
 * - Performance monitoring
 * - Integration with other PCG systems
 */
UCLASS(BlueprintType, Blueprintable)
class AURA_API APCGCacheManager : public AActor
{
    GENERATED_BODY()

public:
    APCGCacheManager();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

public:
    // Cache Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Configuration")
    FPCGCacheConfig CacheConfig;

    UPROPERTY(BlueprintReadOnly, Category = "Cache State")
    bool bIsInitialized = false;

    UPROPERTY(BlueprintReadOnly, Category = "Cache State")
    bool bIsEnabled = true;

    UPROPERTY(BlueprintReadOnly, Category = "Cache State")
    bool bMaintenanceMode = false;

    // Event delegates
    UPROPERTY(BlueprintAssignable, Category = "Cache Events")
    FOnCacheEntryLoaded OnCacheEntryLoaded;

    UPROPERTY(BlueprintAssignable, Category = "Cache Events")
    FOnCacheEntryEvicted OnCacheEntryEvicted;

    UPROPERTY(BlueprintAssignable, Category = "Cache Events")
    FOnCacheStatisticsUpdated OnCacheStatisticsUpdated;

    UPROPERTY(BlueprintAssignable, Category = "Cache Events")
    FOnCacheError OnCacheError;

    // Initialization and Management
    UFUNCTION(BlueprintCallable, Category = "Cache Management")
    void InitializeCache();

    UFUNCTION(BlueprintCallable, Category = "Cache Management")
    void ShutdownCache();

    UFUNCTION(BlueprintCallable, Category = "Cache Management")
    void EnableCache(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "Cache Management")
    void SetMaintenanceMode(bool bMaintenance);

    UFUNCTION(BlueprintCallable, Category = "Cache Management")
    void ClearCache(EPCGCacheStorageType StorageType = EPCGCacheStorageType::Memory);

    UFUNCTION(BlueprintCallable, Category = "Cache Management")
    void OptimizeCache();

    // Cache Operations
    UFUNCTION(BlueprintCallable, Category = "Cache Operations")
    bool StoreData(const FString& Key, const TArray<uint8>& Data, EPCGCachePriority Priority = EPCGCachePriority::Medium);

    UFUNCTION(BlueprintCallable, Category = "Cache Operations")
    bool LoadData(const FString& Key, TArray<uint8>& OutData);

    UFUNCTION(BlueprintCallable, Category = "Cache Operations")
    bool HasEntry(const FString& Key) const;

    UFUNCTION(BlueprintCallable, Category = "Cache Operations")
    bool RemoveEntry(const FString& Key);

    UFUNCTION(BlueprintCallable, Category = "Cache Operations")
    void InvalidateEntry(const FString& Key);

    UFUNCTION(BlueprintCallable, Category = "Cache Operations")
    void RefreshEntry(const FString& Key);

    // Async Operations
    UFUNCTION(Category = "Async Operations")
    void LoadDataAsync(const FString& Key, const FOnCacheEntryLoaded& Callback);

    UFUNCTION(BlueprintCallable, Category = "Async Operations")
    void StoreDataAsync(const FString& Key, const TArray<uint8>& Data, EPCGCachePriority Priority = EPCGCachePriority::Medium);

    UFUNCTION(BlueprintCallable, Category = "Async Operations")
    void PrefetchData(const TArray<FString>& Keys);

    UFUNCTION(BlueprintCallable, Category = "Async Operations")
    void PrefetchByPattern(const FString& Pattern);

    // Entry Management
    UFUNCTION(BlueprintCallable, Category = "Entry Management")
    FPCGCacheEntry GetEntryInfo(const FString& Key) const;

    UFUNCTION(BlueprintCallable, Category = "Entry Management")
    TArray<FPCGCacheEntry> GetAllEntries() const;

    UFUNCTION(BlueprintCallable, Category = "Entry Management")
    TArray<FPCGCacheEntry> GetEntriesByPriority(EPCGCachePriority Priority) const;

    UFUNCTION(BlueprintCallable, Category = "Entry Management")
    TArray<FPCGCacheEntry> GetEntriesByType(const FString& Type) const;

    UFUNCTION(BlueprintCallable, Category = "Entry Management")
    void SetEntryPriority(const FString& Key, EPCGCachePriority Priority);

    UFUNCTION(BlueprintCallable, Category = "Entry Management")
    void AddEntryDependency(const FString& Key, const FString& DependencyKey);

    UFUNCTION(BlueprintCallable, Category = "Entry Management")
    void RemoveEntryDependency(const FString& Key, const FString& DependencyKey);

    // Statistics and Monitoring
    UFUNCTION(BlueprintCallable, Category = "Statistics")
    FPCGCacheStatistics GetCacheStatistics() const;

    UFUNCTION(BlueprintCallable, Category = "Statistics")
    float GetHitRatio() const;

    UFUNCTION(BlueprintCallable, Category = "Statistics")
    float GetCacheHitRate() const;

    UFUNCTION(BlueprintCallable, Category = "Statistics")
    float GetCacheHitRatio() const { return GetCacheHitRate(); }

    UFUNCTION(BlueprintCallable, Category = "Statistics")
    int32 GetFragmentedEntryCount() const;

    UFUNCTION(BlueprintCallable, Category = "Statistics")
    int32 GetTotalEntryCount() const;

    UFUNCTION(BlueprintCallable, Category = "Statistics")
    int32 GetMemoryUsage() const;

    UFUNCTION(BlueprintCallable, Category = "Statistics")
    int32 GetDiskUsage() const;

    UFUNCTION(BlueprintCallable, Category = "Statistics")
    void ResetStatistics();

    UFUNCTION(BlueprintCallable, Category = "Statistics")
    void ExportStatistics(const FString& FilePath) const;

    // Compression and Serialization
    UFUNCTION(BlueprintCallable, Category = "Compression")
    TArray<uint8> CompressData(const TArray<uint8>& Data, EPCGCacheCompressionLevel Level = EPCGCacheCompressionLevel::Balanced) const;

    UFUNCTION(BlueprintCallable, Category = "Compression")
    TArray<uint8> DecompressData(const TArray<uint8>& CompressedData) const;

    UFUNCTION(BlueprintCallable, Category = "Compression")
    float GetCompressionRatio(const TArray<uint8>& OriginalData, const TArray<uint8>& CompressedData) const;

    // Eviction and Cleanup
    UFUNCTION(BlueprintCallable, Category = "Eviction")
    void ForceEviction(int32 TargetMemoryMB);

    UFUNCTION(BlueprintCallable, Category = "Eviction")
    void EvictByPriority(EPCGCachePriority MaxPriority);

    UFUNCTION(BlueprintCallable, Category = "Eviction")
    void EvictExpiredEntries();

    UFUNCTION(BlueprintCallable, Category = "Eviction")
    void EvictLeastRecentlyUsed(int32 Count);

    UFUNCTION(BlueprintCallable, Category = "Eviction")
    void EvictLeastFrequentlyUsed(int32 Count);

    UFUNCTION(BlueprintCallable, Category = "Eviction")
    void ClearUnusedEntries();

    // Integration with PCG Systems
    UFUNCTION(BlueprintCallable, Category = "Integration")
    void IntegrateWithWorldPartition(APCGWorldPartitionManager* WorldPartitionManager);

    UFUNCTION(BlueprintCallable, Category = "Integration")
    void IntegrateWithNanite(APCGNaniteOptimizer* NaniteOptimizer);

    UFUNCTION(BlueprintCallable, Category = "Integration")
    void IntegrateWithLumen(APCGLumenIntegrator* LumenIntegrator);

    UFUNCTION(BlueprintCallable, Category = "Integration")
    void IntegrateWithStreaming(APCGStreamingManager* StreamingManager);

    UFUNCTION(BlueprintCallable, Category = "Integration")
    void IntegrateWithProfiler(UPCGPerformanceProfiler* PerformanceProfiler);

    // Disk Operations
    UFUNCTION(BlueprintCallable, Category = "Disk Operations")
    bool SaveToDisk(const FString& Key);

    UFUNCTION(BlueprintCallable, Category = "Disk Operations")
    bool LoadFromDisk(const FString& Key);

    UFUNCTION(BlueprintCallable, Category = "Disk Operations")
    void CleanupDiskCache();

    UFUNCTION(BlueprintCallable, Category = "Disk Operations")
    int64 GetDiskCacheSize() const;

    // Network Operations
    UFUNCTION(BlueprintCallable, Category = "Network Operations")
    void SyncWithNetworkCache();

    UFUNCTION(BlueprintCallable, Category = "Network Operations")
    void UploadToNetwork(const FString& Key);

    UFUNCTION(BlueprintCallable, Category = "Network Operations")
    void DownloadFromNetwork(const FString& Key);

private:
    // Internal state
    bool bCacheInitialized = false;
    bool bMaintenanceActive = false;
    float LastMaintenanceTime = 0.0f;
    float LastStatisticsUpdate = 0.0f;

    // Cache storage
    TMap<FString, TSharedPtr<TArray<uint8>>> MemoryCache;
    TMap<FString, FPCGCacheEntry> CacheEntries;
    TQueue<FString> EvictionQueue;
    TMap<FString, int32> AccessFrequency;

    // Statistics
    FPCGCacheStatistics CurrentStatistics;
    // CORRIGIDO: Removidas variáveis duplicadas - usando FThreadSafeCounter64 modernas

    // Integration references
    TWeakObjectPtr<APCGWorldPartitionManager> WorldPartitionManagerRef;
    TWeakObjectPtr<APCGNaniteOptimizer> NaniteOptimizerRef;
    TWeakObjectPtr<APCGLumenIntegrator> LumenIntegratorRef;
    TWeakObjectPtr<APCGStreamingManager> StreamingManagerRef;
    TWeakObjectPtr<UPCGPerformanceProfiler> PerformanceProfilerRef;

    // Async operation tracking
    TMap<FString, TFuture<FPCGCacheOperationResult>> PendingOperations;
    TArray<FString> PrefetchQueue;

    // Internal functions
    void UpdateCacheStatistics();
    void PerformMaintenance();
    void ProcessEvictionQueue();
    void UpdateAccessFrequency(const FString& Key);
    bool ShouldEvictEntry(const FPCGCacheEntry& Entry) const;
    FString GenerateChecksum(const TArray<uint8>& Data) const;
    bool ValidateEntry(const FString& Key) const;
    void NotifyDependents(const FString& Key);
    void ProcessPrefetchQueue();
    EPCGCachePriority CalculateAdaptivePriority(const FPCGCacheEntry& Entry) const;
    void OptimizeMemoryLayout();
    void CompactDiskCache();
    void UpdateIntegratedSystems();
    
    // Compression helpers
    TArray<uint8> CompressDataInternal(const TArray<uint8>& Data, EPCGCacheCompressionLevel Level) const;
    TArray<uint8> DecompressDataInternal(const TArray<uint8>& CompressedData) const;
    
    // Disk I/O helpers
    FString GetDiskPath(const FString& Key) const;
    bool WriteToDisk(const FString& Key, const TArray<uint8>& Data);
    bool ReadFromDisk(const FString& Key, TArray<uint8>& OutData);
    
    // Network helpers
    void SendNetworkRequest(const FString& Endpoint, const FString& Data);
    void HandleNetworkResponse(const FString& Response);
    
    // Event handlers
    void OnMemoryPressure();
    void OnDiskSpaceLow();
    void OnNetworkError(const FString& Error);

    // CORRIGIDO: Variáveis adicionais para UE 5.6 integration
    bool bWorldPartitionEnabled;
    bool bNaniteEnabled;
    bool bLumenEnabled;

    // Modern UE 5.6 systems
    TSharedPtr<struct FStreamableManager> StreamableManager;

    // Thread-safe counters para UE 5.6
    FThreadSafeCounter64 TotalCacheRequests;
    FThreadSafeCounter64 TotalCacheHits;
    FThreadSafeCounter64 TotalCacheMisses;
    FThreadSafeCounter64 TotalEvictions;

    // Helper functions para UE 5.6
    bool IsLumenEnabled(UWorld* World) const;
    void FlushCacheToDisk();
};