#include "AMinionWaveManager.h"
#include "DrawDebugHelpers.h"
#include "Kismet/GameplayStatics.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Math/UnrealMathUtility.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Algo/Reverse.h"
#include "Containers/Queue.h"
#include "Containers/Set.h"

// UE 5.6 Modern APIs
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Async/ParallelFor.h"
#include "HAL/ThreadSafeCounter64.h"
#include "Engine/StreamableManager.h"
#include "Subsystems/WorldSubsystem.h"
#include "Stats/Stats.h"
#include "Misc/Timespan.h"

// Logging categories
DEFINE_LOG_CATEGORY_STATIC(LogMinionWaveManager, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogMinionPathfinding, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogMinionWaves, Log, All);

// Stats para profiling UE 5.6
DECLARE_CYCLE_STAT(TEXT("MinionWaveManager ConfigureWaveSystem"), STAT_MinionWaveManager_ConfigureWaveSystem, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("MinionWaveManager ConfigurePathfinding"), STAT_MinionWaveManager_ConfigurePathfinding, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("MinionWaveManager ConfigureRewardSystem"), STAT_MinionWaveManager_ConfigureRewardSystem, STATGROUP_Game);

AMinionWaveManager::AMinionWaveManager()
{
    PrimaryActorTick.bCanEverTick = true;
    
    // Configurar componente raiz
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    RootComponent = RootSceneComponent;
    
    // CORRIGIDO: Configurações conforme mapaimplementacao.md
    WaveInterval = MINION_SPAWN_INTERVAL; // 30 segundos entre ondas
    CurrentWaveNumber = 1;
    MaxWaves = 50;
    bAutoStartWaves = true;
    bInfiniteWaves = false;
    WaveScalingFactor = WAVE_SCALING_FACTOR;

    // Configurações de pathfinding A* conforme documentação
    PathfindingAlgorithm = EPathfindingAlgorithm::AStar;
    GridCellSize = PATHFINDING_GRID_SIZE;
    MaxPathfindingIterations = ASTAR_MAX_ITERATIONS;
    bUseDynamicPathfinding = true;
    bAvoidOtherMinions = true;
    PathUpdateInterval = PATHFINDING_UPDATE_INTERVAL; // 2 segundos conforme doc

    // Configurações de recompensas
    RewardSharingRadius = REWARD_SHARING_RADIUS; // 1200 UU
    bEnableRewardSharing = true;
    bScaleRewardsByWave = true;
    bCapXPRewards = true;

    // Multiplicadores padrão
    EliteWaveGoldMultiplier = 1.5f;
    BossWaveGoldMultiplier = 2.0f;
    SiegeWaveGoldMultiplier = 1.25f;
    EliteWaveXPMultiplier = 1.3f;
    BossWaveXPMultiplier = 1.8f;
    SiegeWaveXPMultiplier = 1.15f;
    
    // Configurações de debug
    bShowPathfindingGrid = false;
    bShowMinionPaths = false;
    bShowFormations = false;
    bShowSpawnPoints = false;
    bEnableDetailedLogging = false;
    
    // Variáveis internas
    bIsInitialized = false;
    bWaveSystemActive = false;
    bWaveSystemPaused = false;
    LastWaveStartTime = 0.0f;
    LastPathUpdateTime = 0.0f;
    NextMinionID = 1;
    
    // Inicializar grid de pathfinding
    NavigationGrid.GridWidth = 200;
    NavigationGrid.GridHeight = 200;
    NavigationGrid.CellSize = GridCellSize;
    NavigationGrid.GridOrigin = FVector(-10000.0f, -10000.0f, 0.0f);
    NavigationGrid.GridBounds = FVector(20000.0f, 20000.0f, 1000.0f);
}

void AMinionWaveManager::BeginPlay()
{
    Super::BeginPlay();
    
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: Iniciando sistema de ondas"));
    }
    
    InitializeWaveSystem();
}

void AMinionWaveManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (!bIsInitialized || bWaveSystemPaused)
    {
        return;
    }
    
    // Atualizar sistema de ondas
    UpdateWaveTimer(DeltaTime);
    
    // Atualizar todos os minions
    UpdateAllMinions(DeltaTime);
    
    // Atualizar cooldowns dos spawn points
    UpdateSpawnPointCooldowns(DeltaTime);
    
    // Atualizar pathfinding se necessário
    if (bUseDynamicPathfinding && GetWorld()->GetTimeSeconds() - LastPathUpdateTime > PathUpdateInterval)
    {
        BatchUpdatePaths();
        LastPathUpdateTime = GetWorld()->GetTimeSeconds();
    }
    
    // Limpar minions mortos
    CleanupDeadMinions();
    
    // Debug rendering
    if (bShowPathfindingGrid)
    {
        DrawDebugPathfindingGrid();
    }
    
    if (bShowMinionPaths)
    {
        DrawDebugMinionPaths();
    }
    
    if (bShowFormations)
    {
        DrawDebugFormations();
    }
    
    if (bShowSpawnPoints)
    {
        DrawDebugSpawnPoints();
    }
}

void AMinionWaveManager::InitializeWaveSystem()
{
    if (bIsInitialized)
    {
        return;
    }
    
    // Inicializar grid de pathfinding
    InitializePathfindingGrid();
    
    // Configurar spawn points
    InitializeSpawnPoints();
    
    // Carregar assets dos minions
    LoadMinionAssets();
    
    // Inicializar ondas padrão
    InitializeDefaultWaves();
    
    // Configurar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(MinionUpdateTimer, [this]()
        {
            if (bUseDynamicPathfinding)
            {
                UpdateNavigationGrid();
            }
        }, 1.0f, true);
    }
    
    bIsInitialized = true;
    bWaveSystemActive = bAutoStartWaves;
    
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: Sistema inicializado com sucesso"));
    }
    
    if (bAutoStartWaves)
    {
        StartNextWave();
    }
}

void AMinionWaveManager::StartNextWave()
{
    if (!bIsInitialized || bWaveSystemPaused)
    {
        return;
    }
    
    // Gerar dados da próxima onda
    EWaveType WaveType = EWaveType::Normal;
    
    // Determinar tipo de onda baseado no número
    if (CurrentWaveNumber % 10 == 0)
    {
        WaveType = EWaveType::Boss;
    }
    else if (CurrentWaveNumber % 5 == 0)
    {
        WaveType = EWaveType::Elite;
    }
    else if (CurrentWaveNumber % 3 == 0)
    {
        WaveType = EWaveType::Siege;
    }
    
    CurrentWave = GenerateWaveData(CurrentWaveNumber, WaveType);
    CurrentWave.bIsActive = true;
    CurrentWave.WaveStartTime = GetWorld()->GetTimeSeconds();
    
    // Configurar formação da onda
    SetupWaveFormation(CurrentWave);
    
    // Spawnar minions da onda
    SpawnWaveMinions(CurrentWave);
    
    LastWaveStartTime = GetWorld()->GetTimeSeconds();
    
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: Iniciando onda %d (Tipo: %d)"), CurrentWaveNumber, (int32)WaveType);
    }
    
    // Configurar timer para próxima onda
    if (bInfiniteWaves || CurrentWaveNumber < MaxWaves)
    {
        if (GetWorld())
        {
            GetWorld()->GetTimerManager().SetTimer(WaveSpawnTimer, [this]()
            {
                CurrentWaveNumber++;
                StartNextWave();
            }, WaveInterval, false);
        }
    }
}

void AMinionWaveManager::StopCurrentWave()
{
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(WaveSpawnTimer);
    }
    
    CurrentWave.bIsActive = false;
    bWaveSystemActive = false;
    
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: Onda atual interrompida"));
    }
}

void AMinionWaveManager::PauseWaveSystem()
{
    bWaveSystemPaused = true;
    
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().PauseTimer(WaveSpawnTimer);
        GetWorld()->GetTimerManager().PauseTimer(MinionUpdateTimer);
    }
    
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: Sistema pausado"));
    }
}

void AMinionWaveManager::ResumeWaveSystem()
{
    bWaveSystemPaused = false;
    
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().UnPauseTimer(WaveSpawnTimer);
        GetWorld()->GetTimerManager().UnPauseTimer(MinionUpdateTimer);
    }
    
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: Sistema retomado"));
    }
}

void AMinionWaveManager::ResetWaveSystem()
{
    // Parar todos os timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(WaveSpawnTimer);
        GetWorld()->GetTimerManager().ClearTimer(MinionUpdateTimer);
        GetWorld()->GetTimerManager().ClearTimer(PathUpdateTimer);
    }
    
    // Destruir todos os minions
    DestroyAllMinions();
    
    // Resetar variáveis
    CurrentWaveNumber = 1;
    bWaveSystemActive = false;
    bWaveSystemPaused = false;
    LastWaveStartTime = 0.0f;
    NextMinionID = 1;
    
    // Limpar caches
    MinionPathCache.Empty();
    WalkabilityCache.Empty();
    
    // Reinicializar se necessário
    if (bAutoStartWaves)
    {
        bWaveSystemActive = true;
        StartNextWave();
    }
    
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: Sistema resetado"));
    }
}

FWaveData AMinionWaveManager::GenerateWaveData(int32 WaveNumber, EWaveType WaveType)
{
    FWaveData NewWave;
    NewWave.WaveNumber = WaveNumber;
    NewWave.WaveType = WaveType;
    
    // Gerar composição de minions
    NewWave.MinionComposition = GenerateMinionComposition(WaveNumber, WaveType);
    NewWave.TotalMinions = NewWave.MinionComposition.Num();
    
    // Calcular multiplicadores de escalonamento
    CalculateWaveScaling(WaveNumber, NewWave.HealthMultiplier, NewWave.DamageMultiplier, NewWave.SpeedMultiplier);
    
    // Configurar intervalo de spawn
    NewWave.SpawnInterval = FMath::Max(0.5f, 2.0f - (WaveNumber * 0.05f));
    
    // Determinar lane primária
    NewWave.PrimaryLane = static_cast<ELaneType>(FMath::RandRange(0, 2));
    
    // Configurar formação baseada no tipo de onda
    switch (WaveType)
    {
        case EWaveType::Normal:
            NewWave.Formation = EFormationType::Line;
            break;
        case EWaveType::Elite:
            NewWave.Formation = EFormationType::Wedge;
            break;
        case EWaveType::Boss:
            NewWave.Formation = EFormationType::Box;
            break;
        case EWaveType::Siege:
            NewWave.Formation = EFormationType::Column;
            break;
        default:
            NewWave.Formation = EFormationType::Scattered;
            break;
    }
    
    // Super minion para ondas especiais
    NewWave.bHasSuperMinion = (WaveType == EWaveType::Boss || WaveType == EWaveType::Siege);
    
    return NewWave;
}

TArray<EMinionType> AMinionWaveManager::GenerateMinionComposition(int32 WaveNumber, EWaveType WaveType)
{
    TArray<EMinionType> Composition;
    
    int32 BaseMinions = FMath::Min(MAX_MINIONS_PER_WAVE, 3 + (WaveNumber / 2));
    
    switch (WaveType)
    {
        case EWaveType::Normal:
        {
            // Composição balanceada
            for (int32 i = 0; i < BaseMinions; i++)
            {
                if (i < BaseMinions / 2)
                {
                    Composition.Add(EMinionType::Melee);
                }
                else
                {
                    Composition.Add(EMinionType::Ranged);
                }
            }
            break;
        }
        case EWaveType::Elite:
        {
            // Mais minions especializados
            for (int32 i = 0; i < BaseMinions + 2; i++)
            {
                EMinionType Type = static_cast<EMinionType>(FMath::RandRange(0, 4));
                Composition.Add(Type);
            }
            break;
        }
        case EWaveType::Boss:
        {
            // Onda com super minion
            Composition.Add(EMinionType::Super);
            for (int32 i = 0; i < BaseMinions; i++)
            {
                Composition.Add(EMinionType::Tank);
            }
            break;
        }
        case EWaveType::Siege:
        {
            // Foco em minions de cerco
            for (int32 i = 0; i < BaseMinions / 2; i++)
            {
                Composition.Add(EMinionType::Siege);
            }
            for (int32 i = 0; i < BaseMinions / 2; i++)
            {
                Composition.Add(EMinionType::Tank);
            }
            break;
        }
        default:
            // Composição aleatória
            for (int32 i = 0; i < BaseMinions; i++)
            {
                EMinionType Type = GetRandomMinionType(WaveNumber);
                Composition.Add(Type);
            }
            break;
    }
    
    return Composition;
}

void AMinionWaveManager::CalculateWaveScaling(int32 WaveNumber, float& HealthMultiplier, float& DamageMultiplier, float& SpeedMultiplier)
{
    float ScalingBase = FMath::Pow(WaveScalingFactor, WaveNumber - 1);
    
    HealthMultiplier = ScalingBase;
    DamageMultiplier = FMath::Pow(WaveScalingFactor * 0.8f, WaveNumber - 1);
    SpeedMultiplier = FMath::Min(2.0f, 1.0f + ((WaveNumber - 1) * 0.05f));
    
    // Limitar escalonamento para evitar valores extremos
    HealthMultiplier = FMath::Clamp(HealthMultiplier, 1.0f, 10.0f);
    DamageMultiplier = FMath::Clamp(DamageMultiplier, 1.0f, 5.0f);
    SpeedMultiplier = FMath::Clamp(SpeedMultiplier, 1.0f, 2.0f);
}

void AMinionWaveManager::SetupWaveFormation(FWaveData& WaveData)
{
    // Determinar posição central baseada na lane primária
    FVector CenterPosition = FVector::ZeroVector;
    
    switch (WaveData.PrimaryLane)
    {
        case ELaneType::Top:
            CenterPosition = FVector(-8000.0f, 6000.0f, 100.0f);
            break;
        case ELaneType::Middle:
            CenterPosition = FVector(-8000.0f, 0.0f, 100.0f);
            break;
        case ELaneType::Bottom:
            CenterPosition = FVector(-8000.0f, -6000.0f, 100.0f);
            break;
    }
    
    // Criar formação
    FMinionFormation Formation = CreateFormation(WaveData.Formation, CenterPosition, WaveData.TotalMinions);
    
    // Armazenar posições de spawn
    WaveData.SpawnPositions = Formation.Positions;
}

int32 AMinionWaveManager::SpawnMinion(EMinionType Type, const FVector& Position, ELaneType Lane)
{
    FMinionData NewMinion;
    NewMinion.MinionID = NextMinionID++;
    NewMinion.Type = Type;
    NewMinion.Position = Position;
    NewMinion.AssignedLane = Lane;
    NewMinion.State = EMinionState::Spawning;
    NewMinion.WaveNumber = CurrentWaveNumber;
    
    // Configurar stats baseados no tipo
    switch (Type)
    {
        case EMinionType::Melee:
            NewMinion.Health = MINION_HEALTH_BASE_MELEE;
            NewMinion.Damage = MINION_DAMAGE;
            NewMinion.Speed = MINION_SPEED_MELEE;
            NewMinion.AttackRange = 150.0f;
            NewMinion.AttackSpeed = 1.2f;
            break;
        case EMinionType::Ranged:
            NewMinion.Health = MINION_HEALTH_BASE_MELEE * 0.8f;
            NewMinion.Damage = MINION_DAMAGE * 1.2f;
            NewMinion.Speed = MINION_SPEED_MELEE * 0.9f;
            NewMinion.AttackRange = 600.0f;
            NewMinion.AttackSpeed = 0.8f;
            break;
        case EMinionType::Caster:
            NewMinion.Health = MINION_HEALTH_BASE_MELEE * 0.6f;
            NewMinion.Damage = MINION_DAMAGE * 1.5f;
            NewMinion.Speed = MINION_SPEED_MELEE * 0.8f;
            NewMinion.AttackRange = 800.0f;
            NewMinion.AttackSpeed = 0.6f;
            break;
        case EMinionType::Tank:
            NewMinion.Health = MINION_HEALTH_BASE_MELEE * 2.0f;
            NewMinion.Damage = MINION_DAMAGE * 0.8f;
            NewMinion.Speed = MINION_SPEED_MELEE * 0.7f;
            NewMinion.AttackRange = 200.0f;
            NewMinion.AttackSpeed = 0.9f;
            break;
        case EMinionType::Support:
            NewMinion.Health = MINION_HEALTH_BASE_MELEE * 0.9f;
            NewMinion.Damage = MINION_DAMAGE * 0.5f;
            NewMinion.Speed = MINION_SPEED_MELEE * 1.1f;
            NewMinion.AttackRange = 400.0f;
            NewMinion.AttackSpeed = 1.5f;
            break;
        case EMinionType::Siege:
            NewMinion.Health = MINION_HEALTH_BASE_MELEE * 1.5f;
            NewMinion.Damage = MINION_DAMAGE * 2.0f;
            NewMinion.Speed = MINION_SPEED_MELEE * 0.6f;
            NewMinion.AttackRange = 1000.0f;
            NewMinion.AttackSpeed = 0.4f;
            break;
        case EMinionType::Super:
            NewMinion.Health = MINION_HEALTH_BASE_MELEE * 3.0f;
            NewMinion.Damage = MINION_DAMAGE * 2.5f;
            NewMinion.Speed = MINION_SPEED_MELEE * 0.8f;
            NewMinion.AttackRange = 300.0f;
            NewMinion.AttackSpeed = 1.0f;
            NewMinion.bIsElite = true;
            break;
    }
    
    // Aplicar escalonamento da onda
    ApplyWaveScaling(NewMinion, CurrentWaveNumber);
    
    NewMinion.MaxHealth = NewMinion.Health;
    
    // Gerar caminho inicial
    FVector EndPosition = GetLaneEndPosition(Lane);
    NewMinion.PathWaypoints = FindPath(Position, EndPosition);
    NewMinion.CurrentWaypointIndex = 0;
    
    // Adicionar à lista de minions ativos
    ActiveMinions.Add(NewMinion);
    
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AMinionWaveManager: Minion %d spawnado (Tipo: %d, Lane: %d)"), NewMinion.MinionID, (int32)Type, (int32)Lane);
    }
    
    return NewMinion.MinionID;
}

void AMinionWaveManager::SpawnWaveMinions(const FWaveData& WaveData)
{
    if (WaveData.SpawnPositions.Num() != WaveData.MinionComposition.Num())
    {
        UE_LOG(LogTemp, Error, TEXT("AMinionWaveManager: Mismatch entre posições de spawn e composição de minions"));
        return;
    }
    
    for (int32 i = 0; i < WaveData.MinionComposition.Num(); i++)
    {
        EMinionType Type = WaveData.MinionComposition[i];
        FVector SpawnPos = WaveData.SpawnPositions[i];
        
        SpawnMinion(Type, SpawnPos, WaveData.PrimaryLane);
        
        // Pequeno delay entre spawns para efeito visual
        if (GetWorld() && i < WaveData.MinionComposition.Num() - 1)
        {
            FTimerHandle SpawnDelayTimer;
            GetWorld()->GetTimerManager().SetTimer(SpawnDelayTimer, [this, Type, SpawnPos, WaveData, i]()
            {
                // Spawn com delay já processado no loop principal
            }, WaveData.SpawnInterval * i, false);
        }
    }
}

void AMinionWaveManager::DestroyMinion(int32 MinionID)
{
    for (int32 i = ActiveMinions.Num() - 1; i >= 0; i--)
    {
        if (ActiveMinions[i].MinionID == MinionID)
        {
            if (bEnableDetailedLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("AMinionWaveManager: Minion %d destruído"), MinionID);
            }
            
            // Remover do cache de paths
            MinionPathCache.Remove(MinionID);
            
            // Remover da lista
            ActiveMinions.RemoveAt(i);
            break;
        }
    }
}

void AMinionWaveManager::DestroyAllMinions()
{
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: Destruindo todos os %d minions ativos"), ActiveMinions.Num());
    }
    
    ActiveMinions.Empty();
    MinionPathCache.Empty();
}

void AMinionWaveManager::InitializePathfindingGrid()
{
    int32 TotalNodes = NavigationGrid.GridWidth * NavigationGrid.GridHeight;
    NavigationGrid.Nodes.Empty(TotalNodes);
    NavigationGrid.Nodes.AddDefaulted(TotalNodes);
    
    // Inicializar cada nó do grid
    for (int32 Y = 0; Y < NavigationGrid.GridHeight; Y++)
    {
        for (int32 X = 0; X < NavigationGrid.GridWidth; X++)
        {
            int32 Index = Y * NavigationGrid.GridWidth + X;
            FPathfindingNode& Node = NavigationGrid.Nodes[Index];
            
            Node.GridCoordinate = FVector2D(X, Y);
            Node.Position = GridToWorldPosition(Node.GridCoordinate);
            Node.bIsWalkable = IsPositionWalkable(Node.Position);
            Node.MovementCost = 1.0f;
            
            // Resetar valores de pathfinding
            Node.GCost = 0.0f;
            Node.HCost = 0.0f;
            Node.FCost = 0.0f;
            Node.bIsInOpenSet = false;
            Node.bIsInClosedSet = false;
            Node.ParentIndex = -1;
        }
    }
    
    // Configurar vizinhos para cada nó
    for (int32 Y = 0; Y < NavigationGrid.GridHeight; Y++)
    {
        for (int32 X = 0; X < NavigationGrid.GridWidth; X++)
        {
            int32 Index = Y * NavigationGrid.GridWidth + X;
            FPathfindingNode& Node = NavigationGrid.Nodes[Index];
            
            // Adicionar vizinhos (8 direções)
            for (int32 DY = -1; DY <= 1; DY++)
            {
                for (int32 DX = -1; DX <= 1; DX++)
                {
                    if (DX == 0 && DY == 0) continue;
                    
                    int32 NeighborX = X + DX;
                    int32 NeighborY = Y + DY;
                    
                    if (NeighborX >= 0 && NeighborX < NavigationGrid.GridWidth &&
                        NeighborY >= 0 && NeighborY < NavigationGrid.GridHeight)
                    {
                        int32 NeighborIndex = NeighborY * NavigationGrid.GridWidth + NeighborX;
                        Node.Neighbors.Add(&NavigationGrid.Nodes[NeighborIndex]);
                    }
                }
            }
        }
    }
    
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: Grid de pathfinding inicializado (%dx%d = %d nós)"), 
               NavigationGrid.GridWidth, NavigationGrid.GridHeight, TotalNodes);
    }
}

TArray<FVector> AMinionWaveManager::FindPath(const FVector& StartPos, const FVector& EndPos, bool bAvoidUnits)
{
    switch (PathfindingAlgorithm)
    {
        case EPathfindingAlgorithm::AStar:
            return FindPathAStar(StartPos, EndPos);
        case EPathfindingAlgorithm::Dijkstra:
            return FindPathDijkstra(StartPos, EndPos);
        default:
            return FindPathAStar(StartPos, EndPos);
    }
}

TArray<FVector> AMinionWaveManager::FindPathAStar(const FVector& StartPos, const FVector& EndPos)
{
    TArray<FVector> Path;
    
    // Converter posições do mundo para coordenadas do grid
    FVector2D StartGrid = WorldToGridCoordinate(StartPos);
    FVector2D EndGrid = WorldToGridCoordinate(EndPos);
    
    // Obter nós de início e fim
    FPathfindingNode* StartNode = GetNodeAtGridPosition(StartGrid.X, StartGrid.Y);
    FPathfindingNode* EndNode = GetNodeAtGridPosition(EndGrid.X, EndGrid.Y);
    
    if (!StartNode || !EndNode || !StartNode->bIsWalkable || !EndNode->bIsWalkable)
    {
        // Retornar caminho direto se não conseguir encontrar nós válidos
        Path.Add(StartPos);
        Path.Add(EndPos);
        return Path;
    }
    
    // Resetar nós para nova busca
    ResetPathfindingNodes();
    
    // Inicializar listas
    OpenSet.Empty();
    ClosedSet.Empty();
    
    // Configurar nó inicial
    StartNode->GCost = 0.0f;
    StartNode->HCost = CalculateHeuristic(StartPos, EndPos);
    StartNode->FCost = StartNode->GCost + StartNode->HCost;
    StartNode->bIsInOpenSet = true;
    OpenSet.Add(StartNode);
    
    int32 Iterations = 0;
    
    while (OpenSet.Num() > 0 && Iterations < MaxPathfindingIterations)
    {
        Iterations++;
        
        // Encontrar nó com menor F cost
        FPathfindingNode* CurrentNode = GetLowestFCostNode(OpenSet);
        
        // Mover para closed set
        OpenSet.Remove(CurrentNode);
        CurrentNode->bIsInOpenSet = false;
        CurrentNode->bIsInClosedSet = true;
        ClosedSet.Add(CurrentNode);
        
        // Verificar se chegamos ao destino
        if (CurrentNode == EndNode)
        {
            Path = ReconstructPath(EndNode);
            break;
        }
        
        // Processar vizinhos
        for (FPathfindingNode* Neighbor : CurrentNode->Neighbors)
        {
            if (!Neighbor->bIsWalkable || Neighbor->bIsInClosedSet)
            {
                continue;
            }
            
            float TentativeGCost = CurrentNode->GCost + CalculateMovementCost(CurrentNode, Neighbor);
            
            if (!Neighbor->bIsInOpenSet)
            {
                Neighbor->ParentIndex = GetNodeIndex(CurrentNode);
                Neighbor->GCost = TentativeGCost;
                Neighbor->HCost = CalculateHeuristic(Neighbor->Position, EndPos);
                Neighbor->FCost = Neighbor->GCost + Neighbor->HCost;
                Neighbor->bIsInOpenSet = true;
                OpenSet.Add(Neighbor);
            }
            else if (TentativeGCost < Neighbor->GCost)
            {
                Neighbor->ParentIndex = GetNodeIndex(CurrentNode);
                Neighbor->GCost = TentativeGCost;
                Neighbor->FCost = Neighbor->GCost + Neighbor->HCost;
            }
        }
    }
    
    // Se não encontrou caminho, retornar linha reta
    if (Path.Num() == 0)
    {
        Path.Add(StartPos);
        Path.Add(EndPos);
    }
    
    if (bEnableDetailedLogging && Iterations >= MaxPathfindingIterations)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: Pathfinding A* atingiu limite de iterações"));
    }
    
    return Path;
}

TArray<FVector> AMinionWaveManager::FindPathDijkstra(const FVector& StartPos, const FVector& EndPos)
{
    // Implementação simplificada do Dijkstra
    // Para este exemplo, usar A* como fallback
    return FindPathAStar(StartPos, EndPos);
}

void AMinionWaveManager::UpdateNavigationGrid()
{
    // Atualizar obstáculos dinâmicos
    UpdateObstacles();
    
    // Atualizar custos de movimento baseados em densidade de minions
    for (FPathfindingNode& Node : NavigationGrid.Nodes)
    {
        int32 MinionsNearby = 0;
        for (const FMinionData& Minion : ActiveMinions)
        {
            if (FVector::Dist(Node.Position, Minion.Position) < MINION_COLLISION_RADIUS * 2.0f)
            {
                MinionsNearby++;
            }
        }
        
        // Aumentar custo de movimento em áreas congestionadas
        Node.MovementCost = 1.0f + (MinionsNearby * 0.5f);
    }
}

bool AMinionWaveManager::IsPositionWalkable(const FVector& Position)
{
    // Verificar cache primeiro
    FVector2D GridPos = WorldToGridCoordinate(Position);
    if (WalkabilityCache.Contains(GridPos))
    {
        return WalkabilityCache[GridPos];
    }
    
    bool bWalkable = true;
    
    // Verificar colisão com paredes
    if (WallManager)
    {
        // Implementar verificação de colisão com paredes
        // bWalkable = !WallManager->IsPositionInsideWall(Position);
    }
    
    // Verificar colisão com rio
    if (RiverManager)
    {
        // Implementar verificação de colisão com rio
        // bWalkable = bWalkable && !RiverManager->IsPositionInWater(Position);
    }
    
    // Verificar limites do mapa
    if (Position.X < NavigationGrid.GridOrigin.X || Position.X > NavigationGrid.GridOrigin.X + NavigationGrid.GridBounds.X ||
        Position.Y < NavigationGrid.GridOrigin.Y || Position.Y > NavigationGrid.GridOrigin.Y + NavigationGrid.GridBounds.Y)
    {
        bWalkable = false;
    }
    
    // Armazenar no cache
    WalkabilityCache.Add(GridPos, bWalkable);
    
    return bWalkable;
}

FVector2D AMinionWaveManager::WorldToGridCoordinate(const FVector& WorldPosition)
{
    FVector RelativePos = WorldPosition - NavigationGrid.GridOrigin;
    int32 X = FMath::FloorToInt(RelativePos.X / NavigationGrid.CellSize);
    int32 Y = FMath::FloorToInt(RelativePos.Y / NavigationGrid.CellSize);
    
    X = FMath::Clamp(X, 0, NavigationGrid.GridWidth - 1);
    Y = FMath::Clamp(Y, 0, NavigationGrid.GridHeight - 1);
    
    return FVector2D(X, Y);
}

FVector AMinionWaveManager::GridToWorldPosition(const FVector2D& GridCoordinate)
{
    FVector WorldPos = NavigationGrid.GridOrigin;
    WorldPos.X += GridCoordinate.X * NavigationGrid.CellSize + (NavigationGrid.CellSize * 0.5f);
    WorldPos.Y += GridCoordinate.Y * NavigationGrid.CellSize + (NavigationGrid.CellSize * 0.5f);
    
    return WorldPos;
}

FPathfindingNode* AMinionWaveManager::GetNodeAtGridPosition(int32 X, int32 Y)
{
    if (X < 0 || X >= NavigationGrid.GridWidth || Y < 0 || Y >= NavigationGrid.GridHeight)
    {
        return nullptr;
    }
    
    int32 Index = Y * NavigationGrid.GridWidth + X;
    if (Index >= 0 && Index < NavigationGrid.Nodes.Num())
    {
        return &NavigationGrid.Nodes[Index];
    }
    
    return nullptr;
}

bool AMinionWaveManager::GetNodeAtGridPosition(int32 X, int32 Y, FPathfindingNode& OutNode)
{
    if (X < 0 || X >= NavigationGrid.GridWidth || Y < 0 || Y >= NavigationGrid.GridHeight)
    {
        return false;
    }
    
    int32 Index = Y * NavigationGrid.GridWidth + X;
    if (Index >= 0 && Index < NavigationGrid.Nodes.Num())
    {
        OutNode = NavigationGrid.Nodes[Index];
        return true;
    }
    
    return false;
}

TArray<FPathfindingNode*> AMinionWaveManager::GetNeighborNodes(FPathfindingNode* Node)
{
    return Node ? Node->Neighbors : TArray<FPathfindingNode*>();
}

float AMinionWaveManager::CalculateHeuristic(const FVector& StartPos, const FVector& EndPos)
{
    // Usar distância Manhattan para heurística
    FVector Delta = EndPos - StartPos;
    return FMath::Abs(Delta.X) + FMath::Abs(Delta.Y);
}

float AMinionWaveManager::CalculateMovementCost(FPathfindingNode* FromNode, FPathfindingNode* ToNode)
{
    if (!FromNode || !ToNode)
    {
        return 0.0f;
    }
    
    // Custo base
    float Cost = ToNode->MovementCost;
    
    // Custo adicional para movimento diagonal
    FVector2D Delta = ToNode->GridCoordinate - FromNode->GridCoordinate;
    if (FMath::Abs(Delta.X) + FMath::Abs(Delta.Y) > 1.0f)
    {
        Cost *= 1.414f; // sqrt(2)
    }
    
    return Cost;
}

void AMinionWaveManager::UpdateMinionMovement(FMinionData& Minion, float DeltaTime)
{
    if (Minion.State != EMinionState::Moving || Minion.PathWaypoints.Num() == 0)
    {
        return;
    }
    
    // Verificar se chegou ao waypoint atual
    if (Minion.CurrentWaypointIndex < Minion.PathWaypoints.Num())
    {
        FVector TargetWaypoint = Minion.PathWaypoints[Minion.CurrentWaypointIndex];
        FVector Direction = (TargetWaypoint - Minion.Position).GetSafeNormal();
        float DistanceToWaypoint = FVector::Dist(Minion.Position, TargetWaypoint);
        
        if (DistanceToWaypoint < 50.0f) // 0.5 metros de tolerância
        {
            Minion.CurrentWaypointIndex++;
            
            // Verificar se chegou ao final do caminho
            if (Minion.CurrentWaypointIndex >= Minion.PathWaypoints.Num())
            {
                // Chegou ao destino final
                Minion.State = EMinionState::Fighting;
                return;
            }
        }
        else
        {
            // Mover em direção ao waypoint
            FVector Movement = Direction * Minion.Speed * DeltaTime;
            Minion.Position += Movement;
            Minion.Velocity = Direction * Minion.Speed;
            
            // Atualizar rotação
            if (!Direction.IsZero())
            {
                Minion.Rotation = FRotationMatrix::MakeFromX(Direction).Rotator();
            }
            
            // Aplicar evitamento de colisão com outros minions
            if (bAvoidOtherMinions)
            {
                UpdateMinionCollisionAvoidance(Minion);
            }
        }
    }
}

void AMinionWaveManager::UpdateMinionBehavior(FMinionData& Minion, float DeltaTime)
{
    switch (Minion.State)
    {
        case EMinionState::Spawning:
        {
            // Transição para movimento após spawn
            Minion.State = EMinionState::Moving;
            break;
        }
        case EMinionState::Moving:
        {
            UpdateMinionMovement(Minion, DeltaTime);
            
            // Verificar se há inimigos próximos
            AActor* NearestTarget = FindNearestTarget(Minion);
            if (NearestTarget && FVector::Dist(Minion.Position, NearestTarget->GetActorLocation()) <= Minion.AttackRange)
            {
                Minion.Target = NearestTarget;
                Minion.State = EMinionState::Fighting;
            }
            break;
        }
        case EMinionState::Fighting:
        {
            UpdateMinionCombat(Minion, DeltaTime);
            break;
        }
        case EMinionState::Retreating:
        {
            // Implementar lógica de retirada se necessário
            Minion.State = EMinionState::Moving;
            break;
        }
        case EMinionState::Dead:
        {
            // Minion será removido na próxima limpeza
            break;
        }
        default:
            break;
    }
}

void AMinionWaveManager::UpdateMinionCombat(FMinionData& Minion, float DeltaTime)
{
    if (!Minion.Target || !IsValid(Minion.Target))
    {
        // Procurar novo alvo ou voltar ao movimento
        Minion.Target = FindNearestTarget(Minion);
        if (!Minion.Target)
        {
            Minion.State = EMinionState::Moving;
            return;
        }
    }
    
    float DistanceToTarget = FVector::Dist(Minion.Position, Minion.Target->GetActorLocation());
    
    if (DistanceToTarget > Minion.AttackRange * 1.2f)
    {
        // Alvo muito longe, voltar ao movimento
        Minion.State = EMinionState::Moving;
        Minion.Target = nullptr;
        return;
    }
    
    // Verificar se pode atacar
    if (CanMinionAttack(Minion))
    {
        ExecuteMinionAttack(Minion, Minion.Target);
        Minion.LastAttackTime = GetWorld()->GetTimeSeconds();
    }
    
    // Rotacionar para o alvo
    FVector DirectionToTarget = (Minion.Target->GetActorLocation() - Minion.Position).GetSafeNormal();
    if (!DirectionToTarget.IsZero())
    {
        Minion.Rotation = FRotationMatrix::MakeFromX(DirectionToTarget).Rotator();
    }
}

AActor* AMinionWaveManager::FindNearestTarget(const FMinionData& Minion)
{
    // Implementação simplificada - procurar torres ou estruturas próximas
    // Em um jogo real, isso seria integrado com o sistema de alvos
    
    AActor* NearestTarget = nullptr;
    float NearestDistance = Minion.AttackRange;
    
    // Procurar torres próximas
    if (LaneManager)
    {
        // Implementar busca por torres na lane do minion
        // NearestTarget = LaneManager->FindNearestTowerInLane(Minion.Position, Minion.AssignedLane);
    }
    
    return NearestTarget;
}

bool AMinionWaveManager::CanMinionAttack(const FMinionData& Minion)
{
    if (!Minion.Target || Minion.State != EMinionState::Fighting)
    {
        return false;
    }
    
    float TimeSinceLastAttack = GetWorld()->GetTimeSeconds() - Minion.LastAttackTime;
    float AttackCooldown = 1.0f / Minion.AttackSpeed;
    
    return TimeSinceLastAttack >= AttackCooldown;
}

void AMinionWaveManager::ExecuteMinionAttack(FMinionData& Minion, AActor* Target)
{
    if (!Target)
    {
        return;
    }
    
    // Aplicar dano ao alvo
    // Em um jogo real, isso seria feito através do sistema de combate
    
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AMinionWaveManager: Minion %d atacou %s por %.1f de dano"), 
               Minion.MinionID, *Target->GetName(), Minion.Damage);
    }
    
    // Efeitos visuais de ataque poderiam ser adicionados aqui
}

FMinionFormation AMinionWaveManager::CreateFormation(EFormationType Type, const FVector& CenterPos, int32 UnitCount)
{
    FMinionFormation Formation;
    Formation.FormationType = Type;
    Formation.CenterPosition = CenterPos;
    Formation.Orientation = FRotator(0.0f, 0.0f, 0.0f);
    Formation.Spacing = FORMATION_SPACING;
    Formation.MaxUnits = UnitCount;
    
    switch (Type)
    {
        case EFormationType::Line:
            Formation.Positions = CalculateLineFormation(CenterPos, Formation.Orientation, UnitCount, Formation.Spacing);
            break;
        case EFormationType::Column:
            Formation.Positions = CalculateColumnFormation(CenterPos, Formation.Orientation, UnitCount, Formation.Spacing);
            break;
        case EFormationType::Wedge:
            Formation.Positions = CalculateWedgeFormation(CenterPos, Formation.Orientation, UnitCount, Formation.Spacing);
            break;
        case EFormationType::Box:
            Formation.Positions = CalculateBoxFormation(CenterPos, Formation.Orientation, UnitCount, Formation.Spacing);
            break;
        case EFormationType::Scattered:
        default:
            // Posições aleatórias em um raio
            for (int32 i = 0; i < UnitCount; i++)
            {
                FVector RandomOffset = FVector(
                    FMath::RandRange(-300.0f, 300.0f),
                    FMath::RandRange(-300.0f, 300.0f),
                    0.0f
                );
                Formation.Positions.Add(CenterPos + RandomOffset);
            }
            break;
    }
    
    return Formation;
}

TArray<FVector> AMinionWaveManager::CalculateLineFormation(const FVector& CenterPos, const FRotator& Orientation, int32 UnitCount, float Spacing)
{
    TArray<FVector> Positions;
    
    FVector RightVector = FRotationMatrix(Orientation).GetUnitAxis(EAxis::Y);
    float TotalWidth = (UnitCount - 1) * Spacing;
    FVector StartPos = CenterPos - (RightVector * TotalWidth * 0.5f);
    
    for (int32 i = 0; i < UnitCount; i++)
    {
        FVector Position = StartPos + (RightVector * Spacing * i);
        Positions.Add(Position);
    }
    
    return Positions;
}

TArray<FVector> AMinionWaveManager::CalculateColumnFormation(const FVector& CenterPos, const FRotator& Orientation, int32 UnitCount, float Spacing)
{
    TArray<FVector> Positions;
    
    FVector ForwardVector = FRotationMatrix(Orientation).GetUnitAxis(EAxis::X);
    float TotalLength = (UnitCount - 1) * Spacing;
    FVector StartPos = CenterPos - (ForwardVector * TotalLength * 0.5f);
    
    for (int32 i = 0; i < UnitCount; i++)
    {
        FVector Position = StartPos + (ForwardVector * Spacing * i);
        Positions.Add(Position);
    }
    
    return Positions;
}

TArray<FVector> AMinionWaveManager::CalculateWedgeFormation(const FVector& CenterPos, const FRotator& Orientation, int32 UnitCount, float Spacing)
{
    TArray<FVector> Positions;
    
    FVector ForwardVector = FRotationMatrix(Orientation).GetUnitAxis(EAxis::X);
    FVector RightVector = FRotationMatrix(Orientation).GetUnitAxis(EAxis::Y);
    
    // Líder na frente
    Positions.Add(CenterPos);
    
    // Resto em formação V
    int32 UnitsPerSide = (UnitCount - 1) / 2;
    for (int32 i = 1; i <= UnitsPerSide; i++)
    {
        // Lado direito
        FVector RightPos = CenterPos - (ForwardVector * Spacing * i) + (RightVector * Spacing * i);
        Positions.Add(RightPos);
        
        // Lado esquerdo
        if (Positions.Num() < UnitCount)
        {
            FVector LeftPos = CenterPos - (ForwardVector * Spacing * i) - (RightVector * Spacing * i);
            Positions.Add(LeftPos);
        }
    }
    
    return Positions;
}

TArray<FVector> AMinionWaveManager::CalculateBoxFormation(const FVector& CenterPos, const FRotator& Orientation, int32 UnitCount, float Spacing)
{
    TArray<FVector> Positions;
    
    int32 UnitsPerRow = FMath::CeilToInt(FMath::Sqrt(static_cast<float>(UnitCount)));
    int32 Rows = FMath::CeilToInt((float)UnitCount / UnitsPerRow);
    
    FVector ForwardVector = FRotationMatrix(Orientation).GetUnitAxis(EAxis::X);
    FVector RightVector = FRotationMatrix(Orientation).GetUnitAxis(EAxis::Y);
    
    FVector StartPos = CenterPos - (ForwardVector * (Rows - 1) * Spacing * 0.5f) - (RightVector * (UnitsPerRow - 1) * Spacing * 0.5f);
    
    for (int32 Row = 0; Row < Rows && Positions.Num() < UnitCount; Row++)
    {
        for (int32 Col = 0; Col < UnitsPerRow && Positions.Num() < UnitCount; Col++)
        {
            FVector Position = StartPos + (ForwardVector * Row * Spacing) + (RightVector * Col * Spacing);
            Positions.Add(Position);
        }
    }
    
    return Positions;
}

void AMinionWaveManager::UpdateFormationPositions(FMinionFormation& Formation, const FVector& NewCenterPos, const FRotator& NewOrientation)
{
    Formation.CenterPosition = NewCenterPos;
    Formation.Orientation = NewOrientation;
    
    // Recalcular posições
    Formation.Positions = CreateFormation(Formation.FormationType, NewCenterPos, Formation.MaxUnits).Positions;
}

void AMinionWaveManager::AssignMinionsToFormation(const FMinionFormation& Formation, TArray<int32>& MinionIDs)
{
    for (int32 i = 0; i < MinionIDs.Num() && i < Formation.Positions.Num(); i++)
    {
        FMinionData* Minion = GetMinionByID(MinionIDs[i]);
        if (Minion)
        {
            // Atualizar caminho para a posição da formação
            Minion->PathWaypoints = FindPath(Minion->Position, Formation.Positions[i]);
            Minion->CurrentWaypointIndex = 0;
        }
    }
}

void AMinionWaveManager::InitializeSpawnPoints()
{
    SpawnPoints.Empty();
    
    // Spawn points para cada lane
    // Top Lane
    AddSpawnPoint(FVector(-9000.0f, 6000.0f, 100.0f), FRotator(0.0f, 0.0f, 0.0f), ELaneType::Top);
    AddSpawnPoint(FVector(-8500.0f, 6200.0f, 100.0f), FRotator(0.0f, 0.0f, 0.0f), ELaneType::Top);
    AddSpawnPoint(FVector(-8500.0f, 5800.0f, 100.0f), FRotator(0.0f, 0.0f, 0.0f), ELaneType::Top);
    
    // Middle Lane
    AddSpawnPoint(FVector(-9000.0f, 0.0f, 100.0f), FRotator(0.0f, 0.0f, 0.0f), ELaneType::Middle);
    AddSpawnPoint(FVector(-8500.0f, 200.0f, 100.0f), FRotator(0.0f, 0.0f, 0.0f), ELaneType::Middle);
    AddSpawnPoint(FVector(-8500.0f, -200.0f, 100.0f), FRotator(0.0f, 0.0f, 0.0f), ELaneType::Middle);
    
    // Bottom Lane
    AddSpawnPoint(FVector(-9000.0f, -6000.0f, 100.0f), FRotator(0.0f, 0.0f, 0.0f), ELaneType::Bottom);
    AddSpawnPoint(FVector(-8500.0f, -6200.0f, 100.0f), FRotator(0.0f, 0.0f, 0.0f), ELaneType::Bottom);
    AddSpawnPoint(FVector(-8500.0f, -5800.0f, 100.0f), FRotator(0.0f, 0.0f, 0.0f), ELaneType::Bottom);
    
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: %d spawn points inicializados"), SpawnPoints.Num());
    }
}

int32 AMinionWaveManager::AddSpawnPoint(const FVector& Position, const FRotator& Rotation, ELaneType LaneType)
{
    FMinionSpawnPoint NewSpawnPoint;
    NewSpawnPoint.Position = Position;
    NewSpawnPoint.Rotation = Rotation;
    NewSpawnPoint.LaneType = LaneType;
    NewSpawnPoint.bIsActive = true;
    NewSpawnPoint.CooldownTime = 1.0f;
    NewSpawnPoint.LastSpawnTime = 0.0f;
    NewSpawnPoint.MaxConcurrentSpawns = 3;
    NewSpawnPoint.CurrentSpawnCount = 0;
    
    return SpawnPoints.Add(NewSpawnPoint);
}

void AMinionWaveManager::RemoveSpawnPoint(int32 SpawnPointIndex)
{
    if (SpawnPoints.IsValidIndex(SpawnPointIndex))
    {
        SpawnPoints.RemoveAt(SpawnPointIndex);
    }
}

bool AMinionWaveManager::GetAvailableSpawnPoint(ELaneType LaneType, FMinionSpawnPoint& OutSpawnPoint)
{
    for (FMinionSpawnPoint& SpawnPoint : SpawnPoints)
    {
        if (SpawnPoint.LaneType == LaneType && SpawnPoint.bIsActive &&
            SpawnPoint.CurrentSpawnCount < SpawnPoint.MaxConcurrentSpawns)
        {
            float TimeSinceLastSpawn = GetWorld()->GetTimeSeconds() - SpawnPoint.LastSpawnTime;
            if (TimeSinceLastSpawn >= SpawnPoint.CooldownTime)
            {
                OutSpawnPoint = SpawnPoint;
                return true;
            }
        }
    }
    
    return false;
}

void AMinionWaveManager::UpdateSpawnPointCooldowns(float DeltaTime)
{
    for (FMinionSpawnPoint& SpawnPoint : SpawnPoints)
    {
        // Atualizar contagem de spawns ativos
        int32 ActiveSpawns = 0;
        for (const FMinionData& Minion : ActiveMinions)
        {
            if (FVector::Dist(Minion.Position, SpawnPoint.Position) < 500.0f)
            {
                ActiveSpawns++;
            }
        }
        SpawnPoint.CurrentSpawnCount = ActiveSpawns;
    }
}

bool AMinionWaveManager::ValidateWaveSystem()
{
    bool bIsValid = true;
    
    // Verificar se o sistema foi inicializado
    if (!bIsInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("AMinionWaveManager: Sistema não foi inicializado"));
        bIsValid = false;
    }
    
    // Verificar spawn points
    if (SpawnPoints.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("AMinionWaveManager: Nenhum spawn point configurado"));
        bIsValid = false;
    }
    
    // Verificar grid de pathfinding
    if (!ValidatePathfindingGrid())
    {
        UE_LOG(LogTemp, Error, TEXT("AMinionWaveManager: Grid de pathfinding inválido"));
        bIsValid = false;
    }
    
    // Verificar referências para outros managers
    if (!LaneManager)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: LaneManager não configurado"));
    }
    
    return bIsValid;
}

bool AMinionWaveManager::ValidatePathfindingGrid()
{
    if (NavigationGrid.Nodes.Num() != NavigationGrid.GridWidth * NavigationGrid.GridHeight)
    {
        return false;
    }
    
    if (NavigationGrid.CellSize <= 0.0f)
    {
        return false;
    }
    
    return true;
}

bool AMinionWaveManager::ValidateMinionData(const FMinionData& Minion)
{
    if (Minion.Health <= 0.0f || Minion.MaxHealth <= 0.0f)
    {
        return false;
    }
    
    if (Minion.Speed <= 0.0f)
    {
        return false;
    }
    
    if (Minion.MinionID <= 0)
    {
        return false;
    }
    
    return true;
}

void AMinionWaveManager::OptimizePathfinding()
{
  // Limpar cache antigo
    if (MinionPathCache.Num() > 100)
    {
        MinionPathCache.Empty();
    }
    
    // Otimizar grid de pathfinding
    if (WalkabilityCache.Num() > 1000)
    {
        WalkabilityCache.Empty();
    }
}

// Funções auxiliares restantes
void AMinionWaveManager::UpdateWaveTimer(float DeltaTime)
{
    // Implementação do timer de ondas
}

void AMinionWaveManager::UpdateAllMinions(float DeltaTime)
{
    for (FMinionData& Minion : ActiveMinions)
    {
        if (ValidateMinionData(Minion))
        {
            UpdateMinionBehavior(Minion, DeltaTime);
        }
    }
}

void AMinionWaveManager::BatchUpdatePaths()
{
    // Atualizar paths em lote para performance
    for (FMinionData& Minion : ActiveMinions)
    {
        if (Minion.State == EMinionState::Moving)
        {
            // Recalcular path se necessário
            FVector EndPos = GetLaneEndPosition(Minion.AssignedLane);
            Minion.PathWaypoints = FindPath(Minion.Position, EndPos);
            Minion.CurrentWaypointIndex = 0;
        }
    }
}

void AMinionWaveManager::CleanupDeadMinions()
{
    for (int32 i = ActiveMinions.Num() - 1; i >= 0; i--)
    {
        if (ActiveMinions[i].State == EMinionState::Dead || ActiveMinions[i].Health <= 0.0f)
        {
            DestroyMinion(ActiveMinions[i].MinionID);
        }
    }
}

void AMinionWaveManager::DrawDebugPathfindingGrid()
{
    if (!GetWorld()) return;
    
    for (const FPathfindingNode& Node : NavigationGrid.Nodes)
    {
        FColor Color = Node.bIsWalkable ? FColor::Green : FColor::Red;
        DrawDebugPoint(GetWorld(), Node.Position, 5.0f, Color, false, 0.1f);
    }
}

void AMinionWaveManager::DrawDebugMinionPaths()
{
    if (!GetWorld()) return;
    
    for (const FMinionData& Minion : ActiveMinions)
    {
        for (int32 i = 0; i < Minion.PathWaypoints.Num() - 1; i++)
        {
            DrawDebugLine(GetWorld(), Minion.PathWaypoints[i], Minion.PathWaypoints[i + 1], FColor::Blue, false, 0.1f, 0, 2.0f);
        }
    }
}

void AMinionWaveManager::DrawDebugFormations()
{
    // Implementar debug de formações
}

void AMinionWaveManager::DrawDebugSpawnPoints()
{
    if (!GetWorld()) return;
    
    for (const FMinionSpawnPoint& SpawnPoint : SpawnPoints)
    {
        FColor Color = SpawnPoint.bIsActive ? FColor::Green : FColor::Red;
        DrawDebugSphere(GetWorld(), SpawnPoint.Position, 100.0f, 12, Color, false, 0.1f);
    }
}

void AMinionWaveManager::LoadMinionAssets()
{
    // Carregar assets dos minions
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: Assets dos minions carregados"));
    }
}

void AMinionWaveManager::InitializeDefaultWaves()
{
    // Inicializar configurações padrão das ondas
    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: Ondas padrão inicializadas"));
    }
}

void AMinionWaveManager::UpdateObstacles()
{
    // Atualizar obstáculos dinâmicos no grid
}

void AMinionWaveManager::ResetPathfindingNodes()
{
    for (FPathfindingNode& Node : NavigationGrid.Nodes)
    {
        Node.GCost = 0.0f;
        Node.HCost = 0.0f;
        Node.FCost = 0.0f;
        Node.bIsInOpenSet = false;
        Node.bIsInClosedSet = false;
        Node.ParentIndex = -1;
    }
}

FPathfindingNode* AMinionWaveManager::GetLowestFCostNode(const TArray<FPathfindingNode*>& NodeList)
{
    if (NodeList.Num() == 0) return nullptr;
    
    FPathfindingNode* LowestNode = NodeList[0];
    for (FPathfindingNode* Node : NodeList)
    {
        if (Node->FCost < LowestNode->FCost)
        {
            LowestNode = Node;
        }
    }
    return LowestNode;
}

TArray<FVector> AMinionWaveManager::ReconstructPath(FPathfindingNode* EndNode)
{
    TArray<FVector> Path;
    FPathfindingNode* CurrentNode = EndNode;
    
    while (CurrentNode)
    {
        Path.Add(CurrentNode->Position);
        CurrentNode = (CurrentNode->ParentIndex >= 0) ? &NavigationGrid.Nodes[CurrentNode->ParentIndex] : nullptr;
    }
    
    Algo::Reverse(Path);
    return Path;
}

void AMinionWaveManager::UpdateMinionCollisionAvoidance(FMinionData& Minion)
{
    FVector AvoidanceForce = FVector::ZeroVector;
    int32 NearbyMinions = 0;
    
    for (const FMinionData& OtherMinion : ActiveMinions)
    {
        if (OtherMinion.MinionID != Minion.MinionID)
        {
            float Distance = FVector::Dist(Minion.Position, OtherMinion.Position);
            if (Distance < MINION_COLLISION_RADIUS * 2.0f)
            {
                FVector AvoidDirection = (Minion.Position - OtherMinion.Position).GetSafeNormal();
                AvoidanceForce += AvoidDirection * (1.0f / FMath::Max(Distance, 1.0f));
                NearbyMinions++;
            }
        }
    }
    
    if (NearbyMinions > 0)
    {
        AvoidanceForce /= NearbyMinions;
        Minion.Position += AvoidanceForce * 50.0f; // Força de evitamento
    }
}

FMinionData* AMinionWaveManager::GetMinionByID(int32 MinionID)
{
    for (FMinionData& Minion : ActiveMinions)
    {
        if (Minion.MinionID == MinionID)
        {
            return &Minion;
        }
    }
    return nullptr;
}

FVector AMinionWaveManager::GetLaneEndPosition(ELaneType Lane)
{
    switch (Lane)
    {
        case ELaneType::Top:
            return FVector(8000.0f, 6000.0f, 100.0f);
        case ELaneType::Middle:
            return FVector(8000.0f, 0.0f, 100.0f);
        case ELaneType::Bottom:
            return FVector(8000.0f, -6000.0f, 100.0f);
        default:
            return FVector(8000.0f, 0.0f, 100.0f);
    }
}

EMinionType AMinionWaveManager::GetRandomMinionType(int32 WaveNumber)
{
    // Probabilidades baseadas no número da onda
    TArray<EMinionType> AvailableTypes;
    
    AvailableTypes.Add(EMinionType::Melee);
    AvailableTypes.Add(EMinionType::Ranged);
    
    if (WaveNumber >= 3)
    {
        AvailableTypes.Add(EMinionType::Caster);
    }
    
    if (WaveNumber >= 5)
    {
        AvailableTypes.Add(EMinionType::Tank);
    }
    
    if (WaveNumber >= 7)
    {
        AvailableTypes.Add(EMinionType::Support);
    }
    
    if (WaveNumber >= 10)
    {
        AvailableTypes.Add(EMinionType::Siege);
    }
    
    return AvailableTypes[FMath::RandRange(0, AvailableTypes.Num() - 1)];
}

void AMinionWaveManager::ApplyWaveScaling(FMinionData& Minion, int32 WaveNumber)
{
    float HealthMult, DamageMult, SpeedMult;
    CalculateWaveScaling(WaveNumber, HealthMult, DamageMult, SpeedMult);
    
    Minion.Health *= HealthMult;
    Minion.Damage *= DamageMult;
    Minion.Speed *= SpeedMult;
}

int32 AMinionWaveManager::GetNodeIndex(FPathfindingNode* Node)
{
    if (!Node || NavigationGrid.Nodes.Num() == 0)
    {
        return -1;
    }
    
    // Calcular índice baseado no endereço do ponteiro
    int32 Index = Node - NavigationGrid.Nodes.GetData();
    
    // Verificar se o índice está dentro dos limites válidos
    if (Index >= 0 && Index < NavigationGrid.Nodes.Num())
    {
        return Index;
    }
    
    return -1;
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES FALTANTES =====

void AMinionWaveManager::LogMinionStatistics()
{
    UE_LOG(LogTemp, Log, TEXT("=== ESTATÍSTICAS DE MINIONS ==="));
    UE_LOG(LogTemp, Log, TEXT("Total de Minions Ativos: %d"), ActiveMinions.Num());

    // Estatísticas por tipo
    TMap<EMinionType, int32> TypeCounts;
    TMap<EMinionState, int32> StateCounts;
    TMap<ELaneType, int32> LaneCounts;

    float TotalHealth = 0.0f;
    float TotalDamage = 0.0f;
    float AverageSpeed = 0.0f;

    for (const FMinionData& Minion : ActiveMinions)
    {
        // Contar por tipo
        if (TypeCounts.Contains(Minion.Type))
        {
            TypeCounts[Minion.Type]++;
        }
        else
        {
            TypeCounts.Add(Minion.Type, 1);
        }

        // Contar por estado
        if (StateCounts.Contains(Minion.State))
        {
            StateCounts[Minion.State]++;
        }
        else
        {
            StateCounts.Add(Minion.State, 1);
        }

        // Contar por lane
        if (LaneCounts.Contains(Minion.AssignedLane))
        {
            LaneCounts[Minion.AssignedLane]++;
        }
        else
        {
            LaneCounts.Add(Minion.AssignedLane, 1);
        }

        // Acumular estatísticas
        TotalHealth += Minion.Health;
        TotalDamage += Minion.Damage;
        AverageSpeed += Minion.Speed;
    }

    if (ActiveMinions.Num() > 0)
    {
        AverageSpeed /= ActiveMinions.Num();
        UE_LOG(LogTemp, Log, TEXT("Vida Total: %.1f"), TotalHealth);
        UE_LOG(LogTemp, Log, TEXT("Dano Total: %.1f"), TotalDamage);
        UE_LOG(LogTemp, Log, TEXT("Velocidade Média: %.1f"), AverageSpeed);
    }

    // Log por tipo
    for (const auto& TypePair : TypeCounts)
    {
        UE_LOG(LogTemp, Log, TEXT("Tipo %d: %d minions"), (int32)TypePair.Key, TypePair.Value);
    }

    // Log por estado
    for (const auto& StatePair : StateCounts)
    {
        UE_LOG(LogTemp, Log, TEXT("Estado %d: %d minions"), (int32)StatePair.Key, StatePair.Value);
    }

    // Log por lane
    for (const auto& LanePair : LaneCounts)
    {
        UE_LOG(LogTemp, Log, TEXT("Lane %d: %d minions"), (int32)LanePair.Key, LanePair.Value);
    }

    UE_LOG(LogTemp, Log, TEXT("==============================="));
}

int32 AMinionWaveManager::GetActiveMinionCount() const
{
    return ActiveMinions.Num();
}

int32 AMinionWaveManager::GetCurrentWaveNumber() const
{
    return CurrentWaveNumber;
}

float AMinionWaveManager::GetWaveProgress() const
{
    if (!bWaveSystemActive || !GetWorld())
    {
        return 0.0f;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();
    float TimeSinceWaveStart = CurrentTime - LastWaveStartTime;

    // Progresso baseado no tempo até a próxima onda
    float Progress = FMath::Clamp(TimeSinceWaveStart / WaveInterval, 0.0f, 1.0f);

    return Progress;
}

TArray<FMinionData> AMinionWaveManager::GetMinionsInLane(ELaneType Lane) const
{
    TArray<FMinionData> MinionsInLane;

    for (const FMinionData& Minion : ActiveMinions)
    {
        if (Minion.AssignedLane == Lane)
        {
            MinionsInLane.Add(Minion);
        }
    }

    return MinionsInLane;
}

TArray<FMinionData> AMinionWaveManager::GetMinionsInRadius(const FVector& Center, float Radius) const
{
    TArray<FMinionData> MinionsInRadius;
    const float RadiusSquared = Radius * Radius;

    for (const FMinionData& Minion : ActiveMinions)
    {
        float DistanceSquared = FVector::DistSquared(Minion.Position, Center);
        if (DistanceSquared <= RadiusSquared)
        {
            MinionsInRadius.Add(Minion);
        }
    }

    return MinionsInRadius;
}

void AMinionWaveManager::SetWaveInterval(float NewInterval)
{
    WaveInterval = FMath::Max(0.1f, NewInterval);

    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AMinionWaveManager: Intervalo de ondas alterado para %.2f segundos"), WaveInterval);
    }
}

void AMinionWaveManager::SetPathfindingAlgorithm(EPathfindingAlgorithm NewAlgorithm)
{
    PathfindingAlgorithm = NewAlgorithm;

    // Limpar cache de paths para forçar recálculo
    MinionPathCache.Empty();

    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AMinionWaveManager: Algoritmo de pathfinding alterado para %d"), (int32)NewAlgorithm);
    }
}

void AMinionWaveManager::SetGridCellSize(float NewCellSize)
{
    if (NewCellSize <= 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AMinionWaveManager: Tamanho de célula inválido: %.2f"), NewCellSize);
        return;
    }

    GridCellSize = NewCellSize;
    NavigationGrid.CellSize = NewCellSize;

    // Reinicializar grid com novo tamanho de célula
    InitializePathfindingGrid();

    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AMinionWaveManager: Tamanho da célula do grid alterado para %.2f"), GridCellSize);
    }
}

void AMinionWaveManager::SetWaveScalingFactor(float NewScalingFactor)
{
    WaveScalingFactor = FMath::Max(1.0f, NewScalingFactor);

    if (bEnableDetailedLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AMinionWaveManager: Fator de escala de ondas alterado para %.2f"), WaveScalingFactor);
    }
}

// Implementações das funções faltantes para linking
TArray<int32> AMinionWaveManager::GetNeighborNodeIndices(int32 NodeIndex)
{
    TArray<int32> NeighborIndices;

    if (NodeIndex < 0 || NodeIndex >= NavigationGrid.Nodes.Num())
    {
        return NeighborIndices;
    }

    FPathfindingNode& Node = NavigationGrid.Nodes[NodeIndex];
    for (FPathfindingNode* Neighbor : Node.Neighbors)
    {
        int32 NeighborIndex = GetNodeIndex(Neighbor);
        if (NeighborIndex >= 0)
        {
            NeighborIndices.Add(NeighborIndex);
        }
    }

    return NeighborIndices;
}

void AMinionWaveManager::CullDistantMinions()
{
    if (!GetWorld()) return;

    // Remover minions muito distantes para otimização
    const float MaxDistance = 15000.0f; // 150 metros
    FVector PlayerLocation = FVector::ZeroVector;

    // Tentar obter posição do player/câmera
    if (APawn* PlayerPawn = GetWorld()->GetFirstPlayerController()->GetPawn())
    {
        PlayerLocation = PlayerPawn->GetActorLocation();
    }

    for (int32 i = ActiveMinions.Num() - 1; i >= 0; i--)
    {
        float Distance = FVector::Dist(ActiveMinions[i].Position, PlayerLocation);
        if (Distance > MaxDistance)
        {
            if (bEnableDetailedLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("AMinionWaveManager: Culling distant minion %d (Distance: %.1f)"),
                       ActiveMinions[i].MinionID, Distance);
            }
            DestroyMinion(ActiveMinions[i].MinionID);
        }
    }
}

void AMinionWaveManager::UpdateMinionLOD()
{
    if (!GetWorld()) return;

    FVector PlayerLocation = FVector::ZeroVector;
    if (APawn* PlayerPawn = GetWorld()->GetFirstPlayerController()->GetPawn())
    {
        PlayerLocation = PlayerPawn->GetActorLocation();
    }

    // Atualizar LOD baseado na distância
    for (FMinionData& Minion : ActiveMinions)
    {
        float Distance = FVector::Dist(Minion.Position, PlayerLocation);

        if (Distance < 2000.0f) // 20 metros - LOD Alto
        {
            Minion.LODLevel = 0;
        }
        else if (Distance < 5000.0f) // 50 metros - LOD Médio
        {
            Minion.LODLevel = 1;
        }
        else // LOD Baixo
        {
            Minion.LODLevel = 2;
        }
    }
}

void AMinionWaveManager::LogWaveStatistics()
{
    if (!GetWorld()) return;

    UE_LOG(LogTemp, Warning, TEXT("=== WAVE STATISTICS ==="));
    UE_LOG(LogTemp, Warning, TEXT("Current Wave: %d"), CurrentWaveNumber);
    UE_LOG(LogTemp, Warning, TEXT("Active Minions: %d"), ActiveMinions.Num());
    UE_LOG(LogTemp, Warning, TEXT("Wave System Active: %s"), bWaveSystemActive ? TEXT("Yes") : TEXT("No"));
    UE_LOG(LogTemp, Warning, TEXT("Wave System Paused: %s"), bWaveSystemPaused ? TEXT("Yes") : TEXT("No"));
    UE_LOG(LogTemp, Warning, TEXT("Spawn Points: %d"), SpawnPoints.Num());
    UE_LOG(LogTemp, Warning, TEXT("Pathfinding Cache Size: %d"), MinionPathCache.Num());
    UE_LOG(LogTemp, Warning, TEXT("Navigation Grid Nodes: %d"), NavigationGrid.Nodes.Num());

    // Estatísticas por tipo de minion
    TMap<EMinionType, int32> MinionTypeCounts;
    for (const FMinionData& Minion : ActiveMinions)
    {
        int32* Count = MinionTypeCounts.Find(Minion.Type);
        if (Count)
        {
            (*Count)++;
        }
        else
        {
            MinionTypeCounts.Add(Minion.Type, 1);
        }
    }

    for (const auto& TypeCount : MinionTypeCounts)
    {
        UE_LOG(LogTemp, Warning, TEXT("Minion Type %d: %d units"), (int32)TypeCount.Key, TypeCount.Value);
    }

    UE_LOG(LogTemp, Warning, TEXT("========================"));
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES OBRIGATÓRIAS PARA AMAPMANGER =====

bool AMinionWaveManager::ConfigureWaveSystem()
{
    SCOPE_CYCLE_COUNTER(STAT_MinionWaveManager_ConfigureWaveSystem);

    UE_LOG(LogMinionWaveManager, Log, TEXT("Configurando sistema de ondas conforme mapaimplementacao.md"));

    // CORRIGIDO: Implementar função temporal conforme documentação
    // Função temporal: Onda_n = 90 + (n-1) × 30 segundos
    // Primeira onda: t = 90s, intervalo: 30s

    // Configurar timing matemático preciso
    WaveInterval = MINION_SPAWN_INTERVAL; // 30 segundos entre ondas
    float FirstWaveDelay = FIRST_WAVE_TIME; // 90 segundos para primeira onda

    // Configurar progressão de minions conforme documentação
    // Progressão de minions: Melee = 3 + floor(n/20), Ranged = 3 + floor(n/25)
    WaveConfigurations.Empty();

    // Gerar configurações para 50 ondas com progressão matemática
    for (int32 WaveNumber = 1; WaveNumber <= MaxWaves; ++WaveNumber)
    {
        FWaveConfiguration WaveConfig;
        WaveConfig.WaveNumber = WaveNumber;
        WaveConfig.WaveType = EWaveType::Normal;

        // CORRIGIDO: Aplicar fórmulas matemáticas da documentação
        int32 MeleeCount = 3 + FMath::FloorToInt(WaveNumber / 20.0f);
        int32 RangedCount = 3 + FMath::FloorToInt(WaveNumber / 25.0f);

        // Minions de cerco: 1 unidade a cada 2-3 ondas
        int32 SiegeCount = (WaveNumber % 3 == 0) ? 1 : 0;

        // Configurar composição da onda
        WaveConfig.MinionComposition.Add(EMinionType::Melee, MeleeCount);
        WaveConfig.MinionComposition.Add(EMinionType::Ranged, RangedCount);
        if (SiegeCount > 0)
        {
            WaveConfig.MinionComposition.Add(EMinionType::Siege, SiegeCount);
        }

        // Calcular timing da onda: Onda_n = 90 + (n-1) × 30
        WaveConfig.SpawnTime = FirstWaveDelay + (WaveNumber - 1) * WaveInterval;

        WaveConfigurations.Add(WaveConfig);
    }

    // Configurar sistema de spawn automático
    bAutoStartWaves = true;
    bWaveSystemActive = true;
    bWaveSystemPaused = false;

    UE_LOG(LogMinionWaveManager, Log, TEXT("Sistema de ondas configurado: %d ondas, primeira em %.1fs, intervalo %.1fs"),
           MaxWaves, FirstWaveDelay, WaveInterval);

    return true;
}

bool AMinionWaveManager::ConfigurePathfinding()
{
    SCOPE_CYCLE_COUNTER(STAT_MinionWaveManager_ConfigurePathfinding);

    UE_LOG(LogMinionPathfinding, Log, TEXT("Configurando pathfinding A* conforme mapaimplementacao.md"));

    // CORRIGIDO: Implementar pathfinding A* com 12 waypoints por lane
    // Distâncias: 800-1200 UU, recálculo a cada 2 segundos

    // Configurar algoritmo A* como padrão
    PathfindingAlgorithm = EPathfindingAlgorithm::AStar;

    // Configurar parâmetros conforme documentação
    GridCellSize = PATHFINDING_GRID_SIZE; // 100 UU por célula
    MaxPathfindingIterations = ASTAR_MAX_ITERATIONS; // 1000 iterações máximas
    PathUpdateInterval = PATHFINDING_UPDATE_INTERVAL; // 2 segundos

    // Configurar sistema de waypoints por lane
    LaneWaypoints.Empty();

    // Gerar 12 waypoints por lane com distâncias 800-1200 UU
    for (int32 LaneIndex = 0; LaneIndex < 3; ++LaneIndex) // 3 lanes: Top, Middle, Bottom
    {
        TArray<FVector> Waypoints;
        ELaneType LaneType = static_cast<ELaneType>(LaneIndex);

        // Calcular posições dos waypoints baseado na geometria das lanes
        FVector LaneStart, LaneEnd;
        GetLaneStartAndEndPositions(LaneType, LaneStart, LaneEnd);

        // Gerar 12 waypoints com distâncias variáveis entre 800-1200 UU
        for (int32 WaypointIndex = 0; WaypointIndex < WAYPOINTS_PER_LANE; ++WaypointIndex)
        {
            float Alpha = static_cast<float>(WaypointIndex) / static_cast<float>(WAYPOINTS_PER_LANE - 1);

            // Interpolar posição base
            FVector BasePosition = FMath::Lerp(LaneStart, LaneEnd, Alpha);

            // Adicionar variação na distância (800-1200 UU)
            float DistanceVariation = FMath::RandRange(WAYPOINT_MIN_DISTANCE, WAYPOINT_MAX_DISTANCE);
            FVector Direction = (LaneEnd - LaneStart).GetSafeNormal();
            FVector WaypointPosition = BasePosition + Direction * (DistanceVariation * 0.1f); // Ajuste fino

            Waypoints.Add(WaypointPosition);
        }

        LaneWaypoints.Add(LaneType, Waypoints);

        UE_LOG(LogMinionPathfinding, Log, TEXT("Lane %d configurada com %d waypoints"),
               LaneIndex, Waypoints.Num());
    }

    // Configurar sistema de cache de pathfinding
    MinionPathCache.Empty();
    WalkabilityCache.Empty();

    // Configurar função de custo A*: g(n) + h(n) onde h = distância euclidiana
    bUseDynamicPathfinding = true;
    bAvoidOtherMinions = true;

    // Configurar prioridades de alvo conforme documentação
    // Prioridade de alvo: Torre > Minion > Campeão
    TargetPriorities.Empty();
    TargetPriorities.Add(TEXT("Tower"), 100);
    TargetPriorities.Add(TEXT("Minion"), 50);
    TargetPriorities.Add(TEXT("Champion"), 25);

    // Inicializar grid de navegação
    InitializeNavigationGrid();

    UE_LOG(LogMinionPathfinding, Log, TEXT("Pathfinding A* configurado: %d lanes, %d waypoints/lane, recálculo %.1fs"),
           LaneWaypoints.Num(), WAYPOINTS_PER_LANE, PathUpdateInterval);

    return true;
}

bool AMinionWaveManager::ConfigureRewardSystem()
{
    SCOPE_CYCLE_COUNTER(STAT_MinionWaveManager_ConfigureRewardSystem);

    UE_LOG(LogMinionWaves, Log, TEXT("Configurando sistema de recompensas conforme mapaimplementacao.md"));

    // CORRIGIDO: Implementar sistema de recompensas escaláveis conforme documentação
    // Ouro Melee: 20 + (1 × número_da_onda)
    // Ouro Ranged: 25 + (1.5 × número_da_onda)
    // XP: 30-500 + (2-25 × número_da_onda)
    // Raio de compartilhamento: 1200 UU

    // Configurar fórmulas de recompensa por tipo de minion
    RewardFormulas.Empty();

    // Configurar recompensas para Melee
    FRewardFormula MeleeReward;
    MeleeReward.MinionType = EMinionType::Melee;
    MeleeReward.BaseGold = 20.0f;
    MeleeReward.GoldPerWave = 1.0f;
    MeleeReward.BaseXP = 30.0f;
    MeleeReward.XPPerWave = 2.0f;
    MeleeReward.MaxXP = 500.0f;
    RewardFormulas.Add(EMinionType::Melee, MeleeReward);

    // Configurar recompensas para Ranged
    FRewardFormula RangedReward;
    RangedReward.MinionType = EMinionType::Ranged;
    RangedReward.BaseGold = 25.0f;
    RangedReward.GoldPerWave = 1.5f;
    RangedReward.BaseXP = 35.0f;
    RangedReward.XPPerWave = 2.5f;
    RangedReward.MaxXP = 500.0f;
    RewardFormulas.Add(EMinionType::Ranged, RangedReward);

    // Configurar recompensas para Siege (Cerco)
    FRewardFormula SiegeReward;
    SiegeReward.MinionType = EMinionType::Siege;
    SiegeReward.BaseGold = 50.0f;
    SiegeReward.GoldPerWave = 3.0f;
    SiegeReward.BaseXP = 100.0f;
    SiegeReward.XPPerWave = 10.0f;
    SiegeReward.MaxXP = 1000.0f;
    RewardFormulas.Add(EMinionType::Siege, SiegeReward);

    // Configurar outros tipos de minion com escalabilidade
    FRewardFormula TankReward;
    TankReward.MinionType = EMinionType::Tank;
    TankReward.BaseGold = 40.0f;
    TankReward.GoldPerWave = 2.0f;
    TankReward.BaseXP = 60.0f;
    TankReward.XPPerWave = 5.0f;
    TankReward.MaxXP = 750.0f;
    RewardFormulas.Add(EMinionType::Tank, TankReward);

    FRewardFormula CasterReward;
    CasterReward.MinionType = EMinionType::Caster;
    CasterReward.BaseGold = 30.0f;
    CasterReward.GoldPerWave = 1.8f;
    CasterReward.BaseXP = 45.0f;
    CasterReward.XPPerWave = 3.0f;
    CasterReward.MaxXP = 600.0f;
    RewardFormulas.Add(EMinionType::Caster, CasterReward);

    // Configurar raio de compartilhamento conforme documentação
    RewardSharingRadius = REWARD_SHARING_RADIUS; // 1200 UU

    // Configurar sistema de distribuição de recompensas
    bEnableRewardSharing = true;
    bScaleRewardsByWave = true;
    bCapXPRewards = true;

    // Configurar multiplicadores especiais
    EliteWaveGoldMultiplier = 1.5f;
    BossWaveGoldMultiplier = 2.0f;
    SiegeWaveGoldMultiplier = 1.25f;

    EliteWaveXPMultiplier = 1.3f;
    BossWaveXPMultiplier = 1.8f;
    SiegeWaveXPMultiplier = 1.15f;

    UE_LOG(LogMinionWaves, Log, TEXT("Sistema de recompensas configurado:"));
    UE_LOG(LogMinionWaves, Log, TEXT("- Melee: %.0f + %.1f×onda ouro, %.0f + %.1f×onda XP"),
           MeleeReward.BaseGold, MeleeReward.GoldPerWave, MeleeReward.BaseXP, MeleeReward.XPPerWave);
    UE_LOG(LogMinionWaves, Log, TEXT("- Ranged: %.0f + %.1f×onda ouro, %.0f + %.1f×onda XP"),
           RangedReward.BaseGold, RangedReward.GoldPerWave, RangedReward.BaseXP, RangedReward.XPPerWave);
    UE_LOG(LogMinionWaves, Log, TEXT("- Raio compartilhamento: %.0f UU"), RewardSharingRadius);

    return true;
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES AUXILIARES =====

void AMinionWaveManager::GetLaneStartAndEndPositions(ELaneType LaneType, FVector& StartPos, FVector& EndPos)
{
    // CORRIGIDO: Implementar geometria matemática precisa das lanes conforme documentação
    // Lane Superior: Y = -0.577X + 6928 (±300 UU largura)
    // Lane Central: Y = 0 (±400 UU largura)
    // Lane Inferior: Y = 0.577X - 6928 (±300 UU largura)
    // Sistema de coordenadas UE5.6: 1 UU = 1 cm, mapa 16000×16000 UU

    const float MapHalfSize = 8000.0f; // Mapa 16000x16000, centro em (0,0)

    switch (LaneType)
    {
        case ELaneType::Top:
            // Lane Superior: Y = -0.577X + 6928
            StartPos = FVector(-MapHalfSize, -0.577f * (-MapHalfSize) + 6928.0f, 0.0f);
            EndPos = FVector(MapHalfSize, -0.577f * MapHalfSize + 6928.0f, 0.0f);
            break;

        case ELaneType::Middle:
            // Lane Central: Y = 0
            StartPos = FVector(-MapHalfSize, 0.0f, 0.0f);
            EndPos = FVector(MapHalfSize, 0.0f, 0.0f);
            break;

        case ELaneType::Bottom:
            // Lane Inferior: Y = 0.577X - 6928
            StartPos = FVector(-MapHalfSize, 0.577f * (-MapHalfSize) - 6928.0f, 0.0f);
            EndPos = FVector(MapHalfSize, 0.577f * MapHalfSize - 6928.0f, 0.0f);
            break;

        default:
            StartPos = FVector::ZeroVector;
            EndPos = FVector::ZeroVector;
            UE_LOG(LogMinionPathfinding, Warning, TEXT("Tipo de lane inválido: %d"), (int32)LaneType);
            break;
    }

    UE_LOG(LogMinionPathfinding, VeryVerbose, TEXT("Lane %d: Start(%.1f, %.1f, %.1f) End(%.1f, %.1f, %.1f)"),
           (int32)LaneType, StartPos.X, StartPos.Y, StartPos.Z, EndPos.X, EndPos.Y, EndPos.Z);
}

void AMinionWaveManager::InitializeNavigationGrid()
{
    UE_LOG(LogMinionPathfinding, Log, TEXT("Inicializando grid de navegação A*"));

    // CORRIGIDO: Implementar grid de navegação otimizado para UE 5.6
    const float MapSize = 16000.0f; // Mapa total conforme documentação
    const float CellSize = GridCellSize; // 100 UU por célula
    const int32 GridWidth = FMath::CeilToInt(MapSize / CellSize);
    const int32 GridHeight = GridWidth;

    // Limpar grid existente
    NavigationGrid.Nodes.Empty();
    NavigationGrid.Width = GridWidth;
    NavigationGrid.Height = GridHeight;
    NavigationGrid.CellSize = CellSize;
    NavigationGrid.Origin = FVector(-MapSize * 0.5f, -MapSize * 0.5f, 0.0f);

    // Usar ParallelFor do UE 5.6 para otimização
    NavigationGrid.Nodes.SetNum(GridWidth * GridHeight);

    ParallelFor(GridHeight, [this, GridWidth, CellSize](int32 Y)
    {
        for (int32 X = 0; X < GridWidth; ++X)
        {
            int32 Index = Y * GridWidth + X;
            FPathfindingNode& Node = NavigationGrid.Nodes[Index];

            Node.GridX = X;
            Node.GridY = Y;
            Node.WorldPosition = NavigationGrid.Origin + FVector(X * CellSize, Y * CellSize, 0.0f);
            Node.bIsWalkable = true; // Por padrão, todas as células são caminháveis
            Node.MovementCost = 1.0f;
            Node.GCost = 0.0f;
            Node.HCost = 0.0f;
            Node.Parent = nullptr;
        }
    });

    UE_LOG(LogMinionPathfinding, Log, TEXT("Grid de navegação inicializado: %dx%d células, %.1f UU/célula"),
           GridWidth, GridHeight, CellSize);
}

float AMinionWaveManager::CalculateReward(EMinionType MinionType, int32 WaveNumber, bool bIsGold)
{
    // CORRIGIDO: Implementar fórmulas matemáticas precisas conforme documentação
    const FRewardFormula* Formula = RewardFormulas.Find(MinionType);
    if (!Formula)
    {
        UE_LOG(LogMinionWaves, Warning, TEXT("Fórmula de recompensa não encontrada para tipo %d"), (int32)MinionType);
        return 0.0f;
    }

    float Reward = 0.0f;

    if (bIsGold)
    {
        // Ouro Melee: 20 + (1 × número_da_onda)
        // Ouro Ranged: 25 + (1.5 × número_da_onda)
        Reward = Formula->BaseGold + (Formula->GoldPerWave * WaveNumber);
    }
    else
    {
        // XP: 30-500 + (2-25 × número_da_onda)
        Reward = Formula->BaseXP + (Formula->XPPerWave * WaveNumber);

        // Aplicar cap de XP se habilitado
        if (bCapXPRewards && Reward > Formula->MaxXP)
        {
            Reward = Formula->MaxXP;
        }
    }

    return FMath::Max(0.0f, Reward);
}

void AMinionWaveManager::DistributeRewards(const FVector& Position, EMinionType MinionType, int32 WaveNumber)
{
    // CORRIGIDO: Implementar distribuição de recompensas com raio de compartilhamento
    // Raio de compartilhamento: 1200 UU conforme documentação

    if (!bEnableRewardSharing)
    {
        return;
    }

    float GoldReward = CalculateReward(MinionType, WaveNumber, true);
    float XPReward = CalculateReward(MinionType, WaveNumber, false);

    // Aplicar multiplicadores especiais baseados no tipo de onda
    // (Implementação futura para ondas especiais)

    UE_LOG(LogMinionWaves, VeryVerbose, TEXT("Distribuindo recompensas: %.1f ouro, %.1f XP em raio %.1f UU"),
           GoldReward, XPReward, RewardSharingRadius);

    // TODO: Implementar busca por jogadores/campeões no raio e distribuir recompensas
    // Por enquanto, apenas log das recompensas calculadas
}
