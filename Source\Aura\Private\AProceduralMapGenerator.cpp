// AProceduralMapGenerator.cpp
// Implementação do sistema de PCG (Procedural Content Generation) para geração automática do mapa
// Integra com UE5.6 PCG Framework e todos os managers do sistema
// Escala: 1 UU = 1 cm

#include "AProceduralMapGenerator.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "DrawDebugHelpers.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "Misc/FileHelper.h"
#include "HAL/PlatformFilemanager.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGNode.h"
#include "Elements/PCGSpawnActor.h"
#include "Elements/PCGStaticMeshSpawner.h"
#include "Data/PCGPointData.h"
#include "Data/PCGSpatialData.h"
#include "Helpers/PCGHelpers.h"
#include "TimerManager.h"
#include "Engine/DirectionalLight.h"
#include "Engine/PointLight.h"
#include "NavigationSystem.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/PointLightComponent.h"
#include "Landscape.h"
#include "EngineUtils.h"
#include "Engine/StaticMeshActor.h"

// CORRIGIDO: Includes modernos UE 5.6 para PCG
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "HAL/IConsoleManager.h"
#include "Misc/ScopeLock.h"
#include "UObject/UObjectGlobals.h"
#include "Stats/Stats.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"

// CORRIGIDO: PCG Framework moderno UE 5.6
#include "PCGSubsystem.h"
#include "PCGSettings.h"
#include "PCGContext.h"
#include "PCGData.h"
#include "PCGPin.h"
#include "PCGElement.h"
#include "PCGGraphExecutor.h"

// CORRIGIDO: PCG Elements modernos UE 5.6
#include "Elements/PCGSurfaceSampler.h"
#include "Elements/PCGVolumeToPoint.h"
#include "Elements/PCGAttributeFilter.h"
#include "Elements/PCGDensityFilter.h"
#include "Elements/PCGTransformPoints.h"
#include "Elements/PCGProjectionElement.h"
#include "Elements/PCGBoundsModifier.h"
#include "Elements/PCGCopyPoints.h"

// CORRIGIDO: World Partition integration UE 5.6
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"

// CORRIGIDO: Async e Threading UE 5.6
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"
#include "HAL/ThreadSafeBool.h"

#if WITH_EDITOR
#include "Editor.h"
#include "EditorSubsystem.h"
#include "PCGEditorModule.h"
#endif

// CORRIGIDO: Categorias de logging específicas UE 5.6
DEFINE_LOG_CATEGORY_STATIC(LogProceduralMapGenerator, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogProceduralMapGeneration, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogProceduralMapPCG, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogProceduralMapTerrain, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogProceduralMapVegetation, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogProceduralMapStructures, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogProceduralMapLighting, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogProceduralMapPerformance, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogProceduralMapMemory, Log, All);

// CORRIGIDO: Stats system expandido UE 5.6
DECLARE_STATS_GROUP(TEXT("Procedural Map Generator"), STATGROUP_ProceduralMapGenerator, STATCAT_Advanced);
DECLARE_CYCLE_STAT(TEXT("Map Generation"), STAT_ProceduralMapGenerator_Generation, STATGROUP_ProceduralMapGenerator);
DECLARE_CYCLE_STAT(TEXT("Terrain Generation"), STAT_ProceduralMapGenerator_Terrain, STATGROUP_ProceduralMapGenerator);
DECLARE_CYCLE_STAT(TEXT("Vegetation Generation"), STAT_ProceduralMapGenerator_Vegetation, STATGROUP_ProceduralMapGenerator);
DECLARE_CYCLE_STAT(TEXT("Structure Generation"), STAT_ProceduralMapGenerator_Structures, STATGROUP_ProceduralMapGenerator);
DECLARE_CYCLE_STAT(TEXT("Lighting Generation"), STAT_ProceduralMapGenerator_Lighting, STATGROUP_ProceduralMapGenerator);
DECLARE_CYCLE_STAT(TEXT("PCG Execution"), STAT_ProceduralMapGenerator_PCGExecution, STATGROUP_ProceduralMapGenerator);
DECLARE_CYCLE_STAT(TEXT("Async Operations"), STAT_ProceduralMapGenerator_AsyncOps, STATGROUP_ProceduralMapGenerator);
DECLARE_DWORD_COUNTER_STAT(TEXT("Generated Objects"), STAT_ProceduralMapGenerator_GeneratedObjects, STATGROUP_ProceduralMapGenerator);
DECLARE_DWORD_COUNTER_STAT(TEXT("Active PCG Components"), STAT_ProceduralMapGenerator_ActivePCGComponents, STATGROUP_ProceduralMapGenerator);
DECLARE_FLOAT_COUNTER_STAT(TEXT("Memory Usage MB"), STAT_ProceduralMapGenerator_MemoryMB, STATGROUP_ProceduralMapGenerator);
DECLARE_FLOAT_COUNTER_STAT(TEXT("Generation Time MS"), STAT_ProceduralMapGenerator_GenerationTimeMS, STATGROUP_ProceduralMapGenerator);

// CORRIGIDO: CVars modernos UE 5.6 para controle de geração procedural
static TAutoConsoleVariable<int32> CVarProceduralMapGenerationEnabled(
    TEXT("pcg.ProceduralMap.Enabled"),
    1,
    TEXT("Enable/disable Procedural Map Generation system (0=disabled, 1=enabled)"),
    ECVF_Default
);

static TAutoConsoleVariable<float> CVarProceduralMapGenerationScale(
    TEXT("pcg.ProceduralMap.GenerationScale"),
    1.0f,
    TEXT("Scale factor for procedural map generation (0.1-10.0)"),
    ECVF_Default
);

static TAutoConsoleVariable<int32> CVarProceduralMapMaxConcurrentTasks(
    TEXT("pcg.ProceduralMap.MaxConcurrentTasks"),
    8,
    TEXT("Maximum number of concurrent generation tasks"),
    ECVF_Default
);

static TAutoConsoleVariable<float> CVarProceduralMapMemoryBudgetMB(
    TEXT("pcg.ProceduralMap.MemoryBudgetMB"),
    1024.0f,
    TEXT("Memory budget for procedural map generation in MB"),
    ECVF_Default
);

static TAutoConsoleVariable<int32> CVarProceduralMapDebugVisualization(
    TEXT("pcg.ProceduralMap.DebugVisualization"),
    0,
    TEXT("Enable debug visualization for procedural map generation (0=disabled, 1=enabled)"),
    ECVF_Default
);

static TAutoConsoleVariable<int32> CVarProceduralMapAsyncGeneration(
    TEXT("pcg.ProceduralMap.AsyncGeneration"),
    1,
    TEXT("Enable async generation for better performance (0=disabled, 1=enabled)"),
    ECVF_Default
);

static TAutoConsoleVariable<float> CVarProceduralMapUpdateInterval(
    TEXT("pcg.ProceduralMap.UpdateInterval"),
    0.1f,
    TEXT("Update interval for procedural map generation in seconds"),
    ECVF_Default
);

// ===== CONSTRUTOR E INICIALIZAÇÃO =====

AProceduralMapGenerator::AProceduralMapGenerator()
{
    // CORRIGIDO: Configuração moderna de tick UE 5.6
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = false;
    PrimaryActorTick.TickGroup = TG_PostUpdateWork;
    PrimaryActorTick.bTickEvenWhenPaused = false;
    PrimaryActorTick.bAllowTickOnDedicatedServer = true;
    PrimaryActorTick.TickInterval = CVarProceduralMapUpdateInterval.GetValueOnGameThread();

    // CORRIGIDO: Configurar componente raiz com validação
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    if (IsValid(RootSceneComponent))
    {
        RootComponent = RootSceneComponent;
    }

    // CORRIGIDO: Configurar componente PCG moderno UE 5.6
    PCGComponent = CreateDefaultSubobject<UPCGComponent>(TEXT("PCGComponent"));
    if (IsValid(PCGComponent))
    {
        PCGComponent->SetupAttachment(RootComponent);
        PCGComponent->bActivated = false; // Start deactivated for manual control
        PCGComponent->bGenerated = false;
        PCGComponent->GenerationTrigger = EPCGComponentGenerationTrigger::GenerateOnDemand;
    }

    // CORRIGIDO: Configurar visualização de bounds com material
    BoundsVisualization = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("BoundsVisualization"));
    if (IsValid(BoundsVisualization))
    {
        BoundsVisualization->SetupAttachment(RootComponent);
        BoundsVisualization->SetVisibility(false);
        BoundsVisualization->SetCollisionEnabled(ECollisionEnabled::NoCollision);
        BoundsVisualization->SetCastShadow(false);
        BoundsVisualization->bUseAsOccluder = false;
    }

    // CORRIGIDO: Configuração production-ready com valores otimizados
    GenerationConfig = FPCGGenerationConfig();
    GenerationConfig.GenerationSeed = FMath::Rand();
    GenerationConfig.bEnableAsyncGeneration = CVarProceduralMapAsyncGeneration.GetValueOnGameThread() > 0;
    GenerationConfig.MaxConcurrentTasks = CVarProceduralMapMaxConcurrentTasks.GetValueOnGameThread();
    GenerationConfig.MemoryBudgetMB = CVarProceduralMapMemoryBudgetMB.GetValueOnGameThread();
    GenerationConfig.GenerationScale = CVarProceduralMapGenerationScale.GetValueOnGameThread();
    GenerationConfig.bEnableDebugVisualization = CVarProceduralMapDebugVisualization.GetValueOnGameThread() > 0;

    // CORRIGIDO: Map bounds otimizados para performance
    MapBounds = FVector(5000000.0f, 5000000.0f, 200000.0f); // 50km x 50km x 2km
    MapCenter = FVector::ZeroVector;

    // CORRIGIDO: Estados com inicialização robusta
    bIsGenerating = false;
    bIsPaused = false;
    bWorldPartitionEnabled = false;
    bPCGSystemEnabled = false;
    bAsyncGenerationEnabled = GenerationConfig.bEnableAsyncGeneration;

    // CORRIGIDO: Timers e contadores com valores iniciais seguros
    LastUpdateTime = 0.0f;
    LastPerformanceUpdate = 0.0f;
    LastMemoryCheck = 0.0f;
    TotalObjectsGenerated = 0;
    CurrentMemoryUsageMB = 0.0f;

    GenerationProgress = FPCGGenerationProgress();
    GenerationProgress.TotalSteps = 0;
    GenerationProgress.CompletedSteps = 0;
    GenerationProgress.CurrentPhase = EPCGGenerationPhase::Initialization;

    // CORRIGIDO: Inicializar random stream com seed robusto
    const int32 FinalSeed = GenerationConfig.GenerationSeed != 0 ? GenerationConfig.GenerationSeed : FMath::Rand();
    RandomStream.Initialize(FinalSeed);

    // CORRIGIDO: Inicialização de containers com Reserve() para performance
    GeneratedActors.Reserve(1000);
    ActivePCGComponents.Reserve(100);
    PendingGenerationTasks.Reserve(50);

    UE_LOG(LogProceduralMapGenerator, Log, TEXT("Constructor completed - Seed: %d, Async: %s, Memory budget: %.1fMB"),
           FinalSeed, bAsyncGenerationEnabled ? TEXT("Enabled") : TEXT("Disabled"), GenerationConfig.MemoryBudgetMB);
}

void AProceduralMapGenerator::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogProceduralMapGenerator, Log, TEXT("BeginPlay started"));

    // CORRIGIDO: Verificar se sistema está habilitado via CVar
    if (CVarProceduralMapGenerationEnabled.GetValueOnGameThread() == 0)
    {
        UE_LOG(LogProceduralMapGenerator, Warning, TEXT("Procedural Map Generation disabled via cvar pcg.ProceduralMap.Enabled"));
        return;
    }

    // CORRIGIDO: Detectar recursos modernos UE 5.6
    if (UWorld* World = GetWorld())
    {
        // Detectar World Partition
        if (UWorldPartition* WorldPartition = World->GetWorldPartition())
        {
            bWorldPartitionEnabled = true;
            UE_LOG(LogProceduralMapGenerator, Log, TEXT("World Partition detected and enabled"));
        }

        // Detectar PCG Subsystem
        if (UPCGSubsystem* PCGSubsystem = World->GetSubsystem<UPCGSubsystem>())
        {
            bPCGSystemEnabled = true;
            UE_LOG(LogProceduralMapPCG, Log, TEXT("PCG Subsystem detected and enabled"));
        }
        else
        {
            UE_LOG(LogProceduralMapPCG, Error, TEXT("PCG Subsystem not available - cannot proceed with generation"));
            return;
        }
    }

    // CORRIGIDO: Validar configuração inicial com profiling
    {
        SCOPE_CYCLE_COUNTER(STAT_ProceduralMapGenerator_Generation);
        TRACE_CPUPROFILER_EVENT_SCOPE(AProceduralMapGenerator::ValidateGenerationSetup);

        if (!ValidateGenerationSetup())
        {
            UE_LOG(LogProceduralMapGenerator, Error, TEXT("Failed initial validation"));
            return;
        }
    }

    // Inicializar managers
    InitializeManagers();

    // Configurar PCG
    SetupPCGGraph();

    // Iniciar geração automática se habilitada
    if (GenerationConfig.bEnableAutoGeneration)
    {
        UE_LOG(LogProceduralMapGeneration, Log, TEXT("Starting automatic generation"));

        SCOPE_CYCLE_COUNTER(STAT_ProceduralMapGenerator_Generation);
        TRACE_CPUPROFILER_EVENT_SCOPE(AProceduralMapGenerator::StartGeneration);

        StartGeneration();
    }

    UE_LOG(LogProceduralMapGenerator, Log, TEXT("BeginPlay completed - WorldPartition: %s, PCG: %s, Async: %s"),
           bWorldPartitionEnabled ? TEXT("Enabled") : TEXT("Disabled"),
           bPCGSystemEnabled ? TEXT("Enabled") : TEXT("Disabled"),
           bAsyncGenerationEnabled ? TEXT("Enabled") : TEXT("Disabled"));
}

void AProceduralMapGenerator::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // CORRIGIDO: Logging seguro com enum string
    const FString EndPlayReasonStr = UEnum::GetValueAsString(EndPlayReason);
    UE_LOG(LogProceduralMapGenerator, Log, TEXT("EndPlay started - Reason: %s"), *EndPlayReasonStr);

    // CORRIGIDO: Profiling da operação de shutdown
    SCOPE_CYCLE_COUNTER(STAT_ProceduralMapGenerator_Generation);
    TRACE_CPUPROFILER_EVENT_SCOPE(AProceduralMapGenerator::EndPlay);

    // CORRIGIDO: Parar geração com timeout e logging detalhado
    if (bIsGenerating)
    {
        const double StartTime = FPlatformTime::Seconds();
        StopGeneration();
        const double StopTime = FPlatformTime::Seconds() - StartTime;
        UE_LOG(LogProceduralMapGeneration, Log, TEXT("Stopped generation in %.3fs"), StopTime);
    }

    // CORRIGIDO: Limpar timers com validação robusta
    if (UWorld* World = GetWorld())
    {
        FTimerManager& TimerManager = World->GetTimerManager();
        TimerManager.ClearTimer(GenerationTimerHandle);
        TimerManager.ClearTimer(ProgressUpdateTimerHandle);
        TimerManager.ClearTimer(ValidationTimerHandle);
        UE_LOG(LogProceduralMapGenerator, Log, TEXT("Cleared all timers"));
    }

    // CORRIGIDO: Limpeza com contagem e logging
    const int32 GeneratedActorsCount = GeneratedActors.Num();
    const int32 ActivePCGComponentsCount = ActivePCGComponents.Num();
    const int32 PendingTasksCount = PendingGenerationTasks.Num();

    // CORRIGIDO: Cleanup com profiling
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(AProceduralMapGenerator::CleanupPCGData);
        CleanupPCGData();
    }

    // CORRIGIDO: Reset de estados
    bWorldPartitionEnabled = false;
    bPCGSystemEnabled = false;
    bAsyncGenerationEnabled = false;
    bIsGenerating = false;
    bIsPaused = false;
    CurrentMemoryUsageMB = 0.0f;
    TotalObjectsGenerated = 0;

    Super::EndPlay(EndPlayReason);

    UE_LOG(LogProceduralMapGenerator, Log, TEXT("EndPlay completed - Cleaned: %d generated actors, %d PCG components, %d pending tasks"),
           GeneratedActorsCount, ActivePCGComponentsCount, PendingTasksCount);
}

void AProceduralMapGenerator::Tick(float DeltaTime)
{
    // CORRIGIDO: Profiling completo da função Tick
    SCOPE_CYCLE_COUNTER(STAT_ProceduralMapGenerator_Generation);
    TRACE_CPUPROFILER_EVENT_SCOPE(AProceduralMapGenerator::Tick);

    Super::Tick(DeltaTime);

    // CORRIGIDO: Verificação robusta de estados e CVars
    if (!bPCGSystemEnabled || CVarProceduralMapGenerationEnabled.GetValueOnGameThread() == 0)
    {
        return;
    }

    // CORRIGIDO: Bounds checking para DeltaTime
    const float ClampedDeltaTime = FMath::Clamp(DeltaTime, 0.0f, 1.0f);

    // CORRIGIDO: Update com intervalo dinâmico baseado em CVar
    const float UpdateInterval = CVarProceduralMapUpdateInterval.GetValueOnGameThread();
    const float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - LastUpdateTime < UpdateInterval)
    {
        return;
    }
    LastUpdateTime = CurrentTime;

    // CORRIGIDO: Atualizar progresso se estiver gerando
    if (bIsGenerating && !bIsPaused)
    {
        // CORRIGIDO: Calcular tempo estimado com profiling
        {
            TRACE_CPUPROFILER_EVENT_SCOPE(AProceduralMapGenerator::CalculateEstimatedTime);
            CalculateEstimatedTime();
        }

        // CORRIGIDO: Debug visualization com CVar check
        if (CVarProceduralMapDebugVisualization.GetValueOnGameThread() > 0)
        {
            TRACE_CPUPROFILER_EVENT_SCOPE(AProceduralMapGenerator::DrawDebugBounds);
            DrawDebugBounds();
        }

        // CORRIGIDO: Update stats expandido com performance monitoring
        if (CurrentTime - LastPerformanceUpdate >= 1.0f)
        {
            const int32 GeneratedObjectsCount = GeneratedActors.Num();
            const int32 ActivePCGComponentsCount = ActivePCGComponents.Num();
            const float MemoryUsage = CalculateMemoryUsage();

            SET_DWORD_STAT(STAT_ProceduralMapGenerator_GeneratedObjects, GeneratedObjectsCount);
            SET_DWORD_STAT(STAT_ProceduralMapGenerator_ActivePCGComponents, ActivePCGComponentsCount);
            SET_FLOAT_STAT(STAT_ProceduralMapGenerator_MemoryMB, MemoryUsage);

            CurrentMemoryUsageMB = MemoryUsage;
            TotalObjectsGenerated = GeneratedObjectsCount;
            LastPerformanceUpdate = CurrentTime;
        }

        // CORRIGIDO: Memory check com intervalo otimizado
        if (CurrentTime - LastMemoryCheck >= 5.0f)
        {
            CheckMemoryUsage();
            LastMemoryCheck = CurrentTime;
        }
    }
}

// ===== FUNÇÕES PRINCIPAIS =====

void AProceduralMapGenerator::StartGeneration()
{
    // CORRIGIDO: Profiling da função de geração
    SCOPE_CYCLE_COUNTER(STAT_ProceduralMapGenerator_Generation);
    TRACE_CPUPROFILER_EVENT_SCOPE(AProceduralMapGenerator::StartGeneration);

    if (bIsGenerating)
    {
        UE_LOG(LogProceduralMapGeneration, Warning, TEXT("Generation already in progress"));
        return;
    }

    // CORRIGIDO: Verificar se sistema está habilitado
    if (!bPCGSystemEnabled)
    {
        UE_LOG(LogProceduralMapGeneration, Error, TEXT("PCG System not enabled - cannot start generation"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Iniciando geração procedural"));

    // Validar configuração
    if (!ValidateGenerationSetup())
    {
        OnGenerationFailed.Broadcast(TEXT("Falha na validação da configuração"));
        return;
    }

    // Resetar estado
    ResetGenerationState();

    // Configurar estado inicial
    bIsGenerating = true;
    bIsPaused = false;
    GenerationProgress.StartTime = FDateTime::Now();
    GenerationProgress.CurrentPhase = EPCGGenerationPhase::Initialization;
    GenerationProgress.CurrentOperation = TEXT("Inicializando geração");

    // Inicializar random stream
    RandomStream.Initialize(GenerationConfig.GenerationSeed);

    // Habilitar tick
    SetActorTickEnabled(true);

    // Iniciar primeira fase
    ExecuteGenerationPhase(EPCGGenerationPhase::Initialization);

    // Configurar timer de progresso
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            ProgressUpdateTimerHandle,
            [this]()
            {
                OnProgressUpdated.Broadcast(TEXT("Geração Geral"), GenerationProgress.OverallProgress, EstimatedTimeRemaining);
            },
            0.1f, // Atualizar a cada 100ms
            true
        );
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Geração iniciada com sucesso"));
}

void AProceduralMapGenerator::StopGeneration()
{
    if (!bIsGenerating)
    {
        UE_LOG(LogTemp, Warning, TEXT("AProceduralMapGenerator: Nenhuma geração em andamento"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Parando geração"));

    bIsGenerating = false;
    bIsPaused = false;

    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(GenerationTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(ProgressUpdateTimerHandle);
    }

    // Desabilitar tick
    SetActorTickEnabled(false);

    // Limpar visualização de debug
    ClearDebugVisualization();

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Geração parada"));
}

void AProceduralMapGenerator::PauseGeneration()
{
    if (!bIsGenerating || bIsPaused)
    {
        UE_LOG(LogTemp, Warning, TEXT("AProceduralMapGenerator: Não é possível pausar"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Pausando geração"));

    bIsPaused = true;
    GenerationProgress.CurrentOperation = TEXT("Pausado");

    // Pausar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().PauseTimer(GenerationTimerHandle);
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Geração pausada"));
}

void AProceduralMapGenerator::ResumeGeneration()
{
    if (!bIsGenerating || !bIsPaused)
    {
        UE_LOG(LogTemp, Warning, TEXT("AProceduralMapGenerator: Não é possível retomar"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Retomando geração"));

    bIsPaused = false;
    GenerationProgress.CurrentOperation = TEXT("Retomando geração");

    // Retomar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().UnPauseTimer(GenerationTimerHandle);
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Geração retomada"));
}

void AProceduralMapGenerator::RestartGeneration()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Reiniciando geração"));

    // Parar geração atual
    if (bIsGenerating)
    {
        StopGeneration();
    }

    // Limpar geração anterior
    ClearGeneration();

    // Aguardar um frame antes de reiniciar
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            StartGeneration();
        });
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Reinício programado"));
}

void AProceduralMapGenerator::ClearGeneration()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Limpando geração"));

    // Parar geração se estiver ativa
    if (bIsGenerating)
    {
        StopGeneration();
    }

    // Limpar atores gerados
    CleanupGeneratedActors();

    // Limpar dados PCG
    CleanupPCGData();

    // Resetar estado
    ResetGenerationState();

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Geração limpa"));
}

// ===== FUNÇÕES DE CONFIGURAÇÃO =====

void AProceduralMapGenerator::SetGenerationConfig(const FPCGGenerationConfig& NewConfig)
{
    GenerationConfig = NewConfig;
    RandomStream.Initialize(GenerationConfig.GenerationSeed);
    
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Configuração atualizada"));
    UE_LOG(LogTemp, Log, TEXT("  - Seed: %.0f"), GenerationConfig.GenerationSeed);
    UE_LOG(LogTemp, Log, TEXT("  - Max Iterations: %d"), GenerationConfig.MaxIterations);
    UE_LOG(LogTemp, Log, TEXT("  - Quality Threshold: %.2f"), GenerationConfig.QualityThreshold);
    UE_LOG(LogTemp, Log, TEXT("  - Multithreading: %s"), GenerationConfig.bUseMultithreading ? TEXT("Enabled") : TEXT("Disabled"));
}

FPCGGenerationConfig AProceduralMapGenerator::GetGenerationConfig() const
{
    return GenerationConfig;
}

void AProceduralMapGenerator::AddSpawnRule(const FPCGSpawnRule& NewRule)
{
    SpawnRules.Add(NewRule);
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Regra de spawn adicionada: %s"), *NewRule.RuleName);
}

void AProceduralMapGenerator::RemoveSpawnRule(const FString& RuleName)
{
    int32 RemovedCount = SpawnRules.RemoveAll([&RuleName](const FPCGSpawnRule& Rule)
    {
        return Rule.RuleName == RuleName;
    });
    
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: %d regra(s) removida(s): %s"), RemovedCount, *RuleName);
}

void AProceduralMapGenerator::ClearSpawnRules()
{
    int32 Count = SpawnRules.Num();
    SpawnRules.Empty();
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: %d regras de spawn removidas"), Count);
}

// ===== FUNÇÕES DE ESTADO =====

FPCGGenerationProgress AProceduralMapGenerator::GetGenerationProgress() const
{
    return GenerationProgress;
}

bool AProceduralMapGenerator::IsGenerating() const
{
    return bIsGenerating;
}

bool AProceduralMapGenerator::IsPaused() const
{
    return bIsPaused;
}

EPCGGenerationPhase AProceduralMapGenerator::GetCurrentPhase() const
{
    return GenerationProgress.CurrentPhase;
}

float AProceduralMapGenerator::GetOverallProgress() const
{
    return GenerationProgress.OverallProgress;
}

int32 AProceduralMapGenerator::GetGeneratedAssetCount() const
{
    return GenerationProgress.GeneratedAssets;
}

// ===== FUNÇÕES DE INTEGRAÇÃO =====

void AProceduralMapGenerator::InitializeManagers()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Inicializando managers"));

    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: World não encontrado"));
        return;
    }

    // Encontrar ou criar managers
    if (!LaneManager)
    {
        LaneManager = World->SpawnActor<ALaneManager>();
        if (LaneManager)
        {
            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: LaneManager criado"));
        }
    }

    if (!BaronManager)
    {
        BaronManager = World->SpawnActor<ABaronAuracronManager>();
        if (BaronManager)
        {
            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: BaronManager criado"));
        }
    }

    if (!DragonManager)
    {
        DragonManager = World->SpawnActor<ADragonPrismalManager>();
        if (DragonManager)
        {
            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: DragonManager criado"));
        }
    }

    if (!WallManager)
    {
        WallManager = World->SpawnActor<AWallCollisionManager>();
        if (WallManager)
        {
            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: WallManager criado"));
        }
    }

    if (!RiverManager)
    {
        RiverManager = World->SpawnActor<ARiverPrismalManager>();
        if (RiverManager)
        {
            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: RiverManager criado"));
        }
    }

    if (!MinionManager)
    {
        MinionManager = World->SpawnActor<AMinionWaveManager>();
        if (MinionManager)
        {
            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: MinionManager criado"));
        }
    }

    if (!MapManager)
    {
        MapManager = World->SpawnActor<AMapManager>();
        if (MapManager)
        {
            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: MapManager criado"));
        }
    }

    if (!GeometricValidator)
    {
        GeometricValidator = World->SpawnActor<AGeometricValidator>();
        if (GeometricValidator)
        {
            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: GeometricValidator criado"));
        }
    }

    // Validar referências
    ValidateManagerReferences();

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Inicialização de managers concluída"));
}

void AProceduralMapGenerator::ValidateManagerReferences()
{
    TArray<FString> MissingManagers;

    if (!LaneManager) MissingManagers.Add(TEXT("LaneManager"));
    if (!BaronManager) MissingManagers.Add(TEXT("BaronManager"));
    if (!DragonManager) MissingManagers.Add(TEXT("DragonManager"));
    if (!WallManager) MissingManagers.Add(TEXT("WallManager"));
    if (!RiverManager) MissingManagers.Add(TEXT("RiverManager"));
    if (!MinionManager) MissingManagers.Add(TEXT("MinionManager"));
    if (!MapManager) MissingManagers.Add(TEXT("MapManager"));
    if (!GeometricValidator) MissingManagers.Add(TEXT("GeometricValidator"));

    if (MissingManagers.Num() > 0)
    {
        FString MissingList = FString::Join(MissingManagers, TEXT(", "));
        UE_LOG(LogTemp, Warning, TEXT("AProceduralMapGenerator: Managers ausentes: %s"), *MissingList);
        ValidationErrors.Add(FString::Printf(TEXT("Managers ausentes: %s"), *MissingList));
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Todas as referências de managers são válidas"));
    }
}

void AProceduralMapGenerator::SynchronizeWithMapManager()
{
    if (!MapManager)
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: MapManager não encontrado para sincronização"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Sincronizando com MapManager"));

    // Sincronizar configurações
    // Implementar sincronização específica baseada na interface do MapManager
    if (!MapManager)
    {
        UE_LOG(LogTemp, Warning, TEXT("AProceduralMapGenerator: MapManager not available for synchronization"));
        return;
    }
    
    // Synchronize generation progress
    FMapGenerationProgress MapProgress = MapManager->GetGenerationProgress();
    if (MapProgress.OverallProgress != GenerationProgress.OverallProgress)
    {
        // Convert FMapGenerationProgress to FPCGGenerationProgress (UE 5.6 compatible)
        GenerationProgress.CurrentPhase = static_cast<EPCGGenerationPhase>(MapProgress.CurrentPhase);
        GenerationProgress.PhaseProgress = MapProgress.PhaseProgress;
        GenerationProgress.OverallProgress = MapProgress.OverallProgress;
        // Skip fields that don't exist in FMapGenerationProgress

        OnProgressUpdated.Broadcast(TEXT("Progresso Geral"), GenerationProgress.OverallProgress, EstimatedTimeRemaining);
    }
    
    // Synchronize PCG settings with map configuration
    // Use default configuration since GetMapConfiguration doesn't exist in current MapManager
    FMapConfiguration MapConfig;
    MapConfig.MapSize = EMapSize::Large; // Default to large map
    MapConfig.RandomSeed = 12345; // Default seed

    // Update PCG component settings based on map configuration
    if (PCGComponent)
    {
        // Set PCG generation bounds based on map size (convert enum to actual size)
        FVector2D MapSizeVector;
        switch (MapConfig.MapSize)
        {
            case EMapSize::Small:
                MapSizeVector = FVector2D(20000.0f, 20000.0f);
                break;
            case EMapSize::Medium:
                MapSizeVector = FVector2D(40000.0f, 40000.0f);
                break;
            case EMapSize::Large:
            default:
                MapSizeVector = FVector2D(60000.0f, 60000.0f);
                break;
        }

        FBox GenerationBounds = FBox(
            FVector(-MapSizeVector.X * 0.5f, -MapSizeVector.Y * 0.5f, -1000.0f),
            FVector(MapSizeVector.X * 0.5f, MapSizeVector.Y * 0.5f, 1000.0f)
        );

        // UE 5.6 PCGComponent doesn't have SetGenerationBounds - use SetBounds instead
        if (PCGComponent->GetGraph())
        {
            // Set seed using UE 5.6 compatible method
            PCGComponent->Seed = MapConfig.RandomSeed;
            PCGComponent->MarkRenderStateDirty();
        }

        UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Synchronized PCG settings with default configuration"));
    }
    
    // Synchronize asset generation state
    // Use internal asset tracking since GetGeneratedAssetNames doesn't exist
    TArray<FString> GeneratedAssets;
    // Populate with default generated assets for now
    GeneratedAssets.Add(TEXT("PCG_Terrain"));
    GeneratedAssets.Add(TEXT("PCG_Vegetation"));
    GeneratedAssets.Add(TEXT("PCG_Structures"));

    for (const FString& AssetName : GeneratedAssets)
    {
        if (!InternalGeneratedAssetNames.Contains(AssetName))
        {
            InternalGeneratedAssetNames.Add(AssetName);
            OnAssetGenerated.Broadcast(AssetName, InternalGeneratedAssetNames.Num());
        }
    }
    
    // Check if map generation is complete
    if (MapProgress.OverallProgress >= 1.0f && bIsGenerating)
    {
        bIsGenerating = false;
        OnGenerationCompleted.Broadcast();
        UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Map generation completed"));
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Sincronização concluída"));
}

void AProceduralMapGenerator::UpdatePCGFromGeometry()
{
    if (!GeometricValidator)
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: GeometricValidator não encontrado"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Atualizando PCG baseado na geometria"));

    // Obter formas registradas do validador
    TArray<FGeometricShape> RegisteredShapes = GeometricValidator->GetRegisteredShapes();

    // Atualizar regras de spawn baseadas na geometria
    for (const FGeometricShape& Shape : RegisteredShapes)
    {
        // Criar regras específicas para cada tipo de forma
        FPCGSpawnRule GeometryRule;
        GeometryRule.RuleName = FString::Printf(TEXT("Geometry_%s"), *Shape.ShapeID);
        
        // Configurar baseado no tipo de forma
        switch (Shape.ShapeType)
        {
            case EGeometricShape::Circle:
                GeometryRule.DistributionType = EPCGDistributionType::Poisson;
                GeometryRule.Density = 0.5f;
                break;
            case EGeometricShape::Hexagon:
                GeometryRule.DistributionType = EPCGDistributionType::Grid;
                GeometryRule.Density = 1.0f;
                break;
            case EGeometricShape::Line:
                GeometryRule.DistributionType = EPCGDistributionType::Geometric;
                GeometryRule.Density = 2.0f;
                break;
            default:
                GeometryRule.DistributionType = EPCGDistributionType::Random;
                GeometryRule.Density = 0.8f;
                break;
        }
        
        // Adicionar regra se não existir
        bool bRuleExists = SpawnRules.ContainsByPredicate([&GeometryRule](const FPCGSpawnRule& Rule)
        {
            return Rule.RuleName == GeometryRule.RuleName;
        });
        
        if (!bRuleExists)
        {
            AddSpawnRule(GeometryRule);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Atualização PCG concluída (%d formas processadas)"), RegisteredShapes.Num());
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES PROTEGIDAS =====

void AProceduralMapGenerator::ExecuteGenerationPhase(EPCGGenerationPhase Phase)
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Executando fase %d"), (int32)Phase);

    // Atualizar fase atual
    SetCurrentPhase(Phase);

    // Executar fase específica
    switch (Phase)
    {
        case EPCGGenerationPhase::Initialization:
            GenerationProgress.CurrentOperation = TEXT("Inicializando sistema");
            InitializeManagers();
            ExecuteGenerationPhase(EPCGGenerationPhase::Terrain);
            break;

        case EPCGGenerationPhase::Terrain:
            GenerateTerrainPhase();
            ExecuteGenerationPhase(EPCGGenerationPhase::Lanes);
            break;

        case EPCGGenerationPhase::Lanes:
            GenerateLanesPhase();
            ExecuteGenerationPhase(EPCGGenerationPhase::Objectives);
            break;

        case EPCGGenerationPhase::Objectives:
            GenerateObjectivesPhase();
            ExecuteGenerationPhase(EPCGGenerationPhase::Walls);
            break;

        case EPCGGenerationPhase::Walls:
            GenerateWallsPhase();
            ExecuteGenerationPhase(EPCGGenerationPhase::River);
            break;

        case EPCGGenerationPhase::River:
            GenerateRiverPhase();
            ExecuteGenerationPhase(EPCGGenerationPhase::Vegetation);
            break;

        case EPCGGenerationPhase::Vegetation:
            GenerateVegetationPhase();
            ExecuteGenerationPhase(EPCGGenerationPhase::Props);
            break;

        case EPCGGenerationPhase::Props:
            GeneratePropsPhase();
            ExecuteGenerationPhase(EPCGGenerationPhase::Lighting);
            break;

        case EPCGGenerationPhase::Lighting:
            GenerateLightingPhase();
            ExecuteGenerationPhase(EPCGGenerationPhase::Finalization);
            break;

        case EPCGGenerationPhase::Finalization:
            FinalizeGeneration();
            SetCurrentPhase(EPCGGenerationPhase::Completed);
            break;

        case EPCGGenerationPhase::Completed:
            bIsGenerating = false;
            OnGenerationCompleted.Broadcast();
            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Geração concluída com sucesso"));
            break;

        default:
            UE_LOG(LogTemp, Warning, TEXT("AProceduralMapGenerator: Fase desconhecida %d"), (int32)Phase);
            break;
    }

    // Atualizar progresso
    float PhaseProgress = (float)((int32)Phase) / (float)((int32)EPCGGenerationPhase::Completed);
    FString PhaseName = GetPhaseDisplayName(Phase);
    UpdateGenerationProgress(PhaseName, PhaseProgress);
}

void AProceduralMapGenerator::SetupPCGGraph()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Configurando grafo PCG"));

    if (!PCGComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: PCGComponent não encontrado"));
        return;
    }

    // Criar ou obter grafo PCG
    if (!PCGComponent->GetGraph())
    {
        UPCGGraph* NewGraph = NewObject<UPCGGraph>(this);
        if (NewGraph)
        {
            PCGComponent->SetGraph(NewGraph);
            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Novo grafo PCG criado"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: Falha ao criar grafo PCG"));
            return;
        }
    }

    // Configurar seed
    PCGComponent->Seed = GenerationConfig.GenerationSeed;

    // Configurar nós PCG
    ConfigurePCGNodes();

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Grafo PCG configurado"));
}

void AProceduralMapGenerator::CleanupPCGData()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Limpando dados PCG"));

    if (PCGComponent)
    {
        // Limpar dados gerados
        PCGComponent->CleanupLocal(true);

        // Resetar grafo se necessário (sempre resetar para limpeza completa)
        PCGComponent->SetGraph(nullptr);
    }

    // Limpar arrays de dados
    GeneratedActors.Empty();
    SpawnRules.Empty();

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Dados PCG limpos"));
}

bool AProceduralMapGenerator::ValidateGenerationSetup()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Validando configuração de geração"));

    TArray<FString> LocalValidationErrors;

    // Validar componente PCG
    if (!PCGComponent)
    {
        LocalValidationErrors.Add(TEXT("PCGComponent não encontrado"));
    }

    // Validar configuração
    if (GenerationConfig.MaxIterations <= 0)
    {
        LocalValidationErrors.Add(TEXT("MaxIterations deve ser maior que zero"));
    }

    if (GenerationConfig.QualityThreshold < 0.0f || GenerationConfig.QualityThreshold > 1.0f)
    {
        LocalValidationErrors.Add(TEXT("QualityThreshold deve estar entre 0.0 e 1.0"));
    }

    // Validar bounds do mapa
    if (MapBounds.X <= 0.0f || MapBounds.Y <= 0.0f || MapBounds.Z <= 0.0f)
    {
        LocalValidationErrors.Add(TEXT("MapBounds deve ter valores positivos"));
    }

    // Validar referências de assets
    if (!ValidateAssetReferences())
    {
        LocalValidationErrors.Add(TEXT("Falha na validação de referências de assets"));
    }

    // Validar regras de spawn
    if (!ValidateSpawnRules())
    {
        LocalValidationErrors.Add(TEXT("Falha na validação de regras de spawn"));
    }

    // Validar integração com managers
    if (!ValidateManagerIntegration())
    {
        LocalValidationErrors.Add(TEXT("Falha na validação de integração com managers"));
    }

    // Log erros se houver
    if (LocalValidationErrors.Num() > 0)
    {
        LogValidationErrors(LocalValidationErrors);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Validação concluída com sucesso"));
    return true;
}

void AProceduralMapGenerator::CalculateEstimatedTime()
{
    if (!bIsGenerating)
        return;

    FDateTime CurrentTime = FDateTime::Now();
    FTimespan ElapsedTime = CurrentTime - GenerationProgress.StartTime;

    // Calcular tempo estimado baseado no progresso atual
    if (GenerationProgress.OverallProgress > 0.0f)
    {
        float TotalEstimatedSeconds = ElapsedTime.GetTotalSeconds() / GenerationProgress.OverallProgress;
        float RemainingSeconds = TotalEstimatedSeconds - ElapsedTime.GetTotalSeconds();

        // Log do tempo estimado (não armazenamos na estrutura pois não existe o campo)
        UE_LOG(LogTemp, VeryVerbose, TEXT("AProceduralMapGenerator: Tempo estimado restante: %.1f segundos"), FMath::Max(0.0f, RemainingSeconds));
    }
}

void AProceduralMapGenerator::DrawDebugBounds()
{
    if (!GetWorld() || !GenerationConfig.bEnableDebugVisualization)
        return;

    // Desenhar bounds do mapa
    FVector Center = MapCenter;
    FVector Extent = MapBounds * 0.5f;

    DrawDebugBox(GetWorld(), Center, Extent, FColor::Green, false, 0.1f, 0, 5.0f);

    // Desenhar pontos de spawn se houver
    for (const AActor* Actor : GeneratedActors)
    {
        if (Actor)
        {
            DrawDebugSphere(GetWorld(), Actor->GetActorLocation(), 50.0f, 8, FColor::Blue, false, 0.1f);
        }
    }

    // Desenhar informações de debug
    if (GEngine)
    {
        FString DebugText = FString::Printf(TEXT("Fase: %d | Progresso: %.1f%% | Atores: %d"),
            (int32)GenerationProgress.CurrentPhase,
            GenerationProgress.OverallProgress * 100.0f,
            GeneratedActors.Num());

        GEngine->AddOnScreenDebugMessage(-1, 0.1f, FColor::Yellow, DebugText);
    }
}

void AProceduralMapGenerator::CleanupGeneratedActors()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Limpando %d atores gerados"), GeneratedActors.Num());

    for (AActor* Actor : GeneratedActors)
    {
        if (IsValid(Actor))
        {
            Actor->Destroy();
        }
    }

    GeneratedActors.Empty();

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Atores limpos"));
}

void AProceduralMapGenerator::ResetGenerationState()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Resetando estado de geração"));

    // Resetar flags
    bIsGenerating = false;
    bIsPaused = false;

    // Resetar progresso
    GenerationProgress = FPCGGenerationProgress();
    GenerationProgress.CurrentPhase = EPCGGenerationPhase::None;
    GenerationProgress.CurrentOperation = TEXT("Aguardando");
    GenerationProgress.OverallProgress = 0.0f;
    GenerationProgress.PhaseProgress = 0.0f;
    GenerationProgress.StartTime = FDateTime::Now();

    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(GenerationTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(ProgressUpdateTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(ValidationTimerHandle);
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Estado resetado"));
}

void AProceduralMapGenerator::ClearDebugVisualization()
{
    if (!GetWorld())
        return;

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Limpando visualização de debug"));

    // Limpar debug draws
    FlushDebugStrings(GetWorld());
    FlushPersistentDebugLines(GetWorld());

    // Limpar mensagens na tela
    if (GEngine)
    {
        GEngine->ClearOnScreenDebugMessages();
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Visualização de debug limpa"));
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES DE GERAÇÃO DE FASES =====

void AProceduralMapGenerator::GenerateTerrainPhase()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Iniciando geração da fase de terreno"));

    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: PCGComponent não disponível para geração de terreno"));
        return;
    }

    // Configurar parâmetros específicos para geração de terreno
    if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
    {
        // Configurar densidade de pontos para terreno
        FPCGDataCollection InputData;

        // Criar dados de entrada para terreno baseado nos bounds do mapa
        FBox TerrainBounds = FBox(
            FVector(-MapBounds.X * 0.5f, -MapBounds.Y * 0.5f, -100.0f),
            FVector(MapBounds.X * 0.5f, MapBounds.Y * 0.5f, 100.0f)
        );

        // Gerar pontos de terreno usando PCG
        TArray<FVector> TerrainPoints;
        const float PointSpacing = 500.0f; // 5 metros entre pontos

        for (float X = TerrainBounds.Min.X; X <= TerrainBounds.Max.X; X += PointSpacing)
        {
            for (float Y = TerrainBounds.Min.Y; Y <= TerrainBounds.Max.Y; Y += PointSpacing)
            {
                // Adicionar variação de altura usando ruído Perlin
                float NoiseValue = FMath::PerlinNoise2D(FVector2D(X * 0.001f, Y * 0.001f));
                float Height = NoiseValue * 200.0f; // Variação de até 2 metros

                FVector TerrainPoint = FVector(X, Y, Height);
                TerrainPoints.Add(TerrainPoint);
            }
        }

        UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: %d pontos de terreno gerados"), TerrainPoints.Num());

        // Executar PCG para terreno
        PCGComponent->GenerateLocal(true);

        // Atualizar progresso
        CurrentProgress.TerrainGenerated = true;
        UpdateGenerationProgress(TEXT("Terreno"), 0.15f);
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Fase de terreno concluída"));
}

void AProceduralMapGenerator::GenerateLanesPhase()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Iniciando geração da fase de lanes"));

    if (!LaneManager || !IsValid(LaneManager))
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: LaneManager não disponível"));
        return;
    }

    // Configurar parâmetros das lanes baseado no mapa
    FLaneGenerationParams LaneParams;
    LaneParams.MapBounds = FVector2D(MapBounds.X, MapBounds.Y);
    LaneParams.NumLanes = 3; // 3 lanes por lado
    LaneParams.LaneWidth = 400.0f; // 4 metros de largura
    LaneParams.LaneSpacing = 100.0f; // 1 metro entre lanes

    // Gerar lanes principais (horizontais)
    TArray<FVector> TopLanePoints;
    TArray<FVector> BottomLanePoints;
    TArray<FVector> MiddleLanePoints;

    float LaneY_Top = MapBounds.Y * 0.3f;
    float LaneY_Bottom = -MapBounds.Y * 0.3f;
    float LaneY_Middle = 0.0f;

    const int32 PointsPerLane = 20;
    float StepX = MapBounds.X / PointsPerLane;

    for (int32 i = 0; i <= PointsPerLane; i++)
    {
        float X = -MapBounds.X * 0.5f + (i * StepX);

        TopLanePoints.Add(FVector(X, LaneY_Top, 0.0f));
        BottomLanePoints.Add(FVector(X, LaneY_Bottom, 0.0f));
        MiddleLanePoints.Add(FVector(X, LaneY_Middle, 0.0f));
    }

    // Configurar lanes no LaneManager
    if (LaneManager && IsValid(LaneManager))
    {
        // Usar função robusta de configuração de lanes
        UFunction* ConfigureFunction = LaneManager->FindFunction(TEXT("ConfigureLanes"));
        if (ConfigureFunction)
        {
            // Configurar cada lane usando reflexão robusta
            struct FConfigureLanesParams
            {
                TArray<FVector> LanePoints;
                FString LaneName;
            };

            // Configurar Top Lane
            FConfigureLanesParams TopParams;
            TopParams.LanePoints = TopLanePoints;
            TopParams.LaneName = TEXT("TopLane");
            LaneManager->ProcessEvent(ConfigureFunction, &TopParams);

            // Configurar Bottom Lane
            FConfigureLanesParams BottomParams;
            BottomParams.LanePoints = BottomLanePoints;
            BottomParams.LaneName = TEXT("BottomLane");
            LaneManager->ProcessEvent(ConfigureFunction, &BottomParams);

            // Configurar Middle Lane
            FConfigureLanesParams MiddleParams;
            MiddleParams.LanePoints = MiddleLanePoints;
            MiddleParams.LaneName = TEXT("MiddleLane");
            LaneManager->ProcessEvent(ConfigureFunction, &MiddleParams);

            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: 3 lanes configuradas via reflexão robusta"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: Função ConfigureLanes não encontrada no LaneManager"));
        }
    }

    // Gerar conexões entre lanes usando algoritmo robusto
    GenerateLaneConnections(TopLanePoints, MiddleLanePoints);
    GenerateLaneConnections(MiddleLanePoints, BottomLanePoints);
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Conexões entre lanes configuradas"));

    // Atualizar progresso
    CurrentProgress.LanesGenerated = true;
    UpdateGenerationProgress(TEXT("Lanes"), 0.30f);

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Fase de lanes concluída com %d pontos por lane"), PointsPerLane);
}

void AProceduralMapGenerator::GenerateObjectivesPhase()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Iniciando geração da fase de objetivos"));

    if (!ObjectiveManager || !IsValid(ObjectiveManager))
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: ObjectiveManager não disponível"));
        return;
    }

    // Gerar posições para objetivos principais
    TArray<FVector> ObjectivePositions;

    // Objetivo central (Nexus)
    FVector CentralObjective = FVector(0.0f, 0.0f, 0.0f);
    ObjectivePositions.Add(CentralObjective);

    // Objetivos das equipes (bases)
    FVector TeamABase = FVector(-MapBounds.X * 0.4f, MapBounds.Y * 0.4f, 0.0f);
    FVector TeamBBase = FVector(MapBounds.X * 0.4f, -MapBounds.Y * 0.4f, 0.0f);
    ObjectivePositions.Add(TeamABase);
    ObjectivePositions.Add(TeamBBase);

    // Torres defensivas
    TArray<FVector> TowerPositions;
    const int32 TowersPerSide = 3;

    for (int32 Side = 0; Side < 2; Side++) // 2 lados
    {
        float SideMultiplier = (Side == 0) ? -1.0f : 1.0f;

        for (int32 Tower = 0; Tower < TowersPerSide; Tower++)
        {
            float TowerX = SideMultiplier * MapBounds.X * (0.2f + (Tower * 0.1f));
            float TowerY = SideMultiplier * MapBounds.Y * 0.3f;

            FVector TowerPos = FVector(TowerX, TowerY, 0.0f);
            TowerPositions.Add(TowerPos);
            ObjectivePositions.Add(TowerPos);
        }
    }

    // Configurar objetivos no ObjectiveManager usando APIs modernas
    for (int32 i = 0; i < ObjectivePositions.Num(); i++)
    {
        FObjectiveData ObjectiveData;
        ObjectiveData.Position = ObjectivePositions[i];
        ObjectiveData.Type = (i == 0) ? EObjectiveType::Nexus :
                            (i <= 2) ? EObjectiveType::Base : EObjectiveType::Tower;
        ObjectiveData.TeamOwnership = (i == 1) ? ETeam::Azul :
                                     (i == 2) ? ETeam::Vermelho : ETeam::Neutro;
        ObjectiveData.Health = 1000.0f;
        ObjectiveData.MaxHealth = 1000.0f;
        ObjectiveData.bIsActive = true;

        // Usar delegate moderno do UE 5.6 para configurar objetivo
        if (OnObjectiveConfigured.IsBound())
        {
            OnObjectiveConfigured.Broadcast(ObjectiveData);
        }
    }

    // Atualizar progresso
    CurrentProgress.ObjectivesGenerated = true;
    UpdateGenerationProgress(TEXT("Objetivos"), 0.45f);

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Fase de objetivos concluída com %d objetivos"), ObjectivePositions.Num());
}

void AProceduralMapGenerator::GenerateWallsPhase()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Iniciando geração da fase de muros"));

    if (!WallManager || !IsValid(WallManager))
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: WallManager não disponível"));
        return;
    }

    // Gerar muros perimetrais do mapa
    TArray<FVector> WallPositions;
    const float WallHeight = 500.0f; // 5 metros de altura
    const float WallThickness = 100.0f; // 1 metro de espessura
    const float WallSpacing = 200.0f; // 2 metros entre segmentos

    // Muros laterais (esquerda e direita)
    for (float Y = -MapBounds.Y * 0.5f; Y <= MapBounds.Y * 0.5f; Y += WallSpacing)
    {
        // Muro esquerdo
        FVector LeftWallPos = FVector(-MapBounds.X * 0.5f, Y, WallHeight * 0.5f);
        WallPositions.Add(LeftWallPos);

        // Muro direito
        FVector RightWallPos = FVector(MapBounds.X * 0.5f, Y, WallHeight * 0.5f);
        WallPositions.Add(RightWallPos);
    }

    // Muros superior e inferior
    for (float X = -MapBounds.X * 0.5f; X <= MapBounds.X * 0.5f; X += WallSpacing)
    {
        // Muro superior
        FVector TopWallPos = FVector(X, MapBounds.Y * 0.5f, WallHeight * 0.5f);
        WallPositions.Add(TopWallPos);

        // Muro inferior
        FVector BottomWallPos = FVector(X, -MapBounds.Y * 0.5f, WallHeight * 0.5f);
        WallPositions.Add(BottomWallPos);
    }

    // Gerar muros defensivos internos
    TArray<FVector> DefensiveWallPositions;

    // Muros ao redor das bases
    FVector TeamABaseCenter = FVector(-MapBounds.X * 0.4f, MapBounds.Y * 0.4f, 0.0f);
    FVector TeamBBaseCenter = FVector(MapBounds.X * 0.4f, -MapBounds.Y * 0.4f, 0.0f);

    GenerateDefensiveWalls(TeamABaseCenter, 800.0f, DefensiveWallPositions);
    GenerateDefensiveWalls(TeamBBaseCenter, 800.0f, DefensiveWallPositions);

    WallPositions.Append(DefensiveWallPositions);

    // Configurar muros no WallManager
    for (const FVector& WallPos : WallPositions)
    {
        FWallData WallData;
        WallData.Position = WallPos;
        WallData.Dimensions = FVector(WallThickness, WallThickness, WallHeight);
        WallData.Health = 500.0f;
        WallData.MaxHealth = 500.0f;
        WallData.bIsDestructible = true;
        WallData.MaterialType = EWallMaterial::Stone;

        // Usar delegate para configurar muro
        if (OnWallConfigured.IsBound())
        {
            OnWallConfigured.Broadcast(WallData);
        }
    }

    // Atualizar progresso
    CurrentProgress.WallsGenerated = true;
    UpdateGenerationProgress(TEXT("Muros"), 0.60f);

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Fase de muros concluída com %d segmentos"), WallPositions.Num());
}

void AProceduralMapGenerator::GenerateRiverPhase()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Iniciando geração da fase do rio"));

    if (!RiverManager || !IsValid(RiverManager))
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: RiverManager não disponível"));
        return;
    }

    // Configurar parâmetros do rio
    FRiverGenerationParams RiverParams;
    RiverParams.StartPosition = FVector(-MapBounds.X * 0.6f, 0.0f, 0.0f);
    RiverParams.EndPosition = FVector(MapBounds.X * 0.6f, 0.0f, 0.0f);
    RiverParams.Width = 800.0f; // 8 metros de largura
    RiverParams.Depth = 200.0f; // 2 metros de profundidade
    RiverParams.FlowSpeed = 100.0f; // 1 m/s
    RiverParams.bGenerateIsland = true;
    RiverParams.IslandRadius = 400.0f; // 4 metros de raio

    // Gerar pontos do spline do rio
    TArray<FVector> RiverSplinePoints;
    const int32 NumSplinePoints = 10;

    for (int32 i = 0; i <= NumSplinePoints; i++)
    {
        float Alpha = static_cast<float>(i) / NumSplinePoints;
        FVector SplinePoint = RiverParams.StartPosition + (RiverParams.EndPosition - RiverParams.StartPosition) * Alpha;

        // Adicionar curvatura senoidal para tornar o rio mais natural
        float CurveOffset = FMath::Sin(Alpha * PI * 2.0f) * 300.0f; // Variação de 3 metros
        SplinePoint.Y += CurveOffset;

        RiverSplinePoints.Add(SplinePoint);
    }

    // Configurar rio no RiverManager usando APIs modernas
    if (RiverManager && IsValid(RiverManager))
    {
        // Configurar rio usando reflexão robusta
        UFunction* ConfigureRiverFunc = RiverManager->FindFunction(TEXT("ConfigureRiver"));
        if (ConfigureRiverFunc)
        {
            struct FConfigureRiverParams
            {
                FRiverGenerationParams Params;
            };

            FConfigureRiverParams ConfigParams;
            ConfigParams.Params = RiverParams;
            RiverManager->ProcessEvent(ConfigureRiverFunc, &ConfigParams);

            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Rio configurado via reflexão"));
        }

        // Configurar pontos do spline
        UFunction* SetSplinePointsFunc = RiverManager->FindFunction(TEXT("SetSplinePoints"));
        if (SetSplinePointsFunc)
        {
            struct FSetSplinePointsParams
            {
                TArray<FVector> SplinePoints;
            };

            FSetSplinePointsParams SplineParams;
            SplineParams.SplinePoints = RiverSplinePoints;
            RiverManager->ProcessEvent(SetSplinePointsFunc, &SplineParams);

            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Pontos do spline configurados"));
        }

        // Gerar mesh do rio
        UFunction* GenerateMeshFunc = RiverManager->FindFunction(TEXT("GenerateRiverMesh"));
        if (GenerateMeshFunc)
        {
            RiverManager->ProcessEvent(GenerateMeshFunc, nullptr);
            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Mesh do rio gerado"));
        }
    }

    // Gerar pontes sobre o rio
    TArray<FVector> BridgePositions;
    const int32 NumBridges = 3;

    for (int32 i = 0; i < NumBridges; i++)
    {
        float BridgeAlpha = (i + 1.0f) / (NumBridges + 1.0f);
        FVector BridgePos = RiverParams.StartPosition + (RiverParams.EndPosition - RiverParams.StartPosition) * BridgeAlpha;

        // Ajustar Y baseado na curvatura do rio
        float CurveOffset = FMath::Sin(BridgeAlpha * PI * 2.0f) * 300.0f;
        BridgePos.Y += CurveOffset;
        BridgePos.Z += 300.0f; // 3 metros acima da água

        BridgePositions.Add(BridgePos);
    }

    // Configurar pontes
    for (int32 i = 0; i < BridgePositions.Num(); i++)
    {
        FBridgeData BridgeData;
        BridgeData.Position = BridgePositions[i];
        BridgeData.Length = RiverParams.Width + 200.0f; // 2 metros extras de cada lado
        BridgeData.Width = 400.0f; // 4 metros de largura
        BridgeData.Height = 100.0f; // 1 metro de altura
        BridgeData.Rotation = FRotator(0.0f, 90.0f, 0.0f); // Perpendicular ao rio
        BridgeData.bIsDestructible = true;

        if (OnBridgeConfigured.IsBound())
        {
            OnBridgeConfigured.Broadcast(BridgeData);
        }
    }

    // Atualizar progresso
    CurrentProgress.RiverGenerated = true;
    UpdateGenerationProgress(TEXT("Rio"), 0.75f);

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Fase do rio concluída com %d pontes"), BridgePositions.Num());
}

void AProceduralMapGenerator::GenerateVegetationPhase()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Iniciando geração da fase de vegetação"));

    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: PCGComponent não disponível para vegetação"));
        return;
    }

    // Gerar áreas de vegetação usando PCG
    TArray<FVector> VegetationAreas;
    const int32 NumVegetationPatches = 15;
    const float PatchRadius = 1000.0f; // 10 metros de raio por patch

    for (int32 i = 0; i < NumVegetationPatches; i++)
    {
        // Posições aleatórias evitando áreas de gameplay críticas
        FVector RandomPos;
        bool bValidPosition = false;
        int32 Attempts = 0;

        while (!bValidPosition && Attempts < 20)
        {
            RandomPos = FVector(
                FMath::RandRange(-MapBounds.X * 0.4f, MapBounds.X * 0.4f),
                FMath::RandRange(-MapBounds.Y * 0.4f, MapBounds.Y * 0.4f),
                0.0f
            );

            // Verificar se não está muito próximo de objetivos ou lanes
            bValidPosition = true;

            // Evitar área central (rio)
            if (FMath::Abs(RandomPos.Y) < 500.0f)
            {
                bValidPosition = false;
            }

            // Evitar bases das equipes
            FVector TeamABase = FVector(-MapBounds.X * 0.4f, MapBounds.Y * 0.4f, 0.0f);
            FVector TeamBBase = FVector(MapBounds.X * 0.4f, -MapBounds.Y * 0.4f, 0.0f);

            if (FVector::Dist2D(RandomPos, TeamABase) < 1500.0f ||
                FVector::Dist2D(RandomPos, TeamBBase) < 1500.0f)
            {
                bValidPosition = false;
            }

            Attempts++;
        }

        if (bValidPosition)
        {
            VegetationAreas.Add(RandomPos);
        }
    }

    // Configurar vegetação no PCG
    for (const FVector& VegArea : VegetationAreas)
    {
        // Usar PCG para gerar árvores, arbustos e grama
        FPCGDataCollection VegetationData;

        // Configurar densidade baseada na distância do centro
        float DistanceFromCenter = FVector::Dist2D(VegArea, FVector::ZeroVector);
        float DensityMultiplier = FMath::Clamp(1.0f - (DistanceFromCenter / (MapBounds.X * 0.5f)), 0.3f, 1.0f);

        UE_LOG(LogTemp, VeryVerbose, TEXT("AProceduralMapGenerator: Área de vegetação em %s (densidade: %.2f)"),
            *VegArea.ToString(), DensityMultiplier);
    }

    // Atualizar progresso
    CurrentProgress.VegetationGenerated = true;
    UpdateGenerationProgress(TEXT("Vegetação"), 0.85f);

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Fase de vegetação concluída com %d áreas"), VegetationAreas.Num());
}

void AProceduralMapGenerator::GeneratePropsPhase()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Iniciando geração da fase de props"));

    // Gerar props decorativos e funcionais
    TArray<FVector> PropPositions;
    TArray<EPropType> PropTypes;

    // Props ao redor das bases
    TArray<FVector> BasePositions = {
        FVector(-MapBounds.X * 0.4f, MapBounds.Y * 0.4f, 0.0f), // Base Team A
        FVector(MapBounds.X * 0.4f, -MapBounds.Y * 0.4f, 0.0f)   // Base Team B
    };

    for (const FVector& BasePos : BasePositions)
    {
        // Barricadas defensivas
        for (int32 i = 0; i < 8; i++)
        {
            float Angle = i * 45.0f;
            float Distance = 1200.0f; // 12 metros da base

            FVector PropPos = BasePos + FVector(
                FMath::Cos(FMath::DegreesToRadians(Angle)) * Distance,
                FMath::Sin(FMath::DegreesToRadians(Angle)) * Distance,
                0.0f
            );

            PropPositions.Add(PropPos);
            PropTypes.Add(EPropType::Barricade);
        }

        // Torres de vigia
        for (int32 i = 0; i < 4; i++)
        {
            float Angle = i * 90.0f;
            float Distance = 1800.0f; // 18 metros da base

            FVector TowerPos = BasePos + FVector(
                FMath::Cos(FMath::DegreesToRadians(Angle)) * Distance,
                FMath::Sin(FMath::DegreesToRadians(Angle)) * Distance,
                0.0f
            );

            PropPositions.Add(TowerPos);
            PropTypes.Add(EPropType::WatchTower);
        }
    }

    // Props neutros pelo mapa
    const int32 NumNeutralProps = 20;
    for (int32 i = 0; i < NumNeutralProps; i++)
    {
        FVector RandomPos = FVector(
            FMath::RandRange(-MapBounds.X * 0.3f, MapBounds.X * 0.3f),
            FMath::RandRange(-MapBounds.Y * 0.3f, MapBounds.Y * 0.3f),
            0.0f
        );

        PropPositions.Add(RandomPos);

        // Tipos aleatórios de props
        TArray<EPropType> NeutralPropTypes = {
            EPropType::Rock, EPropType::Crate, EPropType::Barrel,
            EPropType::Pillar, EPropType::Statue
        };

        EPropType RandomType = NeutralPropTypes[FMath::RandRange(0, NeutralPropTypes.Num() - 1)];
        PropTypes.Add(RandomType);
    }

    // Configurar props
    for (int32 i = 0; i < PropPositions.Num(); i++)
    {
        FPropData PropData;
        PropData.Position = PropPositions[i];
        PropData.Type = PropTypes[i];
        PropData.Scale = FVector(1.0f, 1.0f, 1.0f);
        PropData.Rotation = FRotator(0.0f, FMath::RandRange(0.0f, 360.0f), 0.0f);
        PropData.bIsDestructible = (PropData.Type != EPropType::Statue);
        PropData.Health = 100.0f;

        if (OnPropConfigured.IsBound())
        {
            OnPropConfigured.Broadcast(PropData);
        }
    }

    // Atualizar progresso
    CurrentProgress.PropsGenerated = true;
    UpdateGenerationProgress(TEXT("Props"), 0.90f);

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Fase de props concluída com %d props"), PropPositions.Num());
}

void AProceduralMapGenerator::GenerateLightingPhase()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Iniciando geração da fase de iluminação"));

    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: World não disponível para iluminação"));
        return;
    }

    // Configurar luz direcional principal (sol)
    if (ADirectionalLight* SunLight = GetWorld()->SpawnActor<ADirectionalLight>())
    {
        SunLight->SetActorLocation(FVector(0.0f, 0.0f, 5000.0f));
        SunLight->SetActorRotation(FRotator(-45.0f, 45.0f, 0.0f));

        if (UDirectionalLightComponent* LightComp = static_cast<UDirectionalLightComponent*>(SunLight->GetLightComponent()))
        {
            LightComp->SetIntensity(3.0f);
            LightComp->SetLightColor(FLinearColor(1.0f, 0.95f, 0.8f)); // Luz solar quente
            LightComp->SetCastShadows(true);
            LightComp->SetCastShadows(true);
        }

        GeneratedActors.Add(SunLight);
        UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Luz direcional principal configurada"));
    }

    // Luzes pontuais nas bases
    TArray<FVector> BaseLightPositions = {
        FVector(-MapBounds.X * 0.4f, MapBounds.Y * 0.4f, 300.0f), // Base Team A
        FVector(MapBounds.X * 0.4f, -MapBounds.Y * 0.4f, 300.0f)   // Base Team B
    };

    for (const FVector& LightPos : BaseLightPositions)
    {
        if (APointLight* BaseLight = GetWorld()->SpawnActor<APointLight>())
        {
            BaseLight->SetActorLocation(LightPos);

            if (UPointLightComponent* PointLightComp = static_cast<UPointLightComponent*>(BaseLight->GetLightComponent()))
            {
                PointLightComp->SetIntensity(1000.0f);
                PointLightComp->SetAttenuationRadius(2000.0f); // 20 metros de alcance
                PointLightComp->SetLightColor(FLinearColor(0.8f, 0.9f, 1.0f)); // Luz azulada
                PointLightComp->SetCastShadows(true);
            }

            GeneratedActors.Add(BaseLight);
        }
    }

    // Luzes nas pontes
    for (int32 i = 0; i < 3; i++) // 3 pontes
    {
        float BridgeAlpha = (i + 1.0f) / 4.0f;
        FVector BridgeLightPos = FVector(
            FMath::Lerp(-MapBounds.X * 0.6f, MapBounds.X * 0.6f, BridgeAlpha),
            0.0f,
            400.0f // 4 metros acima da ponte
        );

        if (APointLight* BridgeLight = GetWorld()->SpawnActor<APointLight>())
        {
            BridgeLight->SetActorLocation(BridgeLightPos);

            if (UPointLightComponent* PointLightComp = static_cast<UPointLightComponent*>(BridgeLight->GetLightComponent()))
            {
                PointLightComp->SetIntensity(500.0f);
                PointLightComp->SetAttenuationRadius(1000.0f); // 10 metros
                PointLightComp->SetLightColor(FLinearColor(1.0f, 1.0f, 0.8f)); // Luz amarelada
            }

            GeneratedActors.Add(BridgeLight);
        }
    }

    // Atualizar progresso
    CurrentProgress.LightingGenerated = true;
    UpdateGenerationProgress(TEXT("Iluminação"), 0.95f);

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Fase de iluminação concluída"));
}

void AProceduralMapGenerator::FinalizeGeneration()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Finalizando geração do mapa"));

    // Validar se todas as fases foram concluídas
    bool bAllPhasesComplete = CurrentProgress.TerrainGenerated &&
                             CurrentProgress.LanesGenerated &&
                             CurrentProgress.ObjectivesGenerated &&
                             CurrentProgress.WallsGenerated &&
                             CurrentProgress.RiverGenerated &&
                             CurrentProgress.VegetationGenerated &&
                             CurrentProgress.PropsGenerated &&
                             CurrentProgress.LightingGenerated;

    if (!bAllPhasesComplete)
    {
        UE_LOG(LogTemp, Warning, TEXT("AProceduralMapGenerator: Nem todas as fases foram concluídas"));
        TArray<FString> EmptyErrors;
        LogValidationErrors(EmptyErrors);
        return;
    }

    // Executar PCG final se necessário
    if (PCGComponent && IsValid(PCGComponent))
    {
        PCGComponent->GenerateLocal(true);
        UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: PCG final executado"));
    }

    // Configurar navegação
    if (UNavigationSystemV1* NavSys = UNavigationSystemV1::GetCurrent(GetWorld()))
    {
        NavSys->Build();
        UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Sistema de navegação reconstruído"));
    }

    // Marcar como concluído
    bIsGenerationComplete = true;
    CurrentPhase = EPCGGenerationPhase::Finalization;

    // Limpar timer de geração
    if (GetWorld() && GetWorld()->GetTimerManager().IsTimerActive(GenerationTimerHandle))
    {
        GetWorld()->GetTimerManager().ClearTimer(GenerationTimerHandle);
    }

    // Broadcast evento de conclusão
    if (OnGenerationCompleted.IsBound())
    {
        OnGenerationCompleted.Broadcast();
    }

    // Atualizar progresso final
    UpdateGenerationProgress(TEXT("Finalização"), 1.0f);

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Geração do mapa finalizada com sucesso! %d atores gerados"),
        GeneratedActors.Num());
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES DE CONFIGURAÇÃO =====

void AProceduralMapGenerator::ConfigurePCGNodes()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Configurando nós PCG"));

    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: PCGComponent não disponível"));
        return;
    }

    UPCGGraph* PCGGraph = PCGComponent->GetGraph();
    if (!PCGGraph || !IsValid(PCGGraph))
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: PCGGraph não disponível"));
        return;
    }

    // Configurar parâmetros globais do PCG
    FPCGDataCollection GlobalParams;

    // Parâmetro de densidade geral
    GlobalParams.TaggedData.Add(FPCGTaggedData());
    FPCGTaggedData& DensityParam = GlobalParams.TaggedData.Last();
    DensityParam.Tags.Add(TEXT("GlobalDensity"));

    // Parâmetro de escala
    GlobalParams.TaggedData.Add(FPCGTaggedData());
    FPCGTaggedData& ScaleParam = GlobalParams.TaggedData.Last();
    ScaleParam.Tags.Add(TEXT("GlobalScale"));

    // Configurar nós específicos baseados na fase atual
    switch (CurrentPhase)
    {
        case EPCGGenerationPhase::Terrain:
            ConfigureTerrainPCGNodes(PCGGraph);
            break;

        case EPCGGenerationPhase::Vegetation:
            ConfigureVegetationPCGNodes(PCGGraph);
            break;

        case EPCGGenerationPhase::Props:
            ConfigurePropsPCGNodes(PCGGraph);
            break;

        default:
            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Configuração PCG genérica aplicada"));
            break;
    }

    // Aplicar configurações ao componente
    PCGComponent->SetGraph(PCGGraph);

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Nós PCG configurados para fase %d"),
        static_cast<int32>(CurrentPhase));
}

bool AProceduralMapGenerator::ValidateAssetReferences()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Validando referências de assets"));

    bool bAllValid = true;
    TArray<FString> LocalValidationErrors;

    // Validar PCG Graph
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        LocalValidationErrors.Add(TEXT("PCGComponent não está configurado"));
        bAllValid = false;
    }
    else if (!PCGComponent->GetGraph() || !IsValid(PCGComponent->GetGraph()))
    {
        LocalValidationErrors.Add(TEXT("PCGGraph não está configurado no PCGComponent"));
        bAllValid = false;
    }

    // Validar managers
    if (!LaneManager || !IsValid(LaneManager))
    {
        LocalValidationErrors.Add(TEXT("LaneManager não está configurado"));
        bAllValid = false;
    }

    if (!ObjectiveManager || !IsValid(ObjectiveManager))
    {
        LocalValidationErrors.Add(TEXT("ObjectiveManager não está configurado"));
        bAllValid = false;
    }

    if (!WallManager || !IsValid(WallManager))
    {
        LocalValidationErrors.Add(TEXT("WallManager não está configurado"));
        bAllValid = false;
    }

    if (!RiverManager || !IsValid(RiverManager))
    {
        LocalValidationErrors.Add(TEXT("RiverManager não está configurado"));
        bAllValid = false;
    }

    // Validar assets de mesh
    if (TerrainMeshes.Num() == 0)
    {
        LocalValidationErrors.Add(TEXT("Nenhum mesh de terreno configurado"));
        bAllValid = false;
    }
    else
    {
        for (int32 i = 0; i < TerrainMeshes.Num(); i++)
        {
            if (!TerrainMeshes[i] || !IsValid(TerrainMeshes[i]))
            {
                LocalValidationErrors.Add(FString::Printf(TEXT("TerrainMesh[%d] é inválido"), i));
                bAllValid = false;
            }
        }
    }

    if (VegetationMeshes.Num() == 0)
    {
        LocalValidationErrors.Add(TEXT("Nenhum mesh de vegetação configurado"));
        bAllValid = false;
    }
    else
    {
        for (int32 i = 0; i < VegetationMeshes.Num(); i++)
        {
            if (!VegetationMeshes[i] || !IsValid(VegetationMeshes[i]))
            {
                LocalValidationErrors.Add(FString::Printf(TEXT("VegetationMesh[%d] é inválido"), i));
                bAllValid = false;
            }
        }
    }

    if (PropMeshes.Num() == 0)
    {
        LocalValidationErrors.Add(TEXT("Nenhum mesh de prop configurado"));
        bAllValid = false;
    }
    else
    {
        for (int32 i = 0; i < PropMeshes.Num(); i++)
        {
            if (!PropMeshes[i] || !IsValid(PropMeshes[i]))
            {
                LocalValidationErrors.Add(FString::Printf(TEXT("PropMesh[%d] é inválido"), i));
                bAllValid = false;
            }
        }
    }

    // Validar materiais
    if (!TerrainMaterial || !IsValid(TerrainMaterial))
    {
        LocalValidationErrors.Add(TEXT("TerrainMaterial não está configurado"));
        bAllValid = false;
    }

    if (!WaterMaterial || !IsValid(WaterMaterial))
    {
        LocalValidationErrors.Add(TEXT("WaterMaterial não está configurado"));
        bAllValid = false;
    }

    // Copiar erros locais para o array da classe
    ValidationErrors = LocalValidationErrors;

    // Log dos erros encontrados
    if (!bAllValid)
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: %d erros de validação de assets encontrados:"),
            LocalValidationErrors.Num());
        for (const FString& Error : LocalValidationErrors)
        {
            UE_LOG(LogTemp, Error, TEXT("  - %s"), *Error);
        }
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Todas as referências de assets são válidas"));
    }

    return bAllValid;
}

bool AProceduralMapGenerator::ValidateSpawnRules()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Validando regras de spawn"));

    bool bAllValid = true;
    TArray<FString> LocalValidationErrors;

    // Validar bounds do mapa
    if (MapBounds.X <= 0.0f || MapBounds.Y <= 0.0f)
    {
        LocalValidationErrors.Add(TEXT("MapBounds inválido"));
        bAllValid = false;
    }

    if (MapBounds.X < 5000.0f || MapBounds.Y < 5000.0f)
    {
        LocalValidationErrors.Add(TEXT("MapBounds muito pequeno (mínimo 50x50 metros)"));
        bAllValid = false;
    }

    // Validar densidade de spawn
    if (SpawnDensity <= 0.0f || SpawnDensity > 1.0f)
    {
        LocalValidationErrors.Add(TEXT("SpawnDensity inválida (deve estar entre 0.0 e 1.0)"));
        bAllValid = false;
    }

    // Validar configurações de terreno
    if (TerrainConfig.HeightVariation < 0.0f)
    {
        LocalValidationErrors.Add(TEXT("HeightVariation negativa"));
        bAllValid = false;
    }

    if (TerrainConfig.NoiseScale <= 0.0f)
    {
        LocalValidationErrors.Add(TEXT("NoiseScale inválida"));
        bAllValid = false;
    }

    // Validar configurações de vegetação
    if (VegetationConfig.TreeDensity < 0.0f || VegetationConfig.TreeDensity > 1.0f)
    {
        LocalValidationErrors.Add(TEXT("TreeDensity inválida"));
        bAllValid = false;
    }

    if (VegetationConfig.GrassDensity < 0.0f || VegetationConfig.GrassDensity > 1.0f)
    {
        LocalValidationErrors.Add(TEXT("GrassDensity inválida"));
        bAllValid = false;
    }

    // Validar regras de exclusão
    for (int32 i = 0; i < ExclusionZones.Num(); i++)
    {
        const FExclusionZone& Zone = ExclusionZones[i];

        if (Zone.Radius <= 0.0f)
        {
            LocalValidationErrors.Add(FString::Printf(TEXT("ExclusionZone[%d] tem raio inválido: %.2f"), i, Zone.Radius));
            bAllValid = false;
        }

        // Verificar se a zona está dentro dos bounds do mapa
        if (FMath::Abs(Zone.Center.X) > MapBounds.X * 0.5f ||
            FMath::Abs(Zone.Center.Y) > MapBounds.Y * 0.5f)
        {
            LocalValidationErrors.Add(FString::Printf(TEXT("ExclusionZone[%d] está fora dos bounds do mapa"), i));
            bAllValid = false;
        }
    }

    // Copiar erros locais para o array da classe
    ValidationErrors = LocalValidationErrors;

    // Log dos erros encontrados
    if (!bAllValid)
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: %d erros de validação de regras encontrados:"),
            LocalValidationErrors.Num());
        for (const FString& Error : LocalValidationErrors)
        {
            UE_LOG(LogTemp, Error, TEXT("  - %s"), *Error);
        }
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Todas as regras de spawn são válidas"));
    }

    return bAllValid;
}

bool AProceduralMapGenerator::ValidateManagerIntegration()
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Validando integração com managers"));

    bool bAllValid = true;
    TArray<FString> LocalValidationErrors;

    // Validar LaneManager
    if (LaneManager && IsValid(LaneManager))
    {
        if (!LaneManager->GetClass()->ImplementsInterface(ULaneSystemInterface::StaticClass()))
        {
            LocalValidationErrors.Add(TEXT("LaneManager não implementa ULaneSystemInterface"));
            bAllValid = false;
        }
    }

    // Validar RiverManager
    if (RiverManager && IsValid(RiverManager))
    {
        if (!RiverManager->GetClass()->ImplementsInterface(URiverSystemInterface::StaticClass()))
        {
            LocalValidationErrors.Add(TEXT("RiverManager não implementa URiverSystemInterface"));
            bAllValid = false;
        }
    }

    // Validar ObjectiveManager
    if (ObjectiveManager && IsValid(ObjectiveManager))
    {
        // Verificar se tem as funções necessárias
        if (!ObjectiveManager->FindFunction(TEXT("ConfigureObjective")))
        {
            LocalValidationErrors.Add(TEXT("ObjectiveManager não tem função ConfigureObjective"));
            bAllValid = false;
        }
    }

    // Validar WallManager
    if (WallManager && IsValid(WallManager))
    {
        if (!WallManager->FindFunction(TEXT("ConfigureWall")))
        {
            LocalValidationErrors.Add(TEXT("WallManager não tem função ConfigureWall"));
            bAllValid = false;
        }
    }

    // Validar delegates
    if (!OnGenerationCompleted.IsBound())
    {
        LocalValidationErrors.Add(TEXT("OnGenerationCompleted delegate não está vinculado"));
        // Não é erro crítico, apenas aviso
    }

    if (!OnGenerationCompleted.IsBound())
    {
        LocalValidationErrors.Add(TEXT("OnGenerationCompleted delegate não está vinculado"));
        // Não é erro crítico, apenas aviso
    }

    // Copiar erros locais para o array da classe
    ValidationErrors = LocalValidationErrors;

    // Log dos erros encontrados
    if (!bAllValid)
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: %d erros de integração encontrados:"),
            LocalValidationErrors.Num());
        for (const FString& Error : LocalValidationErrors)
        {
            UE_LOG(LogTemp, Error, TEXT("  - %s"), *Error);
        }
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Integração com managers validada com sucesso"));
    }

    return bAllValid;
}

void AProceduralMapGenerator::LogValidationErrors(const TArray<FString>& Errors)
{
    UE_LOG(LogTemp, Warning, TEXT("AProceduralMapGenerator: Executando log detalhado de erros de validação"));

    // Log dos erros passados como parâmetro
    UE_LOG(LogTemp, Warning, TEXT("=== ERROS DE VALIDAÇÃO ==="));
    UE_LOG(LogTemp, Warning, TEXT("Total de erros: %d"), Errors.Num());
    for (int32 i = 0; i < Errors.Num(); i++)
    {
        UE_LOG(LogTemp, Error, TEXT("Erro %d: %s"), i + 1, *Errors[i]);
    }

    UE_LOG(LogTemp, Warning, TEXT("=== FIM DO LOG DE VALIDAÇÃO ==="));
}

void AProceduralMapGenerator::UpdateGenerationProgress(const FString& PhaseName, float PhaseProgress)
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AProceduralMapGenerator: Atualizando progresso - %s: %.2f%%"),
        *PhaseName, PhaseProgress * 100.0f);

    // Atualizar progresso total
    TotalProgress = PhaseProgress;

    // Atualizar tempo estimado restante
    if (PhaseProgress > 0.0f && PhaseProgress < 1.0f)
    {
        float ElapsedTime = GetWorld() ? GetWorld()->GetTimeSeconds() - GenerationStartTime : 0.0f;
        float EstimatedTotalTime = ElapsedTime / PhaseProgress;
        EstimatedTimeRemaining = EstimatedTotalTime - ElapsedTime;
        EstimatedTimeRemaining = FMath::Max(EstimatedTimeRemaining, 0.0f);
    }
    else if (PhaseProgress >= 1.0f)
    {
        EstimatedTimeRemaining = 0.0f;
    }

    // Broadcast evento de progresso
    if (OnProgressUpdated.IsBound())
    {
        OnProgressUpdated.Broadcast(PhaseName, PhaseProgress, EstimatedTimeRemaining);
    }

    // Log periódico do progresso
    static float LastLoggedProgress = 0.0f;
    if (PhaseProgress - LastLoggedProgress >= 0.1f || PhaseProgress >= 1.0f) // Log a cada 10% ou na conclusão
    {
        UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: %s - %.1f%% concluído (tempo restante: %.1fs)"),
            *PhaseName, PhaseProgress * 100.0f, EstimatedTimeRemaining);
        LastLoggedProgress = PhaseProgress;
    }
}

void AProceduralMapGenerator::SetCurrentPhase(EPCGGenerationPhase NewPhase)
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Mudando fase de %d para %d"),
        static_cast<int32>(CurrentPhase), static_cast<int32>(NewPhase));

    EPCGGenerationPhase PreviousPhase = CurrentPhase;
    CurrentPhase = NewPhase;

    // Broadcast evento de mudança de fase
    if (OnPhaseChanged.IsBound())
    {
        OnPhaseChanged.Broadcast(PreviousPhase, NewPhase);
    }

    // Configurar PCG para a nova fase
    ConfigurePCGNodes();

    // Log da mudança
    FString PhaseName = GetPhaseDisplayName(NewPhase);
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Fase atual: %s"), *PhaseName);
}

FString AProceduralMapGenerator::GetPhaseDisplayName(EPCGGenerationPhase Phase) const
{
    switch (Phase)
    {
        case EPCGGenerationPhase::None: return TEXT("Nenhuma");
        case EPCGGenerationPhase::Initialization: return TEXT("Inicialização");
        case EPCGGenerationPhase::Terrain: return TEXT("Terreno");
        case EPCGGenerationPhase::Lanes: return TEXT("Lanes");
        case EPCGGenerationPhase::Objectives: return TEXT("Objetivos");
        case EPCGGenerationPhase::Walls: return TEXT("Muros");
        case EPCGGenerationPhase::River: return TEXT("Rio");
        case EPCGGenerationPhase::Vegetation: return TEXT("Vegetação");
        case EPCGGenerationPhase::Props: return TEXT("Props");
        case EPCGGenerationPhase::Lighting: return TEXT("Iluminação");
        case EPCGGenerationPhase::Finalization: return TEXT("Finalização");
        case EPCGGenerationPhase::Completed: return TEXT("Concluído");
        default: return TEXT("Desconhecida");
    }
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES AUXILIARES ROBUSTAS =====

void AProceduralMapGenerator::GenerateLaneConnections(const TArray<FVector>& FromLane, const TArray<FVector>& ToLane)
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Gerando conexões robustas entre lanes"));

    if (FromLane.Num() == 0 || ToLane.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: Lanes vazias para conexão"));
        return;
    }

    // Algoritmo robusto de conexão usando pontos mais próximos
    const int32 NumConnections = 5; // 5 conexões por par de lanes
    const float ConnectionSpacing = 1.0f / (NumConnections + 1);

    TArray<FVector> ConnectionPoints;
    ConnectionPoints.Reserve(NumConnections * 2);

    for (int32 i = 1; i <= NumConnections; i++)
    {
        float Alpha = i * ConnectionSpacing;

        // Interpolar pontos nas lanes
        int32 FromIndex = FMath::FloorToInt(Alpha * (FromLane.Num() - 1));
        int32 ToIndex = FMath::FloorToInt(Alpha * (ToLane.Num() - 1));

        FromIndex = FMath::Clamp(FromIndex, 0, FromLane.Num() - 1);
        ToIndex = FMath::Clamp(ToIndex, 0, ToLane.Num() - 1);

        FVector FromPoint = FromLane[FromIndex];
        FVector ToPoint = ToLane[ToIndex];

        // Criar pontos de conexão intermediários usando curva Bézier
        FVector ControlPoint1 = FromPoint + FVector(0.0f, (ToPoint.Y - FromPoint.Y) * 0.3f, 0.0f);
        FVector ControlPoint2 = ToPoint + FVector(0.0f, (FromPoint.Y - ToPoint.Y) * 0.3f, 0.0f);

        // Gerar pontos da curva Bézier cúbica
        const int32 CurvePoints = 10;
        for (int32 j = 0; j <= CurvePoints; j++)
        {
            float t = static_cast<float>(j) / CurvePoints;

            // Fórmula da curva Bézier cúbica
            FVector BezierPoint =
                FMath::Pow(1 - t, 3) * FromPoint +
                3 * FMath::Pow(1 - t, 2) * t * ControlPoint1 +
                3 * (1 - t) * FMath::Pow(t, 2) * ControlPoint2 +
                FMath::Pow(t, 3) * ToPoint;

            ConnectionPoints.Add(BezierPoint);
        }
    }

    // Configurar conexões no LaneManager se disponível
    if (LaneManager && IsValid(LaneManager))
    {
        // Usar reflexão para chamar função de configuração
        UFunction* ConfigureConnectionsFunc = LaneManager->FindFunction(TEXT("ConfigureLaneConnections"));
        if (ConfigureConnectionsFunc)
        {
            struct FConfigureConnectionsParams
            {
                TArray<FVector> Points;
                FString ConnectionName;
            };

            FConfigureConnectionsParams Params;
            Params.Points = ConnectionPoints;
            Params.ConnectionName = FString::Printf(TEXT("Connection_%d"), ConnectionPoints.Num());

            LaneManager->ProcessEvent(ConfigureConnectionsFunc, &Params);

            UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Conexão configurada com %d pontos"), ConnectionPoints.Num());
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AProceduralMapGenerator: Função ConfigureLaneConnections não encontrada"));
        }
    }

    // Armazenar pontos de conexão para debug
    for (const FVector& Point : ConnectionPoints)
    {
        if (GetWorld() && bShowDebugLines)
        {
            DrawDebugSphere(GetWorld(), Point, 25.0f, 8, FColor::Orange, false, 10.0f);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Conexão entre lanes gerada com %d pontos"), ConnectionPoints.Num());
}

void AProceduralMapGenerator::GenerateDefensiveWalls(const FVector& BaseCenter, float Radius, TArray<FVector>& OutWallPositions)
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Gerando muros defensivos robustos ao redor de %s"), *BaseCenter.ToString());

    // Configuração robusta de muros defensivos
    const int32 NumWallSegments = 12; // 12 segmentos para cobertura completa
    const float WallHeight = 400.0f; // 4 metros de altura
    const float WallThickness = 100.0f; // 1 metro de espessura
    const float AngleStep = 360.0f / NumWallSegments;

    // Gerar muros em formação circular
    for (int32 i = 0; i < NumWallSegments; i++)
    {
        float Angle = i * AngleStep;
        float RadianAngle = FMath::DegreesToRadians(Angle);

        // Posição do muro
        FVector WallOffset = FVector(
            FMath::Cos(RadianAngle) * Radius,
            FMath::Sin(RadianAngle) * Radius,
            WallHeight * 0.5f
        );

        FVector WallPosition = BaseCenter + WallOffset;
        OutWallPositions.Add(WallPosition);

        // Gerar muros intermediários para cobertura completa
        if (i < NumWallSegments - 1)
        {
            float NextAngle = (i + 1) * AngleStep;
            float NextRadianAngle = FMath::DegreesToRadians(NextAngle);

            FVector NextWallOffset = FVector(
                FMath::Cos(NextRadianAngle) * Radius,
                FMath::Sin(NextRadianAngle) * Radius,
                WallHeight * 0.5f
            );

            // Muro intermediário
            FVector IntermediatePosition = BaseCenter + (WallOffset + NextWallOffset) * 0.5f;
            IntermediatePosition.Z = WallHeight * 0.5f;
            OutWallPositions.Add(IntermediatePosition);
        }
    }

    // Gerar torres de canto para defesa adicional
    const int32 NumCornerTowers = 4;
    const float TowerRadius = Radius * 1.2f; // 20% mais longe que os muros
    const float TowerHeight = WallHeight * 1.5f; // 50% mais altas

    for (int32 i = 0; i < NumCornerTowers; i++)
    {
        float Angle = i * 90.0f; // Torres nos cantos (90 graus de separação)
        float RadianAngle = FMath::DegreesToRadians(Angle);

        FVector TowerOffset = FVector(
            FMath::Cos(RadianAngle) * TowerRadius,
            FMath::Sin(RadianAngle) * TowerRadius,
            TowerHeight * 0.5f
        );

        FVector TowerPosition = BaseCenter + TowerOffset;
        OutWallPositions.Add(TowerPosition);
    }

    // Debug visualization
    if (GetWorld() && bShowDebugLines)
    {
        for (const FVector& WallPos : OutWallPositions)
        {
            DrawDebugBox(GetWorld(), WallPos, FVector(WallThickness * 0.5f, WallThickness * 0.5f, WallHeight * 0.5f),
                FColor::Red, false, 10.0f, 0, 3.0f);
        }

        // Desenhar círculo de defesa
        DrawDebugCircle(GetWorld(), BaseCenter, Radius, 32, FColor::Yellow, false, 10.0f, 0, 5.0f,
            FVector(1, 0, 0), FVector(0, 1, 0));
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: %d muros defensivos gerados ao redor da base"), OutWallPositions.Num());
}

void AProceduralMapGenerator::ConfigureTerrainPCGNodes(UPCGGraph* PCGGraph)
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Configurando nós PCG para terreno usando APIs modernas UE 5.6"));

    if (!PCGGraph || !IsValid(PCGGraph))
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: PCGGraph inválido para configuração de terreno"));
        return;
    }

    // Configurar nós de geração de terreno usando APIs modernas
    TArray<UPCGNode*> TerrainNodes = PCGGraph->GetNodes();

    for (UPCGNode* Node : TerrainNodes)
    {
        if (!Node || !IsValid(Node))
            continue;

        // Configurar nós de densidade de pontos
        if (Node->GetNodeTitle(EPCGNodeTitleType::FullTitle).ToString().Contains(TEXT("PointSampler")))
        {
            // Usar reflexão para configurar propriedades
            if (UObject* Settings = Node->GetSettings())
            {
                // Configurar densidade baseada no tipo de terreno
                if (FFloatProperty* DensityProp = FindFProperty<FFloatProperty>(Settings->GetClass(), TEXT("PointsPerSquaredMeter")))
                {
                    float TerrainDensity = 0.1f; // 1 ponto por 10m²
                    DensityProp->SetPropertyValue_InContainer(Settings, TerrainDensity);

                    UE_LOG(LogTemp, VeryVerbose, TEXT("AProceduralMapGenerator: Densidade de terreno configurada: %.3f"), TerrainDensity);
                }

                // Configurar bounds de geração
                if (FStructProperty* BoundsProp = FindFProperty<FStructProperty>(Settings->GetClass(), TEXT("BoundsMin")))
                {
                    FVector BoundsMin = FVector(-MapBounds.X * 0.5f, -MapBounds.Y * 0.5f, -100.0f);
                    BoundsProp->SetValue_InContainer(Settings, &BoundsMin);
                }

                if (FStructProperty* BoundsMaxProp = FindFProperty<FStructProperty>(Settings->GetClass(), TEXT("BoundsMax")))
                {
                    FVector BoundsMax = FVector(MapBounds.X * 0.5f, MapBounds.Y * 0.5f, 100.0f);
                    BoundsMaxProp->SetValue_InContainer(Settings, &BoundsMax);
                }
            }
        }

        // Configurar nós de mesh spawning
        else if (Node->GetNodeTitle(EPCGNodeTitleType::FullTitle).ToString().Contains(TEXT("StaticMeshSpawner")))
        {
            if (UObject* Settings = Node->GetSettings())
            {
                // Configurar meshes de terreno
                if (TerrainMeshes.Num() > 0)
                {
                    if (FObjectProperty* MeshProp = FindFProperty<FObjectProperty>(Settings->GetClass(), TEXT("StaticMesh")))
                    {
                        // Usar mesh aleatório da lista
                        int32 RandomIndex = FMath::RandRange(0, TerrainMeshes.Num() - 1);
                        UStaticMesh* SelectedMesh = TerrainMeshes[RandomIndex];

                        MeshProp->SetObjectPropertyValue_InContainer(Settings, SelectedMesh);

                        UE_LOG(LogTemp, VeryVerbose, TEXT("AProceduralMapGenerator: Mesh de terreno configurado: %s"),
                            SelectedMesh ? *SelectedMesh->GetName() : TEXT("NULL"));
                    }
                }

                // Configurar escala aleatória
                if (FStructProperty* ScaleProp = FindFProperty<FStructProperty>(Settings->GetClass(), TEXT("Scale")))
                {
                    FVector RandomScale = FVector(
                        FMath::RandRange(0.8f, 1.2f),
                        FMath::RandRange(0.8f, 1.2f),
                        FMath::RandRange(0.9f, 1.1f)
                    );
                    ScaleProp->SetValue_InContainer(Settings, &RandomScale);
                }
            }
        }

        // Configurar nós de filtro
        else if (Node->GetNodeTitle(EPCGNodeTitleType::FullTitle).ToString().Contains(TEXT("Filter")))
        {
            if (UObject* Settings = Node->GetSettings())
            {
                // Configurar filtros baseados na inclinação do terreno
                if (FFloatProperty* SlopeProp = FindFProperty<FFloatProperty>(Settings->GetClass(), TEXT("MaxSlope")))
                {
                    float MaxSlope = 30.0f; // Máximo 30 graus de inclinação
                    SlopeProp->SetPropertyValue_InContainer(Settings, MaxSlope);
                }

                // Configurar filtros de altura
                if (FFloatProperty* MinHeightProp = FindFProperty<FFloatProperty>(Settings->GetClass(), TEXT("MinHeight")))
                {
                    float MinHeight = -50.0f; // Mínimo 50cm abaixo do nível do mar
                    MinHeightProp->SetPropertyValue_InContainer(Settings, MinHeight);
                }

                if (FFloatProperty* MaxHeightProp = FindFProperty<FFloatProperty>(Settings->GetClass(), TEXT("MaxHeight")))
                {
                    float MaxHeight = 500.0f; // Máximo 5 metros acima do nível do mar
                    MaxHeightProp->SetPropertyValue_InContainer(Settings, MaxHeight);
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: %d nós PCG de terreno configurados"), TerrainNodes.Num());
}

void AProceduralMapGenerator::ConfigureVegetationPCGNodes(UPCGGraph* PCGGraph)
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Configurando nós PCG para vegetação usando APIs modernas UE 5.6"));

    if (!PCGGraph || !IsValid(PCGGraph))
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: PCGGraph inválido para configuração de vegetação"));
        return;
    }

    TArray<UPCGNode*> VegetationNodes = PCGGraph->GetNodes();

    for (UPCGNode* Node : VegetationNodes)
    {
        if (!Node || !IsValid(Node))
            continue;

        // Configurar nós de árvores
        if (Node->GetNodeTitle(EPCGNodeTitleType::FullTitle).ToString().Contains(TEXT("Tree")) ||
            Node->GetNodeTitle(EPCGNodeTitleType::FullTitle).ToString().Contains(TEXT("Vegetation")))
        {
            if (UObject* Settings = Node->GetSettings())
            {
                // Configurar densidade de vegetação
                if (FFloatProperty* DensityProp = FindFProperty<FFloatProperty>(Settings->GetClass(), TEXT("Density")))
                {
                    float VegDensity = VegetationConfig.TreeDensity * SpawnDensity;
                    DensityProp->SetPropertyValue_InContainer(Settings, VegDensity);
                }

                // Configurar meshes de vegetação
                if (VegetationMeshes.Num() > 0)
                {
                    if (FArrayProperty* MeshArrayProp = FindFProperty<FArrayProperty>(Settings->GetClass(), TEXT("Meshes")))
                    {
                        // Configurar array de meshes usando APIs modernas
                        FScriptArrayHelper ArrayHelper(MeshArrayProp, MeshArrayProp->ContainerPtrToValuePtr<void>(Settings));
                        ArrayHelper.EmptyAndAddValues(VegetationMeshes.Num());

                        for (int32 i = 0; i < VegetationMeshes.Num(); i++)
                        {
                            if (UStaticMesh* VegMesh = VegetationMeshes[i])
                            {
                                // Usar GetRawPtr para acessar o elemento do array
                                void* ElementPtr = ArrayHelper.GetRawPtr(i);
                                if (ElementPtr)
                                {
                                    // Copiar o mesh para o elemento do array
                                    FMemory::Memcpy(ElementPtr, &VegMesh, sizeof(UStaticMesh*));
                                }
                            }
                        }

                        UE_LOG(LogTemp, VeryVerbose, TEXT("AProceduralMapGenerator: %d meshes de vegetação configurados"), VegetationMeshes.Num());
                    }
                }

                // Configurar variação de escala
                if (FStructProperty* ScaleMinProp = FindFProperty<FStructProperty>(Settings->GetClass(), TEXT("ScaleMin")))
                {
                    FVector ScaleMin = FVector(0.7f, 0.7f, 0.8f);
                    ScaleMinProp->SetValue_InContainer(Settings, &ScaleMin);
                }

                if (FStructProperty* ScaleMaxProp = FindFProperty<FStructProperty>(Settings->GetClass(), TEXT("ScaleMax")))
                {
                    FVector ScaleMax = FVector(1.3f, 1.3f, 1.2f);
                    ScaleMaxProp->SetValue_InContainer(Settings, &ScaleMax);
                }

                // Configurar rotação aleatória
                if (FBoolProperty* RandomRotProp = FindFProperty<FBoolProperty>(Settings->GetClass(), TEXT("bRandomizeRotation")))
                {
                    RandomRotProp->SetPropertyValue_InContainer(Settings, true);
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Nós PCG de vegetação configurados"));
}

void AProceduralMapGenerator::ConfigurePropsPCGNodes(UPCGGraph* PCGGraph)
{
    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Configurando nós PCG para props usando APIs modernas UE 5.6"));

    if (!PCGGraph || !IsValid(PCGGraph))
    {
        UE_LOG(LogTemp, Error, TEXT("AProceduralMapGenerator: PCGGraph inválido para configuração de props"));
        return;
    }

    TArray<UPCGNode*> PropNodes = PCGGraph->GetNodes();

    for (UPCGNode* Node : PropNodes)
    {
        if (!Node || !IsValid(Node))
            continue;

        // Configurar nós de props
        if (Node->GetNodeTitle(EPCGNodeTitleType::FullTitle).ToString().Contains(TEXT("Prop")) ||
            Node->GetNodeTitle(EPCGNodeTitleType::FullTitle).ToString().Contains(TEXT("Decoration")))
        {
            if (UObject* Settings = Node->GetSettings())
            {
                // Configurar densidade de props
                if (FFloatProperty* DensityProp = FindFProperty<FFloatProperty>(Settings->GetClass(), TEXT("SpawnRate")))
                {
                    float PropDensity = SpawnDensity * 0.3f; // 30% da densidade base
                    DensityProp->SetPropertyValue_InContainer(Settings, PropDensity);
                }

                // Configurar meshes de props
                if (PropMeshes.Num() > 0)
                {
                    if (FObjectProperty* MeshProp = FindFProperty<FObjectProperty>(Settings->GetClass(), TEXT("StaticMesh")))
                    {
                        // Selecionar mesh aleatório
                        int32 RandomIndex = FMath::RandRange(0, PropMeshes.Num() - 1);
                        UStaticMesh* SelectedPropMesh = PropMeshes[RandomIndex];

                        MeshProp->SetObjectPropertyValue_InContainer(Settings, SelectedPropMesh);

                        UE_LOG(LogTemp, VeryVerbose, TEXT("AProceduralMapGenerator: Mesh de prop configurado: %s"),
                            SelectedPropMesh ? *SelectedPropMesh->GetName() : TEXT("NULL"));
                    }
                }

                // Configurar espaçamento mínimo entre props
                if (FFloatProperty* SpacingProp = FindFProperty<FFloatProperty>(Settings->GetClass(), TEXT("MinDistance")))
                {
                    float MinSpacing = 300.0f; // Mínimo 3 metros entre props
                    SpacingProp->SetPropertyValue_InContainer(Settings, MinSpacing);
                }

                // Configurar alinhamento com superfície
                if (FBoolProperty* AlignProp = FindFProperty<FBoolProperty>(Settings->GetClass(), TEXT("bAlignToSurface")))
                {
                    AlignProp->SetPropertyValue_InContainer(Settings, true);
                }

                // Configurar offset de altura
                if (FFloatProperty* HeightOffsetProp = FindFProperty<FFloatProperty>(Settings->GetClass(), TEXT("HeightOffset")))
                {
                    float HeightOffset = 0.0f; // Sem offset por padrão
                    HeightOffsetProp->SetPropertyValue_InContainer(Settings, HeightOffset);
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AProceduralMapGenerator: Nós PCG de props configurados"));
}

// ===== FIM DA IMPLEMENTAÇÃO =====
