// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AProceduralMapGenerator.h"

#ifdef AURA_AProceduralMapGenerator_generated_h
#error "AProceduralMapGenerator.generated.h already included, missing '#pragma once' in AProceduralMapGenerator.h"
#endif
#define AURA_AProceduralMapGenerator_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EPCGGenerationPhase : uint8;
struct FBridgeData;
struct FObjectiveData;
struct FPCGGenerationConfig;
struct FPCGGenerationProgress;
struct FPCGSpawnRule;
struct FPropData;
struct FWallData;

// ********** Begin ScriptStruct FGenerationProgress ***********************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_83_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FGenerationProgress_Statics; \
	static class UScriptStruct* StaticStruct();


struct FGenerationProgress;
// ********** End ScriptStruct FGenerationProgress *************************************************

// ********** Begin ScriptStruct FExclusionZone ****************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_126_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FExclusionZone_Statics; \
	static class UScriptStruct* StaticStruct();


struct FExclusionZone;
// ********** End ScriptStruct FExclusionZone ******************************************************

// ********** Begin ScriptStruct FTerrainGenerationConfig ******************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_153_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTerrainGenerationConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTerrainGenerationConfig;
// ********** End ScriptStruct FTerrainGenerationConfig ********************************************

// ********** Begin ScriptStruct FVegetationConfig *************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_180_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FVegetationConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FVegetationConfig;
// ********** End ScriptStruct FVegetationConfig ***************************************************

// ********** Begin ScriptStruct FPCGAssetReference ************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_233_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGAssetReference_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGAssetReference;
// ********** End ScriptStruct FPCGAssetReference **************************************************

// ********** Begin ScriptStruct FPCGSpawnRule *****************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_282_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGSpawnRule_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGSpawnRule;
// ********** End ScriptStruct FPCGSpawnRule *******************************************************

// ********** Begin ScriptStruct FPCGGenerationConfig **********************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_341_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGGenerationConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGGenerationConfig;
// ********** End ScriptStruct FPCGGenerationConfig ************************************************

// ********** Begin ScriptStruct FPCGGenerationProgress ********************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_391_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGGenerationProgress_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGGenerationProgress;
// ********** End ScriptStruct FPCGGenerationProgress **********************************************

// ********** Begin ScriptStruct FLaneGenerationParams *********************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_439_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLaneGenerationParams_Statics; \
	static class UScriptStruct* StaticStruct();


struct FLaneGenerationParams;
// ********** End ScriptStruct FLaneGenerationParams ***********************************************

// ********** Begin ScriptStruct FObjectiveData ****************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_465_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FObjectiveData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FObjectiveData;
// ********** End ScriptStruct FObjectiveData ******************************************************

// ********** Begin ScriptStruct FWallData *********************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_499_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWallData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FWallData;
// ********** End ScriptStruct FWallData ***********************************************************

// ********** Begin ScriptStruct FPropData *********************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_535_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPropData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPropData;
// ********** End ScriptStruct FPropData ***********************************************************

// ********** Begin Delegate FOnPCGGenerationCompleted *********************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_569_DELEGATE \
AURA_API void FOnPCGGenerationCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnPCGGenerationCompleted);


// ********** End Delegate FOnPCGGenerationCompleted ***********************************************

// ********** Begin Delegate FOnPCGGenerationFailed ************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_570_DELEGATE \
AURA_API void FOnPCGGenerationFailed_DelegateWrapper(const FMulticastScriptDelegate& OnPCGGenerationFailed, const FString& ErrorMessage);


// ********** End Delegate FOnPCGGenerationFailed **************************************************

// ********** Begin Delegate FOnPCGAssetGenerated **************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_571_DELEGATE \
AURA_API void FOnPCGAssetGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnPCGAssetGenerated, const FString& AssetName, int32 Count);


// ********** End Delegate FOnPCGAssetGenerated ****************************************************

// ********** Begin Delegate FOnObjectiveConfigured ************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_574_DELEGATE \
AURA_API void FOnObjectiveConfigured_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveConfigured, FObjectiveData const& ObjectiveData);


// ********** End Delegate FOnObjectiveConfigured **************************************************

// ********** Begin Delegate FOnWallConfigured *****************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_575_DELEGATE \
AURA_API void FOnWallConfigured_DelegateWrapper(const FMulticastScriptDelegate& OnWallConfigured, FWallData const& WallData);


// ********** End Delegate FOnWallConfigured *******************************************************

// ********** Begin Delegate FOnBridgeConfigured ***************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_576_DELEGATE \
AURA_API void FOnBridgeConfigured_DelegateWrapper(const FMulticastScriptDelegate& OnBridgeConfigured, FBridgeData const& BridgeData);


// ********** End Delegate FOnBridgeConfigured *****************************************************

// ********** Begin Delegate FOnPropConfigured *****************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_577_DELEGATE \
AURA_API void FOnPropConfigured_DelegateWrapper(const FMulticastScriptDelegate& OnPropConfigured, FPropData const& PropData);


// ********** End Delegate FOnPropConfigured *******************************************************

// ********** Begin Delegate FOnPhaseChanged *******************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_578_DELEGATE \
AURA_API void FOnPhaseChanged_DelegateWrapper(const FMulticastScriptDelegate& OnPhaseChanged, EPCGGenerationPhase OldPhase, EPCGGenerationPhase NewPhase);


// ********** End Delegate FOnPhaseChanged *********************************************************

// ********** Begin Delegate FOnProgressUpdated ****************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_579_DELEGATE \
AURA_API void FOnProgressUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnProgressUpdated, const FString& PhaseName, float Progress, float EstimatedTime);


// ********** End Delegate FOnProgressUpdated ******************************************************

// ********** Begin Class AProceduralMapGenerator **************************************************
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_586_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execUpdatePCGFromGeometry); \
	DECLARE_FUNCTION(execSynchronizeWithMapManager); \
	DECLARE_FUNCTION(execValidateManagerReferences); \
	DECLARE_FUNCTION(execInitializeManagers); \
	DECLARE_FUNCTION(execGetGeneratedAssetCount); \
	DECLARE_FUNCTION(execGetOverallProgress); \
	DECLARE_FUNCTION(execGetCurrentPhase); \
	DECLARE_FUNCTION(execIsPaused); \
	DECLARE_FUNCTION(execIsGenerating); \
	DECLARE_FUNCTION(execGetGenerationProgress); \
	DECLARE_FUNCTION(execClearSpawnRules); \
	DECLARE_FUNCTION(execRemoveSpawnRule); \
	DECLARE_FUNCTION(execAddSpawnRule); \
	DECLARE_FUNCTION(execGetGenerationConfig); \
	DECLARE_FUNCTION(execSetGenerationConfig); \
	DECLARE_FUNCTION(execClearGeneration); \
	DECLARE_FUNCTION(execRestartGeneration); \
	DECLARE_FUNCTION(execResumeGeneration); \
	DECLARE_FUNCTION(execPauseGeneration); \
	DECLARE_FUNCTION(execStopGeneration); \
	DECLARE_FUNCTION(execStartGeneration);


AURA_API UClass* Z_Construct_UClass_AProceduralMapGenerator_NoRegister();

#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_586_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAProceduralMapGenerator(); \
	friend struct Z_Construct_UClass_AProceduralMapGenerator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_AProceduralMapGenerator_NoRegister(); \
public: \
	DECLARE_CLASS2(AProceduralMapGenerator, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_AProceduralMapGenerator_NoRegister) \
	DECLARE_SERIALIZER(AProceduralMapGenerator)


#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_586_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AProceduralMapGenerator(AProceduralMapGenerator&&) = delete; \
	AProceduralMapGenerator(const AProceduralMapGenerator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AProceduralMapGenerator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AProceduralMapGenerator); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AProceduralMapGenerator) \
	NO_API virtual ~AProceduralMapGenerator();


#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_583_PROLOG
#define FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_586_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_586_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_586_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h_586_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AProceduralMapGenerator;

// ********** End Class AProceduralMapGenerator ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_AProceduralMapGenerator_h

// ********** Begin Enum EPCGGenerationPhase *******************************************************
#define FOREACH_ENUM_EPCGGENERATIONPHASE(op) \
	op(EPCGGenerationPhase::None) \
	op(EPCGGenerationPhase::Initialization) \
	op(EPCGGenerationPhase::Terrain) \
	op(EPCGGenerationPhase::Lanes) \
	op(EPCGGenerationPhase::Objectives) \
	op(EPCGGenerationPhase::Walls) \
	op(EPCGGenerationPhase::River) \
	op(EPCGGenerationPhase::Vegetation) \
	op(EPCGGenerationPhase::Props) \
	op(EPCGGenerationPhase::Lighting) \
	op(EPCGGenerationPhase::Finalization) \
	op(EPCGGenerationPhase::Completed) 

enum class EPCGGenerationPhase : uint8;
template<> struct TIsUEnumClass<EPCGGenerationPhase> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGGenerationPhase>();
// ********** End Enum EPCGGenerationPhase *********************************************************

// ********** Begin Enum EObjectiveType ************************************************************
#define FOREACH_ENUM_EOBJECTIVETYPE(op) \
	op(EObjectiveType::Nexus) \
	op(EObjectiveType::Base) \
	op(EObjectiveType::Tower) \
	op(EObjectiveType::Outpost) 

enum class EObjectiveType : uint8;
template<> struct TIsUEnumClass<EObjectiveType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EObjectiveType>();
// ********** End Enum EObjectiveType **************************************************************

// ********** Begin Enum EPropType *****************************************************************
#define FOREACH_ENUM_EPROPTYPE(op) \
	op(EPropType::Rock) \
	op(EPropType::Crate) \
	op(EPropType::Barrel) \
	op(EPropType::Pillar) \
	op(EPropType::Statue) \
	op(EPropType::Barricade) \
	op(EPropType::WatchTower) 

enum class EPropType : uint8;
template<> struct TIsUEnumClass<EPropType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPropType>();
// ********** End Enum EPropType *******************************************************************

// ********** Begin Enum EPCGMapAssetType **********************************************************
#define FOREACH_ENUM_EPCGMAPASSETTYPE(op) \
	op(EPCGMapAssetType::StaticMesh) \
	op(EPCGMapAssetType::SkeletalMesh) \
	op(EPCGMapAssetType::Material) \
	op(EPCGMapAssetType::Texture) \
	op(EPCGMapAssetType::Landscape) \
	op(EPCGMapAssetType::Foliage) \
	op(EPCGMapAssetType::Particle) \
	op(EPCGMapAssetType::Audio) \
	op(EPCGMapAssetType::Blueprint) 

enum class EPCGMapAssetType : uint8;
template<> struct TIsUEnumClass<EPCGMapAssetType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGMapAssetType>();
// ********** End Enum EPCGMapAssetType ************************************************************

// ********** Begin Enum EPCGDistributionType ******************************************************
#define FOREACH_ENUM_EPCGDISTRIBUTIONTYPE(op) \
	op(EPCGDistributionType::Random) \
	op(EPCGDistributionType::Grid) \
	op(EPCGDistributionType::Poisson) \
	op(EPCGDistributionType::Geometric) \
	op(EPCGDistributionType::Weighted) \
	op(EPCGDistributionType::Clustered) 

enum class EPCGDistributionType : uint8;
template<> struct TIsUEnumClass<EPCGDistributionType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGDistributionType>();
// ********** End Enum EPCGDistributionType ********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
