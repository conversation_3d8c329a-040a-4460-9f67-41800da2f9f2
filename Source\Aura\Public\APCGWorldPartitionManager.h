#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/LoaderAdapter/LoaderAdapterShape.h"
#include "WorldPartition/WorldPartitionStreamingPolicy.h"
// UE5.6 PCG API includes - using correct paths
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGSubsystem.h"

#include "Async/AsyncWork.h"
#include "HAL/ThreadSafeBool.h"
#include "Containers/Queue.h"
#include "APCGWorldPartitionManager.generated.h"

// Forward Declarations
class UWorldPartition;
class UDataLayerManager;
class UPCGComponent;
class AProceduralMapGenerator;

// Enums for World Partition Management
UENUM(BlueprintType)
enum class EPCGWorldPartitionState : uint8
{
    Uninitialized   UMETA(DisplayName = "Uninitialized"),
    Initializing    UMETA(DisplayName = "Initializing"),
    Active          UMETA(DisplayName = "Active"),
    Streaming       UMETA(DisplayName = "Streaming"),
    Paused          UMETA(DisplayName = "Paused"),
    Error           UMETA(DisplayName = "Error")
};

UENUM(BlueprintType)
enum class EPCGStreamingPriority : uint8
{
    Low             UMETA(DisplayName = "Low Priority"),
    Medium          UMETA(DisplayName = "Medium Priority"),
    High            UMETA(DisplayName = "High Priority"),
    Critical        UMETA(DisplayName = "Critical Priority")
};

UENUM(BlueprintType)
enum class EPCGDataLayerType : uint8
{
    Terrain         UMETA(DisplayName = "Terrain Layer"),
    Vegetation      UMETA(DisplayName = "Vegetation Layer"),
    Structures      UMETA(DisplayName = "Structures Layer"),
    Interactive     UMETA(DisplayName = "Interactive Layer"),
    Lighting        UMETA(DisplayName = "Lighting Layer"),
    Audio           UMETA(DisplayName = "Audio Layer")
};

// CORRIGIDO: Estrutura para células de World Partition
USTRUCT(BlueprintType)
struct AURA_API FPCGWorldPartitionCell
{
    GENERATED_BODY()

    // Coordenadas da célula no grid
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    FIntPoint Coordinates = FIntPoint::ZeroValue;

    // Bounds da célula no mundo
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    FBox WorldBounds = FBox(ForceInit);

    // Estado da célula
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    bool bIsLoaded = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    bool bIsGenerating = false;

    // Prioridade de carregamento
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    float LoadingPriority = 0.0f;

    // Timestamp do último acesso
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    double LastAccessTime = 0.0;

    FPCGWorldPartitionCell()
    {
        Coordinates = FIntPoint::ZeroValue;
        WorldBounds = FBox(ForceInit);
        bIsLoaded = false;
        bIsGenerating = false;
        LoadingPriority = 0.0f;
        LastAccessTime = 0.0;
    }
};

// Structures for World Partition Configuration
USTRUCT(BlueprintType)
struct AURA_API FPCGWorldPartitionConfig
{
    GENERATED_BODY()

    // Grid size for world partition cells (in UE units, 1 UU = 1 cm)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition")
    FIntPoint GridSize = FIntPoint(25600, 25600); // 256m x 256m cells

    // Loading radius around player (in UE units)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float LoadingRadius = 51200.0f; // 512m radius

    // Unloading radius (should be larger than loading radius)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float UnloadingRadius = 76800.0f; // 768m radius

    // Maximum number of concurrent streaming operations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxConcurrentStreams = 4;

    // Enable hierarchical LOD for distant cells
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bEnableHLOD = true;

    // HLOD transition distance
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float HLODTransitionDistance = 102400.0f; // 1024m

    FPCGWorldPartitionConfig()
    {
        GridSize = FIntPoint(25600, 25600);
        LoadingRadius = 51200.0f;
        UnloadingRadius = 76800.0f;
        MaxConcurrentStreams = 4;
        bEnableHLOD = true;
        HLODTransitionDistance = 102400.0f;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGStreamingCell
{
    GENERATED_BODY()

    // Cell coordinates in the world partition grid
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    FIntPoint CellCoordinates = FIntPoint::ZeroValue;

    // World bounds of this cell
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell")
    FBox WorldBounds = FBox(ForceInit);

    // PCG components in this cell
    UPROPERTY(EditAnywhere, Category = "PCG")
    TArray<TWeakObjectPtr<UPCGComponent>> PCGComponents;

    // Data layers associated with this cell
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Layers")
    TArray<FName> DataLayers;

    // Streaming priority for this cell
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    EPCGStreamingPriority StreamingPriority = EPCGStreamingPriority::Medium;

    // Is this cell currently loaded?
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsLoaded = false;

    // Is this cell currently generating PCG content?
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsGenerating = false;

    // Generation progress (0.0 to 1.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    float GenerationProgress = 0.0f;

    FPCGStreamingCell()
    {
        CellCoordinates = FIntPoint::ZeroValue;
        WorldBounds = FBox(ForceInit);
        StreamingPriority = EPCGStreamingPriority::Medium;
        bIsLoaded = false;
        bIsGenerating = false;
        GenerationProgress = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGDataLayerConfig
{
    GENERATED_BODY()

    // Data layer name
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Layer")
    FName LayerName = NAME_None;

    // Data layer type
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Layer")
    EPCGDataLayerType LayerType = EPCGDataLayerType::Terrain;

    // PCG graph to use for this layer
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PCG")
    TSoftObjectPtr<UPCGGraph> PCGGraph;

    // Should this layer be loaded by default?
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    bool bLoadByDefault = true;

    // Streaming priority for this layer
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    EPCGStreamingPriority StreamingPriority = EPCGStreamingPriority::Medium;

    // Memory budget for this layer (in MB)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MemoryBudgetMB = 100.0f;

    FPCGDataLayerConfig()
    {
        LayerName = NAME_None;
        LayerType = EPCGDataLayerType::Terrain;
        bLoadByDefault = true;
        StreamingPriority = EPCGStreamingPriority::Medium;
        MemoryBudgetMB = 100.0f;
    }
};

/**
 * APCGWorldPartitionManager
 * 
 * Advanced World Partition manager for PCG content generation in large worlds.
 * Integrates with UE5.6's World Partition system to provide seamless streaming
 * of procedurally generated content across massive game worlds.
 * 
 * Features:
 * - Automatic cell-based content streaming
 * - Data layer management for different content types
 * - HLOD integration for distant content
 * - Memory-efficient PCG generation
 * - Multi-threaded streaming operations
 * - Integration with Nanite and Lumen systems
 */
UCLASS(BlueprintType, Blueprintable)
class AURA_API APCGWorldPartitionManager : public AActor
{
    GENERATED_BODY()

public:
    APCGWorldPartitionManager();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

public:
    // === Core World Partition Functions ===
    
    UFUNCTION(BlueprintCallable, Category = "PCG World Partition")
    bool InitializeWorldPartition();
    
    UFUNCTION(BlueprintCallable, Category = "PCG World Partition")
    void ShutdownWorldPartition();
    
    UFUNCTION(BlueprintCallable, Category = "PCG World Partition")
    bool SetWorldPartitionConfig(const FPCGWorldPartitionConfig& NewConfig);
    
    UFUNCTION(BlueprintCallable, Category = "PCG World Partition")
    FPCGWorldPartitionConfig GetWorldPartitionConfig() const { return WorldPartitionConfig; }
    
    // === Cell Management Functions ===
    
    UFUNCTION(BlueprintCallable, Category = "PCG Cells")
    bool CreateStreamingCell(const FIntPoint& CellCoordinates, const FPCGStreamingCell& CellConfig);
    
    UFUNCTION(BlueprintCallable, Category = "PCG Cells")
    bool RemoveStreamingCell(const FIntPoint& CellCoordinates);
    
    UFUNCTION(BlueprintCallable, Category = "PCG Cells")
    bool GetStreamingCell(const FIntPoint& CellCoordinates, FPCGStreamingCell& OutCell) const;
    
    UFUNCTION(BlueprintCallable, Category = "PCG Cells")
    TArray<FPCGStreamingCell> GetLoadedCells() const;
    
    UFUNCTION(BlueprintCallable, Category = "PCG Cells")
    TArray<FPCGStreamingCell> GetCellsInRadius(const FVector& WorldLocation, float Radius) const;
    
    // === Data Layer Management ===
    
    UFUNCTION(BlueprintCallable, Category = "Data Layers")
    bool CreateDataLayer(const FPCGDataLayerConfig& LayerConfig);
    
    UFUNCTION(BlueprintCallable, Category = "Data Layers")
    bool RemoveDataLayer(const FName& LayerName);
    
    UFUNCTION(BlueprintCallable, Category = "Data Layers")
    bool SetDataLayerState(const FName& LayerName, bool bShouldBeLoaded);
    
    UFUNCTION(BlueprintCallable, Category = "Data Layers")
    TArray<FPCGDataLayerConfig> GetDataLayers() const;
    
    // === Streaming Control ===
    
    UFUNCTION(BlueprintCallable, Category = "Streaming")
    void SetStreamingSource(const FVector& WorldLocation);
    
    UFUNCTION(BlueprintCallable, Category = "Streaming")
    void AddStreamingSource(const FVector& WorldLocation, float Priority = 1.0f);
    
    UFUNCTION(BlueprintCallable, Category = "Streaming")
    void RemoveStreamingSource(const FVector& WorldLocation);
    
    UFUNCTION(BlueprintCallable, Category = "Streaming")
    void ClearStreamingSources();
    
    // === PCG Generation Control ===
    
    UFUNCTION(BlueprintCallable, Category = "PCG Generation")
    bool StartCellGeneration(const FIntPoint& CellCoordinates);
    
    UFUNCTION(BlueprintCallable, Category = "PCG Generation")
    bool StopCellGeneration(const FIntPoint& CellCoordinates);
    
    UFUNCTION(BlueprintCallable, Category = "PCG Generation")
    void StartAllCellGeneration();
    
    UFUNCTION(BlueprintCallable, Category = "PCG Generation")
    void StopAllCellGeneration();
    
    // === State Query Functions ===
    
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "State")
    EPCGWorldPartitionState GetCurrentState() const { return CurrentState; }
    
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "State")
    bool IsInitialized() const { return CurrentState != EPCGWorldPartitionState::Uninitialized; }
    
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "State")
    int32 GetLoadedCellCount() const;
    
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "State")
    int32 GetGeneratingCellCount() const;
    
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "State")
    float GetOverallGenerationProgress() const;
    
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Performance")
    float GetMemoryUsageMB() const;
    
    // === Integration Functions ===
    
    UFUNCTION(BlueprintCallable, Category = "Integration")
    void SetProceduralMapGenerator(AProceduralMapGenerator* Generator);
    
    UFUNCTION(BlueprintCallable, Category = "Integration")
    bool IntegrateWithNanite();
    
    UFUNCTION(BlueprintCallable, Category = "Integration")
    bool IntegrateWithLumen();

protected:
    // === Core Properties ===
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FPCGWorldPartitionConfig WorldPartitionConfig;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TArray<FPCGDataLayerConfig> DataLayerConfigs;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    EPCGWorldPartitionState CurrentState;
    
    // === Subsystem References ===
    
    UPROPERTY()
    TObjectPtr<UWorldPartitionSubsystem> WorldPartitionSubsystem;
    
    UPROPERTY()
    TObjectPtr<UDataLayerManager> DataLayerManager;
    
    UPROPERTY()
    TObjectPtr<UPCGSubsystem> PCGSubsystem;
    
    // === Streaming Data ===
    
    UPROPERTY()
    TMap<FIntPoint, FPCGStreamingCell> StreamingCells;
    
    UPROPERTY()
    TArray<FVector> StreamingSources;
    
    UPROPERTY()
    TWeakObjectPtr<AProceduralMapGenerator> ProceduralMapGenerator;
    
    // === Threading and Performance ===

    FThreadSafeBool bIsStreamingActive;
    FThreadSafeBool bIsGenerationActive;

    // CORRIGIDO: Campos adicionais para UE 5.6
    bool bWorldPartitionEnabled;
    bool bDataLayersEnabled;
    bool bFastGeometryStreamingEnabled;

    // CORRIGIDO: Timers para performance monitoring
    float LastUpdateTime;
    float LastPerformanceUpdate;
    float LastMemoryCheck;

    // CORRIGIDO: Contadores para estatísticas
    int32 TotalCellsLoaded;
    int32 TotalCellsGenerated;
    float CurrentMemoryUsageMB;

    // CORRIGIDO: Containers adicionais
    TArray<FPCGWorldPartitionCell> ActiveCells;
    TArray<FPCGWorldPartitionCell> PendingCells;

    TQueue<FIntPoint> PendingCellLoads;
    TQueue<FIntPoint> PendingCellUnloads;
    
    // === Internal Functions ===

    void UpdateStreaming(float DeltaTime);
    void ProcessPendingCellOperations();
    void UpdateCellGeneration(float DeltaTime);

    bool LoadCell(const FIntPoint& CellCoordinates);
    bool UnloadCell(const FIntPoint& CellCoordinates);

    FIntPoint WorldLocationToCellCoordinates(const FVector& WorldLocation) const;
    FBox CellCoordinatesToWorldBounds(const FIntPoint& CellCoordinates) const;

    bool ShouldCellBeLoaded(const FIntPoint& CellCoordinates) const;
    float CalculateCellPriority(const FIntPoint& CellCoordinates) const;

    void InitializeSubsystems();
    void ValidateConfiguration();

    // CORRIGIDO: Funções adicionais para UE 5.6
    int32 GetActiveDataLayerCount() const;
    void CheckMemoryUsage();
    
    // === Event Handlers ===
    
    UFUNCTION()
    void OnCellLoaded(const FIntPoint& CellCoordinates);
    
    UFUNCTION()
    void OnCellUnloaded(const FIntPoint& CellCoordinates);
    
    UFUNCTION()
    void OnPCGGenerationComplete(UPCGComponent* PCGComponent);
};