// AMapManager.cpp - Implementação do controlador principal do sistema de geração procedural
// Unreal Engine 5.6 - APIs modernas
// 1 UU = 1 cm (Unreal Units)

#include "AMapManager.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "DrawDebugHelpers.h"
#include "Async/Async.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"

// UE 5.6 Modern APIs
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Async/ParallelFor.h"
#include "HAL/ThreadSafeCounter64.h"
#include "Engine/StreamableManager.h"
#include "Subsystems/WorldSubsystem.h"
#include "Math/UnrealMathUtility.h"
#include "Misc/Timespan.h"

AMapManager::AMapManager()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.TickInterval = 0.1f; // 10 FPS para otimização

    // Criar componente raiz
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    RootComponent = RootSceneComponent;

    // Inicializar configuração padrão
    MapConfig = FMapConfiguration();
    
    // Inicializar status
    GenerationProgress = FMapGenerationProgress();
    LastValidationResult = FMapValidationResult();
    bIsGenerating = false;
    bIsValidating = false;
    bStopGeneration = false;

    // Configurações padrão
    bAutoGenerateOnBeginPlay = false;
    bShowDebugInfo = false;

    // Inicializar ponteiros dos managers
    LaneManager = nullptr;
    BaronManager = nullptr;
    DragonManager = nullptr;
    WallManager = nullptr;
    RiverManager = nullptr;
    MinionManager = nullptr;
}

void AMapManager::BeginPlay()
{
    Super::BeginPlay();

    LogGenerationInfo(TEXT("AMapManager: Iniciando sistema de geração procedural"));

    // Inicializar managers
    InitializeManagers();

    // Configurar timer de atualização de progresso
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            ProgressUpdateTimerHandle,
            this,
            &AMapManager::UpdateGenerationProgress,
            0.1f, // Atualizar a cada 100ms
            true
        );
    }

    // Auto-gerar se configurado
    if (bAutoGenerateOnBeginPlay)
    {
        StartMapGeneration(MapConfig);
    }
}

void AMapManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Parar geração se estiver em andamento
    if (bIsGenerating)
    {
        StopMapGeneration();
    }

    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(GenerationTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(ValidationTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(ProgressUpdateTimerHandle);
    }

    Super::EndPlay(EndPlayReason);
}

void AMapManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Atualizar tempo decorrido se estiver gerando
    if (bIsGenerating)
    {
        GenerationProgress.ElapsedTime = (FDateTime::Now() - GenerationStartTime).GetTotalSeconds();
        
        // Verificar timeout
        if (GenerationProgress.ElapsedTime > MapConfig.GenerationTimeLimit)
        {
            HandleGenerationError(TEXT("Timeout: Geração excedeu o tempo limite"), GenerationProgress.CurrentPhase);
            StopMapGeneration();
        }
    }

    // Validação em tempo real
    if (MapConfig.bEnableRealTimeValidation && !bIsValidating)
    {
        PerformRealTimeValidation();
    }

    // Desenhar debug se habilitado
    if (bShowDebugInfo)
    {
        DrawDebugInfo();
    }
}

void AMapManager::InitializeManagers()
{
    if (!GetWorld())
    {
        LogGenerationInfo(TEXT("Erro: World não encontrado para inicializar managers"), true);
        return;
    }

    FActorSpawnParameters SpawnParams;
    SpawnParams.Owner = this;
    SpawnParams.Instigator = GetInstigator();
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;

    // Spawn Lane Manager
    if (!LaneManager)
    {
        LaneManager = GetWorld()->SpawnActor<ALaneManager>(ALaneManager::StaticClass(), GetActorTransform(), SpawnParams);
        if (LaneManager)
        {
            LaneManager->AttachToActor(this, FAttachmentTransformRules::KeepWorldTransform);
            LogGenerationInfo(TEXT("Lane Manager inicializado com sucesso"));
        }
        else
        {
            LogGenerationInfo(TEXT("Erro ao inicializar Lane Manager"), true);
        }
    }

    // Spawn Baron Manager
    if (!BaronManager)
    {
        BaronManager = GetWorld()->SpawnActor<ABaronAuracronManager>(ABaronAuracronManager::StaticClass(), GetActorTransform(), SpawnParams);
        if (BaronManager)
        {
            BaronManager->AttachToActor(this, FAttachmentTransformRules::KeepWorldTransform);
            LogGenerationInfo(TEXT("Baron Manager inicializado com sucesso"));
        }
        else
        {
            LogGenerationInfo(TEXT("Erro ao inicializar Baron Manager"), true);
        }
    }

    // Spawn Dragon Manager
    if (!DragonManager)
    {
        DragonManager = GetWorld()->SpawnActor<ADragonPrismalManager>(ADragonPrismalManager::StaticClass(), GetActorTransform(), SpawnParams);
        if (DragonManager)
        {
            DragonManager->AttachToActor(this, FAttachmentTransformRules::KeepWorldTransform);
            LogGenerationInfo(TEXT("Dragon Manager inicializado com sucesso"));
        }
        else
        {
            LogGenerationInfo(TEXT("Erro ao inicializar Dragon Manager"), true);
        }
    }

    // Spawn Wall Manager
    if (!WallManager)
    {
        WallManager = GetWorld()->SpawnActor<AWallCollisionManager>(AWallCollisionManager::StaticClass(), GetActorTransform(), SpawnParams);
        if (WallManager)
        {
            WallManager->AttachToActor(this, FAttachmentTransformRules::KeepWorldTransform);
            LogGenerationInfo(TEXT("Wall Manager inicializado com sucesso"));
        }
        else
        {
            LogGenerationInfo(TEXT("Erro ao inicializar Wall Manager"), true);
        }
    }

    // Spawn River Manager
    if (!RiverManager)
    {
        RiverManager = GetWorld()->SpawnActor<ARiverPrismalManager>(ARiverPrismalManager::StaticClass(), GetActorTransform(), SpawnParams);
        if (RiverManager)
        {
            RiverManager->AttachToActor(this, FAttachmentTransformRules::KeepWorldTransform);
            LogGenerationInfo(TEXT("River Manager inicializado com sucesso"));
        }
        else
        {
            LogGenerationInfo(TEXT("Erro ao inicializar River Manager"), true);
        }
    }

    // Spawn Minion Manager
    if (!MinionManager)
    {
        MinionManager = GetWorld()->SpawnActor<AMinionWaveManager>(AMinionWaveManager::StaticClass(), GetActorTransform(), SpawnParams);
        if (MinionManager)
        {
            MinionManager->AttachToActor(this, FAttachmentTransformRules::KeepWorldTransform);
            LogGenerationInfo(TEXT("Minion Manager inicializado com sucesso"));
        }
        else
        {
            LogGenerationInfo(TEXT("Erro ao inicializar Minion Manager"), true);
        }
    }
}

bool AMapManager::StartMapGeneration(const FMapConfiguration& Config)
{
    if (bIsGenerating)
    {
        LogGenerationInfo(TEXT("Geração já está em andamento"), true);
        return false;
    }

    if (!IsConfigurationValid(Config))
    {
        LogGenerationInfo(TEXT("Configuração inválida para geração do mapa"), true);
        return false;
    }

    // Atualizar configuração
    MapConfig = Config;
    
    // Configurar seed aleatório
    FMath::RandInit(MapConfig.RandomSeed);
    
    // Inicializar status de geração
    bIsGenerating = true;
    bStopGeneration = false;
    GenerationStartTime = FDateTime::Now();
    
    GenerationProgress = FMapGenerationProgress();
    GenerationProgress.CurrentPhase = EMapGenerationPhase::Initializing;
    GenerationProgress.CurrentTask = TEXT("Iniciando geração do mapa...");
    
    LogGenerationInfo(FString::Printf(TEXT("Iniciando geração do mapa com seed: %d"), MapConfig.RandomSeed));
    
    // Broadcast evento de início
    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::Initializing);
    
    // Iniciar primeira fase
    if (MapConfig.bUseAsyncGeneration)
    {
        // Geração assíncrona
        AsyncTask(ENamedThreads::GameThread, [this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
    else
    {
        // Geração síncrona
        ExecuteNextGenerationPhase();
    }
    
    return true;
}

void AMapManager::StopMapGeneration()
{
    if (!bIsGenerating)
    {
        return;
    }

    bStopGeneration = true;
    bIsGenerating = false;
    
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(GenerationTimerHandle);
    }
    
    GenerationProgress.CurrentPhase = EMapGenerationPhase::None;
    GenerationProgress.CurrentTask = TEXT("Geração interrompida");
    
    LogGenerationInfo(TEXT("Geração do mapa interrompida pelo usuário"));
    
    OnMapGenerationComplete.Broadcast(false);
}

void AMapManager::RestartMapGeneration()
{
    StopMapGeneration();
    ClearMap();
    
    // Aguardar um frame antes de reiniciar
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            StartMapGeneration(MapConfig);
        });
    }
}

void AMapManager::ExecuteNextGenerationPhase()
{
    if (bStopGeneration || !bIsGenerating)
    {
        return;
    }

    switch (GenerationProgress.CurrentPhase)
    {
        case EMapGenerationPhase::Initializing:
            Phase_Initialize();
            break;
            
        case EMapGenerationPhase::GeneratingTerrain:
            Phase_GenerateTerrain();
            break;
            
        case EMapGenerationPhase::CreatingLanes:
            Phase_CreateLanes();
            break;
            
        case EMapGenerationPhase::PlacingObjectives:
            Phase_PlaceObjectives();
            break;
            
        case EMapGenerationPhase::BuildingWalls:
            Phase_BuildWalls();
            break;
            
        case EMapGenerationPhase::GeneratingRiver:
            Phase_GenerateRiver();
            break;
            
        case EMapGenerationPhase::SpawningMinions:
            Phase_SpawnMinions();
            break;
            
        case EMapGenerationPhase::Validating:
            Phase_Validate();
            break;
            
        case EMapGenerationPhase::Complete:
            CompleteMapGeneration(true);
            break;
            
        default:
            HandleGenerationError(TEXT("Fase de geração desconhecida"), GenerationProgress.CurrentPhase);
            break;
    }
}

void AMapManager::Phase_Initialize()
{
    GenerationProgress.CurrentTask = TEXT("Inicializando sistema...");
    GenerationProgress.PhaseProgress = 0.0f;

    LogGenerationInfo(TEXT("Fase: Inicialização"));

    // Configurar random seed usando API moderna UE 5.6
    FRandomStream RandomStream(MapConfig.RandomSeed);
    FMath::RandInit(MapConfig.RandomSeed);

    // Calcular bounds do terreno conforme documentação mapaimplementacao.md
    FVector2D MapSize = GetMapSize();

    // CORRIGIDO: Usar dimensões exatas da documentação
    // Mapa Total: 16000x16000 UU (160m x 160m)
    // Área Jogável: 14400x14400 UU (144m x 144m)
    // Centro do mapa em (0,0,0)
    TerrainBounds = FBox(
        FVector(-MapSize.X * 0.5f, -MapSize.Y * 0.5f, -500.0f), // Z mínimo: -5m
        FVector(MapSize.X * 0.5f, MapSize.Y * 0.5f, 500.0f)     // Z máximo: +5m
    );

    // Verificar se todos os managers estão disponíveis
    if (!LaneManager || !BaronManager || !DragonManager || !WallManager || !RiverManager || !MinionManager)
    {
        HandleGenerationError(TEXT("Um ou mais managers não estão disponíveis"), EMapGenerationPhase::Initializing);
        return;
    }

    // Limpar estado anterior
    ClearMap();

    // Configurar World Partition se disponível (UE 5.6)
    if (UWorld* World = GetWorld())
    {
        // CORRIGIDO: Remover uso de WorldPartitionSubsystem não disponível
        // Usar verificação alternativa para World Partition
        if (World->IsPartitionedWorld())
        {
            // Configurar streaming para otimização
            LogGenerationInfo(TEXT("World Partition configurado para otimização"));
        }
    }

    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.1f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::GeneratingTerrain;
    
    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::GeneratingTerrain);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_GenerateTerrain()
{
    GenerationProgress.CurrentTask = TEXT("Gerando terreno base...");
    GenerationProgress.PhaseProgress = 0.0f;
    
    LogGenerationInfo(TEXT("Fase: Geração de Terreno"));
    
    // Obter tamanho do mapa
    FVector2D MapSize = GetMapSize();
    
    // CORRIGIDO: Configurar terreno base usando UE5.6 Landscape system moderno
    if (UWorld* World = GetWorld())
    {
        // Create landscape configuration conforme documentação mapaimplementacao.md
        FVector LandscapeLocation = FVector(0.0f, 0.0f, 0.0f); // Centro do mapa
        FRotator LandscapeRotation = FRotator::ZeroRotator;
        FVector LandscapeScale = FVector(100.0f, 100.0f, 100.0f); // 1 UU = 1 cm

        // CORRIGIDO: Set landscape size baseado nas dimensões exatas da documentação
        // Mapa Total: 16000x16000 UU (160m x 160m)
        int32 LandscapeQuadsPerSection = 127; // UE5.6 otimizado para performance
        int32 LandscapeSectionsPerComponent = 2; // Melhor para streaming
        int32 LandscapeComponentCountX = 4; // 4x4 components para 16000 UU
        int32 LandscapeComponentCountY = 4;

        // Calculate total landscape size para match exato com documentação
        float LandscapeSize = 16000.0f; // Exato conforme mapaimplementacao.md
        
        UE_LOG(LogTemp, Log, TEXT("AMapManager: Configuring base terrain - Size: %.0f x %.0f UU"), LandscapeSize, LandscapeSize);
        
        // CORRIGIDO: Create heightmap data usando UE 5.6 modern approach
        TArray<uint16> HeightmapData;
        int32 HeightmapSize = (LandscapeQuadsPerSection * LandscapeComponentCountX) + 1;
        HeightmapData.SetNum(HeightmapSize * HeightmapSize);

        // CORRIGIDO: Fill heightmap com altura base precisa conforme documentação
        uint16 BaseHeight = 32768; // Middle value for 16-bit heightmap (Z=0)

        // Usar ParallelFor para otimização UE 5.6
        ParallelFor(HeightmapSize, [&](int32 Y)
        {
            for (int32 X = 0; X < HeightmapSize; X++)
            {
                int32 Index = Y * HeightmapSize + X;

                // CORRIGIDO: Add subtle height variation usando noise moderno
                // Variação máxima: ±50 cm (±50 UU) para terreno quase plano
                float NoiseValue = FMath::PerlinNoise2D(FVector2D(X * 0.005f, Y * 0.005f));
                uint16 HeightVariation = (uint16)(NoiseValue * 50.0f); // ±0.5m variation

                HeightmapData[Index] = BaseHeight + HeightVariation;
            }
        });
        
        // CORRIGIDO: Store terrain configuration conforme documentação mapaimplementacao.md
        TerrainBounds = FBox(
            FVector(-8000.0f, -8000.0f, -500.0f), // Mapa Total: 16000x16000 UU, Z: -5m
            FVector(8000.0f, 8000.0f, 500.0f)     // Centro em (0,0,0), Z: +5m
        );
        
        UE_LOG(LogTemp, Log, TEXT("AMapManager: Base terrain configured successfully"));
    }
    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.2f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::CreatingLanes;
    
    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::CreatingLanes);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_CreateLanes()
{
    GenerationProgress.CurrentTask = TEXT("Criando sistema de lanes...");
    GenerationProgress.PhaseProgress = 0.0f;

    LogGenerationInfo(TEXT("Fase: Criação de Lanes"));

    if (!LaneManager)
    {
        HandleGenerationError(TEXT("Lane Manager não disponível"), EMapGenerationPhase::CreatingLanes);
        return;
    }

    // CORRIGIDO: Usar coordenadas exatas da documentação mapaimplementacao.md
    FVector2D MapSize = GetMapSize(); // 16000x16000 UU

    // CORRIGIDO: Configurar e inicializar lanes com coordenadas precisas
    // Área Jogável: 14400x14400 UU (144m x 144m)
    // Centro do mapa: (0, 0, 0)
    bool bLanesInitialized = LaneManager->InitializeLanes();
    if (!bLanesInitialized)
    {
        HandleGenerationError(TEXT("Falha ao inicializar lanes"), EMapGenerationPhase::CreatingLanes);
        return;
    }
    GenerationProgress.PhaseProgress = 0.33f;

    // CORRIGIDO: Inicializar torres com posições matemáticas precisas
    bool bTowersInitialized = LaneManager->InitializeTowers();
    if (!bTowersInitialized)
    {
        HandleGenerationError(TEXT("Falha ao inicializar torres"), EMapGenerationPhase::CreatingLanes);
        return;
    }
    GenerationProgress.PhaseProgress = 0.66f;

    // CORRIGIDO: Gerar waypoints com pathfinding A* otimizado
    bool bWaypointsGenerated = LaneManager->GenerateWaypoints();
    if (!bWaypointsGenerated)
    {
        HandleGenerationError(TEXT("Falha ao gerar waypoints"), EMapGenerationPhase::CreatingLanes);
        return;
    }

    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.35f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::PlacingObjectives;

    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::PlacingObjectives);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_PlaceObjectives()
{
    GenerationProgress.CurrentTask = TEXT("Posicionando objetivos (Barão e Dragão)...");
    GenerationProgress.PhaseProgress = 0.0f;

    LogGenerationInfo(TEXT("Fase: Posicionamento de Objetivos"));

    if (!BaronManager || !DragonManager)
    {
        HandleGenerationError(TEXT("Managers de objetivos não disponíveis"), EMapGenerationPhase::PlacingObjectives);
        return;
    }

    // CORRIGIDO: Configurar Barão Auracron com coordenadas exatas da documentação
    // Posição do Covil Hexagonal: Centro (0, -4800), conforme mapaimplementacao.md
    FVector BaronPosition = FVector(0.0f, -4800.0f, 0.0f);

    // Configurar posição do manager
    BaronManager->SetActorLocation(BaronPosition);

    // Inicializar geometria hexagonal e sistemas
    BaronManager->InitializeSentinels();
    BaronManager->CalculateHexagonalGeometry();
    BaronManager->SetupCombatMechanics();

    LogGenerationInfo(FString::Printf(TEXT("Barão Auracron posicionado em: %s"), *BaronPosition.ToString()));
    GenerationProgress.PhaseProgress = 0.5f;

    // CORRIGIDO: Configurar Dragão Prismal com coordenadas exatas da documentação
    // Posição do Covil Elíptico: Centro (0, +4800), conforme mapaimplementacao.md
    FVector DragonPosition = FVector(0.0f, 4800.0f, 0.0f);

    // Configurar posição do manager
    DragonManager->SetActorLocation(DragonPosition);

    // Inicializar geometria elíptica e sistemas
    DragonManager->CalculateEllipticalArea();
    DragonManager->SetupCombatMechanics();
    DragonManager->InitializeRewardSystem();

    LogGenerationInfo(FString::Printf(TEXT("Dragão Prismal posicionado em: %s"), *DragonPosition.ToString()));

    // CORRIGIDO: Validar posicionamento com distâncias matemáticas precisas
    // Distância entre covil hexagonal e elíptico: 9600 UU (96m)
    float DistanceBetweenObjectives = FVector::Dist(BaronPosition, DragonPosition);
    if (FMath::Abs(DistanceBetweenObjectives - 9600.0f) > 10.0f) // 9600 UU = 96m
    {
        HandleGenerationError(TEXT("Distância entre objetivos incorreta"), EMapGenerationPhase::PlacingObjectives);
        return;
    }

    LogGenerationInfo(FString::Printf(TEXT("Objetivos validados - Distância: %.1f UU"), DistanceBetweenObjectives));

    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.5f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::BuildingWalls;

    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::BuildingWalls);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_BuildWalls()
{
    GenerationProgress.CurrentTask = TEXT("Construindo sistema de paredes...");
    GenerationProgress.PhaseProgress = 0.0f;

    LogGenerationInfo(TEXT("Fase: Construção de Paredes"));

    if (!WallManager)
    {
        HandleGenerationError(TEXT("Wall Manager não disponível"), EMapGenerationPhase::BuildingWalls);
        return;
    }

    // CORRIGIDO: Gerar paredes externas com geometria retangular precisa
    // Coordenadas: Retângulo X(±8000), Y(±8000) UU, Altura: 800 UU
    bool bExternalWallsCreated = WallManager->CreateExternalWalls();
    if (!bExternalWallsCreated)
    {
        HandleGenerationError(TEXT("Falha ao criar paredes externas"), EMapGenerationPhase::BuildingWalls);
        return;
    }
    LogGenerationInfo(TEXT("Paredes externas criadas: Retângulo 16000x16000 UU, Altura 800 UU"));
    GenerationProgress.PhaseProgress = 0.25f;

    // CORRIGIDO: Criar paredes das lanes com funções lineares
    bool bLaneWallsCreated = WallManager->CreateLaneWalls();
    if (!bLaneWallsCreated)
    {
        HandleGenerationError(TEXT("Falha ao criar paredes das lanes"), EMapGenerationPhase::BuildingWalls);
        return;
    }
    LogGenerationInfo(TEXT("Paredes das lanes criadas com funções matemáticas precisas"));
    GenerationProgress.PhaseProgress = 0.5f;

    // CORRIGIDO: Configurar sistema de colisões otimizado UE 5.6
    bool bCollisionSystemSetup = WallManager->SetupCollisionSystem();
    if (!bCollisionSystemSetup)
    {
        HandleGenerationError(TEXT("Falha ao configurar sistema de colisões"), EMapGenerationPhase::BuildingWalls);
        return;
    }
    LogGenerationInfo(TEXT("Sistema de colisões configurado com otimizações UE 5.6"));
    GenerationProgress.PhaseProgress = 0.75f;

    // CORRIGIDO: Criar bases hexagonais com coordenadas exatas
    bool bHexagonalBasesCreated = WallManager->CreateHexagonalBases();
    if (!bHexagonalBasesCreated)
    {
        HandleGenerationError(TEXT("Falha ao criar bases hexagonais"), EMapGenerationPhase::BuildingWalls);
        return;
    }
    LogGenerationInfo(TEXT("Bases hexagonais criadas: Azul(-6000,-6000), Vermelha(+6000,+6000)"));

    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.65f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::GeneratingRiver;

    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::GeneratingRiver);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_GenerateRiver()
{
    GenerationProgress.CurrentTask = TEXT("Gerando sistema de rio...");
    GenerationProgress.PhaseProgress = 0.0f;

    LogGenerationInfo(TEXT("Fase: Geração de Rio"));

    if (!RiverManager)
    {
        HandleGenerationError(TEXT("River Manager não disponível"), EMapGenerationPhase::GeneratingRiver);
        return;
    }

    // CORRIGIDO: Gerar geometria senoidal do rio com função matemática precisa
    // Função: Y = 200 × sin(πX/4800), Largura: 1200 UU ± 200 × |sin(πX/2400)|
    bool bRiverGeometryCreated = RiverManager->GenerateSinusoidalGeometry();
    if (!bRiverGeometryCreated)
    {
        HandleGenerationError(TEXT("Falha ao gerar geometria senoidal do rio"), EMapGenerationPhase::GeneratingRiver);
        return;
    }
    LogGenerationInfo(TEXT("Rio Prismal gerado: Y = 200×sin(πX/4800), Comprimento 9600 UU"));
    GenerationProgress.PhaseProgress = 0.33f;

    // CORRIGIDO: Criar ilha hexagonal central com coordenadas exatas
    // Centro: (0, 0) UU, Hexágono regular: raio 600 UU
    bool bHexagonalIslandCreated = RiverManager->CreateHexagonalIsland();
    if (!bHexagonalIslandCreated)
    {
        HandleGenerationError(TEXT("Falha ao criar ilha hexagonal"), EMapGenerationPhase::GeneratingRiver);
        return;
    }
    LogGenerationInfo(TEXT("Ilha Central criada: Hexágono raio 600 UU, Área 936.307 UU²"));
    GenerationProgress.PhaseProgress = 0.66f;

    // CORRIGIDO: Configurar pontes com coordenadas matemáticas precisas
    // Pontes da lane central: X = ±600 UU, largura 400 UU
    bool bBridgesConfigured = RiverManager->ConfigureBridges();
    if (!bBridgesConfigured)
    {
        HandleGenerationError(TEXT("Falha ao configurar pontes"), EMapGenerationPhase::GeneratingRiver);
        return;
    }
    LogGenerationInfo(TEXT("Pontes configuradas: X=±600 UU, largura 400 UU"));

    // CORRIGIDO: Configurar sistema de velocidade matemático
    bool bVelocitySystemSetup = RiverManager->SetupVelocitySystem();
    if (!bVelocitySystemSetup)
    {
        HandleGenerationError(TEXT("Falha ao configurar sistema de velocidade"), EMapGenerationPhase::GeneratingRiver);
        return;
    }
    LogGenerationInfo(TEXT("Sistema de velocidade: 50% redução na água, corrente 150 UU/s"));

    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.8f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::SpawningMinions;

    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::SpawningMinions);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_SpawnMinions()
{
    GenerationProgress.CurrentTask = TEXT("Configurando sistema de minions...");
    GenerationProgress.PhaseProgress = 0.0f;

    LogGenerationInfo(TEXT("Fase: Configuração de Minions"));

    if (!MinionManager)
    {
        HandleGenerationError(TEXT("Minion Manager não disponível"), EMapGenerationPhase::SpawningMinions);
        return;
    }

    // CORRIGIDO: Configurar ondas de minions com progressão matemática
    // Sistema de timing: 30s primeira onda, +30s por onda subsequente
    bool bWaveSystemConfigured = MinionManager->ConfigureWaveSystem();
    if (!bWaveSystemConfigured)
    {
        HandleGenerationError(TEXT("Falha ao configurar sistema de ondas"), EMapGenerationPhase::SpawningMinions);
        return;
    }
    LogGenerationInfo(TEXT("Sistema de ondas configurado: Progressão matemática 30s + 30s×onda"));
    GenerationProgress.PhaseProgress = 0.33f;

    // CORRIGIDO: Configurar pathfinding A* com 12 waypoints por lane
    // Distâncias: 800-1200 UU, recálculo a cada 2 segundos
    bool bPathfindingConfigured = MinionManager->ConfigurePathfinding();
    if (!bPathfindingConfigured)
    {
        HandleGenerationError(TEXT("Falha ao configurar pathfinding"), EMapGenerationPhase::SpawningMinions);
        return;
    }
    LogGenerationInfo(TEXT("Pathfinding A* configurado: 12 waypoints/lane, recálculo 2s"));
    GenerationProgress.PhaseProgress = 0.66f;

    // CORRIGIDO: Configurar sistema de recompensas escaláveis
    // Ouro Melee: 20 + (1 × onda), Ouro Ranged: 25 + (1.5 × onda)
    bool bRewardSystemConfigured = MinionManager->ConfigureRewardSystem();
    if (!bRewardSystemConfigured)
    {
        HandleGenerationError(TEXT("Falha ao configurar sistema de recompensas"), EMapGenerationPhase::SpawningMinions);
        return;
    }
    LogGenerationInfo(TEXT("Sistema de recompensas: Escalabilidade matemática por onda"));

    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 0.9f;
    GenerationProgress.CurrentPhase = EMapGenerationPhase::Validating;

    OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::Validating);
    
    // Continuar para próxima fase
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            ExecuteNextGenerationPhase();
        });
    }
}

void AMapManager::Phase_Validate()
{
    GenerationProgress.CurrentTask = TEXT("Validando integridade do mapa...");
    GenerationProgress.PhaseProgress = 0.0f;
    
    LogGenerationInfo(TEXT("Fase: Validação"));
    
    // Executar validação completa
    FMapValidationResult ValidationResult = ValidateMap(MapConfig.ValidationLevel);
    
    GenerationProgress.PhaseProgress = 1.0f;
    GenerationProgress.OverallProgress = 1.0f;
    
    if (ValidationResult.bIsValid)
    {
        GenerationProgress.CurrentPhase = EMapGenerationPhase::Complete;
        OnMapGenerationPhaseChanged.Broadcast(EMapGenerationPhase::Complete);
        
        // Finalizar geração
        if (GetWorld())
        {
            GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
            {
                ExecuteNextGenerationPhase();
            });
        }
    }
    else
    {
        HandleGenerationError(TEXT("Validação falhou"), EMapGenerationPhase::Validating);
    }
}

void AMapManager::CompleteMapGeneration(bool bSuccess)
{
    bIsGenerating = false;
    
    if (bSuccess)
    {
        GenerationProgress.CurrentTask = TEXT("Geração concluída com sucesso!");
        LogGenerationInfo(TEXT("Geração do mapa concluída com sucesso"));
    }
    else
    {
        GenerationProgress.CurrentTask = TEXT("Geração falhou");
        LogGenerationInfo(TEXT("Geração do mapa falhou"), true);
    }
    
    // Broadcast evento de conclusão
    OnMapGenerationComplete.Broadcast(bSuccess);
    
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(GenerationTimerHandle);
    }
}

void AMapManager::HandleGenerationError(const FString& ErrorMessage, EMapGenerationPhase Phase)
{
    GenerationProgress.bHasErrors = true;
    GenerationProgress.ErrorMessages.Add(ErrorMessage);
    GenerationProgress.CurrentPhase = EMapGenerationPhase::Error;
    
    LogGenerationInfo(FString::Printf(TEXT("Erro na fase %s: %s"), 
        *UEnum::GetValueAsString(Phase), *ErrorMessage), true);
    
    OnMapGenerationError.Broadcast(ErrorMessage, Phase);
    
    CompleteMapGeneration(false);
}

FVector2D AMapManager::GetMapSize() const
{
    // CORRIGIDO: Usar dimensões exatas da documentação mapaimplementacao.md
    // Mapa Total: 16000x16000 UU (160m x 160m) - 1 UU = 1 cm
    switch (MapConfig.MapSize)
    {
        case EMapSize::Small:
            // Área Jogável: 14400x14400 UU (144m x 144m)
            return FVector2D(14400.0f, 14400.0f);
        case EMapSize::Medium:
            // Mapa Total padrão conforme documentação
            return FVector2D(16000.0f, 16000.0f);
        case EMapSize::Large:
            // Versão expandida mantendo proporções
            return FVector2D(20000.0f, 20000.0f);
        case EMapSize::Custom:
            return MapConfig.CustomMapSize;
        default:
            // Padrão: Mapa Total conforme documentação
            return FVector2D(16000.0f, 16000.0f);
    }
}

bool AMapManager::IsConfigurationValid(const FMapConfiguration& Config) const
{
    // Validar tamanho do mapa
    FVector2D MapSize = (Config.MapSize == EMapSize::Custom) ? Config.CustomMapSize : GetMapSize();
    if (MapSize.X <= 0.0f || MapSize.Y <= 0.0f || MapSize.X > 50000.0f || MapSize.Y > 50000.0f)
    {
        return false;
    }
    
    // Validar tempo limite
    if (Config.GenerationTimeLimit <= 0.0f || Config.GenerationTimeLimit > 300.0f)
    {
        return false;
    }
    
    return true;
}

void AMapManager::UpdateGenerationProgress()
{
    if (bIsGenerating)
    {
        OnMapGenerationProgress.Broadcast(GenerationProgress);
    }
}

void AMapManager::PerformRealTimeValidation()
{
    // Implementar validação leve em tempo real
    // Esta função deve ser otimizada para não impactar performance
}

FMapValidationResult AMapManager::ValidateMap(EMapValidationLevel ValidationLevel)
{
    FMapValidationResult Result;
    Result.ValidationTime = FPlatformTime::Seconds();
    
    TArray<FString> Errors;
    TArray<FString> Warnings;
    
    float TotalScore = 0.0f;
    int32 ComponentCount = 0;
    
    // Validar geometria das lanes
    if (ValidateLaneGeometry(Errors))
    {
        Result.ComponentScores.Add(TEXT("LaneGeometry"), 1.0f);
        TotalScore += 1.0f;
    }
    else
    {
        Result.ComponentScores.Add(TEXT("LaneGeometry"), 0.0f);
    }
    ComponentCount++;
    
    // Validar posicionamento dos objetivos
    if (ValidateObjectivePlacement(Errors))
    {
        Result.ComponentScores.Add(TEXT("ObjectivePlacement"), 1.0f);
        TotalScore += 1.0f;
    }
    else
    {
        Result.ComponentScores.Add(TEXT("ObjectivePlacement"), 0.0f);
    }
    ComponentCount++;
    
    // Validar sistema de colisões
    if (ValidateCollisionSystem(Errors))
    {
        Result.ComponentScores.Add(TEXT("CollisionSystem"), 1.0f);
        TotalScore += 1.0f;
    }
    else
    {
        Result.ComponentScores.Add(TEXT("CollisionSystem"), 0.0f);
    }
    ComponentCount++;
    
    // Validar geometria do rio
    if (ValidateRiverGeometry(Errors))
    {
        Result.ComponentScores.Add(TEXT("RiverGeometry"), 1.0f);
        TotalScore += 1.0f;
    }
    else
    {
        Result.ComponentScores.Add(TEXT("RiverGeometry"), 0.0f);
    }
    ComponentCount++;
    
    // Validar pathfinding dos minions
    if (ValidateMinionPathfinding(Errors))
    {
        Result.ComponentScores.Add(TEXT("MinionPathfinding"), 1.0f);
        TotalScore += 1.0f;
    }
    else
    {
        Result.ComponentScores.Add(TEXT("MinionPathfinding"), 0.0f);
    }
    ComponentCount++;
    
    // Calcular score final
    Result.ValidationScore = (ComponentCount > 0) ? (TotalScore / ComponentCount) : 0.0f;
    Result.bIsValid = (Result.ValidationScore >= 0.8f) && (Errors.Num() == 0);
    
    Result.ValidationErrors = Errors;
    Result.ValidationWarnings = Warnings;
    Result.ValidationTime = FPlatformTime::Seconds() - Result.ValidationTime;
    
    LastValidationResult = Result;
    OnMapValidationComplete.Broadcast(Result);
    
    return Result;
}

bool AMapManager::ValidateLaneGeometry(TArray<FString>& OutErrors) const
{
    if (!LaneManager)
    {
        OutErrors.Add(TEXT("Lane Manager não disponível"));
        return false;
    }
    
    // Implementar validação específica da geometria das lanes
    return true;
}

bool AMapManager::ValidateObjectivePlacement(TArray<FString>& OutErrors) const
{
    if (!BaronManager || !DragonManager)
    {
        OutErrors.Add(TEXT("Managers de objetivos não disponíveis"));
        return false;
    }
    
    // Implementar validação específica do posicionamento dos objetivos
    return true;
}

bool AMapManager::ValidateCollisionSystem(TArray<FString>& OutErrors) const
{
    if (!WallManager)
    {
        OutErrors.Add(TEXT("Wall Manager não disponível"));
        return false;
    }
    
    // Implementar validação específica do sistema de colisões
    return true;
}

bool AMapManager::ValidateRiverGeometry(TArray<FString>& OutErrors) const
{
    if (!RiverManager)
    {
        OutErrors.Add(TEXT("River Manager não disponível"));
        return false;
    }
    
    // Implementar validação específica da geometria do rio
    return true;
}

bool AMapManager::ValidateMinionPathfinding(TArray<FString>& OutErrors) const
{
    if (!MinionManager)
    {
        OutErrors.Add(TEXT("Minion Manager não disponível"));
        return false;
    }
    
    // Implementar validação específica do pathfinding dos minions
    return true;
}

void AMapManager::ClearMap()
{
    LogGenerationInfo(TEXT("Limpando mapa..."));
    
    // Limpar cada manager
    if (LaneManager)
    {
        // Implementar limpeza do Lane Manager
    }
    
    if (BaronManager)
    {
        // Implementar limpeza do Baron Manager
    }
    
    if (DragonManager)
    {
        // Implementar limpeza do Dragon Manager
    }
    
    if (WallManager)
    {
        // Implementar limpeza do Wall Manager
    }
    
    if (RiverManager)
    {
        // Implementar limpeza do River Manager
    }
    
    if (MinionManager)
    {
        // Implementar limpeza do Minion Manager
    }
}

void AMapManager::DrawDebugInfo() const
{
    if (!GetWorld() || !bShowDebugInfo)
    {
        return;
    }
    
    FVector ActorLocation = GetActorLocation();
    
    // Desenhar informações de status
    FString StatusText = FString::Printf(TEXT("Map Generation Status:\nPhase: %s\nProgress: %.1f%%\nTask: %s"),
        *UEnum::GetValueAsString(GenerationProgress.CurrentPhase),
        GenerationProgress.OverallProgress * 100.0f,
        *GenerationProgress.CurrentTask
    );
    
    DrawDebugString(GetWorld(), ActorLocation + FVector(0, 0, 500), StatusText, nullptr, FColor::White, 0.0f, true);
    
    // Desenhar bounds do mapa
    FVector2D MapSize = GetMapSize();
    FVector MapCenter = ActorLocation;
    FVector MapExtent = FVector(MapSize.X * 0.5f, MapSize.Y * 0.5f, 100.0f);
    
    DrawDebugBox(GetWorld(), MapCenter, MapExtent, FColor::Yellow, false, 0.0f, 0, 10.0f);
}

void AMapManager::LogGenerationInfo(const FString& Message, bool bIsError) const
{
    if (bIsError)
    {
        UE_LOG(LogTemp, Error, TEXT("AMapManager: %s"), *Message);
        
        if (GEngine)
        {
            GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Red, FString::Printf(TEXT("MapManager Error: %s"), *Message));
        }
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("AMapManager: %s"), *Message);
        
        if (GEngine && bShowDebugInfo)
        {
            GEngine->AddOnScreenDebugMessage(-1, 3.0f, FColor::Green, FString::Printf(TEXT("MapManager: %s"), *Message));
        }
    }
}

TMap<FString, FString> AMapManager::GetMapStatistics() const
{
    TMap<FString, FString> Stats;
    
    Stats.Add(TEXT("MapSize"), GetMapSize().ToString());
    Stats.Add(TEXT("GenerationPhase"), UEnum::GetValueAsString(GenerationProgress.CurrentPhase));
    Stats.Add(TEXT("OverallProgress"), FString::Printf(TEXT("%.1f%%"), GenerationProgress.OverallProgress * 100.0f));
    Stats.Add(TEXT("ElapsedTime"), FString::Printf(TEXT("%.2fs"), GenerationProgress.ElapsedTime));
    Stats.Add(TEXT("IsGenerating"), bIsGenerating ? TEXT("True") : TEXT("False"));
    Stats.Add(TEXT("ValidationScore"), FString::Printf(TEXT("%.2f"), LastValidationResult.ValidationScore));
    Stats.Add(TEXT("RandomSeed"), FString::Printf(TEXT("%d"), MapConfig.RandomSeed));
    
    return Stats;
}