{"Version": "1.2", "Data": {"Source": "c:\\aura\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\aura\\module.aura.gen.10.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\intermediate\\build\\win64\\x64\\auraeditor\\development\\unrealed\\sharedpch.unrealed.project.rtti.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\aura\\definitions.aura.h", "c:\\aura\\intermediate\\build\\win64\\unrealeditor\\inc\\aura\\uht\\aminionwavemanager.gen.cpp", "c:\\aura\\source\\aura\\public\\aminionwavemanager.h", "c:\\aura\\intermediate\\build\\win64\\unrealeditor\\inc\\aura\\uht\\aminionwavemanager.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}