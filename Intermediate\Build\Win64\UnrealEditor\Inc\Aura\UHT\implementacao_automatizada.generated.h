// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "implementacao_automatizada.h"

#ifdef AURA_implementacao_automatizada_generated_h
#error "implementacao_automatizada.generated.h already included, missing '#pragma once' in implementacao_automatizada.h"
#endif
#define AURA_implementacao_automatizada_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FLaneEspecificacao ************************************************
#define FID_Aura_Source_Aura_Public_implementacao_automatizada_h_33_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLaneEspecificacao_Statics; \
	static class UScriptStruct* StaticStruct();


struct FLaneEspecificacao;
// ********** End ScriptStruct FLaneEspecificacao **************************************************

// ********** Begin ScriptStruct FCovilEspecificacao ***********************************************
#define FID_Aura_Source_Aura_Public_implementacao_automatizada_h_63_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FCovilEspecificacao_Statics; \
	static class UScriptStruct* StaticStruct();


struct FCovilEspecificacao;
// ********** End ScriptStruct FCovilEspecificacao *************************************************

// ********** Begin Class AImplementacaoAutomatizada ***********************************************
#define FID_Aura_Source_Aura_Public_implementacao_automatizada_h_99_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCorrigirDesviosPrecisao); \
	DECLARE_FUNCTION(execGerarRelatorioValidacao); \
	DECLARE_FUNCTION(execVerificarPrecisaoCompleta); \
	DECLARE_FUNCTION(execCriarIlhaCentral); \
	DECLARE_FUNCTION(execCriarParedes); \
	DECLARE_FUNCTION(execCriarTorres); \
	DECLARE_FUNCTION(execCriarCovils); \
	DECLARE_FUNCTION(execCriarBases); \
	DECLARE_FUNCTION(execCriarRioPrismal); \
	DECLARE_FUNCTION(execCriarLanes); \
	DECLARE_FUNCTION(execCriarMapaCompleto); \
	DECLARE_FUNCTION(execCalcularPosicaoMinion); \
	DECLARE_FUNCTION(execCalcularPontosRioSenoidal); \
	DECLARE_FUNCTION(execCalcularVerticesHexagono); \
	DECLARE_FUNCTION(execCalcularPosicaoTorre); \
	DECLARE_FUNCTION(execValidarPontoEmElipse); \
	DECLARE_FUNCTION(execValidarPontoEmHexagono); \
	DECLARE_FUNCTION(execValidarPontoNoRio); \
	DECLARE_FUNCTION(execValidarPontoNaLane);


AURA_API UClass* Z_Construct_UClass_AImplementacaoAutomatizada_NoRegister();

#define FID_Aura_Source_Aura_Public_implementacao_automatizada_h_99_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAImplementacaoAutomatizada(); \
	friend struct Z_Construct_UClass_AImplementacaoAutomatizada_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_AImplementacaoAutomatizada_NoRegister(); \
public: \
	DECLARE_CLASS2(AImplementacaoAutomatizada, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_AImplementacaoAutomatizada_NoRegister) \
	DECLARE_SERIALIZER(AImplementacaoAutomatizada)


#define FID_Aura_Source_Aura_Public_implementacao_automatizada_h_99_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AImplementacaoAutomatizada(AImplementacaoAutomatizada&&) = delete; \
	AImplementacaoAutomatizada(const AImplementacaoAutomatizada&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AImplementacaoAutomatizada); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AImplementacaoAutomatizada); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AImplementacaoAutomatizada) \
	NO_API virtual ~AImplementacaoAutomatizada();


#define FID_Aura_Source_Aura_Public_implementacao_automatizada_h_96_PROLOG
#define FID_Aura_Source_Aura_Public_implementacao_automatizada_h_99_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_implementacao_automatizada_h_99_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_implementacao_automatizada_h_99_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_implementacao_automatizada_h_99_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AImplementacaoAutomatizada;

// ********** End Class AImplementacaoAutomatizada *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_implementacao_automatizada_h

// ********** Begin Enum ETeam *********************************************************************
#define FOREACH_ENUM_ETEAM(op) \
	op(ETeam::Azul) \
	op(ETeam::Vermelho) \
	op(ETeam::Neutro) 

enum class ETeam : uint8;
template<> struct TIsUEnumClass<ETeam> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<ETeam>();
// ********** End Enum ETeam ***********************************************************************

// ********** Begin Enum ETowerType ****************************************************************
#define FOREACH_ENUM_ETOWERTYPE(op) \
	op(ETowerType::Externa) \
	op(ETowerType::Interna) \
	op(ETowerType::Inibidor) \
	op(ETowerType::Nexus) 

enum class ETowerType : uint8;
template<> struct TIsUEnumClass<ETowerType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<ETowerType>();
// ********** End Enum ETowerType ******************************************************************

// ********** Begin Enum EImplementacaoLaneType ****************************************************
#define FOREACH_ENUM_EIMPLEMENTACAOLANETYPE(op) \
	op(EImplementacaoLaneType::Superior) \
	op(EImplementacaoLaneType::Central) \
	op(EImplementacaoLaneType::Inferior) 

enum class EImplementacaoLaneType : uint8;
template<> struct TIsUEnumClass<EImplementacaoLaneType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EImplementacaoLaneType>();
// ********** End Enum EImplementacaoLaneType ******************************************************

// ********** Begin Enum ECovilType ****************************************************************
#define FOREACH_ENUM_ECOVILTYPE(op) \
	op(ECovilType::DragaoPrismal) \
	op(ECovilType::BaraoAuracron) \
	op(ECovilType::SentinelaCristalina) \
	op(ECovilType::GuardiaoPortal) 

enum class ECovilType : uint8;
template<> struct TIsUEnumClass<ECovilType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<ECovilType>();
// ********** End Enum ECovilType ******************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
