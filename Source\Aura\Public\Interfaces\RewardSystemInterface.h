#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "GameFramework/Actor.h"
#include "RewardSystemInterface.generated.h"

UINTERFACE(MinimalAPI, BlueprintType)
class URewardSystemInterface : public UInterface
{
    GENERATED_BODY()
};

/**
 * Interface para sistema de recompensas
 */
class AURA_API IRewardSystemInterface
{
    GENERATED_BODY()

public:
    // Função para dar recompensas ao jogador
    UFUNCTION(BlueprintImplementableEvent, Category = "Reward System")
    void GivePlayerReward(AActor* Player, int32 GoldAmount, int32 ExperienceAmount);
    
    // Função para dar recompensas de área
    UFUNCTION(BlueprintImplementableEvent, Category = "Reward System")
    void GiveAreaReward(const FVector& Location, float Radius, int32 GoldAmount, int32 ExperienceAmount);
    
    // Função para verificar se pode dar recompensa
    UFUNCTION(BlueprintImplementableEvent, Category = "Reward System")
    bool CanGiveReward(AActor* Player) const;
};
