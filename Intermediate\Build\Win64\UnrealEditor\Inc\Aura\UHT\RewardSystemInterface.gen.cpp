// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Interfaces/RewardSystemInterface.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRewardSystemInterface() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_URewardSystemInterface();
AURA_API UClass* Z_Construct_UClass_URewardSystemInterface_NoRegister();
COREUOBJECT_API UClass* Z_Construct_UClass_UInterface();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Interface URewardSystemInterface Function CanGiveReward ************************
struct RewardSystemInterface_eventCanGiveReward_Parms
{
	AActor* Player;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RewardSystemInterface_eventCanGiveReward_Parms()
		: ReturnValue(false)
	{
	}
};
bool IRewardSystemInterface::CanGiveReward(AActor* Player) const
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_CanGiveReward instead.");
	RewardSystemInterface_eventCanGiveReward_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_URewardSystemInterface_CanGiveReward = FName(TEXT("CanGiveReward"));
bool IRewardSystemInterface::Execute_CanGiveReward(const UObject* O, AActor* Player)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URewardSystemInterface::StaticClass()));
	RewardSystemInterface_eventCanGiveReward_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URewardSystemInterface_CanGiveReward);
	if (Func)
	{
		Parms.Player=Player;
		const_cast<UObject*>(O)->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reward System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xa3o para verificar se pode dar recompensa\n" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RewardSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xa3o para verificar se pode dar recompensa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RewardSystemInterface_eventCanGiveReward_Parms, Player), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RewardSystemInterface_eventCanGiveReward_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RewardSystemInterface_eventCanGiveReward_Parms), &Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URewardSystemInterface, nullptr, "CanGiveReward", Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::PropPointers), sizeof(RewardSystemInterface_eventCanGiveReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x48020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RewardSystemInterface_eventCanGiveReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URewardSystemInterface_CanGiveReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URewardSystemInterface_CanGiveReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URewardSystemInterface Function CanGiveReward **************************

// ********** Begin Interface URewardSystemInterface Function GiveAreaReward ***********************
struct RewardSystemInterface_eventGiveAreaReward_Parms
{
	FVector Location;
	float Radius;
	int32 GoldAmount;
	int32 ExperienceAmount;
};
void IRewardSystemInterface::GiveAreaReward(FVector const& Location, float Radius, int32 GoldAmount, int32 ExperienceAmount)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GiveAreaReward instead.");
}
static FName NAME_URewardSystemInterface_GiveAreaReward = FName(TEXT("GiveAreaReward"));
void IRewardSystemInterface::Execute_GiveAreaReward(UObject* O, FVector const& Location, float Radius, int32 GoldAmount, int32 ExperienceAmount)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URewardSystemInterface::StaticClass()));
	RewardSystemInterface_eventGiveAreaReward_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URewardSystemInterface_GiveAreaReward);
	if (Func)
	{
		Parms.Location=Location;
		Parms.Radius=Radius;
		Parms.GoldAmount=GoldAmount;
		Parms.ExperienceAmount=ExperienceAmount;
		O->ProcessEvent(Func, &Parms);
	}
}
struct Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reward System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xa3o para dar recompensas de \xc3\xa1rea\n" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RewardSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xa3o para dar recompensas de \xc3\xa1rea" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GoldAmount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExperienceAmount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RewardSystemInterface_eventGiveAreaReward_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RewardSystemInterface_eventGiveAreaReward_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::NewProp_GoldAmount = { "GoldAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RewardSystemInterface_eventGiveAreaReward_Parms, GoldAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::NewProp_ExperienceAmount = { "ExperienceAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RewardSystemInterface_eventGiveAreaReward_Parms, ExperienceAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::NewProp_GoldAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::NewProp_ExperienceAmount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URewardSystemInterface, nullptr, "GiveAreaReward", Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::PropPointers), sizeof(RewardSystemInterface_eventGiveAreaReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C20800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RewardSystemInterface_eventGiveAreaReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URewardSystemInterface Function GiveAreaReward *************************

// ********** Begin Interface URewardSystemInterface Function GivePlayerReward *********************
struct RewardSystemInterface_eventGivePlayerReward_Parms
{
	AActor* Player;
	int32 GoldAmount;
	int32 ExperienceAmount;
};
void IRewardSystemInterface::GivePlayerReward(AActor* Player, int32 GoldAmount, int32 ExperienceAmount)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GivePlayerReward instead.");
}
static FName NAME_URewardSystemInterface_GivePlayerReward = FName(TEXT("GivePlayerReward"));
void IRewardSystemInterface::Execute_GivePlayerReward(UObject* O, AActor* Player, int32 GoldAmount, int32 ExperienceAmount)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(URewardSystemInterface::StaticClass()));
	RewardSystemInterface_eventGivePlayerReward_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_URewardSystemInterface_GivePlayerReward);
	if (Func)
	{
		Parms.Player=Player;
		Parms.GoldAmount=GoldAmount;
		Parms.ExperienceAmount=ExperienceAmount;
		O->ProcessEvent(Func, &Parms);
	}
}
struct Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reward System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xa3o para dar recompensas ao jogador\n" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/RewardSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xa3o para dar recompensas ao jogador" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GoldAmount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExperienceAmount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RewardSystemInterface_eventGivePlayerReward_Parms, Player), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::NewProp_GoldAmount = { "GoldAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RewardSystemInterface_eventGivePlayerReward_Parms, GoldAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::NewProp_ExperienceAmount = { "ExperienceAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RewardSystemInterface_eventGivePlayerReward_Parms, ExperienceAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::NewProp_GoldAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::NewProp_ExperienceAmount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URewardSystemInterface, nullptr, "GivePlayerReward", Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::PropPointers), sizeof(RewardSystemInterface_eventGivePlayerReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RewardSystemInterface_eventGivePlayerReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface URewardSystemInterface Function GivePlayerReward ***********************

// ********** Begin Interface URewardSystemInterface ***********************************************
void URewardSystemInterface::StaticRegisterNativesURewardSystemInterface()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URewardSystemInterface;
UClass* URewardSystemInterface::GetPrivateStaticClass()
{
	using TClass = URewardSystemInterface;
	if (!Z_Registration_Info_UClass_URewardSystemInterface.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RewardSystemInterface"),
			Z_Registration_Info_UClass_URewardSystemInterface.InnerSingleton,
			StaticRegisterNativesURewardSystemInterface,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URewardSystemInterface.InnerSingleton;
}
UClass* Z_Construct_UClass_URewardSystemInterface_NoRegister()
{
	return URewardSystemInterface::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URewardSystemInterface_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Interfaces/RewardSystemInterface.h" },
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URewardSystemInterface_CanGiveReward, "CanGiveReward" }, // 2729161775
		{ &Z_Construct_UFunction_URewardSystemInterface_GiveAreaReward, "GiveAreaReward" }, // 2506282354
		{ &Z_Construct_UFunction_URewardSystemInterface_GivePlayerReward, "GivePlayerReward" }, // 503087085
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<IRewardSystemInterface>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_URewardSystemInterface_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UInterface,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URewardSystemInterface_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URewardSystemInterface_Statics::ClassParams = {
	&URewardSystemInterface::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x000840A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URewardSystemInterface_Statics::Class_MetaDataParams), Z_Construct_UClass_URewardSystemInterface_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URewardSystemInterface()
{
	if (!Z_Registration_Info_UClass_URewardSystemInterface.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URewardSystemInterface.OuterSingleton, Z_Construct_UClass_URewardSystemInterface_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URewardSystemInterface.OuterSingleton;
}
URewardSystemInterface::URewardSystemInterface(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(URewardSystemInterface);
// ********** End Interface URewardSystemInterface *************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h__Script_Aura_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URewardSystemInterface, URewardSystemInterface::StaticClass, TEXT("URewardSystemInterface"), &Z_Registration_Info_UClass_URewardSystemInterface, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URewardSystemInterface), 177749257U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h__Script_Aura_619297334(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h__Script_Aura_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
