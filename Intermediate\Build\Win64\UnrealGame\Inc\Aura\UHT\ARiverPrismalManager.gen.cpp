// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "ARiverPrismalManager.h"
#include "Engine/HitResult.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeARiverPrismalManager() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_ARiverPrismalManager();
AURA_API UClass* Z_Construct_UClass_ARiverPrismalManager_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EIslandType();
AURA_API UEnum* Z_Construct_UEnum_Aura_ERiverFlowDirection();
AURA_API UEnum* Z_Construct_UEnum_Aura_ERiverSegmentType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EWaterQuality();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FBridgeData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FHexagonalIsland();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FRiverCollisionData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FRiverSegment();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FWaterProperties();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UBoxComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERiverFlowDirection *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERiverFlowDirection;
static UEnum* ERiverFlowDirection_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERiverFlowDirection.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERiverFlowDirection.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_ERiverFlowDirection, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("ERiverFlowDirection"));
	}
	return Z_Registration_Info_UEnum_ERiverFlowDirection.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<ERiverFlowDirection>()
{
	return ERiverFlowDirection_StaticEnum();
}
struct Z_Construct_UEnum_Aura_ERiverFlowDirection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Bidirectional.DisplayName", "Bidirectional" },
		{ "Bidirectional.Name", "ERiverFlowDirection::Bidirectional" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums para o sistema de rio\n" },
#endif
		{ "Eastward.DisplayName", "Eastward" },
		{ "Eastward.Name", "ERiverFlowDirection::Eastward" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
		{ "Northward.DisplayName", "Northward" },
		{ "Northward.Name", "ERiverFlowDirection::Northward" },
		{ "Southward.DisplayName", "Southward" },
		{ "Southward.Name", "ERiverFlowDirection::Southward" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums para o sistema de rio" },
#endif
		{ "Westward.DisplayName", "Westward" },
		{ "Westward.Name", "ERiverFlowDirection::Westward" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERiverFlowDirection::Eastward", (int64)ERiverFlowDirection::Eastward },
		{ "ERiverFlowDirection::Westward", (int64)ERiverFlowDirection::Westward },
		{ "ERiverFlowDirection::Northward", (int64)ERiverFlowDirection::Northward },
		{ "ERiverFlowDirection::Southward", (int64)ERiverFlowDirection::Southward },
		{ "ERiverFlowDirection::Bidirectional", (int64)ERiverFlowDirection::Bidirectional },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_ERiverFlowDirection_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"ERiverFlowDirection",
	"ERiverFlowDirection",
	Z_Construct_UEnum_Aura_ERiverFlowDirection_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ERiverFlowDirection_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ERiverFlowDirection_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_ERiverFlowDirection_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_ERiverFlowDirection()
{
	if (!Z_Registration_Info_UEnum_ERiverFlowDirection.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERiverFlowDirection.InnerSingleton, Z_Construct_UEnum_Aura_ERiverFlowDirection_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERiverFlowDirection.InnerSingleton;
}
// ********** End Enum ERiverFlowDirection *********************************************************

// ********** Begin Enum ERiverSegmentType *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERiverSegmentType;
static UEnum* ERiverSegmentType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERiverSegmentType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERiverSegmentType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_ERiverSegmentType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("ERiverSegmentType"));
	}
	return Z_Registration_Info_UEnum_ERiverSegmentType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<ERiverSegmentType>()
{
	return ERiverSegmentType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_ERiverSegmentType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Bend.DisplayName", "Bend" },
		{ "Bend.Name", "ERiverSegmentType::Bend" },
		{ "BlueprintType", "true" },
		{ "Bridge.DisplayName", "Bridge" },
		{ "Bridge.Name", "ERiverSegmentType::Bridge" },
		{ "Curved.DisplayName", "Curved" },
		{ "Curved.Name", "ERiverSegmentType::Curved" },
		{ "Island.DisplayName", "Island" },
		{ "Island.Name", "ERiverSegmentType::Island" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
		{ "Straight.DisplayName", "Straight" },
		{ "Straight.Name", "ERiverSegmentType::Straight" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERiverSegmentType::Straight", (int64)ERiverSegmentType::Straight },
		{ "ERiverSegmentType::Curved", (int64)ERiverSegmentType::Curved },
		{ "ERiverSegmentType::Bend", (int64)ERiverSegmentType::Bend },
		{ "ERiverSegmentType::Island", (int64)ERiverSegmentType::Island },
		{ "ERiverSegmentType::Bridge", (int64)ERiverSegmentType::Bridge },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_ERiverSegmentType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"ERiverSegmentType",
	"ERiverSegmentType",
	Z_Construct_UEnum_Aura_ERiverSegmentType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ERiverSegmentType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ERiverSegmentType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_ERiverSegmentType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_ERiverSegmentType()
{
	if (!Z_Registration_Info_UEnum_ERiverSegmentType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERiverSegmentType.InnerSingleton, Z_Construct_UEnum_Aura_ERiverSegmentType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERiverSegmentType.InnerSingleton;
}
// ********** End Enum ERiverSegmentType ***********************************************************

// ********** Begin Enum EWaterQuality *************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EWaterQuality;
static UEnum* EWaterQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EWaterQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EWaterQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EWaterQuality, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EWaterQuality"));
	}
	return Z_Registration_Info_UEnum_EWaterQuality.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EWaterQuality>()
{
	return EWaterQuality_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EWaterQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Clear.DisplayName", "Clear" },
		{ "Clear.Name", "EWaterQuality::Clear" },
		{ "Magical.DisplayName", "Magical" },
		{ "Magical.Name", "EWaterQuality::Magical" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
		{ "Murky.DisplayName", "Murky" },
		{ "Murky.Name", "EWaterQuality::Murky" },
		{ "Polluted.DisplayName", "Polluted" },
		{ "Polluted.Name", "EWaterQuality::Polluted" },
		{ "Pure.DisplayName", "Pure" },
		{ "Pure.Name", "EWaterQuality::Pure" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EWaterQuality::Pure", (int64)EWaterQuality::Pure },
		{ "EWaterQuality::Clear", (int64)EWaterQuality::Clear },
		{ "EWaterQuality::Murky", (int64)EWaterQuality::Murky },
		{ "EWaterQuality::Polluted", (int64)EWaterQuality::Polluted },
		{ "EWaterQuality::Magical", (int64)EWaterQuality::Magical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EWaterQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EWaterQuality",
	"EWaterQuality",
	Z_Construct_UEnum_Aura_EWaterQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EWaterQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EWaterQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EWaterQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EWaterQuality()
{
	if (!Z_Registration_Info_UEnum_EWaterQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EWaterQuality.InnerSingleton, Z_Construct_UEnum_Aura_EWaterQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EWaterQuality.InnerSingleton;
}
// ********** End Enum EWaterQuality ***************************************************************

// ********** Begin Enum EIslandType ***************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EIslandType;
static UEnum* EIslandType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EIslandType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EIslandType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EIslandType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EIslandType"));
	}
	return Z_Registration_Info_UEnum_EIslandType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EIslandType>()
{
	return EIslandType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EIslandType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Circular.DisplayName", "Circular" },
		{ "Circular.Name", "EIslandType::Circular" },
		{ "Hexagonal.DisplayName", "Hexagonal" },
		{ "Hexagonal.Name", "EIslandType::Hexagonal" },
		{ "Irregular.DisplayName", "Irregular" },
		{ "Irregular.Name", "EIslandType::Irregular" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
		{ "Rectangular.DisplayName", "Rectangular" },
		{ "Rectangular.Name", "EIslandType::Rectangular" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EIslandType::Hexagonal", (int64)EIslandType::Hexagonal },
		{ "EIslandType::Circular", (int64)EIslandType::Circular },
		{ "EIslandType::Irregular", (int64)EIslandType::Irregular },
		{ "EIslandType::Rectangular", (int64)EIslandType::Rectangular },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EIslandType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EIslandType",
	"EIslandType",
	Z_Construct_UEnum_Aura_EIslandType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EIslandType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EIslandType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EIslandType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EIslandType()
{
	if (!Z_Registration_Info_UEnum_EIslandType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EIslandType.InnerSingleton, Z_Construct_UEnum_Aura_EIslandType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EIslandType.InnerSingleton;
}
// ********** End Enum EIslandType *****************************************************************

// ********** Begin ScriptStruct FRiverSegment *****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRiverSegment;
class UScriptStruct* FRiverSegment::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRiverSegment.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRiverSegment.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRiverSegment, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("RiverSegment"));
	}
	return Z_Registration_Info_UScriptStruct_FRiverSegment.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRiverSegment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estruturas de dados para o rio\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estruturas de dados para o rio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPosition_MetaData[] = {
		{ "Category", "River Segment" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPosition_MetaData[] = {
		{ "Category", "River Segment" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterPosition_MetaData[] = {
		{ "Category", "River Segment" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SegmentType_MetaData[] = {
		{ "Category", "River Segment" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "Category", "River Segment" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Depth_MetaData[] = {
		{ "Category", "River Segment" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpeed_MetaData[] = {
		{ "Category", "River Segment" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowDirection_MetaData[] = {
		{ "Category", "River Segment" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplinePoints_MetaData[] = {
		{ "Category", "River Segment" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurvatureRadius_MetaData[] = {
		{ "Category", "River Segment" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterPosition;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SegmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SegmentType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Depth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowDirection;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SplinePoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SplinePoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurvatureRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRiverSegment>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_StartPosition = { "StartPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverSegment, StartPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPosition_MetaData), NewProp_StartPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_EndPosition = { "EndPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverSegment, EndPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPosition_MetaData), NewProp_EndPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_CenterPosition = { "CenterPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverSegment, CenterPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterPosition_MetaData), NewProp_CenterPosition_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_SegmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_SegmentType = { "SegmentType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverSegment, SegmentType), Z_Construct_UEnum_Aura_ERiverSegmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SegmentType_MetaData), NewProp_SegmentType_MetaData) }; // 3079482006
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverSegment, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_Depth = { "Depth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverSegment, Depth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Depth_MetaData), NewProp_Depth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_FlowSpeed = { "FlowSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverSegment, FlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpeed_MetaData), NewProp_FlowSpeed_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_FlowDirection = { "FlowDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverSegment, FlowDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowDirection_MetaData), NewProp_FlowDirection_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_SplinePoints_Inner = { "SplinePoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_SplinePoints = { "SplinePoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverSegment, SplinePoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplinePoints_MetaData), NewProp_SplinePoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_CurvatureRadius = { "CurvatureRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverSegment, CurvatureRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurvatureRadius_MetaData), NewProp_CurvatureRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRiverSegment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_StartPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_EndPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_CenterPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_SegmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_SegmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_Depth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_FlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_FlowDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_SplinePoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_SplinePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverSegment_Statics::NewProp_CurvatureRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRiverSegment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRiverSegment_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"RiverSegment",
	Z_Construct_UScriptStruct_FRiverSegment_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRiverSegment_Statics::PropPointers),
	sizeof(FRiverSegment),
	alignof(FRiverSegment),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRiverSegment_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRiverSegment_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRiverSegment()
{
	if (!Z_Registration_Info_UScriptStruct_FRiverSegment.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRiverSegment.InnerSingleton, Z_Construct_UScriptStruct_FRiverSegment_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRiverSegment.InnerSingleton;
}
// ********** End ScriptStruct FRiverSegment *******************************************************

// ********** Begin ScriptStruct FHexagonalIsland **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHexagonalIsland;
class UScriptStruct* FHexagonalIsland::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHexagonalIsland.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHexagonalIsland.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHexagonalIsland, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("HexagonalIsland"));
	}
	return Z_Registration_Info_UScriptStruct_FHexagonalIsland.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHexagonalIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterPosition_MetaData[] = {
		{ "Category", "Island" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "Category", "Island" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Height_MetaData[] = {
		{ "Category", "Island" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandType_MetaData[] = {
		{ "Category", "Island" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HexagonVertices_MetaData[] = {
		{ "Category", "Island" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EdgeMidpoints_MetaData[] = {
		{ "Category", "Island" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EdgeLength_MetaData[] = {
		{ "Category", "Island" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Apothem_MetaData[] = {
		{ "Category", "Island" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasVegetation_MetaData[] = {
		{ "Category", "Island" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAccessible_MetaData[] = {
		{ "Category", "Island" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HexagonVertices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HexagonVertices;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EdgeMidpoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EdgeMidpoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EdgeLength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Apothem;
	static void NewProp_bHasVegetation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasVegetation;
	static void NewProp_bIsAccessible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAccessible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHexagonalIsland>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_CenterPosition = { "CenterPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalIsland, CenterPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterPosition_MetaData), NewProp_CenterPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalIsland, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalIsland, Height), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Height_MetaData), NewProp_Height_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_IslandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalIsland, IslandType), Z_Construct_UEnum_Aura_EIslandType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandType_MetaData), NewProp_IslandType_MetaData) }; // 4155264728
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_HexagonVertices_Inner = { "HexagonVertices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_HexagonVertices = { "HexagonVertices", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalIsland, HexagonVertices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HexagonVertices_MetaData), NewProp_HexagonVertices_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_EdgeMidpoints_Inner = { "EdgeMidpoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_EdgeMidpoints = { "EdgeMidpoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalIsland, EdgeMidpoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EdgeMidpoints_MetaData), NewProp_EdgeMidpoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_EdgeLength = { "EdgeLength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalIsland, EdgeLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EdgeLength_MetaData), NewProp_EdgeLength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_Apothem = { "Apothem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHexagonalIsland, Apothem), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Apothem_MetaData), NewProp_Apothem_MetaData) };
void Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_bHasVegetation_SetBit(void* Obj)
{
	((FHexagonalIsland*)Obj)->bHasVegetation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_bHasVegetation = { "bHasVegetation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FHexagonalIsland), &Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_bHasVegetation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasVegetation_MetaData), NewProp_bHasVegetation_MetaData) };
void Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_bIsAccessible_SetBit(void* Obj)
{
	((FHexagonalIsland*)Obj)->bIsAccessible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_bIsAccessible = { "bIsAccessible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FHexagonalIsland), &Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_bIsAccessible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAccessible_MetaData), NewProp_bIsAccessible_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHexagonalIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_CenterPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_IslandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_HexagonVertices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_HexagonVertices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_EdgeMidpoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_EdgeMidpoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_EdgeLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_Apothem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_bHasVegetation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewProp_bIsAccessible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHexagonalIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHexagonalIsland_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"HexagonalIsland",
	Z_Construct_UScriptStruct_FHexagonalIsland_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHexagonalIsland_Statics::PropPointers),
	sizeof(FHexagonalIsland),
	alignof(FHexagonalIsland),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHexagonalIsland_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHexagonalIsland_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHexagonalIsland()
{
	if (!Z_Registration_Info_UScriptStruct_FHexagonalIsland.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHexagonalIsland.InnerSingleton, Z_Construct_UScriptStruct_FHexagonalIsland_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHexagonalIsland.InnerSingleton;
}
// ********** End ScriptStruct FHexagonalIsland ****************************************************

// ********** Begin ScriptStruct FWaterProperties **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FWaterProperties;
class UScriptStruct* FWaterProperties::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FWaterProperties.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FWaterProperties.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FWaterProperties, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("WaterProperties"));
	}
	return Z_Registration_Info_UScriptStruct_FWaterProperties.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FWaterProperties_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quality_MetaData[] = {
		{ "Category", "Water Properties" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Temperature_MetaData[] = {
		{ "Category", "Water Properties" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Viscosity_MetaData[] = {
		{ "Category", "Water Properties" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaterColor_MetaData[] = {
		{ "Category", "Water Properties" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transparency_MetaData[] = {
		{ "Category", "Water Properties" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RefractionIndex_MetaData[] = {
		{ "Category", "Water Properties" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasCurrent_MetaData[] = {
		{ "Category", "Water Properties" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSwimmable_MetaData[] = {
		{ "Category", "Water Properties" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCausesDamage_MetaData[] = {
		{ "Category", "Water Properties" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpeed_MetaData[] = {
		{ "Category", "Water Properties" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SurfaceIncline_MetaData[] = {
		{ "Category", "Water Properties" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveAmplitude_MetaData[] = {
		{ "Category", "Water Properties" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveFrequency_MetaData[] = {
		{ "Category", "Water Properties" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Temperature;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Viscosity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WaterColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Transparency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RefractionIndex;
	static void NewProp_bHasCurrent_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasCurrent;
	static void NewProp_bIsSwimmable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSwimmable;
	static void NewProp_bCausesDamage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCausesDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SurfaceIncline;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WaveAmplitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WaveFrequency;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FWaterProperties>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaterProperties, Quality), Z_Construct_UEnum_Aura_EWaterQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quality_MetaData), NewProp_Quality_MetaData) }; // 2945749054
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_Temperature = { "Temperature", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaterProperties, Temperature), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Temperature_MetaData), NewProp_Temperature_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_Viscosity = { "Viscosity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaterProperties, Viscosity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Viscosity_MetaData), NewProp_Viscosity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_WaterColor = { "WaterColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaterProperties, WaterColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaterColor_MetaData), NewProp_WaterColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_Transparency = { "Transparency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaterProperties, Transparency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transparency_MetaData), NewProp_Transparency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_RefractionIndex = { "RefractionIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaterProperties, RefractionIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RefractionIndex_MetaData), NewProp_RefractionIndex_MetaData) };
void Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_bHasCurrent_SetBit(void* Obj)
{
	((FWaterProperties*)Obj)->bHasCurrent = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_bHasCurrent = { "bHasCurrent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWaterProperties), &Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_bHasCurrent_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasCurrent_MetaData), NewProp_bHasCurrent_MetaData) };
void Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_bIsSwimmable_SetBit(void* Obj)
{
	((FWaterProperties*)Obj)->bIsSwimmable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_bIsSwimmable = { "bIsSwimmable", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWaterProperties), &Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_bIsSwimmable_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSwimmable_MetaData), NewProp_bIsSwimmable_MetaData) };
void Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_bCausesDamage_SetBit(void* Obj)
{
	((FWaterProperties*)Obj)->bCausesDamage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_bCausesDamage = { "bCausesDamage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWaterProperties), &Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_bCausesDamage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCausesDamage_MetaData), NewProp_bCausesDamage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_FlowSpeed = { "FlowSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaterProperties, FlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpeed_MetaData), NewProp_FlowSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_SurfaceIncline = { "SurfaceIncline", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaterProperties, SurfaceIncline), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SurfaceIncline_MetaData), NewProp_SurfaceIncline_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_WaveAmplitude = { "WaveAmplitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaterProperties, WaveAmplitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveAmplitude_MetaData), NewProp_WaveAmplitude_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_WaveFrequency = { "WaveFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaterProperties, WaveFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveFrequency_MetaData), NewProp_WaveFrequency_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FWaterProperties_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_Quality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_Temperature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_Viscosity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_WaterColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_Transparency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_RefractionIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_bHasCurrent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_bIsSwimmable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_bCausesDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_FlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_SurfaceIncline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_WaveAmplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaterProperties_Statics::NewProp_WaveFrequency,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWaterProperties_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FWaterProperties_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"WaterProperties",
	Z_Construct_UScriptStruct_FWaterProperties_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWaterProperties_Statics::PropPointers),
	sizeof(FWaterProperties),
	alignof(FWaterProperties),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWaterProperties_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FWaterProperties_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FWaterProperties()
{
	if (!Z_Registration_Info_UScriptStruct_FWaterProperties.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FWaterProperties.InnerSingleton, Z_Construct_UScriptStruct_FWaterProperties_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FWaterProperties.InnerSingleton;
}
// ********** End ScriptStruct FWaterProperties ****************************************************

// ********** Begin ScriptStruct FRiverCollisionData ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRiverCollisionData;
class UScriptStruct* FRiverCollisionData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRiverCollisionData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRiverCollisionData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRiverCollisionData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("RiverCollisionData"));
	}
	return Z_Registration_Info_UScriptStruct_FRiverCollisionData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRiverCollisionData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionPoint_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaterSurfaceNormal_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaterDepthAtPoint_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollidingActor_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionTime_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowVelocityAtPoint_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsUnderwater_MetaData[] = {
		{ "Category", "Collision" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionPoint;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WaterSurfaceNormal;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WaterDepthAtPoint;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CollidingActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CollisionTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowVelocityAtPoint;
	static void NewProp_bIsUnderwater_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsUnderwater;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRiverCollisionData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_CollisionPoint = { "CollisionPoint", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverCollisionData, CollisionPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionPoint_MetaData), NewProp_CollisionPoint_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_WaterSurfaceNormal = { "WaterSurfaceNormal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverCollisionData, WaterSurfaceNormal), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaterSurfaceNormal_MetaData), NewProp_WaterSurfaceNormal_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_WaterDepthAtPoint = { "WaterDepthAtPoint", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverCollisionData, WaterDepthAtPoint), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaterDepthAtPoint_MetaData), NewProp_WaterDepthAtPoint_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_CollidingActor = { "CollidingActor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverCollisionData, CollidingActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollidingActor_MetaData), NewProp_CollidingActor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_CollisionTime = { "CollisionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverCollisionData, CollisionTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionTime_MetaData), NewProp_CollisionTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_FlowVelocityAtPoint = { "FlowVelocityAtPoint", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRiverCollisionData, FlowVelocityAtPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowVelocityAtPoint_MetaData), NewProp_FlowVelocityAtPoint_MetaData) };
void Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_bIsUnderwater_SetBit(void* Obj)
{
	((FRiverCollisionData*)Obj)->bIsUnderwater = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_bIsUnderwater = { "bIsUnderwater", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRiverCollisionData), &Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_bIsUnderwater_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsUnderwater_MetaData), NewProp_bIsUnderwater_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRiverCollisionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_CollisionPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_WaterSurfaceNormal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_WaterDepthAtPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_CollidingActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_CollisionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_FlowVelocityAtPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewProp_bIsUnderwater,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRiverCollisionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRiverCollisionData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"RiverCollisionData",
	Z_Construct_UScriptStruct_FRiverCollisionData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRiverCollisionData_Statics::PropPointers),
	sizeof(FRiverCollisionData),
	alignof(FRiverCollisionData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRiverCollisionData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRiverCollisionData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRiverCollisionData()
{
	if (!Z_Registration_Info_UScriptStruct_FRiverCollisionData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRiverCollisionData.InnerSingleton, Z_Construct_UScriptStruct_FRiverCollisionData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRiverCollisionData.InnerSingleton;
}
// ********** End ScriptStruct FRiverCollisionData *************************************************

// ********** Begin ScriptStruct FBridgeData *******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FBridgeData;
class UScriptStruct* FBridgeData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FBridgeData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FBridgeData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FBridgeData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("BridgeData"));
	}
	return Z_Registration_Info_UScriptStruct_FBridgeData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FBridgeData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Bridge" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Bridge" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Length_MetaData[] = {
		{ "Category", "Bridge" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "Category", "Bridge" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Height_MetaData[] = {
		{ "Category", "Bridge" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDestructible_MetaData[] = {
		{ "Category", "Bridge" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AuthorizedUnits_MetaData[] = {
		{ "Category", "Bridge" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Length;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static void NewProp_bIsDestructible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDestructible;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AuthorizedUnits_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AuthorizedUnits;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FBridgeData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBridgeData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBridgeData, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_Length = { "Length", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBridgeData, Length), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Length_MetaData), NewProp_Length_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBridgeData, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBridgeData, Height), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Height_MetaData), NewProp_Height_MetaData) };
void Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_bIsDestructible_SetBit(void* Obj)
{
	((FBridgeData*)Obj)->bIsDestructible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_bIsDestructible = { "bIsDestructible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FBridgeData), &Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_bIsDestructible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDestructible_MetaData), NewProp_bIsDestructible_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_AuthorizedUnits_Inner = { "AuthorizedUnits", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_AuthorizedUnits = { "AuthorizedUnits", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBridgeData, AuthorizedUnits), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AuthorizedUnits_MetaData), NewProp_AuthorizedUnits_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FBridgeData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_Length,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_bIsDestructible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_AuthorizedUnits_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBridgeData_Statics::NewProp_AuthorizedUnits,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBridgeData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FBridgeData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"BridgeData",
	Z_Construct_UScriptStruct_FBridgeData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBridgeData_Statics::PropPointers),
	sizeof(FBridgeData),
	alignof(FBridgeData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBridgeData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FBridgeData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FBridgeData()
{
	if (!Z_Registration_Info_UScriptStruct_FBridgeData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FBridgeData.InnerSingleton, Z_Construct_UScriptStruct_FBridgeData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FBridgeData.InnerSingleton;
}
// ********** End ScriptStruct FBridgeData *********************************************************

// ********** Begin Class ARiverPrismalManager Function AddBridge **********************************
struct Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics
{
	struct RiverPrismalManager_eventAddBridge_Parms
	{
		FVector Position;
		FRotator Rotation;
		float Length;
		float Width;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bridge System" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Length;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventAddBridge_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventAddBridge_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::NewProp_Length = { "Length", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventAddBridge_Parms, Length), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventAddBridge_Parms, Width), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventAddBridge_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::NewProp_Length,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "AddBridge", Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::RiverPrismalManager_eventAddBridge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::RiverPrismalManager_eventAddBridge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_AddBridge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_AddBridge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execAddBridge)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Rotation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Length);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Width);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->AddBridge(Z_Param_Out_Position,Z_Param_Out_Rotation,Z_Param_Length,Z_Param_Width);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function AddBridge ************************************

// ********** Begin Class ARiverPrismalManager Function ApplyWaterCurrent **************************
struct Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics
{
	struct RiverPrismalManager_eventApplyWaterCurrent_Parms
	{
		FVector ActorVelocity;
		FVector Position;
		float DeltaTime;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Water Collision" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorVelocity_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActorVelocity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::NewProp_ActorVelocity = { "ActorVelocity", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventApplyWaterCurrent_Parms, ActorVelocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorVelocity_MetaData), NewProp_ActorVelocity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventApplyWaterCurrent_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventApplyWaterCurrent_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventApplyWaterCurrent_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::NewProp_ActorVelocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::NewProp_DeltaTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "ApplyWaterCurrent", Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::RiverPrismalManager_eventApplyWaterCurrent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::RiverPrismalManager_eventApplyWaterCurrent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execApplyWaterCurrent)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ActorVelocity);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->ApplyWaterCurrent(Z_Param_Out_ActorVelocity,Z_Param_Out_Position,Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function ApplyWaterCurrent ****************************

// ********** Begin Class ARiverPrismalManager Function CalculateDistanceToRiver *******************
struct Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics
{
	struct RiverPrismalManager_eventCalculateDistanceToRiver_Parms
	{
		FVector Position;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Math Utilities" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateDistanceToRiver_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateDistanceToRiver_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "CalculateDistanceToRiver", Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::RiverPrismalManager_eventCalculateDistanceToRiver_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::RiverPrismalManager_eventCalculateDistanceToRiver_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execCalculateDistanceToRiver)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateDistanceToRiver(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function CalculateDistanceToRiver *********************

// ********** Begin Class ARiverPrismalManager Function CalculateFlowDirection *********************
struct Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics
{
	struct RiverPrismalManager_eventCalculateFlowDirection_Parms
	{
		FVector Position;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Geometry" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateFlowDirection_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateFlowDirection_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "CalculateFlowDirection", Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::RiverPrismalManager_eventCalculateFlowDirection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::RiverPrismalManager_eventCalculateFlowDirection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execCalculateFlowDirection)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateFlowDirection(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function CalculateFlowDirection ***********************

// ********** Begin Class ARiverPrismalManager Function CalculateFlowSpeed *************************
struct Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics
{
	struct RiverPrismalManager_eventCalculateFlowSpeed_Parms
	{
		FVector Position;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Geometry" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateFlowSpeed_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateFlowSpeed_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "CalculateFlowSpeed", Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::RiverPrismalManager_eventCalculateFlowSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::RiverPrismalManager_eventCalculateFlowSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execCalculateFlowSpeed)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateFlowSpeed(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function CalculateFlowSpeed ***************************

// ********** Begin Class ARiverPrismalManager Function CalculateHexagonApothem ********************
struct Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics
{
	struct RiverPrismalManager_eventCalculateHexagonApothem_Parms
	{
		float Radius;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Geometry" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateHexagonApothem_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateHexagonApothem_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "CalculateHexagonApothem", Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::RiverPrismalManager_eventCalculateHexagonApothem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::RiverPrismalManager_eventCalculateHexagonApothem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execCalculateHexagonApothem)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateHexagonApothem(Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function CalculateHexagonApothem **********************

// ********** Begin Class ARiverPrismalManager Function CalculateHexagonEdgeLength *****************
struct Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics
{
	struct RiverPrismalManager_eventCalculateHexagonEdgeLength_Parms
	{
		float Radius;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Geometry" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateHexagonEdgeLength_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateHexagonEdgeLength_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "CalculateHexagonEdgeLength", Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::RiverPrismalManager_eventCalculateHexagonEdgeLength_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::RiverPrismalManager_eventCalculateHexagonEdgeLength_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execCalculateHexagonEdgeLength)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateHexagonEdgeLength(Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function CalculateHexagonEdgeLength *******************

// ********** Begin Class ARiverPrismalManager Function CalculateHexagonEdgeMidpoints **************
struct Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics
{
	struct RiverPrismalManager_eventCalculateHexagonEdgeMidpoints_Parms
	{
		TArray<FVector> Vertices;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Geometry" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Vertices_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Vertices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Vertices;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::NewProp_Vertices_Inner = { "Vertices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::NewProp_Vertices = { "Vertices", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateHexagonEdgeMidpoints_Parms, Vertices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Vertices_MetaData), NewProp_Vertices_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateHexagonEdgeMidpoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::NewProp_Vertices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::NewProp_Vertices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "CalculateHexagonEdgeMidpoints", Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::RiverPrismalManager_eventCalculateHexagonEdgeMidpoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::RiverPrismalManager_eventCalculateHexagonEdgeMidpoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execCalculateHexagonEdgeMidpoints)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Vertices);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->CalculateHexagonEdgeMidpoints(Z_Param_Out_Vertices);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function CalculateHexagonEdgeMidpoints ****************

// ********** Begin Class ARiverPrismalManager Function CalculateHexagonVertices *******************
struct Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics
{
	struct RiverPrismalManager_eventCalculateHexagonVertices_Parms
	{
		FVector Center;
		float Radius;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Geometry" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateHexagonVertices_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateHexagonVertices_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateHexagonVertices_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "CalculateHexagonVertices", Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::RiverPrismalManager_eventCalculateHexagonVertices_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::RiverPrismalManager_eventCalculateHexagonVertices_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execCalculateHexagonVertices)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->CalculateHexagonVertices(Z_Param_Out_Center,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function CalculateHexagonVertices *********************

// ********** Begin Class ARiverPrismalManager Function CalculateSinusoidalPoint *******************
struct Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics
{
	struct RiverPrismalManager_eventCalculateSinusoidalPoint_Parms
	{
		float Distance;
		FVector BaseDirection;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Geometry" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseDirection;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateSinusoidalPoint_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::NewProp_BaseDirection = { "BaseDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateSinusoidalPoint_Parms, BaseDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseDirection_MetaData), NewProp_BaseDirection_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateSinusoidalPoint_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::NewProp_BaseDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "CalculateSinusoidalPoint", Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::RiverPrismalManager_eventCalculateSinusoidalPoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::RiverPrismalManager_eventCalculateSinusoidalPoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execCalculateSinusoidalPoint)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_BaseDirection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateSinusoidalPoint(Z_Param_Distance,Z_Param_Out_BaseDirection);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function CalculateSinusoidalPoint *********************

// ********** Begin Class ARiverPrismalManager Function CalculateWaterDepth ************************
struct Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics
{
	struct RiverPrismalManager_eventCalculateWaterDepth_Parms
	{
		FVector Position;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Geometry" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateWaterDepth_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateWaterDepth_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "CalculateWaterDepth", Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::RiverPrismalManager_eventCalculateWaterDepth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::RiverPrismalManager_eventCalculateWaterDepth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execCalculateWaterDepth)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateWaterDepth(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function CalculateWaterDepth **************************

// ********** Begin Class ARiverPrismalManager Function CalculateWaterNormal ***********************
struct Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics
{
	struct RiverPrismalManager_eventCalculateWaterNormal_Parms
	{
		FVector Position;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Math Utilities" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateWaterNormal_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCalculateWaterNormal_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "CalculateWaterNormal", Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::RiverPrismalManager_eventCalculateWaterNormal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::RiverPrismalManager_eventCalculateWaterNormal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execCalculateWaterNormal)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateWaterNormal(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function CalculateWaterNormal *************************

// ********** Begin Class ARiverPrismalManager Function CanUnitCrossBridge *************************
struct Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics
{
	struct RiverPrismalManager_eventCanUnitCrossBridge_Parms
	{
		AActor* Unit;
		int32 BridgeIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bridge System" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BridgeIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCanUnitCrossBridge_Parms, Unit), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::NewProp_BridgeIndex = { "BridgeIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCanUnitCrossBridge_Parms, BridgeIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverPrismalManager_eventCanUnitCrossBridge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverPrismalManager_eventCanUnitCrossBridge_Parms), &Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::NewProp_Unit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::NewProp_BridgeIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "CanUnitCrossBridge", Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::RiverPrismalManager_eventCanUnitCrossBridge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::RiverPrismalManager_eventCanUnitCrossBridge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execCanUnitCrossBridge)
{
	P_GET_OBJECT(AActor,Z_Param_Unit);
	P_GET_PROPERTY(FIntProperty,Z_Param_BridgeIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanUnitCrossBridge(Z_Param_Unit,Z_Param_BridgeIndex);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function CanUnitCrossBridge ***************************

// ********** Begin Class ARiverPrismalManager Function CheckWaterCollision ************************
struct Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics
{
	struct RiverPrismalManager_eventCheckWaterCollision_Parms
	{
		FVector Position;
		FRiverCollisionData OutCollisionData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Water Collision" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de colis\xc3\xb5""es aqu\xc3\xa1ticas\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de colis\xc3\xb5""es aqu\xc3\xa1ticas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutCollisionData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCheckWaterCollision_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::NewProp_OutCollisionData = { "OutCollisionData", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventCheckWaterCollision_Parms, OutCollisionData), Z_Construct_UScriptStruct_FRiverCollisionData, METADATA_PARAMS(0, nullptr) }; // 547632616
void Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverPrismalManager_eventCheckWaterCollision_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverPrismalManager_eventCheckWaterCollision_Parms), &Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::NewProp_OutCollisionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "CheckWaterCollision", Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::RiverPrismalManager_eventCheckWaterCollision_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::RiverPrismalManager_eventCheckWaterCollision_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execCheckWaterCollision)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FRiverCollisionData,Z_Param_Out_OutCollisionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CheckWaterCollision(Z_Param_Out_Position,Z_Param_Out_OutCollisionData);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function CheckWaterCollision **************************

// ********** Begin Class ARiverPrismalManager Function CreateBridges ******************************
struct Z_Construct_UFunction_ARiverPrismalManager_CreateBridges_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bridge System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de pontes\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de pontes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_CreateBridges_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "CreateBridges", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_CreateBridges_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_CreateBridges_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARiverPrismalManager_CreateBridges()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_CreateBridges_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execCreateBridges)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateBridges();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function CreateBridges ********************************

// ********** Begin Class ARiverPrismalManager Function DrawDebugBridges ***************************
struct Z_Construct_UFunction_ARiverPrismalManager_DrawDebugBridges_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_DrawDebugBridges_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "DrawDebugBridges", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_DrawDebugBridges_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_DrawDebugBridges_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARiverPrismalManager_DrawDebugBridges()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_DrawDebugBridges_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execDrawDebugBridges)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugBridges();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function DrawDebugBridges *****************************

// ********** Begin Class ARiverPrismalManager Function DrawDebugIsland ****************************
struct Z_Construct_UFunction_ARiverPrismalManager_DrawDebugIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_DrawDebugIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "DrawDebugIsland", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_DrawDebugIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_DrawDebugIsland_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARiverPrismalManager_DrawDebugIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_DrawDebugIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execDrawDebugIsland)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugIsland();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function DrawDebugIsland ******************************

// ********** Begin Class ARiverPrismalManager Function DrawDebugRiver *****************************
struct Z_Construct_UFunction_ARiverPrismalManager_DrawDebugRiver_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_DrawDebugRiver_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "DrawDebugRiver", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_DrawDebugRiver_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_DrawDebugRiver_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARiverPrismalManager_DrawDebugRiver()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_DrawDebugRiver_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execDrawDebugRiver)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugRiver();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function DrawDebugRiver *******************************

// ********** Begin Class ARiverPrismalManager Function DrawDebugWaterFlow *************************
struct Z_Construct_UFunction_ARiverPrismalManager_DrawDebugWaterFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_DrawDebugWaterFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "DrawDebugWaterFlow", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_DrawDebugWaterFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_DrawDebugWaterFlow_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARiverPrismalManager_DrawDebugWaterFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_DrawDebugWaterFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execDrawDebugWaterFlow)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugWaterFlow();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function DrawDebugWaterFlow ***************************

// ********** Begin Class ARiverPrismalManager Function FindWaterPath ******************************
struct Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics
{
	struct RiverPrismalManager_eventFindWaterPath_Parms
	{
		FVector StartPos;
		FVector EndPos;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Water Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de navega\xc3\xa7\xc3\xa3o aqu\xc3\xa1tica\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de navega\xc3\xa7\xc3\xa3o aqu\xc3\xa1tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventFindWaterPath_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventFindWaterPath_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventFindWaterPath_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "FindWaterPath", Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::RiverPrismalManager_eventFindWaterPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::RiverPrismalManager_eventFindWaterPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execFindWaterPath)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->FindWaterPath(Z_Param_Out_StartPos,Z_Param_Out_EndPos);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function FindWaterPath ********************************

// ********** Begin Class ARiverPrismalManager Function GenerateHexagonalIsland ********************
struct Z_Construct_UFunction_ARiverPrismalManager_GenerateHexagonalIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es da ilha hexagonal\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es da ilha hexagonal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GenerateHexagonalIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GenerateHexagonalIsland", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GenerateHexagonalIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GenerateHexagonalIsland_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GenerateHexagonalIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GenerateHexagonalIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGenerateHexagonalIsland)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateHexagonalIsland();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GenerateHexagonalIsland **********************

// ********** Begin Class ARiverPrismalManager Function GenerateRiverBankPoints ********************
struct Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics
{
	struct RiverPrismalManager_eventGenerateRiverBankPoints_Parms
	{
		TArray<FVector> CenterLine;
		float BankOffset;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Geometry" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterLine_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterLine_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CenterLine;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BankOffset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::NewProp_CenterLine_Inner = { "CenterLine", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::NewProp_CenterLine = { "CenterLine", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGenerateRiverBankPoints_Parms, CenterLine), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterLine_MetaData), NewProp_CenterLine_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::NewProp_BankOffset = { "BankOffset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGenerateRiverBankPoints_Parms, BankOffset), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGenerateRiverBankPoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::NewProp_CenterLine_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::NewProp_CenterLine,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::NewProp_BankOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GenerateRiverBankPoints", Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::RiverPrismalManager_eventGenerateRiverBankPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::RiverPrismalManager_eventGenerateRiverBankPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGenerateRiverBankPoints)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_CenterLine);
	P_GET_PROPERTY(FFloatProperty,Z_Param_BankOffset);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GenerateRiverBankPoints(Z_Param_Out_CenterLine,Z_Param_BankOffset);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GenerateRiverBankPoints **********************

// ********** Begin Class ARiverPrismalManager Function GenerateRiverGeometry **********************
struct Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverGeometry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GenerateRiverGeometry", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverGeometry_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGenerateRiverGeometry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateRiverGeometry();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GenerateRiverGeometry ************************

// ********** Begin Class ARiverPrismalManager Function GenerateSinusoidalPath *********************
struct Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics
{
	struct RiverPrismalManager_eventGenerateSinusoidalPath_Parms
	{
		FVector StartPos;
		FVector EndPos;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Geometry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de geometria senoidal\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de geometria senoidal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGenerateSinusoidalPath_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGenerateSinusoidalPath_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGenerateSinusoidalPath_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GenerateSinusoidalPath", Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::RiverPrismalManager_eventGenerateSinusoidalPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::RiverPrismalManager_eventGenerateSinusoidalPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGenerateSinusoidalPath)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GenerateSinusoidalPath(Z_Param_Out_StartPos,Z_Param_Out_EndPos);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GenerateSinusoidalPath ***********************

// ********** Begin Class ARiverPrismalManager Function GetClosestPointOnIsland ********************
struct Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics
{
	struct RiverPrismalManager_eventGetClosestPointOnIsland_Parms
	{
		FVector Position;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Geometry" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetClosestPointOnIsland_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetClosestPointOnIsland_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GetClosestPointOnIsland", Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::RiverPrismalManager_eventGetClosestPointOnIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::RiverPrismalManager_eventGetClosestPointOnIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGetClosestPointOnIsland)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetClosestPointOnIsland(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GetClosestPointOnIsland **********************

// ********** Begin Class ARiverPrismalManager Function GetIslandArea ******************************
struct Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea_Statics
{
	struct RiverPrismalManager_eventGetIslandArea_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetIslandArea_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GetIslandArea", Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea_Statics::RiverPrismalManager_eventGetIslandArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea_Statics::RiverPrismalManager_eventGetIslandArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGetIslandArea)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetIslandArea();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GetIslandArea ********************************

// ********** Begin Class ARiverPrismalManager Function GetIslandCenter ****************************
struct Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter_Statics
{
	struct RiverPrismalManager_eventGetIslandCenter_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetIslandCenter_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GetIslandCenter", Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter_Statics::RiverPrismalManager_eventGetIslandCenter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter_Statics::RiverPrismalManager_eventGetIslandCenter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGetIslandCenter)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetIslandCenter();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GetIslandCenter ******************************

// ********** Begin Class ARiverPrismalManager Function GetNearestBridgePosition *******************
struct Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics
{
	struct RiverPrismalManager_eventGetNearestBridgePosition_Parms
	{
		FVector Position;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bridge System" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetNearestBridgePosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetNearestBridgePosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GetNearestBridgePosition", Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::RiverPrismalManager_eventGetNearestBridgePosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::RiverPrismalManager_eventGetNearestBridgePosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGetNearestBridgePosition)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetNearestBridgePosition(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GetNearestBridgePosition *********************

// ********** Begin Class ARiverPrismalManager Function GetRiverCenter *****************************
struct Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter_Statics
{
	struct RiverPrismalManager_eventGetRiverCenter_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetRiverCenter_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GetRiverCenter", Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter_Statics::RiverPrismalManager_eventGetRiverCenter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter_Statics::RiverPrismalManager_eventGetRiverCenter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGetRiverCenter)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetRiverCenter();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GetRiverCenter *******************************

// ********** Begin Class ARiverPrismalManager Function GetRiverVolume *****************************
struct Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume_Statics
{
	struct RiverPrismalManager_eventGetRiverVolume_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetRiverVolume_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GetRiverVolume", Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume_Statics::RiverPrismalManager_eventGetRiverVolume_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume_Statics::RiverPrismalManager_eventGetRiverVolume_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGetRiverVolume)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetRiverVolume();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GetRiverVolume *******************************

// ********** Begin Class ARiverPrismalManager Function GetSafeWaterPosition ***********************
struct Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics
{
	struct RiverPrismalManager_eventGetSafeWaterPosition_Parms
	{
		FVector DesiredPosition;
		float SafeDistance;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Water Navigation" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DesiredPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DesiredPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SafeDistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::NewProp_DesiredPosition = { "DesiredPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetSafeWaterPosition_Parms, DesiredPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DesiredPosition_MetaData), NewProp_DesiredPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::NewProp_SafeDistance = { "SafeDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetSafeWaterPosition_Parms, SafeDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetSafeWaterPosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::NewProp_DesiredPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::NewProp_SafeDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GetSafeWaterPosition", Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::RiverPrismalManager_eventGetSafeWaterPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::RiverPrismalManager_eventGetSafeWaterPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGetSafeWaterPosition)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_DesiredPosition);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SafeDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetSafeWaterPosition(Z_Param_Out_DesiredPosition,Z_Param_SafeDistance);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GetSafeWaterPosition *************************

// ********** Begin Class ARiverPrismalManager Function GetSwimmingLanes ***************************
struct Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics
{
	struct RiverPrismalManager_eventGetSwimmingLanes_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Water Navigation" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetSwimmingLanes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GetSwimmingLanes", Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::RiverPrismalManager_eventGetSwimmingLanes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::RiverPrismalManager_eventGetSwimmingLanes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGetSwimmingLanes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GetSwimmingLanes();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GetSwimmingLanes *****************************

// ********** Begin Class ARiverPrismalManager Function GetTotalRiverLength ************************
struct Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength_Statics
{
	struct RiverPrismalManager_eventGetTotalRiverLength_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Getters\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Getters" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetTotalRiverLength_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GetTotalRiverLength", Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength_Statics::RiverPrismalManager_eventGetTotalRiverLength_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength_Statics::RiverPrismalManager_eventGetTotalRiverLength_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGetTotalRiverLength)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalRiverLength();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GetTotalRiverLength **************************

// ********** Begin Class ARiverPrismalManager Function GetWaterSurfacePosition ********************
struct Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics
{
	struct RiverPrismalManager_eventGetWaterSurfacePosition_Parms
	{
		FVector Position;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Water Collision" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetWaterSurfacePosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventGetWaterSurfacePosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "GetWaterSurfacePosition", Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::RiverPrismalManager_eventGetWaterSurfacePosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::RiverPrismalManager_eventGetWaterSurfacePosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execGetWaterSurfacePosition)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetWaterSurfacePosition(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function GetWaterSurfacePosition **********************

// ********** Begin Class ARiverPrismalManager Function InitializeRiverSystem **********************
struct Z_Construct_UFunction_ARiverPrismalManager_InitializeRiverSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es principais de inicializa\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es principais de inicializa\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_InitializeRiverSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "InitializeRiverSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_InitializeRiverSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_InitializeRiverSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARiverPrismalManager_InitializeRiverSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_InitializeRiverSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execInitializeRiverSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeRiverSystem();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function InitializeRiverSystem ************************

// ********** Begin Class ARiverPrismalManager Function InterpolateWaterDepth **********************
struct Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics
{
	struct RiverPrismalManager_eventInterpolateWaterDepth_Parms
	{
		FVector Position;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Math Utilities" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventInterpolateWaterDepth_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventInterpolateWaterDepth_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "InterpolateWaterDepth", Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::RiverPrismalManager_eventInterpolateWaterDepth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::RiverPrismalManager_eventInterpolateWaterDepth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execInterpolateWaterDepth)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->InterpolateWaterDepth(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function InterpolateWaterDepth ************************

// ********** Begin Class ARiverPrismalManager Function IsPointInsideHexagon ***********************
struct Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics
{
	struct RiverPrismalManager_eventIsPointInsideHexagon_Parms
	{
		FVector Point;
		FHexagonalIsland Island;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Geometry" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Island_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Island;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventIsPointInsideHexagon_Parms, Point), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::NewProp_Island = { "Island", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventIsPointInsideHexagon_Parms, Island), Z_Construct_UScriptStruct_FHexagonalIsland, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Island_MetaData), NewProp_Island_MetaData) }; // 3488135994
void Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverPrismalManager_eventIsPointInsideHexagon_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverPrismalManager_eventIsPointInsideHexagon_Parms), &Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::NewProp_Island,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "IsPointInsideHexagon", Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::RiverPrismalManager_eventIsPointInsideHexagon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::RiverPrismalManager_eventIsPointInsideHexagon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execIsPointInsideHexagon)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point);
	P_GET_STRUCT_REF(FHexagonalIsland,Z_Param_Out_Island);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPointInsideHexagon(Z_Param_Out_Point,Z_Param_Out_Island);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function IsPointInsideHexagon *************************

// ********** Begin Class ARiverPrismalManager Function IsPositionInWater **************************
struct Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics
{
	struct RiverPrismalManager_eventIsPositionInWater_Parms
	{
		FVector Position;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Water Collision" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventIsPositionInWater_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
void Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverPrismalManager_eventIsPositionInWater_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverPrismalManager_eventIsPositionInWater_Parms), &Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "IsPositionInWater", Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::RiverPrismalManager_eventIsPositionInWater_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::RiverPrismalManager_eventIsPositionInWater_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execIsPositionInWater)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPositionInWater(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function IsPositionInWater ****************************

// ********** Begin Class ARiverPrismalManager Function IsPositionOnIsland *************************
struct Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics
{
	struct RiverPrismalManager_eventIsPositionOnIsland_Parms
	{
		FVector Position;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Water Collision" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventIsPositionOnIsland_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
void Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverPrismalManager_eventIsPositionOnIsland_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverPrismalManager_eventIsPositionOnIsland_Parms), &Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "IsPositionOnIsland", Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::RiverPrismalManager_eventIsPositionOnIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::RiverPrismalManager_eventIsPositionOnIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execIsPositionOnIsland)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPositionOnIsland(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function IsPositionOnIsland ***************************

// ********** Begin Class ARiverPrismalManager Function IsWaterPathClear ***************************
struct Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics
{
	struct RiverPrismalManager_eventIsWaterPathClear_Parms
	{
		FVector StartPos;
		FVector EndPos;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Water Navigation" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventIsWaterPathClear_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventIsWaterPathClear_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
void Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverPrismalManager_eventIsWaterPathClear_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverPrismalManager_eventIsWaterPathClear_Parms), &Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "IsWaterPathClear", Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::RiverPrismalManager_eventIsWaterPathClear_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::RiverPrismalManager_eventIsWaterPathClear_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execIsWaterPathClear)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsWaterPathClear(Z_Param_Out_StartPos,Z_Param_Out_EndPos);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function IsWaterPathClear *****************************

// ********** Begin Class ARiverPrismalManager Function OnActorEnterWater **************************
struct Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics
{
	struct RiverPrismalManager_eventOnActorEnterWater_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComponent;
		int32 OtherBodyIndex;
		bool bFromSweep;
		FHitResult SweepResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SweepResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComponent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static void NewProp_bFromSweep_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromSweep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SweepResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnActorEnterWater_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnActorEnterWater_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_OtherComponent = { "OtherComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnActorEnterWater_Parms, OtherComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComponent_MetaData), NewProp_OtherComponent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnActorEnterWater_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_bFromSweep_SetBit(void* Obj)
{
	((RiverPrismalManager_eventOnActorEnterWater_Parms*)Obj)->bFromSweep = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_bFromSweep = { "bFromSweep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverPrismalManager_eventOnActorEnterWater_Parms), &Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_bFromSweep_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_SweepResult = { "SweepResult", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnActorEnterWater_Parms, SweepResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SweepResult_MetaData), NewProp_SweepResult_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_OtherComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_OtherBodyIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_bFromSweep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::NewProp_SweepResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "OnActorEnterWater", Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::RiverPrismalManager_eventOnActorEnterWater_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::RiverPrismalManager_eventOnActorEnterWater_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execOnActorEnterWater)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComponent);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_GET_UBOOL(Z_Param_bFromSweep);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_SweepResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnActorEnterWater(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComponent,Z_Param_OtherBodyIndex,Z_Param_bFromSweep,Z_Param_Out_SweepResult);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function OnActorEnterWater ****************************

// ********** Begin Class ARiverPrismalManager Function OnActorExitWater ***************************
struct Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics
{
	struct RiverPrismalManager_eventOnActorExitWater_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComponent;
		int32 OtherBodyIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComponent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnActorExitWater_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnActorExitWater_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::NewProp_OtherComponent = { "OtherComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnActorExitWater_Parms, OtherComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComponent_MetaData), NewProp_OtherComponent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnActorExitWater_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::NewProp_OtherComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::NewProp_OtherBodyIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "OnActorExitWater", Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::RiverPrismalManager_eventOnActorExitWater_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::RiverPrismalManager_eventOnActorExitWater_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execOnActorExitWater)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComponent);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnActorExitWater(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComponent,Z_Param_OtherBodyIndex);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function OnActorExitWater *****************************

// ********** Begin Class ARiverPrismalManager Function OnWaterHit *********************************
struct Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics
{
	struct RiverPrismalManager_eventOnWaterHit_Parms
	{
		UPrimitiveComponent* HitComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComponent;
		FVector NormalImpulse;
		FHitResult Hit;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eventos de colis\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos de colis\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HitComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Hit_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HitComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NormalImpulse;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Hit;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::NewProp_HitComponent = { "HitComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnWaterHit_Parms, HitComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HitComponent_MetaData), NewProp_HitComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnWaterHit_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::NewProp_OtherComponent = { "OtherComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnWaterHit_Parms, OtherComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComponent_MetaData), NewProp_OtherComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::NewProp_NormalImpulse = { "NormalImpulse", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnWaterHit_Parms, NormalImpulse), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::NewProp_Hit = { "Hit", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventOnWaterHit_Parms, Hit), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Hit_MetaData), NewProp_Hit_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::NewProp_HitComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::NewProp_OtherComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::NewProp_NormalImpulse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::NewProp_Hit,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "OnWaterHit", Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::RiverPrismalManager_eventOnWaterHit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::RiverPrismalManager_eventOnWaterHit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execOnWaterHit)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_HitComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComponent);
	P_GET_STRUCT(FVector,Z_Param_NormalImpulse);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_Hit);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnWaterHit(Z_Param_HitComponent,Z_Param_OtherActor,Z_Param_OtherComponent,Z_Param_NormalImpulse,Z_Param_Out_Hit);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function OnWaterHit ***********************************

// ********** Begin Class ARiverPrismalManager Function ProjectPointOntoRiver **********************
struct Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics
{
	struct RiverPrismalManager_eventProjectPointOntoRiver_Parms
	{
		FVector Position;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Math Utilities" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventProjectPointOntoRiver_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventProjectPointOntoRiver_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "ProjectPointOntoRiver", Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::RiverPrismalManager_eventProjectPointOntoRiver_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::RiverPrismalManager_eventProjectPointOntoRiver_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execProjectPointOntoRiver)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->ProjectPointOntoRiver(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function ProjectPointOntoRiver ************************

// ********** Begin Class ARiverPrismalManager Function RemoveBridge *******************************
struct Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge_Statics
{
	struct RiverPrismalManager_eventRemoveBridge_Parms
	{
		int32 BridgeIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bridge System" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_BridgeIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge_Statics::NewProp_BridgeIndex = { "BridgeIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventRemoveBridge_Parms, BridgeIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge_Statics::NewProp_BridgeIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "RemoveBridge", Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge_Statics::RiverPrismalManager_eventRemoveBridge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge_Statics::RiverPrismalManager_eventRemoveBridge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execRemoveBridge)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_BridgeIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveBridge(Z_Param_BridgeIndex);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function RemoveBridge *********************************

// ********** Begin Class ARiverPrismalManager Function RotateVectorAroundAxis *********************
struct Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics
{
	struct RiverPrismalManager_eventRotateVectorAroundAxis_Parms
	{
		FVector Vector;
		FVector Axis;
		float AngleDegrees;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Math Utilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de utilidade matem\xc3\xa1tica\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de utilidade matem\xc3\xa1tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Vector_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Axis_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Vector;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Axis;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AngleDegrees;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::NewProp_Vector = { "Vector", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventRotateVectorAroundAxis_Parms, Vector), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Vector_MetaData), NewProp_Vector_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::NewProp_Axis = { "Axis", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventRotateVectorAroundAxis_Parms, Axis), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Axis_MetaData), NewProp_Axis_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::NewProp_AngleDegrees = { "AngleDegrees", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventRotateVectorAroundAxis_Parms, AngleDegrees), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventRotateVectorAroundAxis_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::NewProp_Vector,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::NewProp_Axis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::NewProp_AngleDegrees,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "RotateVectorAroundAxis", Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::RiverPrismalManager_eventRotateVectorAroundAxis_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::RiverPrismalManager_eventRotateVectorAroundAxis_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execRotateVectorAroundAxis)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Vector);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Axis);
	P_GET_PROPERTY(FFloatProperty,Z_Param_AngleDegrees);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->RotateVectorAroundAxis(Z_Param_Out_Vector,Z_Param_Out_Axis,Z_Param_AngleDegrees);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function RotateVectorAroundAxis ***********************

// ********** Begin Class ARiverPrismalManager Function SetIslandRadius ****************************
struct Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius_Statics
{
	struct RiverPrismalManager_eventSetIslandRadius_Parms
	{
		float NewRadius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setters" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius_Statics::NewProp_NewRadius = { "NewRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventSetIslandRadius_Parms, NewRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius_Statics::NewProp_NewRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "SetIslandRadius", Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius_Statics::RiverPrismalManager_eventSetIslandRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius_Statics::RiverPrismalManager_eventSetIslandRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execSetIslandRadius)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetIslandRadius(Z_Param_NewRadius);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function SetIslandRadius ******************************

// ********** Begin Class ARiverPrismalManager Function SetRiverDimensions *************************
struct Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics
{
	struct RiverPrismalManager_eventSetRiverDimensions_Parms
	{
		float NewLength;
		float NewWidth;
		float NewDepth;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setters" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewLength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewDepth;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::NewProp_NewLength = { "NewLength", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventSetRiverDimensions_Parms, NewLength), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::NewProp_NewWidth = { "NewWidth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventSetRiverDimensions_Parms, NewWidth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::NewProp_NewDepth = { "NewDepth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventSetRiverDimensions_Parms, NewDepth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::NewProp_NewLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::NewProp_NewWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::NewProp_NewDepth,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "SetRiverDimensions", Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::RiverPrismalManager_eventSetRiverDimensions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::RiverPrismalManager_eventSetRiverDimensions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execSetRiverDimensions)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewLength);
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewWidth);
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewDepth);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetRiverDimensions(Z_Param_NewLength,Z_Param_NewWidth,Z_Param_NewDepth);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function SetRiverDimensions ***************************

// ********** Begin Class ARiverPrismalManager Function SetSinusoidalParameters ********************
struct Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics
{
	struct RiverPrismalManager_eventSetSinusoidalParameters_Parms
	{
		float NewAmplitude;
		float NewFrequency;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setters" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewAmplitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewFrequency;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::NewProp_NewAmplitude = { "NewAmplitude", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventSetSinusoidalParameters_Parms, NewAmplitude), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::NewProp_NewFrequency = { "NewFrequency", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventSetSinusoidalParameters_Parms, NewFrequency), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::NewProp_NewAmplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::NewProp_NewFrequency,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "SetSinusoidalParameters", Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::RiverPrismalManager_eventSetSinusoidalParameters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::RiverPrismalManager_eventSetSinusoidalParameters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execSetSinusoidalParameters)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewAmplitude);
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewFrequency);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSinusoidalParameters(Z_Param_NewAmplitude,Z_Param_NewFrequency);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function SetSinusoidalParameters **********************

// ********** Begin Class ARiverPrismalManager Function SetupWaterPhysics **************************
struct Z_Construct_UFunction_ARiverPrismalManager_SetupWaterPhysics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River System" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_SetupWaterPhysics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "SetupWaterPhysics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_SetupWaterPhysics_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_SetupWaterPhysics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARiverPrismalManager_SetupWaterPhysics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_SetupWaterPhysics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execSetupWaterPhysics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupWaterPhysics();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function SetupWaterPhysics ****************************

// ********** Begin Class ARiverPrismalManager Function SetWaterProperties *************************
struct Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties_Statics
{
	struct RiverPrismalManager_eventSetWaterProperties_Parms
	{
		FWaterProperties InWaterConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Setters\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Setters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InWaterConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InWaterConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties_Statics::NewProp_InWaterConfig = { "InWaterConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RiverPrismalManager_eventSetWaterProperties_Parms, InWaterConfig), Z_Construct_UScriptStruct_FWaterProperties, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InWaterConfig_MetaData), NewProp_InWaterConfig_MetaData) }; // 2467210794
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties_Statics::NewProp_InWaterConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "SetWaterProperties", Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties_Statics::RiverPrismalManager_eventSetWaterProperties_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties_Statics::RiverPrismalManager_eventSetWaterProperties_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execSetWaterProperties)
{
	P_GET_STRUCT_REF(FWaterProperties,Z_Param_Out_InWaterConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWaterProperties(Z_Param_Out_InWaterConfig);
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function SetWaterProperties ***************************

// ********** Begin Class ARiverPrismalManager Function ValidateIslandGeometry *********************
struct Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics
{
	struct RiverPrismalManager_eventValidateIslandGeometry_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverPrismalManager_eventValidateIslandGeometry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverPrismalManager_eventValidateIslandGeometry_Parms), &Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "ValidateIslandGeometry", Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::RiverPrismalManager_eventValidateIslandGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::RiverPrismalManager_eventValidateIslandGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execValidateIslandGeometry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateIslandGeometry();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function ValidateIslandGeometry ***********************

// ********** Begin Class ARiverPrismalManager Function ValidateRiverGeometry **********************
struct Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics
{
	struct RiverPrismalManager_eventValidateRiverGeometry_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o e debug\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o e debug" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverPrismalManager_eventValidateRiverGeometry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverPrismalManager_eventValidateRiverGeometry_Parms), &Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "ValidateRiverGeometry", Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::RiverPrismalManager_eventValidateRiverGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::RiverPrismalManager_eventValidateRiverGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execValidateRiverGeometry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateRiverGeometry();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function ValidateRiverGeometry ************************

// ********** Begin Class ARiverPrismalManager Function ValidateWaterPhysics ***********************
struct Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics
{
	struct RiverPrismalManager_eventValidateWaterPhysics_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RiverPrismalManager_eventValidateWaterPhysics_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RiverPrismalManager_eventValidateWaterPhysics_Parms), &Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARiverPrismalManager, nullptr, "ValidateWaterPhysics", Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::RiverPrismalManager_eventValidateWaterPhysics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::RiverPrismalManager_eventValidateWaterPhysics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARiverPrismalManager::execValidateWaterPhysics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateWaterPhysics();
	P_NATIVE_END;
}
// ********** End Class ARiverPrismalManager Function ValidateWaterPhysics *************************

// ********** Begin Class ARiverPrismalManager *****************************************************
void ARiverPrismalManager::StaticRegisterNativesARiverPrismalManager()
{
	UClass* Class = ARiverPrismalManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddBridge", &ARiverPrismalManager::execAddBridge },
		{ "ApplyWaterCurrent", &ARiverPrismalManager::execApplyWaterCurrent },
		{ "CalculateDistanceToRiver", &ARiverPrismalManager::execCalculateDistanceToRiver },
		{ "CalculateFlowDirection", &ARiverPrismalManager::execCalculateFlowDirection },
		{ "CalculateFlowSpeed", &ARiverPrismalManager::execCalculateFlowSpeed },
		{ "CalculateHexagonApothem", &ARiverPrismalManager::execCalculateHexagonApothem },
		{ "CalculateHexagonEdgeLength", &ARiverPrismalManager::execCalculateHexagonEdgeLength },
		{ "CalculateHexagonEdgeMidpoints", &ARiverPrismalManager::execCalculateHexagonEdgeMidpoints },
		{ "CalculateHexagonVertices", &ARiverPrismalManager::execCalculateHexagonVertices },
		{ "CalculateSinusoidalPoint", &ARiverPrismalManager::execCalculateSinusoidalPoint },
		{ "CalculateWaterDepth", &ARiverPrismalManager::execCalculateWaterDepth },
		{ "CalculateWaterNormal", &ARiverPrismalManager::execCalculateWaterNormal },
		{ "CanUnitCrossBridge", &ARiverPrismalManager::execCanUnitCrossBridge },
		{ "CheckWaterCollision", &ARiverPrismalManager::execCheckWaterCollision },
		{ "CreateBridges", &ARiverPrismalManager::execCreateBridges },
		{ "DrawDebugBridges", &ARiverPrismalManager::execDrawDebugBridges },
		{ "DrawDebugIsland", &ARiverPrismalManager::execDrawDebugIsland },
		{ "DrawDebugRiver", &ARiverPrismalManager::execDrawDebugRiver },
		{ "DrawDebugWaterFlow", &ARiverPrismalManager::execDrawDebugWaterFlow },
		{ "FindWaterPath", &ARiverPrismalManager::execFindWaterPath },
		{ "GenerateHexagonalIsland", &ARiverPrismalManager::execGenerateHexagonalIsland },
		{ "GenerateRiverBankPoints", &ARiverPrismalManager::execGenerateRiverBankPoints },
		{ "GenerateRiverGeometry", &ARiverPrismalManager::execGenerateRiverGeometry },
		{ "GenerateSinusoidalPath", &ARiverPrismalManager::execGenerateSinusoidalPath },
		{ "GetClosestPointOnIsland", &ARiverPrismalManager::execGetClosestPointOnIsland },
		{ "GetIslandArea", &ARiverPrismalManager::execGetIslandArea },
		{ "GetIslandCenter", &ARiverPrismalManager::execGetIslandCenter },
		{ "GetNearestBridgePosition", &ARiverPrismalManager::execGetNearestBridgePosition },
		{ "GetRiverCenter", &ARiverPrismalManager::execGetRiverCenter },
		{ "GetRiverVolume", &ARiverPrismalManager::execGetRiverVolume },
		{ "GetSafeWaterPosition", &ARiverPrismalManager::execGetSafeWaterPosition },
		{ "GetSwimmingLanes", &ARiverPrismalManager::execGetSwimmingLanes },
		{ "GetTotalRiverLength", &ARiverPrismalManager::execGetTotalRiverLength },
		{ "GetWaterSurfacePosition", &ARiverPrismalManager::execGetWaterSurfacePosition },
		{ "InitializeRiverSystem", &ARiverPrismalManager::execInitializeRiverSystem },
		{ "InterpolateWaterDepth", &ARiverPrismalManager::execInterpolateWaterDepth },
		{ "IsPointInsideHexagon", &ARiverPrismalManager::execIsPointInsideHexagon },
		{ "IsPositionInWater", &ARiverPrismalManager::execIsPositionInWater },
		{ "IsPositionOnIsland", &ARiverPrismalManager::execIsPositionOnIsland },
		{ "IsWaterPathClear", &ARiverPrismalManager::execIsWaterPathClear },
		{ "OnActorEnterWater", &ARiverPrismalManager::execOnActorEnterWater },
		{ "OnActorExitWater", &ARiverPrismalManager::execOnActorExitWater },
		{ "OnWaterHit", &ARiverPrismalManager::execOnWaterHit },
		{ "ProjectPointOntoRiver", &ARiverPrismalManager::execProjectPointOntoRiver },
		{ "RemoveBridge", &ARiverPrismalManager::execRemoveBridge },
		{ "RotateVectorAroundAxis", &ARiverPrismalManager::execRotateVectorAroundAxis },
		{ "SetIslandRadius", &ARiverPrismalManager::execSetIslandRadius },
		{ "SetRiverDimensions", &ARiverPrismalManager::execSetRiverDimensions },
		{ "SetSinusoidalParameters", &ARiverPrismalManager::execSetSinusoidalParameters },
		{ "SetupWaterPhysics", &ARiverPrismalManager::execSetupWaterPhysics },
		{ "SetWaterProperties", &ARiverPrismalManager::execSetWaterProperties },
		{ "ValidateIslandGeometry", &ARiverPrismalManager::execValidateIslandGeometry },
		{ "ValidateRiverGeometry", &ARiverPrismalManager::execValidateRiverGeometry },
		{ "ValidateWaterPhysics", &ARiverPrismalManager::execValidateWaterPhysics },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ARiverPrismalManager;
UClass* ARiverPrismalManager::GetPrivateStaticClass()
{
	using TClass = ARiverPrismalManager;
	if (!Z_Registration_Info_UClass_ARiverPrismalManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RiverPrismalManager"),
			Z_Registration_Info_UClass_ARiverPrismalManager.InnerSingleton,
			StaticRegisterNativesARiverPrismalManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ARiverPrismalManager.InnerSingleton;
}
UClass* Z_Construct_UClass_ARiverPrismalManager_NoRegister()
{
	return ARiverPrismalManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ARiverPrismalManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Gerenciador do sistema de rio com geometria senoidal e ilha hexagonal central\n * Implementa f\xc3\xadsica da \xc3\xa1gua, colis\xc3\xb5""es e navega\xc3\xa7\xc3\xa3o aqu\xc3\xa1tica\n */" },
#endif
		{ "IncludePath", "ARiverPrismalManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerenciador do sistema de rio com geometria senoidal e ilha hexagonal central\nImplementa f\xc3\xadsica da \xc3\xa1gua, colis\xc3\xb5""es e navega\xc3\xa7\xc3\xa3o aqu\xc3\xa1tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes principais\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes principais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverSpline_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandMesh_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaterCollisionBox_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverLength_MetaData[] = {
		{ "Category", "River Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es do rio\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es do rio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverWidth_MetaData[] = {
		{ "Category", "River Settings" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverDepth_MetaData[] = {
		{ "Category", "River Settings" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SinusoidalAmplitude_MetaData[] = {
		{ "Category", "River Settings" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SinusoidalFrequency_MetaData[] = {
		{ "Category", "River Settings" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverSegmentCount_MetaData[] = {
		{ "Category", "River Settings" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowDirection_MetaData[] = {
		{ "Category", "River Settings" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CentralIsland_MetaData[] = {
		{ "Category", "Island Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es da ilha\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es da ilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateIsland_MetaData[] = {
		{ "Category", "Island Settings" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaterConfig_MetaData[] = {
		{ "Category", "Water Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Propriedades da \xc3\xa1gua\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedades da \xc3\xa1gua" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverSegments_MetaData[] = {
		{ "Category", "River Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Arrays de dados\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Arrays de dados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverMeshes_MetaData[] = {
		{ "Category", "River Data" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaterCollisionBoxes_MetaData[] = {
		{ "Category", "River Data" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Bridges_MetaData[] = {
		{ "Category", "Bridge Data" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BridgeMeshes_MetaData[] = {
		{ "Category", "Bridge Data" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecentWaterCollisions_MetaData[] = {
		{ "Category", "Collision Data" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaterMaterial_MetaData[] = {
		{ "Category", "Materials" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Materiais\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Materiais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandMaterial_MetaData[] = {
		{ "Category", "Materials" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BridgeMaterial_MetaData[] = {
		{ "Category", "Materials" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDebugLines_MetaData[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de debug\n" },
#endif
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de debug" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowFlowVectors_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowIslandGeometry_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCollisionLogging_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/ARiverPrismalManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RiverSpline;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IslandMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WaterCollisionBox;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RiverLength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RiverWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RiverDepth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SinusoidalAmplitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SinusoidalFrequency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RiverSegmentCount;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FlowDirection_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FlowDirection;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CentralIsland;
	static void NewProp_bGenerateIsland_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateIsland;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WaterConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RiverSegments_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RiverSegments;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RiverMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RiverMeshes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WaterCollisionBoxes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WaterCollisionBoxes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Bridges_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Bridges;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BridgeMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BridgeMeshes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RecentWaterCollisions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RecentWaterCollisions;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WaterMaterial;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IslandMaterial;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BridgeMaterial;
	static void NewProp_bShowDebugLines_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDebugLines;
	static void NewProp_bShowFlowVectors_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowFlowVectors;
	static void NewProp_bShowIslandGeometry_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowIslandGeometry;
	static void NewProp_bEnableCollisionLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCollisionLogging;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ARiverPrismalManager_AddBridge, "AddBridge" }, // 304698607
		{ &Z_Construct_UFunction_ARiverPrismalManager_ApplyWaterCurrent, "ApplyWaterCurrent" }, // 2431153752
		{ &Z_Construct_UFunction_ARiverPrismalManager_CalculateDistanceToRiver, "CalculateDistanceToRiver" }, // 951965510
		{ &Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowDirection, "CalculateFlowDirection" }, // 3955684655
		{ &Z_Construct_UFunction_ARiverPrismalManager_CalculateFlowSpeed, "CalculateFlowSpeed" }, // 2643112119
		{ &Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonApothem, "CalculateHexagonApothem" }, // 3323167262
		{ &Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeLength, "CalculateHexagonEdgeLength" }, // 1137174605
		{ &Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonEdgeMidpoints, "CalculateHexagonEdgeMidpoints" }, // 3777512419
		{ &Z_Construct_UFunction_ARiverPrismalManager_CalculateHexagonVertices, "CalculateHexagonVertices" }, // 3653207686
		{ &Z_Construct_UFunction_ARiverPrismalManager_CalculateSinusoidalPoint, "CalculateSinusoidalPoint" }, // 3389001821
		{ &Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterDepth, "CalculateWaterDepth" }, // 969958415
		{ &Z_Construct_UFunction_ARiverPrismalManager_CalculateWaterNormal, "CalculateWaterNormal" }, // 1212811775
		{ &Z_Construct_UFunction_ARiverPrismalManager_CanUnitCrossBridge, "CanUnitCrossBridge" }, // 3492485286
		{ &Z_Construct_UFunction_ARiverPrismalManager_CheckWaterCollision, "CheckWaterCollision" }, // 3505923601
		{ &Z_Construct_UFunction_ARiverPrismalManager_CreateBridges, "CreateBridges" }, // 1703439757
		{ &Z_Construct_UFunction_ARiverPrismalManager_DrawDebugBridges, "DrawDebugBridges" }, // 2198067381
		{ &Z_Construct_UFunction_ARiverPrismalManager_DrawDebugIsland, "DrawDebugIsland" }, // **********
		{ &Z_Construct_UFunction_ARiverPrismalManager_DrawDebugRiver, "DrawDebugRiver" }, // **********
		{ &Z_Construct_UFunction_ARiverPrismalManager_DrawDebugWaterFlow, "DrawDebugWaterFlow" }, // **********
		{ &Z_Construct_UFunction_ARiverPrismalManager_FindWaterPath, "FindWaterPath" }, // **********
		{ &Z_Construct_UFunction_ARiverPrismalManager_GenerateHexagonalIsland, "GenerateHexagonalIsland" }, // **********
		{ &Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverBankPoints, "GenerateRiverBankPoints" }, // **********
		{ &Z_Construct_UFunction_ARiverPrismalManager_GenerateRiverGeometry, "GenerateRiverGeometry" }, // *********
		{ &Z_Construct_UFunction_ARiverPrismalManager_GenerateSinusoidalPath, "GenerateSinusoidalPath" }, // **********
		{ &Z_Construct_UFunction_ARiverPrismalManager_GetClosestPointOnIsland, "GetClosestPointOnIsland" }, // **********
		{ &Z_Construct_UFunction_ARiverPrismalManager_GetIslandArea, "GetIslandArea" }, // **********
		{ &Z_Construct_UFunction_ARiverPrismalManager_GetIslandCenter, "GetIslandCenter" }, // *********
		{ &Z_Construct_UFunction_ARiverPrismalManager_GetNearestBridgePosition, "GetNearestBridgePosition" }, // **********
		{ &Z_Construct_UFunction_ARiverPrismalManager_GetRiverCenter, "GetRiverCenter" }, // **********
		{ &Z_Construct_UFunction_ARiverPrismalManager_GetRiverVolume, "GetRiverVolume" }, // **********
		{ &Z_Construct_UFunction_ARiverPrismalManager_GetSafeWaterPosition, "GetSafeWaterPosition" }, // **********
		{ &Z_Construct_UFunction_ARiverPrismalManager_GetSwimmingLanes, "GetSwimmingLanes" }, // 2551338552
		{ &Z_Construct_UFunction_ARiverPrismalManager_GetTotalRiverLength, "GetTotalRiverLength" }, // 1094994244
		{ &Z_Construct_UFunction_ARiverPrismalManager_GetWaterSurfacePosition, "GetWaterSurfacePosition" }, // 1322469397
		{ &Z_Construct_UFunction_ARiverPrismalManager_InitializeRiverSystem, "InitializeRiverSystem" }, // 1983788182
		{ &Z_Construct_UFunction_ARiverPrismalManager_InterpolateWaterDepth, "InterpolateWaterDepth" }, // 1597420845
		{ &Z_Construct_UFunction_ARiverPrismalManager_IsPointInsideHexagon, "IsPointInsideHexagon" }, // 379639911
		{ &Z_Construct_UFunction_ARiverPrismalManager_IsPositionInWater, "IsPositionInWater" }, // 4249852942
		{ &Z_Construct_UFunction_ARiverPrismalManager_IsPositionOnIsland, "IsPositionOnIsland" }, // 2395093800
		{ &Z_Construct_UFunction_ARiverPrismalManager_IsWaterPathClear, "IsWaterPathClear" }, // 167798847
		{ &Z_Construct_UFunction_ARiverPrismalManager_OnActorEnterWater, "OnActorEnterWater" }, // 243214855
		{ &Z_Construct_UFunction_ARiverPrismalManager_OnActorExitWater, "OnActorExitWater" }, // 2315002960
		{ &Z_Construct_UFunction_ARiverPrismalManager_OnWaterHit, "OnWaterHit" }, // 729573165
		{ &Z_Construct_UFunction_ARiverPrismalManager_ProjectPointOntoRiver, "ProjectPointOntoRiver" }, // 2123011252
		{ &Z_Construct_UFunction_ARiverPrismalManager_RemoveBridge, "RemoveBridge" }, // 1998522194
		{ &Z_Construct_UFunction_ARiverPrismalManager_RotateVectorAroundAxis, "RotateVectorAroundAxis" }, // 875518545
		{ &Z_Construct_UFunction_ARiverPrismalManager_SetIslandRadius, "SetIslandRadius" }, // 3766511066
		{ &Z_Construct_UFunction_ARiverPrismalManager_SetRiverDimensions, "SetRiverDimensions" }, // 1410689863
		{ &Z_Construct_UFunction_ARiverPrismalManager_SetSinusoidalParameters, "SetSinusoidalParameters" }, // 4287904611
		{ &Z_Construct_UFunction_ARiverPrismalManager_SetupWaterPhysics, "SetupWaterPhysics" }, // 3453711491
		{ &Z_Construct_UFunction_ARiverPrismalManager_SetWaterProperties, "SetWaterProperties" }, // 2020780768
		{ &Z_Construct_UFunction_ARiverPrismalManager_ValidateIslandGeometry, "ValidateIslandGeometry" }, // 3095978522
		{ &Z_Construct_UFunction_ARiverPrismalManager_ValidateRiverGeometry, "ValidateRiverGeometry" }, // 4122743553
		{ &Z_Construct_UFunction_ARiverPrismalManager_ValidateWaterPhysics, "ValidateWaterPhysics" }, // 1991218749
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ARiverPrismalManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverSpline = { "RiverSpline", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, RiverSpline), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverSpline_MetaData), NewProp_RiverSpline_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_IslandMesh = { "IslandMesh", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, IslandMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandMesh_MetaData), NewProp_IslandMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_WaterCollisionBox = { "WaterCollisionBox", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, WaterCollisionBox), Z_Construct_UClass_UBoxComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaterCollisionBox_MetaData), NewProp_WaterCollisionBox_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverLength = { "RiverLength", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, RiverLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverLength_MetaData), NewProp_RiverLength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverWidth = { "RiverWidth", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, RiverWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverWidth_MetaData), NewProp_RiverWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverDepth = { "RiverDepth", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, RiverDepth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverDepth_MetaData), NewProp_RiverDepth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_SinusoidalAmplitude = { "SinusoidalAmplitude", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, SinusoidalAmplitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SinusoidalAmplitude_MetaData), NewProp_SinusoidalAmplitude_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_SinusoidalFrequency = { "SinusoidalFrequency", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, SinusoidalFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SinusoidalFrequency_MetaData), NewProp_SinusoidalFrequency_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverSegmentCount = { "RiverSegmentCount", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, RiverSegmentCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverSegmentCount_MetaData), NewProp_RiverSegmentCount_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_FlowDirection_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_FlowDirection = { "FlowDirection", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, FlowDirection), Z_Construct_UEnum_Aura_ERiverFlowDirection, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowDirection_MetaData), NewProp_FlowDirection_MetaData) }; // 2551558300
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_CentralIsland = { "CentralIsland", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, CentralIsland), Z_Construct_UScriptStruct_FHexagonalIsland, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CentralIsland_MetaData), NewProp_CentralIsland_MetaData) }; // 3488135994
void Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bGenerateIsland_SetBit(void* Obj)
{
	((ARiverPrismalManager*)Obj)->bGenerateIsland = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bGenerateIsland = { "bGenerateIsland", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARiverPrismalManager), &Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bGenerateIsland_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateIsland_MetaData), NewProp_bGenerateIsland_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_WaterConfig = { "WaterConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, WaterConfig), Z_Construct_UScriptStruct_FWaterProperties, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaterConfig_MetaData), NewProp_WaterConfig_MetaData) }; // 2467210794
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverSegments_Inner = { "RiverSegments", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRiverSegment, METADATA_PARAMS(0, nullptr) }; // 647102434
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverSegments = { "RiverSegments", nullptr, (EPropertyFlags)0x0020080000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, RiverSegments), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverSegments_MetaData), NewProp_RiverSegments_MetaData) }; // 647102434
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverMeshes_Inner = { "RiverMeshes", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverMeshes = { "RiverMeshes", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, RiverMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverMeshes_MetaData), NewProp_RiverMeshes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_WaterCollisionBoxes_Inner = { "WaterCollisionBoxes", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UBoxComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_WaterCollisionBoxes = { "WaterCollisionBoxes", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, WaterCollisionBoxes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaterCollisionBoxes_MetaData), NewProp_WaterCollisionBoxes_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_Bridges_Inner = { "Bridges", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FBridgeData, METADATA_PARAMS(0, nullptr) }; // 2297366356
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_Bridges = { "Bridges", nullptr, (EPropertyFlags)0x0020080000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, Bridges), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Bridges_MetaData), NewProp_Bridges_MetaData) }; // 2297366356
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_BridgeMeshes_Inner = { "BridgeMeshes", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_BridgeMeshes = { "BridgeMeshes", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, BridgeMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BridgeMeshes_MetaData), NewProp_BridgeMeshes_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RecentWaterCollisions_Inner = { "RecentWaterCollisions", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRiverCollisionData, METADATA_PARAMS(0, nullptr) }; // 547632616
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RecentWaterCollisions = { "RecentWaterCollisions", nullptr, (EPropertyFlags)0x0020080000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, RecentWaterCollisions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecentWaterCollisions_MetaData), NewProp_RecentWaterCollisions_MetaData) }; // 547632616
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_WaterMaterial = { "WaterMaterial", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, WaterMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaterMaterial_MetaData), NewProp_WaterMaterial_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_IslandMaterial = { "IslandMaterial", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, IslandMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandMaterial_MetaData), NewProp_IslandMaterial_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_BridgeMaterial = { "BridgeMaterial", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARiverPrismalManager, BridgeMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BridgeMaterial_MetaData), NewProp_BridgeMaterial_MetaData) };
void Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bShowDebugLines_SetBit(void* Obj)
{
	((ARiverPrismalManager*)Obj)->bShowDebugLines = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bShowDebugLines = { "bShowDebugLines", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARiverPrismalManager), &Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bShowDebugLines_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDebugLines_MetaData), NewProp_bShowDebugLines_MetaData) };
void Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bShowFlowVectors_SetBit(void* Obj)
{
	((ARiverPrismalManager*)Obj)->bShowFlowVectors = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bShowFlowVectors = { "bShowFlowVectors", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARiverPrismalManager), &Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bShowFlowVectors_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowFlowVectors_MetaData), NewProp_bShowFlowVectors_MetaData) };
void Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bShowIslandGeometry_SetBit(void* Obj)
{
	((ARiverPrismalManager*)Obj)->bShowIslandGeometry = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bShowIslandGeometry = { "bShowIslandGeometry", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARiverPrismalManager), &Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bShowIslandGeometry_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowIslandGeometry_MetaData), NewProp_bShowIslandGeometry_MetaData) };
void Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bEnableCollisionLogging_SetBit(void* Obj)
{
	((ARiverPrismalManager*)Obj)->bEnableCollisionLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bEnableCollisionLogging = { "bEnableCollisionLogging", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARiverPrismalManager), &Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bEnableCollisionLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCollisionLogging_MetaData), NewProp_bEnableCollisionLogging_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ARiverPrismalManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverSpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_IslandMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_WaterCollisionBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverDepth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_SinusoidalAmplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_SinusoidalFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverSegmentCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_FlowDirection_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_FlowDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_CentralIsland,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bGenerateIsland,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_WaterConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverSegments_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverSegments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RiverMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_WaterCollisionBoxes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_WaterCollisionBoxes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_Bridges_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_Bridges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_BridgeMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_BridgeMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RecentWaterCollisions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_RecentWaterCollisions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_WaterMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_IslandMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_BridgeMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bShowDebugLines,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bShowFlowVectors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bShowIslandGeometry,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARiverPrismalManager_Statics::NewProp_bEnableCollisionLogging,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARiverPrismalManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ARiverPrismalManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARiverPrismalManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ARiverPrismalManager_Statics::ClassParams = {
	&ARiverPrismalManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ARiverPrismalManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ARiverPrismalManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ARiverPrismalManager_Statics::Class_MetaDataParams), Z_Construct_UClass_ARiverPrismalManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ARiverPrismalManager()
{
	if (!Z_Registration_Info_UClass_ARiverPrismalManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ARiverPrismalManager.OuterSingleton, Z_Construct_UClass_ARiverPrismalManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ARiverPrismalManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ARiverPrismalManager);
ARiverPrismalManager::~ARiverPrismalManager() {}
// ********** End Class ARiverPrismalManager *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ARiverPrismalManager_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERiverFlowDirection_StaticEnum, TEXT("ERiverFlowDirection"), &Z_Registration_Info_UEnum_ERiverFlowDirection, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2551558300U) },
		{ ERiverSegmentType_StaticEnum, TEXT("ERiverSegmentType"), &Z_Registration_Info_UEnum_ERiverSegmentType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3079482006U) },
		{ EWaterQuality_StaticEnum, TEXT("EWaterQuality"), &Z_Registration_Info_UEnum_EWaterQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2945749054U) },
		{ EIslandType_StaticEnum, TEXT("EIslandType"), &Z_Registration_Info_UEnum_EIslandType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4155264728U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRiverSegment::StaticStruct, Z_Construct_UScriptStruct_FRiverSegment_Statics::NewStructOps, TEXT("RiverSegment"), &Z_Registration_Info_UScriptStruct_FRiverSegment, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRiverSegment), 647102434U) },
		{ FHexagonalIsland::StaticStruct, Z_Construct_UScriptStruct_FHexagonalIsland_Statics::NewStructOps, TEXT("HexagonalIsland"), &Z_Registration_Info_UScriptStruct_FHexagonalIsland, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHexagonalIsland), 3488135994U) },
		{ FWaterProperties::StaticStruct, Z_Construct_UScriptStruct_FWaterProperties_Statics::NewStructOps, TEXT("WaterProperties"), &Z_Registration_Info_UScriptStruct_FWaterProperties, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FWaterProperties), 2467210794U) },
		{ FRiverCollisionData::StaticStruct, Z_Construct_UScriptStruct_FRiverCollisionData_Statics::NewStructOps, TEXT("RiverCollisionData"), &Z_Registration_Info_UScriptStruct_FRiverCollisionData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRiverCollisionData), 547632616U) },
		{ FBridgeData::StaticStruct, Z_Construct_UScriptStruct_FBridgeData_Statics::NewStructOps, TEXT("BridgeData"), &Z_Registration_Info_UScriptStruct_FBridgeData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FBridgeData), 2297366356U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ARiverPrismalManager, ARiverPrismalManager::StaticClass, TEXT("ARiverPrismalManager"), &Z_Registration_Info_UClass_ARiverPrismalManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ARiverPrismalManager), 4029544553U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ARiverPrismalManager_h__Script_Aura_1884066483(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ARiverPrismalManager_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ARiverPrismalManager_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ARiverPrismalManager_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ARiverPrismalManager_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ARiverPrismalManager_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ARiverPrismalManager_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
