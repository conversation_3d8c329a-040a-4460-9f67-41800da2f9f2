// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Interfaces/LaneSystemInterface.h"

#ifdef AURA_LaneSystemInterface_generated_h
#error "LaneSystemInterface.generated.h already included, missing '#pragma once' in LaneSystemInterface.h"
#endif
#define AURA_LaneSystemInterface_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Interface ULaneSystemInterface *************************************************
#define FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h_11_CALLBACK_WRAPPERS
AURA_API UClass* Z_Construct_UClass_ULaneSystemInterface_NoRegister();

#define FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h_11_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	AURA_API ULaneSystemInterface(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	ULaneSystemInterface(ULaneSystemInterface&&) = delete; \
	ULaneSystemInterface(const ULaneSystemInterface&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(AURA_API, ULaneSystemInterface); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ULaneSystemInterface); \
	DEFINE_ABSTRACT_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(ULaneSystemInterface) \
	virtual ~ULaneSystemInterface() = default;


#define FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h_11_GENERATED_UINTERFACE_BODY() \
private: \
	static void StaticRegisterNativesULaneSystemInterface(); \
	friend struct Z_Construct_UClass_ULaneSystemInterface_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_ULaneSystemInterface_NoRegister(); \
public: \
	DECLARE_CLASS2(ULaneSystemInterface, UInterface, COMPILED_IN_FLAGS(CLASS_Abstract | CLASS_Interface), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_ULaneSystemInterface_NoRegister) \
	DECLARE_SERIALIZER(ULaneSystemInterface)


#define FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h_11_GENERATED_BODY \
	PRAGMA_DISABLE_DEPRECATION_WARNINGS \
	FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h_11_GENERATED_UINTERFACE_BODY() \
	FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h_11_ENHANCED_CONSTRUCTORS \
private: \
	PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h_11_INCLASS_IINTERFACE_NO_PURE_DECLS \
protected: \
	virtual ~ILaneSystemInterface() {} \
public: \
	typedef ULaneSystemInterface UClassType; \
	typedef ILaneSystemInterface ThisClass; \
	static bool Execute_ConfigureLanes(UObject* O, TArray<FVector> const& LanePoints, const FString& LaneName); \
	static bool Execute_CreateLaneConnection(UObject* O, const FString& FromLane, const FString& ToLane, TArray<FVector> const& ConnectionPoints); \
	static TArray<FString> Execute_GetAllLaneNames(UObject* O); \
	static FVector Execute_GetClosestPointOnLane(UObject* O, FVector const& Position, const FString& LaneName); \
	static TArray<FVector> Execute_GetLanePoints(UObject* O, const FString& LaneName); \
	static float Execute_GetLaneWidthAtPosition(UObject* O, FVector const& Position, const FString& LaneName); \
	static bool Execute_IsPositionInLane(UObject* O, FVector const& Position, const FString& LaneName, float Tolerance); \
	static bool Execute_RemoveLane(UObject* O, const FString& LaneName); \
	static bool Execute_SetLaneProperties(UObject* O, const FString& LaneName, float Width, float SpeedLimit, bool bBidirectional); \
	static bool Execute_ValidateLane(UObject* O, const FString& LaneName); \
	virtual UObject* _getUObject() const { return nullptr; }


#define FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h_8_PROLOG
#define FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h_20_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h_11_CALLBACK_WRAPPERS \
	FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h_11_INCLASS_IINTERFACE_NO_PURE_DECLS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ULaneSystemInterface;

// ********** End Interface ULaneSystemInterface ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
