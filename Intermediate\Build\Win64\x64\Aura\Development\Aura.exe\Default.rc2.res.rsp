/nologo
/D_WIN64
/l 0x409
/I "."
/I "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/INCLUDE"
/I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/ucrt"
/I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/shared"
/I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/um"
/I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/winrt"
/DIS_PROGRAM=0
/DUE_GAME=1
/DUSE_SHADER_COMPILER_WORKER_TRACE=0
/DUE_REFERENCE_COLLECTOR_REQUIRE_OBJECTPTR=1
/DWITH_VERSE_VM=0
/DENABLE_PGO_PROFILE=0
/DUSE_VORBIS_FOR_STREAMING=1
/DUSE_XMA2_FOR_STREAMING=1
/DWITH_DEV_AUTOMATION_TESTS=1
/DWITH_PERF_AUTOMATION_TESTS=1
/DWITH_LOW_LEVEL_TESTS=0
/DEXPLICIT_TESTS_TARGET=0
/DWITH_TESTS=1
/DUNICODE
/D_UNICODE
/D__UNREAL__
/DIS_MONOLITHIC=1
/DIS_MERGEDMODULES=0
/DWITH_ENGINE=1
/DWITH_UNREAL_DEVELOPER_TOOLS=1
/DWITH_UNREAL_TARGET_DEVELOPER_TOOLS=1
/DWITH_APPLICATION_CORE=1
/DWITH_COREUOBJECT=1
/DUE_TRACE_ENABLED=1
/DUE_TRACE_FORCE_ENABLED=0
/DWITH_VERSE=1
/DUE_USE_VERSE_PATHS=1
/DWITH_VERSE_BPVM=1
/DUSE_STATS_WITHOUT_ENGINE=0
/DWITH_PLUGIN_SUPPORT=0
/DWITH_ACCESSIBILITY=1
/DWITH_PERFCOUNTERS=0
/DWITH_FIXED_TIME_STEP_SUPPORT=1
/DUSE_LOGGING_IN_SHIPPING=0
/DALLOW_CONSOLE_IN_SHIPPING=0
/DALLOW_PROFILEGPU_IN_TEST=0
/DALLOW_PROFILEGPU_IN_SHIPPING=0
/DWITH_LOGGING_TO_MEMORY=0
/DUSE_CACHE_FREED_OS_ALLOCS=1
/DUSE_CHECKS_IN_SHIPPING=0
/DUSE_UTF8_TCHARS=0
/DUSE_ESTIMATED_UTCNOW=0
/DUE_ALLOW_EXEC_COMMANDS_IN_SHIPPING=1
/DWITH_EDITOR=0
/DWITH_EDITORONLY_DATA=0
/DWITH_CLIENT_CODE=1
/DWITH_SERVER_CODE=1
/DUE_FNAME_OUTLINE_NUMBER=0
/DWITH_PUSH_MODEL=0
/DWITH_CEF3=1
/DWITH_LIVE_CODING=1
/DWITH_CPP_MODULES=0
/DWITH_CPP_COROUTINES=0
/DWITH_PROCESS_PRIORITY_CONTROL=0
/DUBT_MODULE_MANIFEST="UnrealGame.modules"
/DUBT_MODULE_MANIFEST_DEBUGGAME="UnrealGame-Win64-DebugGame.modules"
/DUBT_COMPILED_PLATFORM=Win64
/DUBT_COMPILED_TARGET=Game
/DUE_APP_NAME="UnrealGame"
/DUE_WARNINGS_AS_ERRORS=0
/DUE_ENGINE_DIRECTORY="../../../Program Files/Epic Games/UE_5.6/Engine/"
/DFORCE_ANSI_ALLOCATOR=0
/DUSE_MALLOC_BINNED2=1
/DUSE_MALLOC_BINNED3=0
/DNDIS_MINIPORT_MAJOR_VERSION=0
/DWIN32=1
/D_WIN32_WINNT=0x0601
/DWINVER=0x0601
/DPLATFORM_WINDOWS=1
/DPLATFORM_MICROSOFT=1
/DOVERRIDE_PLATFORM_HEADER_NAME=Windows
/DRHI_RAYTRACING=1
/DWINDOWS_MAX_NUM_TLS_SLOTS=2048
/DWINDOWS_MAX_NUM_THREADS_WITH_TLS_SLOTS=512
/DNDEBUG=1
/DUE_BUILD_DEVELOPMENT=1
/DORIGINAL_FILE_NAME="Aura.exe"
/DBUILD_ICON_FILE_NAME="\"..\\Build\\Windows\\Resources\\Default.ico\""
/DPROJECT_COPYRIGHT_STRING="Fill out your copyright notice in the Description page of Project Settings."
/DPROJECT_PRODUCT_IDENTIFIER=Aura
/fo "C:/Aura/Intermediate/Build/Win64/x64/Aura/Development/Aura.exe/Default.rc2.res"
"../Build/Windows/Resources/Default.rc2"