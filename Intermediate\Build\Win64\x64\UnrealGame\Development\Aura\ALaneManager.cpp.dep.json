{"Version": "1.2", "Data": {"Source": "c:\\aura\\source\\aura\\private\\alanemanager.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\intermediate\\build\\win64\\x64\\aura\\development\\engine\\sharedpch.engine.project.rtti.exceptions.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\intermediate\\build\\win64\\x64\\unrealgame\\development\\aura\\definitions.aura.h", "c:\\aura\\source\\aura\\public\\alanemanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\splinecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\curves\\spline.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\spline.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\splinecomponent.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\alanemanager.generated.h", "c:\\aura\\source\\aura\\public\\aminionwavemanager.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\aminionwavemanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\assetregistry\\arfilter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\dialoguetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\gameplaystatics.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\overlapresult.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\overlapresult.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}