{"Version": "1.2", "Data": {"Source": "c:\\aura\\source\\aura\\private\\testes_precisao_geometrica.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\intermediate\\build\\win64\\x64\\aura\\development\\engine\\sharedpch.engine.project.rtti.exceptions.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\intermediate\\build\\win64\\x64\\unrealgame\\development\\aura\\definitions.aura.h", "c:\\aura\\source\\aura\\public\\testes_precisao_geometrica.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\noexporttypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\serialization\\testundeclaredscriptstructobjectreferences.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\polyglottextdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\assetregistry\\arfilter.h", "c:\\aura\\source\\aura\\public\\implementacao_automatizada.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\implementacao_automatizada.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\testes_precisao_geometrica.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\tests\\automationcommon.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}