#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/Engine.h"
#include "implementacao_automatizada.h"
#include "testes_precisao_geometrica.generated.h"

// ========== ESTRUTURAS DE RESULTADO ==========

USTRUCT(BlueprintType)
struct AURA_API FResultadoTeste
{
    GENERATED_BODY()
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool Sucesso;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString NomeTeste;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString Detalhes;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float PrecisaoObtida;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float PrecisaoEsperada;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> ErrosEncontrados;
    
    FResultadoTeste()
    {
        Sucesso = false;
        NomeTeste = "";
        Detalhes = "";
        PrecisaoObtida = 0.0f;
        PrecisaoEsperada = 100.0f;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FRelatorioCompleto
{
    GENERATED_BODY()
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FResultadoTeste> ResultadosTestes;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 TestesPassaram;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 TestesFalharam;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float PrecisaoGeral;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool MapaAprovado;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString ResumoExecutivo;
    
    FRelatorioCompleto()
    {
        TestesPassaram = 0;
        TestesFalharam = 0;
        PrecisaoGeral = 0.0f;
        MapaAprovado = false;
        ResumoExecutivo = "";
    }
};

/**
 * SISTEMA DE TESTES AUTOMATIZADOS - PRECISÃO GEOMÉTRICA 100%
 * Verifica matematicamente cada aspecto do mapa Aura
 * Garante que todas as especificações sejam implementadas corretamente
 */

UCLASS(BlueprintType)
class AURA_API UTestePrecisaoGeometrica : public UObject
{
    GENERATED_BODY()

public:
    UTestePrecisaoGeometrica();

    // ========== TESTES DE VALIDAÇÃO PRINCIPAL ==========
    
    UFUNCTION(BlueprintCallable, Category = "Testes")
    static bool ExecutarTodosOsTestes();
    
    UFUNCTION(BlueprintCallable, Category = "Testes")
    static FString GerarRelatorioCompleto();
    
    // ========== TESTES ESPECÍFICOS POR COMPONENTE ==========
    
    UFUNCTION(BlueprintCallable, Category = "Testes Lanes")
    static bool TestarPrecisaoLanes();
    
    UFUNCTION(BlueprintCallable, Category = "Testes Rio")
    static bool TestarFuncaoSenoidalRio();
    
    UFUNCTION(BlueprintCallable, Category = "Testes Geometria")
    static bool TestarGeometriaHexagonos();
    
    UFUNCTION(BlueprintCallable, Category = "Testes Geometria")
    static bool TestarGeometriaElipses();
    
    UFUNCTION(BlueprintCallable, Category = "Testes Geometria")
    static bool TestarGeometriaCirculos();
    
    UFUNCTION(BlueprintCallable, Category = "Testes Torres")
    static bool TestarPosicionamentoTorres();
    
    UFUNCTION(BlueprintCallable, Category = "Testes Bases")
    static bool TestarEspecificacoesBases();
    
    UFUNCTION(BlueprintCallable, Category = "Testes Covils")
    static bool TestarDimensoesCovils();
    
    UFUNCTION(BlueprintCallable, Category = "Testes Paredes")
    static bool TestarSistemaParedes();
    
    UFUNCTION(BlueprintCallable, Category = "Testes Minions")
    static bool TestarSistemaMinions();
    
    // ========== TESTES DE PRECISÃO MATEMÁTICA ==========
    
    UFUNCTION(BlueprintCallable, Category = "Precisão")
    static bool TestarToleranciasCoordenadas();
    
    UFUNCTION(BlueprintCallable, Category = "Precisão")
    static bool TestarCalculosAngulares();
    
    UFUNCTION(BlueprintCallable, Category = "Precisão")
    static bool TestarCalculosArea();
    
    UFUNCTION(BlueprintCallable, Category = "Precisão")
    static bool TestarCalculosDistancia();
    
    // ========== TESTES DE INTEGRAÇÃO ==========
    
    UFUNCTION(BlueprintCallable, Category = "Integração")
    static bool TestarColisoesBordas();
    
    UFUNCTION(BlueprintCallable, Category = "Integração")
    static bool TestarConectividadeLanes();
    
    UFUNCTION(BlueprintCallable, Category = "Integração")
    static bool TestarFluxoMinions();
    
    UFUNCTION(BlueprintCallable, Category = "Integração")
    static bool TestarAcessoCovils();
    
    // ========== ESTRUTURAS DE RESULTADO (movidas para escopo global) ==========

private:
    // ========== CONSTANTES DE TESTE ==========
    
    static constexpr float TOLERANCIA_TESTE_COORDENADA = 0.1f;  // ±0.1 UU
    static constexpr float TOLERANCIA_TESTE_ANGULO = 0.01f;     // ±0.01°
    static constexpr float TOLERANCIA_TESTE_AREA = 0.0001f;     // ±0.01%
    static constexpr float TOLERANCIA_TESTE_DISTANCIA = 0.1f;   // ±0.1 UU
    
    // ========== VALORES ESPERADOS EXATOS ==========
    
    // Lanes
    static constexpr float LANE_SUPERIOR_M = -0.57735026919f;
    static constexpr float LANE_SUPERIOR_B = 6928.0f;
    static constexpr float LANE_INFERIOR_M = 0.57735026919f;
    static constexpr float LANE_INFERIOR_B = -6928.0f;
    static constexpr float LANE_ANGULO_30_GRAUS = 0.57735026919f; // tan(30°)
    
    // Rio
    static constexpr float RIO_AMPLITUDE = 200.0f;
    static constexpr float RIO_PERIODO = 9600.0f;
    static constexpr float RIO_LARGURA_MIN = 1000.0f;
    static constexpr float RIO_LARGURA_MAX = 1400.0f;
    
    // Bases
    static constexpr float BASE_RAIO = 1200.0f;
    static constexpr float BASE_AREA_ESPERADA = 3742320.0f;  // (3√3/2) × 1200²
    
    // Ilha Central
    static constexpr float ILHA_RAIO = 600.0f;
    static constexpr float ILHA_AREA_ESPERADA = 936307.0f;   // (3√3/2) × 600²
    
    // Covils
    static constexpr float DRAGAO_SEMI_A = 800.0f;
    static constexpr float DRAGAO_SEMI_B = 600.0f;
    static constexpr float DRAGAO_AREA_ESPERADA = 1507964.0f; // π × 800 × 600
    
    static constexpr float BARAO_RAIO = 700.0f;
    static constexpr float BARAO_AREA_ESPERADA = 1272792.0f;  // (3√3/2) × 700²
    
    static constexpr float SENTINELA_RAIO = 400.0f;
    static constexpr float SENTINELA_AREA_ESPERADA = 502655.0f; // π × 400²
    
    // ========== FUNÇÕES AUXILIARES DE TESTE ==========
    
    static FResultadoTeste CriarResultadoTeste(const FString& Nome, bool Sucesso, const FString& Detalhes);
    static void LogResultadoTeste(const FString& NomeTeste, bool Sucesso, const FString& Detalhes);
    static bool CompararFloat(float Valor1, float Valor2, float Tolerancia);
    static bool CompararVetor(const FVector& V1, const FVector& V2, float Tolerancia);
    static float CalcularPrecisaoPercentual(float ValorObtido, float ValorEsperado);
    static FString FormatarErro(const FString& Contexto, float Obtido, float Esperado);
};

// ========== MACROS PARA TESTES AUTOMATIZADOS ==========

#define TESTE_ASSERT_FLOAT_IGUAL(obtido, esperado, tolerancia, contexto) \
    if (!UTestePrecisaoGeometrica::CompararFloat(obtido, esperado, tolerancia)) { \
        return UTestePrecisaoGeometrica::CriarResultadoTeste(contexto, false, \
            FString::Printf(TEXT("Esperado: %.6f, Obtido: %.6f, Diferença: %.6f"), \
            esperado, obtido, FMath::Abs(obtido - esperado))); \
    }

#define TESTE_ASSERT_VETOR_IGUAL(obtido, esperado, tolerancia, contexto) \
    if (!UTestePrecisaoGeometrica::CompararVetor(obtido, esperado, tolerancia)) { \
        return UTestePrecisaoGeometrica::CriarResultadoTeste(contexto, false, \
            FString::Printf(TEXT("Esperado: (%.2f, %.2f, %.2f), Obtido: (%.2f, %.2f, %.2f)"), \
            esperado.X, esperado.Y, esperado.Z, obtido.X, obtido.Y, obtido.Z)); \
    }

#define TESTE_ASSERT_BOOL(condicao, contexto) \
    if (!(condicao)) { \
        return UTestePrecisaoGeometrica::CriarResultadoTeste(contexto, false, \
            TEXT("Condição falhou: " #condicao)); \
    }

// ========== TESTES AUTOMATIZADOS UNREAL ENGINE ==========
// Implementações dos testes estão no arquivo .cpp

// ========== COMANDOS DE CONSOLE PARA TESTES ==========

/**
 * Comando de console para executar todos os testes
 * Uso: Aura.TestarTudo
 */
static FAutoConsoleCommand TestarTudoCommand(
    TEXT("Aura.TestarTudo"),
    TEXT("Executa todos os testes de precisão geométrica do mapa Aura"),
    FConsoleCommandDelegate::CreateStatic([]() {
        bool Resultado = UTestePrecisaoGeometrica::ExecutarTodosOsTestes();
        UE_LOG(LogTemp, Log, TEXT("Resultado dos testes: %s"), Resultado ? TEXT("PASSOU") : TEXT("FALHOU"));
    })
);

/**
 * Comando de console para gerar relatório
 * Uso: Aura.GerarRelatorio
 */
extern FAutoConsoleCommand GerarRelatorioCommand;

/**
 * Comando de console para testar apenas as lanes
 * Uso: Aura.TestarLanes
 */
extern FAutoConsoleCommand TestarLanesCommand;

/**
 * Comando de console para testar apenas o rio
 * Uso: Aura.TestarRio
 */
extern FAutoConsoleCommand TestarRioCommand;

/**
 * Comando de console para testar geometrias
 * Uso: Aura.TestarGeometrias
 */
static FAutoConsoleCommand TestarGeometriasCommand(
    TEXT("Aura.TestarGeometrias"),
    TEXT("Testa todas as geometrias (hexágonos, elipses, círculos)"),
    FConsoleCommandDelegate::CreateStatic([]() {
        bool Hex = UTestePrecisaoGeometrica::TestarGeometriaHexagonos();
        bool Eli = UTestePrecisaoGeometrica::TestarGeometriaElipses();
        bool Cir = UTestePrecisaoGeometrica::TestarGeometriaCirculos();
        bool Resultado = Hex && Eli && Cir;
        UE_LOG(LogTemp, Log, TEXT("Teste das geometrias: %s (Hex:%s, Eli:%s, Cir:%s)"), 
               Resultado ? TEXT("PASSOU") : TEXT("FALHOU"),
               Hex ? TEXT("OK") : TEXT("FALHOU"),
               Eli ? TEXT("OK") : TEXT("FALHOU"),
               Cir ? TEXT("OK") : TEXT("FALHOU"));
    })
);