// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Interfaces/LaneSystemInterface.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeLaneSystemInterface() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_ULaneSystemInterface();
AURA_API UClass* Z_Construct_UClass_ULaneSystemInterface_NoRegister();
COREUOBJECT_API UClass* Z_Construct_UClass_UInterface();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Interface ULaneSystemInterface Function ConfigureLanes *************************
struct LaneSystemInterface_eventConfigureLanes_Parms
{
	TArray<FVector> LanePoints;
	FString LaneName;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	LaneSystemInterface_eventConfigureLanes_Parms()
		: ReturnValue(false)
	{
	}
};
bool ILaneSystemInterface::ConfigureLanes(TArray<FVector> const& LanePoints, const FString& LaneName)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_ConfigureLanes instead.");
	LaneSystemInterface_eventConfigureLanes_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_ULaneSystemInterface_ConfigureLanes = FName(TEXT("ConfigureLanes"));
bool ILaneSystemInterface::Execute_ConfigureLanes(UObject* O, TArray<FVector> const& LanePoints, const FString& LaneName)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(ULaneSystemInterface::StaticClass()));
	LaneSystemInterface_eventConfigureLanes_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_ULaneSystemInterface_ConfigureLanes);
	if (Func)
	{
		Parms.LanePoints=LanePoints;
		Parms.LaneName=LaneName;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configura lanes usando pontos de spline\n     * @param LanePoints - Array de pontos que definem a lane\n     * @param LaneName - Nome identificador da lane\n     * @return true se configurado com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/LaneSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura lanes usando pontos de spline\n@param LanePoints - Array de pontos que definem a lane\n@param LaneName - Nome identificador da lane\n@return true se configurado com sucesso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LanePoints_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_LanePoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LanePoints;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LaneName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::NewProp_LanePoints_Inner = { "LanePoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::NewProp_LanePoints = { "LanePoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventConfigureLanes_Parms, LanePoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LanePoints_MetaData), NewProp_LanePoints_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::NewProp_LaneName = { "LaneName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventConfigureLanes_Parms, LaneName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneName_MetaData), NewProp_LaneName_MetaData) };
void Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneSystemInterface_eventConfigureLanes_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneSystemInterface_eventConfigureLanes_Parms), &Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::NewProp_LanePoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::NewProp_LanePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::NewProp_LaneName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ULaneSystemInterface, nullptr, "ConfigureLanes", Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::PropPointers), sizeof(LaneSystemInterface_eventConfigureLanes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(LaneSystemInterface_eventConfigureLanes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface ULaneSystemInterface Function ConfigureLanes ***************************

// ********** Begin Interface ULaneSystemInterface Function CreateLaneConnection *******************
struct LaneSystemInterface_eventCreateLaneConnection_Parms
{
	FString FromLane;
	FString ToLane;
	TArray<FVector> ConnectionPoints;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	LaneSystemInterface_eventCreateLaneConnection_Parms()
		: ReturnValue(false)
	{
	}
};
bool ILaneSystemInterface::CreateLaneConnection(const FString& FromLane, const FString& ToLane, TArray<FVector> const& ConnectionPoints)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_CreateLaneConnection instead.");
	LaneSystemInterface_eventCreateLaneConnection_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_ULaneSystemInterface_CreateLaneConnection = FName(TEXT("CreateLaneConnection"));
bool ILaneSystemInterface::Execute_CreateLaneConnection(UObject* O, const FString& FromLane, const FString& ToLane, TArray<FVector> const& ConnectionPoints)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(ULaneSystemInterface::StaticClass()));
	LaneSystemInterface_eventCreateLaneConnection_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_ULaneSystemInterface_CreateLaneConnection);
	if (Func)
	{
		Parms.FromLane=FromLane;
		Parms.ToLane=ToLane;
		Parms.ConnectionPoints=ConnectionPoints;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configura conex\xc3\xb5""es entre lanes\n     * @param FromLane - Nome da lane de origem\n     * @param ToLane - Nome da lane de destino\n     * @param ConnectionPoints - Pontos de conex\xc3\xa3o\n     * @return true se conex\xc3\xa3o criada com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/LaneSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura conex\xc3\xb5""es entre lanes\n@param FromLane - Nome da lane de origem\n@param ToLane - Nome da lane de destino\n@param ConnectionPoints - Pontos de conex\xc3\xa3o\n@return true se conex\xc3\xa3o criada com sucesso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FromLane_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ToLane_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionPoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FromLane;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ToLane;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConnectionPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ConnectionPoints;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::NewProp_FromLane = { "FromLane", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventCreateLaneConnection_Parms, FromLane), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FromLane_MetaData), NewProp_FromLane_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::NewProp_ToLane = { "ToLane", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventCreateLaneConnection_Parms, ToLane), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ToLane_MetaData), NewProp_ToLane_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::NewProp_ConnectionPoints_Inner = { "ConnectionPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::NewProp_ConnectionPoints = { "ConnectionPoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventCreateLaneConnection_Parms, ConnectionPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionPoints_MetaData), NewProp_ConnectionPoints_MetaData) };
void Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneSystemInterface_eventCreateLaneConnection_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneSystemInterface_eventCreateLaneConnection_Parms), &Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::NewProp_FromLane,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::NewProp_ToLane,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::NewProp_ConnectionPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::NewProp_ConnectionPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ULaneSystemInterface, nullptr, "CreateLaneConnection", Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::PropPointers), sizeof(LaneSystemInterface_eventCreateLaneConnection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(LaneSystemInterface_eventCreateLaneConnection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface ULaneSystemInterface Function CreateLaneConnection *********************

// ********** Begin Interface ULaneSystemInterface Function GetAllLaneNames ************************
struct LaneSystemInterface_eventGetAllLaneNames_Parms
{
	TArray<FString> ReturnValue;
};
TArray<FString> ILaneSystemInterface::GetAllLaneNames()
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetAllLaneNames instead.");
	LaneSystemInterface_eventGetAllLaneNames_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_ULaneSystemInterface_GetAllLaneNames = FName(TEXT("GetAllLaneNames"));
TArray<FString> ILaneSystemInterface::Execute_GetAllLaneNames(UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(ULaneSystemInterface::StaticClass()));
	LaneSystemInterface_eventGetAllLaneNames_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_ULaneSystemInterface_GetAllLaneNames);
	if (Func)
	{
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m todas as lanes configuradas\n     * @return Array com nomes de todas as lanes\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/LaneSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m todas as lanes configuradas\n@return Array com nomes de todas as lanes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventGetAllLaneNames_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ULaneSystemInterface, nullptr, "GetAllLaneNames", Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames_Statics::PropPointers), sizeof(LaneSystemInterface_eventGetAllLaneNames_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(LaneSystemInterface_eventGetAllLaneNames_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface ULaneSystemInterface Function GetAllLaneNames **************************

// ********** Begin Interface ULaneSystemInterface Function GetClosestPointOnLane ******************
struct LaneSystemInterface_eventGetClosestPointOnLane_Parms
{
	FVector Position;
	FString LaneName;
	FVector ReturnValue;

	/** Constructor, initializes return property only **/
	LaneSystemInterface_eventGetClosestPointOnLane_Parms()
		: ReturnValue(ForceInit)
	{
	}
};
FVector ILaneSystemInterface::GetClosestPointOnLane(FVector const& Position, const FString& LaneName)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetClosestPointOnLane instead.");
	LaneSystemInterface_eventGetClosestPointOnLane_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_ULaneSystemInterface_GetClosestPointOnLane = FName(TEXT("GetClosestPointOnLane"));
FVector ILaneSystemInterface::Execute_GetClosestPointOnLane(UObject* O, FVector const& Position, const FString& LaneName)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(ULaneSystemInterface::StaticClass()));
	LaneSystemInterface_eventGetClosestPointOnLane_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_ULaneSystemInterface_GetClosestPointOnLane);
	if (Func)
	{
		Parms.Position=Position;
		Parms.LaneName=LaneName;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m o ponto mais pr\xc3\xb3ximo em uma lane\n     * @param Position - Posi\xc3\xa7\xc3\xa3o de refer\xc3\xaancia\n     * @param LaneName - Nome da lane\n     * @return Ponto mais pr\xc3\xb3ximo na lane\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/LaneSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m o ponto mais pr\xc3\xb3ximo em uma lane\n@param Position - Posi\xc3\xa7\xc3\xa3o de refer\xc3\xaancia\n@param LaneName - Nome da lane\n@return Ponto mais pr\xc3\xb3ximo na lane" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LaneName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventGetClosestPointOnLane_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::NewProp_LaneName = { "LaneName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventGetClosestPointOnLane_Parms, LaneName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneName_MetaData), NewProp_LaneName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventGetClosestPointOnLane_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::NewProp_LaneName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ULaneSystemInterface, nullptr, "GetClosestPointOnLane", Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::PropPointers), sizeof(LaneSystemInterface_eventGetClosestPointOnLane_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C20800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(LaneSystemInterface_eventGetClosestPointOnLane_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface ULaneSystemInterface Function GetClosestPointOnLane ********************

// ********** Begin Interface ULaneSystemInterface Function GetLanePoints **************************
struct LaneSystemInterface_eventGetLanePoints_Parms
{
	FString LaneName;
	TArray<FVector> ReturnValue;
};
TArray<FVector> ILaneSystemInterface::GetLanePoints(const FString& LaneName)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetLanePoints instead.");
	LaneSystemInterface_eventGetLanePoints_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_ULaneSystemInterface_GetLanePoints = FName(TEXT("GetLanePoints"));
TArray<FVector> ILaneSystemInterface::Execute_GetLanePoints(UObject* O, const FString& LaneName)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(ULaneSystemInterface::StaticClass()));
	LaneSystemInterface_eventGetLanePoints_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_ULaneSystemInterface_GetLanePoints);
	if (Func)
	{
		Parms.LaneName=LaneName;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m informa\xc3\xa7\xc3\xb5""es de uma lane espec\xc3\xad""fica\n     * @param LaneName - Nome da lane\n     * @return Array de pontos da lane\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/LaneSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m informa\xc3\xa7\xc3\xb5""es de uma lane espec\xc3\xad""fica\n@param LaneName - Nome da lane\n@return Array de pontos da lane" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LaneName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::NewProp_LaneName = { "LaneName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventGetLanePoints_Parms, LaneName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneName_MetaData), NewProp_LaneName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventGetLanePoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::NewProp_LaneName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ULaneSystemInterface, nullptr, "GetLanePoints", Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::PropPointers), sizeof(LaneSystemInterface_eventGetLanePoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(LaneSystemInterface_eventGetLanePoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface ULaneSystemInterface Function GetLanePoints ****************************

// ********** Begin Interface ULaneSystemInterface Function GetLaneWidthAtPosition *****************
struct LaneSystemInterface_eventGetLaneWidthAtPosition_Parms
{
	FVector Position;
	FString LaneName;
	float ReturnValue;

	/** Constructor, initializes return property only **/
	LaneSystemInterface_eventGetLaneWidthAtPosition_Parms()
		: ReturnValue(0)
	{
	}
};
float ILaneSystemInterface::GetLaneWidthAtPosition(FVector const& Position, const FString& LaneName)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetLaneWidthAtPosition instead.");
	LaneSystemInterface_eventGetLaneWidthAtPosition_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_ULaneSystemInterface_GetLaneWidthAtPosition = FName(TEXT("GetLaneWidthAtPosition"));
float ILaneSystemInterface::Execute_GetLaneWidthAtPosition(UObject* O, FVector const& Position, const FString& LaneName)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(ULaneSystemInterface::StaticClass()));
	LaneSystemInterface_eventGetLaneWidthAtPosition_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_ULaneSystemInterface_GetLaneWidthAtPosition);
	if (Func)
	{
		Parms.Position=Position;
		Parms.LaneName=LaneName;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m a largura de uma lane em um ponto espec\xc3\xad""fico\n     * @param Position - Posi\xc3\xa7\xc3\xa3o na lane\n     * @param LaneName - Nome da lane\n     * @return Largura da lane no ponto\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/LaneSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m a largura de uma lane em um ponto espec\xc3\xad""fico\n@param Position - Posi\xc3\xa7\xc3\xa3o na lane\n@param LaneName - Nome da lane\n@return Largura da lane no ponto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LaneName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventGetLaneWidthAtPosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::NewProp_LaneName = { "LaneName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventGetLaneWidthAtPosition_Parms, LaneName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneName_MetaData), NewProp_LaneName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventGetLaneWidthAtPosition_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::NewProp_LaneName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ULaneSystemInterface, nullptr, "GetLaneWidthAtPosition", Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::PropPointers), sizeof(LaneSystemInterface_eventGetLaneWidthAtPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C20800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(LaneSystemInterface_eventGetLaneWidthAtPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface ULaneSystemInterface Function GetLaneWidthAtPosition *******************

// ********** Begin Interface ULaneSystemInterface Function IsPositionInLane ***********************
struct LaneSystemInterface_eventIsPositionInLane_Parms
{
	FVector Position;
	FString LaneName;
	float Tolerance;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	LaneSystemInterface_eventIsPositionInLane_Parms()
		: ReturnValue(false)
	{
	}
};
bool ILaneSystemInterface::IsPositionInLane(FVector const& Position, const FString& LaneName, float Tolerance)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_IsPositionInLane instead.");
	LaneSystemInterface_eventIsPositionInLane_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_ULaneSystemInterface_IsPositionInLane = FName(TEXT("IsPositionInLane"));
bool ILaneSystemInterface::Execute_IsPositionInLane(UObject* O, FVector const& Position, const FString& LaneName, float Tolerance)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(ULaneSystemInterface::StaticClass()));
	LaneSystemInterface_eventIsPositionInLane_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_ULaneSystemInterface_IsPositionInLane);
	if (Func)
	{
		Parms.Position=Position;
		Parms.LaneName=LaneName;
		Parms.Tolerance=Tolerance;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se uma posi\xc3\xa7\xc3\xa3o est\xc3\xa1 dentro de uma lane\n     * @param Position - Posi\xc3\xa7\xc3\xa3o para verificar\n     * @param LaneName - Nome da lane\n     * @param Tolerance - Toler\xc3\xa2ncia em unidades\n     * @return true se est\xc3\xa1 dentro da lane\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/LaneSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se uma posi\xc3\xa7\xc3\xa3o est\xc3\xa1 dentro de uma lane\n@param Position - Posi\xc3\xa7\xc3\xa3o para verificar\n@param LaneName - Nome da lane\n@param Tolerance - Toler\xc3\xa2ncia em unidades\n@return true se est\xc3\xa1 dentro da lane" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LaneName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventIsPositionInLane_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::NewProp_LaneName = { "LaneName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventIsPositionInLane_Parms, LaneName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneName_MetaData), NewProp_LaneName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventIsPositionInLane_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneSystemInterface_eventIsPositionInLane_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneSystemInterface_eventIsPositionInLane_Parms), &Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::NewProp_LaneName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ULaneSystemInterface, nullptr, "IsPositionInLane", Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::PropPointers), sizeof(LaneSystemInterface_eventIsPositionInLane_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C20800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(LaneSystemInterface_eventIsPositionInLane_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface ULaneSystemInterface Function IsPositionInLane *************************

// ********** Begin Interface ULaneSystemInterface Function RemoveLane *****************************
struct LaneSystemInterface_eventRemoveLane_Parms
{
	FString LaneName;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	LaneSystemInterface_eventRemoveLane_Parms()
		: ReturnValue(false)
	{
	}
};
bool ILaneSystemInterface::RemoveLane(const FString& LaneName)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_RemoveLane instead.");
	LaneSystemInterface_eventRemoveLane_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_ULaneSystemInterface_RemoveLane = FName(TEXT("RemoveLane"));
bool ILaneSystemInterface::Execute_RemoveLane(UObject* O, const FString& LaneName)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(ULaneSystemInterface::StaticClass()));
	LaneSystemInterface_eventRemoveLane_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_ULaneSystemInterface_RemoveLane);
	if (Func)
	{
		Parms.LaneName=LaneName;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remove uma lane do sistema\n     * @param LaneName - Nome da lane para remover\n     * @return true se removida com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/LaneSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove uma lane do sistema\n@param LaneName - Nome da lane para remover\n@return true se removida com sucesso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LaneName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::NewProp_LaneName = { "LaneName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventRemoveLane_Parms, LaneName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneName_MetaData), NewProp_LaneName_MetaData) };
void Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneSystemInterface_eventRemoveLane_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneSystemInterface_eventRemoveLane_Parms), &Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::NewProp_LaneName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ULaneSystemInterface, nullptr, "RemoveLane", Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::PropPointers), sizeof(LaneSystemInterface_eventRemoveLane_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(LaneSystemInterface_eventRemoveLane_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULaneSystemInterface_RemoveLane()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULaneSystemInterface_RemoveLane_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface ULaneSystemInterface Function RemoveLane *******************************

// ********** Begin Interface ULaneSystemInterface Function SetLaneProperties **********************
struct LaneSystemInterface_eventSetLaneProperties_Parms
{
	FString LaneName;
	float Width;
	float SpeedLimit;
	bool bBidirectional;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	LaneSystemInterface_eventSetLaneProperties_Parms()
		: ReturnValue(false)
	{
	}
};
bool ILaneSystemInterface::SetLaneProperties(const FString& LaneName, float Width, float SpeedLimit, bool bBidirectional)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_SetLaneProperties instead.");
	LaneSystemInterface_eventSetLaneProperties_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_ULaneSystemInterface_SetLaneProperties = FName(TEXT("SetLaneProperties"));
bool ILaneSystemInterface::Execute_SetLaneProperties(UObject* O, const FString& LaneName, float Width, float SpeedLimit, bool bBidirectional)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(ULaneSystemInterface::StaticClass()));
	LaneSystemInterface_eventSetLaneProperties_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_ULaneSystemInterface_SetLaneProperties);
	if (Func)
	{
		Parms.LaneName=LaneName;
		Parms.Width=Width;
		Parms.SpeedLimit=SpeedLimit;
		Parms.bBidirectional=bBidirectional;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configura propriedades avan\xc3\xa7""adas de uma lane\n     * @param LaneName - Nome da lane\n     * @param Width - Largura da lane\n     * @param SpeedLimit - Limite de velocidade\n     * @param bBidirectional - Se \xc3\xa9 bidirecional\n     * @return true se configurado com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/LaneSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura propriedades avan\xc3\xa7""adas de uma lane\n@param LaneName - Nome da lane\n@param Width - Largura da lane\n@param SpeedLimit - Limite de velocidade\n@param bBidirectional - Se \xc3\xa9 bidirecional\n@return true se configurado com sucesso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LaneName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpeedLimit;
	static void NewProp_bBidirectional_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBidirectional;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_LaneName = { "LaneName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventSetLaneProperties_Parms, LaneName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneName_MetaData), NewProp_LaneName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventSetLaneProperties_Parms, Width), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_SpeedLimit = { "SpeedLimit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventSetLaneProperties_Parms, SpeedLimit), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_bBidirectional_SetBit(void* Obj)
{
	((LaneSystemInterface_eventSetLaneProperties_Parms*)Obj)->bBidirectional = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_bBidirectional = { "bBidirectional", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneSystemInterface_eventSetLaneProperties_Parms), &Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_bBidirectional_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneSystemInterface_eventSetLaneProperties_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneSystemInterface_eventSetLaneProperties_Parms), &Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_LaneName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_SpeedLimit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_bBidirectional,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ULaneSystemInterface, nullptr, "SetLaneProperties", Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::PropPointers), sizeof(LaneSystemInterface_eventSetLaneProperties_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(LaneSystemInterface_eventSetLaneProperties_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface ULaneSystemInterface Function SetLaneProperties ************************

// ********** Begin Interface ULaneSystemInterface Function ValidateLane ***************************
struct LaneSystemInterface_eventValidateLane_Parms
{
	FString LaneName;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	LaneSystemInterface_eventValidateLane_Parms()
		: ReturnValue(false)
	{
	}
};
bool ILaneSystemInterface::ValidateLane(const FString& LaneName)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_ValidateLane instead.");
	LaneSystemInterface_eventValidateLane_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_ULaneSystemInterface_ValidateLane = FName(TEXT("ValidateLane"));
bool ILaneSystemInterface::Execute_ValidateLane(UObject* O, const FString& LaneName)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(ULaneSystemInterface::StaticClass()));
	LaneSystemInterface_eventValidateLane_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_ULaneSystemInterface_ValidateLane);
	if (Func)
	{
		Parms.LaneName=LaneName;
		O->ProcessEvent(Func, &Parms);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida se uma lane est\xc3\xa1 configurada corretamente\n     * @param LaneName - Nome da lane para validar\n     * @return true se v\xc3\xa1lida\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Interfaces/LaneSystemInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida se uma lane est\xc3\xa1 configurada corretamente\n@param LaneName - Nome da lane para validar\n@return true se v\xc3\xa1lida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LaneName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::NewProp_LaneName = { "LaneName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneSystemInterface_eventValidateLane_Parms, LaneName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneName_MetaData), NewProp_LaneName_MetaData) };
void Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneSystemInterface_eventValidateLane_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneSystemInterface_eventValidateLane_Parms), &Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::NewProp_LaneName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ULaneSystemInterface, nullptr, "ValidateLane", Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::PropPointers), sizeof(LaneSystemInterface_eventValidateLane_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(LaneSystemInterface_eventValidateLane_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULaneSystemInterface_ValidateLane()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULaneSystemInterface_ValidateLane_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Interface ULaneSystemInterface Function ValidateLane *****************************

// ********** Begin Interface ULaneSystemInterface *************************************************
void ULaneSystemInterface::StaticRegisterNativesULaneSystemInterface()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_ULaneSystemInterface;
UClass* ULaneSystemInterface::GetPrivateStaticClass()
{
	using TClass = ULaneSystemInterface;
	if (!Z_Registration_Info_UClass_ULaneSystemInterface.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("LaneSystemInterface"),
			Z_Registration_Info_UClass_ULaneSystemInterface.InnerSingleton,
			StaticRegisterNativesULaneSystemInterface,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ULaneSystemInterface.InnerSingleton;
}
UClass* Z_Construct_UClass_ULaneSystemInterface_NoRegister()
{
	return ULaneSystemInterface::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ULaneSystemInterface_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/Interfaces/LaneSystemInterface.h" },
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ULaneSystemInterface_ConfigureLanes, "ConfigureLanes" }, // 785988027
		{ &Z_Construct_UFunction_ULaneSystemInterface_CreateLaneConnection, "CreateLaneConnection" }, // 3770953841
		{ &Z_Construct_UFunction_ULaneSystemInterface_GetAllLaneNames, "GetAllLaneNames" }, // 3241274298
		{ &Z_Construct_UFunction_ULaneSystemInterface_GetClosestPointOnLane, "GetClosestPointOnLane" }, // 4237663677
		{ &Z_Construct_UFunction_ULaneSystemInterface_GetLanePoints, "GetLanePoints" }, // 2770340396
		{ &Z_Construct_UFunction_ULaneSystemInterface_GetLaneWidthAtPosition, "GetLaneWidthAtPosition" }, // 1845702421
		{ &Z_Construct_UFunction_ULaneSystemInterface_IsPositionInLane, "IsPositionInLane" }, // 2346332176
		{ &Z_Construct_UFunction_ULaneSystemInterface_RemoveLane, "RemoveLane" }, // 524579027
		{ &Z_Construct_UFunction_ULaneSystemInterface_SetLaneProperties, "SetLaneProperties" }, // 3937063967
		{ &Z_Construct_UFunction_ULaneSystemInterface_ValidateLane, "ValidateLane" }, // 2514589013
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ILaneSystemInterface>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_ULaneSystemInterface_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UInterface,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ULaneSystemInterface_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ULaneSystemInterface_Statics::ClassParams = {
	&ULaneSystemInterface::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x000840A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ULaneSystemInterface_Statics::Class_MetaDataParams), Z_Construct_UClass_ULaneSystemInterface_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ULaneSystemInterface()
{
	if (!Z_Registration_Info_UClass_ULaneSystemInterface.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ULaneSystemInterface.OuterSingleton, Z_Construct_UClass_ULaneSystemInterface_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ULaneSystemInterface.OuterSingleton;
}
ULaneSystemInterface::ULaneSystemInterface(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(ULaneSystemInterface);
// ********** End Interface ULaneSystemInterface ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h__Script_Aura_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ULaneSystemInterface, ULaneSystemInterface::StaticClass, TEXT("ULaneSystemInterface"), &Z_Registration_Info_UClass_ULaneSystemInterface, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ULaneSystemInterface), 2008738430U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h__Script_Aura_1417931241(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_Interfaces_LaneSystemInterface_h__Script_Aura_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
