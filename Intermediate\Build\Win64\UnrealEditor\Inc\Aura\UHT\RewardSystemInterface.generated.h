// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Interfaces/RewardSystemInterface.h"

#ifdef AURA_RewardSystemInterface_generated_h
#error "RewardSystemInterface.generated.h already included, missing '#pragma once' in RewardSystemInterface.h"
#endif
#define AURA_RewardSystemInterface_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;

// ********** Begin Interface URewardSystemInterface ***********************************************
#define FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h_11_CALLBACK_WRAPPERS
AURA_API UClass* Z_Construct_UClass_URewardSystemInterface_NoRegister();

#define FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h_11_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	AURA_API URewardSystemInterface(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	URewardSystemInterface(URewardSystemInterface&&) = delete; \
	URewardSystemInterface(const URewardSystemInterface&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(AURA_API, URewardSystemInterface); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URewardSystemInterface); \
	DEFINE_ABSTRACT_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(URewardSystemInterface) \
	virtual ~URewardSystemInterface() = default;


#define FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h_11_GENERATED_UINTERFACE_BODY() \
private: \
	static void StaticRegisterNativesURewardSystemInterface(); \
	friend struct Z_Construct_UClass_URewardSystemInterface_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_URewardSystemInterface_NoRegister(); \
public: \
	DECLARE_CLASS2(URewardSystemInterface, UInterface, COMPILED_IN_FLAGS(CLASS_Abstract | CLASS_Interface), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_URewardSystemInterface_NoRegister) \
	DECLARE_SERIALIZER(URewardSystemInterface)


#define FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h_11_GENERATED_BODY \
	PRAGMA_DISABLE_DEPRECATION_WARNINGS \
	FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h_11_GENERATED_UINTERFACE_BODY() \
	FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h_11_ENHANCED_CONSTRUCTORS \
private: \
	PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h_11_INCLASS_IINTERFACE_NO_PURE_DECLS \
protected: \
	virtual ~IRewardSystemInterface() {} \
public: \
	typedef URewardSystemInterface UClassType; \
	typedef IRewardSystemInterface ThisClass; \
	static bool Execute_CanGiveReward(const UObject* O, AActor* Player); \
	static void Execute_GiveAreaReward(UObject* O, FVector const& Location, float Radius, int32 GoldAmount, int32 ExperienceAmount); \
	static void Execute_GivePlayerReward(UObject* O, AActor* Player, int32 GoldAmount, int32 ExperienceAmount); \
	virtual UObject* _getUObject() const { return nullptr; }


#define FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h_8_PROLOG
#define FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h_19_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h_11_CALLBACK_WRAPPERS \
	FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h_11_INCLASS_IINTERFACE_NO_PURE_DECLS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URewardSystemInterface;

// ********** End Interface URewardSystemInterface *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_Interfaces_RewardSystemInterface_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
