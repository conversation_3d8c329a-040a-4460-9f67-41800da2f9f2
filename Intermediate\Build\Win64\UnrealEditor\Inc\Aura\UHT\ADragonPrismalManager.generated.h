// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "ADragonPrismalManager.h"

#ifdef AURA_ADragonPrismalManager_generated_h
#error "ADragonPrismalManager.generated.h already included, missing '#pragma once' in ADragonPrismalManager.h"
#endif
#define AURA_ADragonPrismalManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class AController;
class UPrimitiveComponent;
enum class EDragonElement : uint8;
enum class EDragonState : uint8;
enum class EDragonType : uint8;
struct FDamageEvent;
struct FDragonAttack;
struct FHitResult;

// ********** Begin ScriptStruct FDragonData *******************************************************
#define FID_Aura_Source_Aura_Public_ADragonPrismalManager_h_84_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FDragonData_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FDragonData;
// ********** End ScriptStruct FDragonData *********************************************************

// ********** Begin ScriptStruct FEllipticalArea ***************************************************
#define FID_Aura_Source_Aura_Public_ADragonPrismalManager_h_159_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FEllipticalArea_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FEllipticalArea;
// ********** End ScriptStruct FEllipticalArea *****************************************************

// ********** Begin ScriptStruct FDragonReward *****************************************************
#define FID_Aura_Source_Aura_Public_ADragonPrismalManager_h_203_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FDragonReward_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FDragonReward;
// ********** End ScriptStruct FDragonReward *******************************************************

// ********** Begin ScriptStruct FDragonAttack *****************************************************
#define FID_Aura_Source_Aura_Public_ADragonPrismalManager_h_245_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FDragonAttack_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FDragonAttack;
// ********** End ScriptStruct FDragonAttack *******************************************************

// ********** Begin ScriptStruct FFlightPath *******************************************************
#define FID_Aura_Source_Aura_Public_ADragonPrismalManager_h_283_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FFlightPath_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FFlightPath;
// ********** End ScriptStruct FFlightPath *********************************************************

// ********** Begin Class ADragonPrismalManager ****************************************************
#define FID_Aura_Source_Aura_Public_ADragonPrismalManager_h_312_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRewardAreaExit); \
	DECLARE_FUNCTION(execOnRewardAreaEnter); \
	DECLARE_FUNCTION(execOnDetectionAreaExit); \
	DECLARE_FUNCTION(execOnDetectionAreaEnter); \
	DECLARE_FUNCTION(execOnCombatAreaExit); \
	DECLARE_FUNCTION(execOnCombatAreaEnter); \
	DECLARE_FUNCTION(execInitializeRewardSystem); \
	DECLARE_FUNCTION(execSetupCombatMechanics); \
	DECLARE_FUNCTION(execUpdateAttacksForElement); \
	DECLARE_FUNCTION(execSetDragonElement); \
	DECLARE_FUNCTION(execSetDragonType); \
	DECLARE_FUNCTION(execGetDragonState); \
	DECLARE_FUNCTION(execSetDragonState); \
	DECLARE_FUNCTION(execGetTimeUntilRespawn); \
	DECLARE_FUNCTION(execGetTimeUntilSpawn); \
	DECLARE_FUNCTION(execShouldSpawnDragon); \
	DECLARE_FUNCTION(execDrawDebugLandingZones); \
	DECLARE_FUNCTION(execDrawDebugFlightPath); \
	DECLARE_FUNCTION(execDrawDebugEllipse); \
	DECLARE_FUNCTION(execValidateEllipticalGeometry); \
	DECLARE_FUNCTION(execCalculateSoloBonus); \
	DECLARE_FUNCTION(execCalculateTeamBonus); \
	DECLARE_FUNCTION(execGiveRewardToParticipant); \
	DECLARE_FUNCTION(execCalculateParticipationPercentage); \
	DECLARE_FUNCTION(execGetEligibleParticipants); \
	DECLARE_FUNCTION(execCalculateAndDistributeRewards); \
	DECLARE_FUNCTION(execCalculateFlightPathLength); \
	DECLARE_FUNCTION(execAdvanceToNextWaypoint); \
	DECLARE_FUNCTION(execGetNextWaypoint); \
	DECLARE_FUNCTION(execUpdateFlightMovement); \
	DECLARE_FUNCTION(execGenerateFlightPath); \
	DECLARE_FUNCTION(execExecuteAreaAttack); \
	DECLARE_FUNCTION(execExecuteBreathAttack); \
	DECLARE_FUNCTION(execPerformAttack); \
	DECLARE_FUNCTION(execOnDragonDeath); \
	DECLARE_FUNCTION(execTakeDamage); \
	DECLARE_FUNCTION(execEndCombat); \
	DECLARE_FUNCTION(execStartCombat); \
	DECLARE_FUNCTION(execLandDragon); \
	DECLARE_FUNCTION(execStartDragonFlight); \
	DECLARE_FUNCTION(execUpdateDragonHealth); \
	DECLARE_FUNCTION(execDespawnDragon); \
	DECLARE_FUNCTION(execSpawnDragon); \
	DECLARE_FUNCTION(execCalculateLandingZones); \
	DECLARE_FUNCTION(execGetClosestPointOnEllipse); \
	DECLARE_FUNCTION(execIsPositionInEllipse); \
	DECLARE_FUNCTION(execGenerateEllipsePoints); \
	DECLARE_FUNCTION(execCalculateEccentricity); \
	DECLARE_FUNCTION(execCalculateEllipticalPerimeter); \
	DECLARE_FUNCTION(execCalculateEllipticalArea); \
	DECLARE_FUNCTION(execCalculateEllipticalGeometry);


AURA_API UClass* Z_Construct_UClass_ADragonPrismalManager_NoRegister();

#define FID_Aura_Source_Aura_Public_ADragonPrismalManager_h_312_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesADragonPrismalManager(); \
	friend struct Z_Construct_UClass_ADragonPrismalManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_ADragonPrismalManager_NoRegister(); \
public: \
	DECLARE_CLASS2(ADragonPrismalManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_ADragonPrismalManager_NoRegister) \
	DECLARE_SERIALIZER(ADragonPrismalManager)


#define FID_Aura_Source_Aura_Public_ADragonPrismalManager_h_312_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ADragonPrismalManager(ADragonPrismalManager&&) = delete; \
	ADragonPrismalManager(const ADragonPrismalManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ADragonPrismalManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ADragonPrismalManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ADragonPrismalManager) \
	NO_API virtual ~ADragonPrismalManager();


#define FID_Aura_Source_Aura_Public_ADragonPrismalManager_h_309_PROLOG
#define FID_Aura_Source_Aura_Public_ADragonPrismalManager_h_312_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_ADragonPrismalManager_h_312_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_ADragonPrismalManager_h_312_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_ADragonPrismalManager_h_312_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ADragonPrismalManager;

// ********** End Class ADragonPrismalManager ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_ADragonPrismalManager_h

// ********** Begin Enum EDamageType ***************************************************************
#define FOREACH_ENUM_EDAMAGETYPE(op) \
	op(EDamageType::Physical) \
	op(EDamageType::Magical) \
	op(EDamageType::TrueDamage) 

enum class EDamageType : uint8;
template<> struct TIsUEnumClass<EDamageType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EDamageType>();
// ********** End Enum EDamageType *****************************************************************

// ********** Begin Enum EDragonAttackType *********************************************************
#define FOREACH_ENUM_EDRAGONATTACKTYPE(op) \
	op(EDragonAttackType::SoproPrismal) \
	op(EDragonAttackType::Cristalizacao) \
	op(EDragonAttackType::AreaDamage) 

enum class EDragonAttackType : uint8;
template<> struct TIsUEnumClass<EDragonAttackType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EDragonAttackType>();
// ********** End Enum EDragonAttackType ***********************************************************

// ********** Begin Enum EDragonElement ************************************************************
#define FOREACH_ENUM_EDRAGONELEMENT(op) \
	op(EDragonElement::Prismal) \
	op(EDragonElement::Fire) \
	op(EDragonElement::Ice) \
	op(EDragonElement::Lightning) \
	op(EDragonElement::Water) \
	op(EDragonElement::Earth) \
	op(EDragonElement::Air) 

enum class EDragonElement : uint8;
template<> struct TIsUEnumClass<EDragonElement> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EDragonElement>();
// ********** End Enum EDragonElement **************************************************************

// ********** Begin Enum EDragonState **************************************************************
#define FOREACH_ENUM_EDRAGONSTATE(op) \
	op(EDragonState::Dormant) \
	op(EDragonState::Spawning) \
	op(EDragonState::Active) \
	op(EDragonState::Combat) \
	op(EDragonState::Patrolling) \
	op(EDragonState::Flying) \
	op(EDragonState::Landing) \
	op(EDragonState::Dead) 

enum class EDragonState : uint8;
template<> struct TIsUEnumClass<EDragonState> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EDragonState>();
// ********** End Enum EDragonState ****************************************************************

// ********** Begin Enum EDragonType ***************************************************************
#define FOREACH_ENUM_EDRAGONTYPE(op) \
	op(EDragonType::Infernal) \
	op(EDragonType::Ocean) \
	op(EDragonType::Mountain) \
	op(EDragonType::Cloud) 

enum class EDragonType : uint8;
template<> struct TIsUEnumClass<EDragonType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EDragonType>();
// ********** End Enum EDragonType *****************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
