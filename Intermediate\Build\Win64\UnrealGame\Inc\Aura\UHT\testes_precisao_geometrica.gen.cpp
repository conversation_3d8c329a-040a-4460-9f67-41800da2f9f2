// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "testes_precisao_geometrica.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodetestes_precisao_geometrica() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_UTestePrecisaoGeometrica();
AURA_API UClass* Z_Construct_UClass_UTestePrecisaoGeometrica_NoRegister();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FRelatorioCompleto();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FResultadoTeste();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FResultadoTeste ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FResultadoTeste;
class UScriptStruct* FResultadoTeste::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FResultadoTeste.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FResultadoTeste.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FResultadoTeste, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("ResultadoTeste"));
	}
	return Z_Registration_Info_UScriptStruct_FResultadoTeste.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FResultadoTeste_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== ESTRUTURAS DE RESULTADO ==========\n" },
#endif
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== ESTRUTURAS DE RESULTADO ==========" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Sucesso_MetaData[] = {
		{ "Category", "ResultadoTeste" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NomeTeste_MetaData[] = {
		{ "Category", "ResultadoTeste" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Detalhes_MetaData[] = {
		{ "Category", "ResultadoTeste" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrecisaoObtida_MetaData[] = {
		{ "Category", "ResultadoTeste" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrecisaoEsperada_MetaData[] = {
		{ "Category", "ResultadoTeste" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrosEncontrados_MetaData[] = {
		{ "Category", "ResultadoTeste" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_Sucesso_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_Sucesso;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NomeTeste;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Detalhes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PrecisaoObtida;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PrecisaoEsperada;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrosEncontrados_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ErrosEncontrados;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FResultadoTeste>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_Sucesso_SetBit(void* Obj)
{
	((FResultadoTeste*)Obj)->Sucesso = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_Sucesso = { "Sucesso", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FResultadoTeste), &Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_Sucesso_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Sucesso_MetaData), NewProp_Sucesso_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_NomeTeste = { "NomeTeste", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResultadoTeste, NomeTeste), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NomeTeste_MetaData), NewProp_NomeTeste_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_Detalhes = { "Detalhes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResultadoTeste, Detalhes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Detalhes_MetaData), NewProp_Detalhes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_PrecisaoObtida = { "PrecisaoObtida", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResultadoTeste, PrecisaoObtida), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrecisaoObtida_MetaData), NewProp_PrecisaoObtida_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_PrecisaoEsperada = { "PrecisaoEsperada", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResultadoTeste, PrecisaoEsperada), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrecisaoEsperada_MetaData), NewProp_PrecisaoEsperada_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_ErrosEncontrados_Inner = { "ErrosEncontrados", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_ErrosEncontrados = { "ErrosEncontrados", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResultadoTeste, ErrosEncontrados), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrosEncontrados_MetaData), NewProp_ErrosEncontrados_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FResultadoTeste_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_Sucesso,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_NomeTeste,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_Detalhes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_PrecisaoObtida,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_PrecisaoEsperada,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_ErrosEncontrados_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewProp_ErrosEncontrados,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FResultadoTeste_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FResultadoTeste_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"ResultadoTeste",
	Z_Construct_UScriptStruct_FResultadoTeste_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FResultadoTeste_Statics::PropPointers),
	sizeof(FResultadoTeste),
	alignof(FResultadoTeste),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FResultadoTeste_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FResultadoTeste_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FResultadoTeste()
{
	if (!Z_Registration_Info_UScriptStruct_FResultadoTeste.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FResultadoTeste.InnerSingleton, Z_Construct_UScriptStruct_FResultadoTeste_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FResultadoTeste.InnerSingleton;
}
// ********** End ScriptStruct FResultadoTeste *****************************************************

// ********** Begin ScriptStruct FRelatorioCompleto ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRelatorioCompleto;
class UScriptStruct* FRelatorioCompleto::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRelatorioCompleto.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRelatorioCompleto.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRelatorioCompleto, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("RelatorioCompleto"));
	}
	return Z_Registration_Info_UScriptStruct_FRelatorioCompleto.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRelatorioCompleto_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResultadosTestes_MetaData[] = {
		{ "Category", "RelatorioCompleto" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestesPassaram_MetaData[] = {
		{ "Category", "RelatorioCompleto" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestesFalharam_MetaData[] = {
		{ "Category", "RelatorioCompleto" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrecisaoGeral_MetaData[] = {
		{ "Category", "RelatorioCompleto" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapaAprovado_MetaData[] = {
		{ "Category", "RelatorioCompleto" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResumoExecutivo_MetaData[] = {
		{ "Category", "RelatorioCompleto" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ResultadosTestes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ResultadosTestes;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TestesPassaram;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TestesFalharam;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PrecisaoGeral;
	static void NewProp_MapaAprovado_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_MapaAprovado;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ResumoExecutivo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRelatorioCompleto>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_ResultadosTestes_Inner = { "ResultadosTestes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FResultadoTeste, METADATA_PARAMS(0, nullptr) }; // 1073518523
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_ResultadosTestes = { "ResultadosTestes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRelatorioCompleto, ResultadosTestes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResultadosTestes_MetaData), NewProp_ResultadosTestes_MetaData) }; // 1073518523
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_TestesPassaram = { "TestesPassaram", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRelatorioCompleto, TestesPassaram), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestesPassaram_MetaData), NewProp_TestesPassaram_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_TestesFalharam = { "TestesFalharam", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRelatorioCompleto, TestesFalharam), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestesFalharam_MetaData), NewProp_TestesFalharam_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_PrecisaoGeral = { "PrecisaoGeral", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRelatorioCompleto, PrecisaoGeral), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrecisaoGeral_MetaData), NewProp_PrecisaoGeral_MetaData) };
void Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_MapaAprovado_SetBit(void* Obj)
{
	((FRelatorioCompleto*)Obj)->MapaAprovado = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_MapaAprovado = { "MapaAprovado", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRelatorioCompleto), &Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_MapaAprovado_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapaAprovado_MetaData), NewProp_MapaAprovado_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_ResumoExecutivo = { "ResumoExecutivo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRelatorioCompleto, ResumoExecutivo), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResumoExecutivo_MetaData), NewProp_ResumoExecutivo_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_ResultadosTestes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_ResultadosTestes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_TestesPassaram,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_TestesFalharam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_PrecisaoGeral,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_MapaAprovado,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewProp_ResumoExecutivo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"RelatorioCompleto",
	Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::PropPointers),
	sizeof(FRelatorioCompleto),
	alignof(FRelatorioCompleto),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRelatorioCompleto()
{
	if (!Z_Registration_Info_UScriptStruct_FRelatorioCompleto.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRelatorioCompleto.InnerSingleton, Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRelatorioCompleto.InnerSingleton;
}
// ********** End ScriptStruct FRelatorioCompleto **************************************************

// ********** Begin Class UTestePrecisaoGeometrica Function ExecutarTodosOsTestes ******************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics
{
	struct TestePrecisaoGeometrica_eventExecutarTodosOsTestes_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Testes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== TESTES DE VALIDA\xc3\x87\xc3\x83O PRINCIPAL ==========\n" },
#endif
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== TESTES DE VALIDA\xc3\x87\xc3\x83O PRINCIPAL ==========" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventExecutarTodosOsTestes_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventExecutarTodosOsTestes_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "ExecutarTodosOsTestes", Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::TestePrecisaoGeometrica_eventExecutarTodosOsTestes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::TestePrecisaoGeometrica_eventExecutarTodosOsTestes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execExecutarTodosOsTestes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::ExecutarTodosOsTestes();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function ExecutarTodosOsTestes ********************

// ********** Begin Class UTestePrecisaoGeometrica Function GerarRelatorioCompleto *****************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto_Statics
{
	struct TestePrecisaoGeometrica_eventGerarRelatorioCompleto_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Testes" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TestePrecisaoGeometrica_eventGerarRelatorioCompleto_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "GerarRelatorioCompleto", Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto_Statics::TestePrecisaoGeometrica_eventGerarRelatorioCompleto_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto_Statics::TestePrecisaoGeometrica_eventGerarRelatorioCompleto_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execGerarRelatorioCompleto)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UTestePrecisaoGeometrica::GerarRelatorioCompleto();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function GerarRelatorioCompleto *******************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarAcessoCovils *********************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics
{
	struct TestePrecisaoGeometrica_eventTestarAcessoCovils_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integra\xc3\xa7\xc3\xa3o" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarAcessoCovils_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarAcessoCovils_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarAcessoCovils", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::TestePrecisaoGeometrica_eventTestarAcessoCovils_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::TestePrecisaoGeometrica_eventTestarAcessoCovils_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarAcessoCovils)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarAcessoCovils();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarAcessoCovils ***********************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarCalculosAngulares ****************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics
{
	struct TestePrecisaoGeometrica_eventTestarCalculosAngulares_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Precis\xc3\xa3o" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarCalculosAngulares_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarCalculosAngulares_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarCalculosAngulares", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::TestePrecisaoGeometrica_eventTestarCalculosAngulares_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::TestePrecisaoGeometrica_eventTestarCalculosAngulares_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarCalculosAngulares)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarCalculosAngulares();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarCalculosAngulares ******************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarCalculosArea *********************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics
{
	struct TestePrecisaoGeometrica_eventTestarCalculosArea_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Precis\xc3\xa3o" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarCalculosArea_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarCalculosArea_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarCalculosArea", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::TestePrecisaoGeometrica_eventTestarCalculosArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::TestePrecisaoGeometrica_eventTestarCalculosArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarCalculosArea)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarCalculosArea();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarCalculosArea ***********************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarCalculosDistancia ****************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics
{
	struct TestePrecisaoGeometrica_eventTestarCalculosDistancia_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Precis\xc3\xa3o" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarCalculosDistancia_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarCalculosDistancia_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarCalculosDistancia", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::TestePrecisaoGeometrica_eventTestarCalculosDistancia_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::TestePrecisaoGeometrica_eventTestarCalculosDistancia_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarCalculosDistancia)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarCalculosDistancia();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarCalculosDistancia ******************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarColisoesBordas *******************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics
{
	struct TestePrecisaoGeometrica_eventTestarColisoesBordas_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integra\xc3\xa7\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== TESTES DE INTEGRA\xc3\x87\xc3\x83O ==========\n" },
#endif
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== TESTES DE INTEGRA\xc3\x87\xc3\x83O ==========" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarColisoesBordas_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarColisoesBordas_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarColisoesBordas", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::TestePrecisaoGeometrica_eventTestarColisoesBordas_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::TestePrecisaoGeometrica_eventTestarColisoesBordas_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarColisoesBordas)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarColisoesBordas();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarColisoesBordas *********************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarConectividadeLanes ***************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics
{
	struct TestePrecisaoGeometrica_eventTestarConectividadeLanes_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integra\xc3\xa7\xc3\xa3o" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarConectividadeLanes_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarConectividadeLanes_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarConectividadeLanes", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::TestePrecisaoGeometrica_eventTestarConectividadeLanes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::TestePrecisaoGeometrica_eventTestarConectividadeLanes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarConectividadeLanes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarConectividadeLanes();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarConectividadeLanes *****************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarDimensoesCovils ******************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics
{
	struct TestePrecisaoGeometrica_eventTestarDimensoesCovils_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Testes Covils" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarDimensoesCovils_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarDimensoesCovils_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarDimensoesCovils", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::TestePrecisaoGeometrica_eventTestarDimensoesCovils_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::TestePrecisaoGeometrica_eventTestarDimensoesCovils_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarDimensoesCovils)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarDimensoesCovils();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarDimensoesCovils ********************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarEspecificacoesBases **************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics
{
	struct TestePrecisaoGeometrica_eventTestarEspecificacoesBases_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Testes Bases" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarEspecificacoesBases_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarEspecificacoesBases_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarEspecificacoesBases", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::TestePrecisaoGeometrica_eventTestarEspecificacoesBases_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::TestePrecisaoGeometrica_eventTestarEspecificacoesBases_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarEspecificacoesBases)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarEspecificacoesBases();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarEspecificacoesBases ****************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarFluxoMinions *********************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics
{
	struct TestePrecisaoGeometrica_eventTestarFluxoMinions_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integra\xc3\xa7\xc3\xa3o" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarFluxoMinions_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarFluxoMinions_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarFluxoMinions", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::TestePrecisaoGeometrica_eventTestarFluxoMinions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::TestePrecisaoGeometrica_eventTestarFluxoMinions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarFluxoMinions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarFluxoMinions();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarFluxoMinions ***********************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarFuncaoSenoidalRio ****************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics
{
	struct TestePrecisaoGeometrica_eventTestarFuncaoSenoidalRio_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Testes Rio" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarFuncaoSenoidalRio_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarFuncaoSenoidalRio_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarFuncaoSenoidalRio", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::TestePrecisaoGeometrica_eventTestarFuncaoSenoidalRio_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::TestePrecisaoGeometrica_eventTestarFuncaoSenoidalRio_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarFuncaoSenoidalRio)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarFuncaoSenoidalRio();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarFuncaoSenoidalRio ******************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarGeometriaCirculos ****************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics
{
	struct TestePrecisaoGeometrica_eventTestarGeometriaCirculos_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Testes Geometria" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarGeometriaCirculos_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarGeometriaCirculos_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarGeometriaCirculos", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::TestePrecisaoGeometrica_eventTestarGeometriaCirculos_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::TestePrecisaoGeometrica_eventTestarGeometriaCirculos_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarGeometriaCirculos)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarGeometriaCirculos();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarGeometriaCirculos ******************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarGeometriaElipses *****************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics
{
	struct TestePrecisaoGeometrica_eventTestarGeometriaElipses_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Testes Geometria" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarGeometriaElipses_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarGeometriaElipses_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarGeometriaElipses", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::TestePrecisaoGeometrica_eventTestarGeometriaElipses_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::TestePrecisaoGeometrica_eventTestarGeometriaElipses_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarGeometriaElipses)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarGeometriaElipses();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarGeometriaElipses *******************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarGeometriaHexagonos ***************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics
{
	struct TestePrecisaoGeometrica_eventTestarGeometriaHexagonos_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Testes Geometria" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarGeometriaHexagonos_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarGeometriaHexagonos_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarGeometriaHexagonos", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::TestePrecisaoGeometrica_eventTestarGeometriaHexagonos_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::TestePrecisaoGeometrica_eventTestarGeometriaHexagonos_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarGeometriaHexagonos)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarGeometriaHexagonos();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarGeometriaHexagonos *****************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarPosicionamentoTorres *************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics
{
	struct TestePrecisaoGeometrica_eventTestarPosicionamentoTorres_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Testes Torres" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarPosicionamentoTorres_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarPosicionamentoTorres_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarPosicionamentoTorres", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::TestePrecisaoGeometrica_eventTestarPosicionamentoTorres_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::TestePrecisaoGeometrica_eventTestarPosicionamentoTorres_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarPosicionamentoTorres)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarPosicionamentoTorres();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarPosicionamentoTorres ***************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarPrecisaoLanes ********************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics
{
	struct TestePrecisaoGeometrica_eventTestarPrecisaoLanes_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Testes Lanes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== TESTES ESPEC\xc3\x8d""FICOS POR COMPONENTE ==========\n" },
#endif
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== TESTES ESPEC\xc3\x8d""FICOS POR COMPONENTE ==========" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarPrecisaoLanes_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarPrecisaoLanes_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarPrecisaoLanes", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::TestePrecisaoGeometrica_eventTestarPrecisaoLanes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::TestePrecisaoGeometrica_eventTestarPrecisaoLanes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarPrecisaoLanes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarPrecisaoLanes();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarPrecisaoLanes **********************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarSistemaMinions *******************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics
{
	struct TestePrecisaoGeometrica_eventTestarSistemaMinions_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Testes Minions" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarSistemaMinions_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarSistemaMinions_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarSistemaMinions", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::TestePrecisaoGeometrica_eventTestarSistemaMinions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::TestePrecisaoGeometrica_eventTestarSistemaMinions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarSistemaMinions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarSistemaMinions();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarSistemaMinions *********************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarSistemaParedes *******************
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics
{
	struct TestePrecisaoGeometrica_eventTestarSistemaParedes_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Testes Paredes" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarSistemaParedes_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarSistemaParedes_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarSistemaParedes", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::TestePrecisaoGeometrica_eventTestarSistemaParedes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::TestePrecisaoGeometrica_eventTestarSistemaParedes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarSistemaParedes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarSistemaParedes();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarSistemaParedes *********************

// ********** Begin Class UTestePrecisaoGeometrica Function TestarToleranciasCoordenadas ***********
struct Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics
{
	struct TestePrecisaoGeometrica_eventTestarToleranciasCoordenadas_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Precis\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== TESTES DE PRECIS\xc3\x83O MATEM\xc3\x81TICA ==========\n" },
#endif
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== TESTES DE PRECIS\xc3\x83O MATEM\xc3\x81TICA ==========" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TestePrecisaoGeometrica_eventTestarToleranciasCoordenadas_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TestePrecisaoGeometrica_eventTestarToleranciasCoordenadas_Parms), &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UTestePrecisaoGeometrica, nullptr, "TestarToleranciasCoordenadas", Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::TestePrecisaoGeometrica_eventTestarToleranciasCoordenadas_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::TestePrecisaoGeometrica_eventTestarToleranciasCoordenadas_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTestePrecisaoGeometrica::execTestarToleranciasCoordenadas)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UTestePrecisaoGeometrica::TestarToleranciasCoordenadas();
	P_NATIVE_END;
}
// ********** End Class UTestePrecisaoGeometrica Function TestarToleranciasCoordenadas *************

// ********** Begin Class UTestePrecisaoGeometrica *************************************************
void UTestePrecisaoGeometrica::StaticRegisterNativesUTestePrecisaoGeometrica()
{
	UClass* Class = UTestePrecisaoGeometrica::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ExecutarTodosOsTestes", &UTestePrecisaoGeometrica::execExecutarTodosOsTestes },
		{ "GerarRelatorioCompleto", &UTestePrecisaoGeometrica::execGerarRelatorioCompleto },
		{ "TestarAcessoCovils", &UTestePrecisaoGeometrica::execTestarAcessoCovils },
		{ "TestarCalculosAngulares", &UTestePrecisaoGeometrica::execTestarCalculosAngulares },
		{ "TestarCalculosArea", &UTestePrecisaoGeometrica::execTestarCalculosArea },
		{ "TestarCalculosDistancia", &UTestePrecisaoGeometrica::execTestarCalculosDistancia },
		{ "TestarColisoesBordas", &UTestePrecisaoGeometrica::execTestarColisoesBordas },
		{ "TestarConectividadeLanes", &UTestePrecisaoGeometrica::execTestarConectividadeLanes },
		{ "TestarDimensoesCovils", &UTestePrecisaoGeometrica::execTestarDimensoesCovils },
		{ "TestarEspecificacoesBases", &UTestePrecisaoGeometrica::execTestarEspecificacoesBases },
		{ "TestarFluxoMinions", &UTestePrecisaoGeometrica::execTestarFluxoMinions },
		{ "TestarFuncaoSenoidalRio", &UTestePrecisaoGeometrica::execTestarFuncaoSenoidalRio },
		{ "TestarGeometriaCirculos", &UTestePrecisaoGeometrica::execTestarGeometriaCirculos },
		{ "TestarGeometriaElipses", &UTestePrecisaoGeometrica::execTestarGeometriaElipses },
		{ "TestarGeometriaHexagonos", &UTestePrecisaoGeometrica::execTestarGeometriaHexagonos },
		{ "TestarPosicionamentoTorres", &UTestePrecisaoGeometrica::execTestarPosicionamentoTorres },
		{ "TestarPrecisaoLanes", &UTestePrecisaoGeometrica::execTestarPrecisaoLanes },
		{ "TestarSistemaMinions", &UTestePrecisaoGeometrica::execTestarSistemaMinions },
		{ "TestarSistemaParedes", &UTestePrecisaoGeometrica::execTestarSistemaParedes },
		{ "TestarToleranciasCoordenadas", &UTestePrecisaoGeometrica::execTestarToleranciasCoordenadas },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UTestePrecisaoGeometrica;
UClass* UTestePrecisaoGeometrica::GetPrivateStaticClass()
{
	using TClass = UTestePrecisaoGeometrica;
	if (!Z_Registration_Info_UClass_UTestePrecisaoGeometrica.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("TestePrecisaoGeometrica"),
			Z_Registration_Info_UClass_UTestePrecisaoGeometrica.InnerSingleton,
			StaticRegisterNativesUTestePrecisaoGeometrica,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UTestePrecisaoGeometrica.InnerSingleton;
}
UClass* Z_Construct_UClass_UTestePrecisaoGeometrica_NoRegister()
{
	return UTestePrecisaoGeometrica::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UTestePrecisaoGeometrica_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * SISTEMA DE TESTES AUTOMATIZADOS - PRECIS\xc3\x83O GEOM\xc3\x89TRICA 100%\n * Verifica matematicamente cada aspecto do mapa Aura\n * Garante que todas as especifica\xc3\xa7\xc3\xb5""es sejam implementadas corretamente\n */" },
#endif
		{ "IncludePath", "testes_precisao_geometrica.h" },
		{ "ModuleRelativePath", "Public/testes_precisao_geometrica.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "SISTEMA DE TESTES AUTOMATIZADOS - PRECIS\xc3\x83O GEOM\xc3\x89TRICA 100%\nVerifica matematicamente cada aspecto do mapa Aura\nGarante que todas as especifica\xc3\xa7\xc3\xb5""es sejam implementadas corretamente" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_ExecutarTodosOsTestes, "ExecutarTodosOsTestes" }, // 1788668120
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_GerarRelatorioCompleto, "GerarRelatorioCompleto" }, // 2036083054
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarAcessoCovils, "TestarAcessoCovils" }, // 452451545
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosAngulares, "TestarCalculosAngulares" }, // 1199259826
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosArea, "TestarCalculosArea" }, // 922345403
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarCalculosDistancia, "TestarCalculosDistancia" }, // 1333700042
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarColisoesBordas, "TestarColisoesBordas" }, // 2811776034
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarConectividadeLanes, "TestarConectividadeLanes" }, // 3390325096
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarDimensoesCovils, "TestarDimensoesCovils" }, // 1317320385
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarEspecificacoesBases, "TestarEspecificacoesBases" }, // 2082406873
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFluxoMinions, "TestarFluxoMinions" }, // 3606565937
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarFuncaoSenoidalRio, "TestarFuncaoSenoidalRio" }, // 1181533091
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaCirculos, "TestarGeometriaCirculos" }, // 181513104
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaElipses, "TestarGeometriaElipses" }, // 3858722867
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarGeometriaHexagonos, "TestarGeometriaHexagonos" }, // 2861783311
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPosicionamentoTorres, "TestarPosicionamentoTorres" }, // 635136375
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarPrecisaoLanes, "TestarPrecisaoLanes" }, // 1734042250
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaMinions, "TestarSistemaMinions" }, // 1984953787
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarSistemaParedes, "TestarSistemaParedes" }, // 747519662
		{ &Z_Construct_UFunction_UTestePrecisaoGeometrica_TestarToleranciasCoordenadas, "TestarToleranciasCoordenadas" }, // 3181347996
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UTestePrecisaoGeometrica>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UTestePrecisaoGeometrica_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UTestePrecisaoGeometrica_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UTestePrecisaoGeometrica_Statics::ClassParams = {
	&UTestePrecisaoGeometrica::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UTestePrecisaoGeometrica_Statics::Class_MetaDataParams), Z_Construct_UClass_UTestePrecisaoGeometrica_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UTestePrecisaoGeometrica()
{
	if (!Z_Registration_Info_UClass_UTestePrecisaoGeometrica.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UTestePrecisaoGeometrica.OuterSingleton, Z_Construct_UClass_UTestePrecisaoGeometrica_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UTestePrecisaoGeometrica.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UTestePrecisaoGeometrica);
UTestePrecisaoGeometrica::~UTestePrecisaoGeometrica() {}
// ********** End Class UTestePrecisaoGeometrica ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h__Script_Aura_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FResultadoTeste::StaticStruct, Z_Construct_UScriptStruct_FResultadoTeste_Statics::NewStructOps, TEXT("ResultadoTeste"), &Z_Registration_Info_UScriptStruct_FResultadoTeste, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FResultadoTeste), 1073518523U) },
		{ FRelatorioCompleto::StaticStruct, Z_Construct_UScriptStruct_FRelatorioCompleto_Statics::NewStructOps, TEXT("RelatorioCompleto"), &Z_Registration_Info_UScriptStruct_FRelatorioCompleto, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRelatorioCompleto), 2552732196U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UTestePrecisaoGeometrica, UTestePrecisaoGeometrica::StaticClass, TEXT("UTestePrecisaoGeometrica"), &Z_Registration_Info_UClass_UTestePrecisaoGeometrica, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UTestePrecisaoGeometrica), 2796210006U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h__Script_Aura_400411113(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h__Script_Aura_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
