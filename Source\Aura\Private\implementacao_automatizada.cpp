#include "implementacao_automatizada.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/Material.h"
#include "UObject/ConstructorHelpers.h"
#include "Engine/Engine.h"
#include "EngineUtils.h"
#include "Engine/World.h"


AImplementacaoAutomatizada::AImplementacaoAutomatizada()
{
    PrimaryActorTick.bCanEverTick = false;

    // Criar componente de mesh
    MeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("MeshComponent"));
    RootComponent = MeshComponent;

    // Inicializar especificações das lanes com valores matemáticos precisos
    LanesEspecificacoes.SetNum(3);

    // Lane Superior: Y = -0.577X + 6928
    LanesEspecificacoes[0].CoeficienteAngular = -0.57735026919f;
    LanesEspecificacoes[0].CoeficienteLinear = 6928.0f;
    LanesEspecificacoes[0].Largura = 600.0f;
    LanesEspecificacoes[0].PontoInicial = FVector(-6000.0f, 10392.0f, 0.0f);
    LanesEspecificacoes[0].PontoFinal = FVector(6000.0f, -3464.0f, 0.0f);

    // Lane Central: Y = 0
    LanesEspecificacoes[1].CoeficienteAngular = 0.0f;
    LanesEspecificacoes[1].CoeficienteLinear = 0.0f;
    LanesEspecificacoes[1].Largura = 800.0f;
    LanesEspecificacoes[1].PontoInicial = FVector(-4800.0f, 0.0f, 0.0f);
    LanesEspecificacoes[1].PontoFinal = FVector(4800.0f, 0.0f, 0.0f);

    // Lane Inferior: Y = 0.577X - 6928
    LanesEspecificacoes[2].CoeficienteAngular = 0.57735026919f;
    LanesEspecificacoes[2].CoeficienteLinear = -6928.0f;
    LanesEspecificacoes[2].Largura = 600.0f;
    LanesEspecificacoes[2].PontoInicial = FVector(-6000.0f, -10392.0f, 0.0f);
    LanesEspecificacoes[2].PontoFinal = FVector(6000.0f, 3464.0f, 0.0f);

    // Inicializar especificações dos covils
    CovilsEspecificacoes.SetNum(6);

    // Dragão Prismal (Elipse)
    CovilsEspecificacoes[0].Centro = FVector(0.0f, 4800.0f, 0.0f);
    CovilsEspecificacoes[0].TipoGeometria = "Elipse";
    CovilsEspecificacoes[0].Parametro1 = 800.0f;  // Semi-eixo A
    CovilsEspecificacoes[0].Parametro2 = 600.0f;  // Semi-eixo B

    // Barão Auracron (Hexágono)
    CovilsEspecificacoes[1].Centro = FVector(0.0f, -4800.0f, 0.0f);
    CovilsEspecificacoes[1].TipoGeometria = "Hexagono";
    CovilsEspecificacoes[1].Parametro1 = 700.0f;  // Raio

    // Sentinelas Cristalinas (4 Círculos)
    CovilsEspecificacoes[2].Centro = FVector(3000.0f, 2400.0f, 0.0f);
    CovilsEspecificacoes[2].TipoGeometria = "Circulo";
    CovilsEspecificacoes[2].Parametro1 = 400.0f;

    CovilsEspecificacoes[3].Centro = FVector(-3000.0f, 2400.0f, 0.0f);
    CovilsEspecificacoes[3].TipoGeometria = "Circulo";
    CovilsEspecificacoes[3].Parametro1 = 400.0f;

    CovilsEspecificacoes[4].Centro = FVector(3000.0f, -2400.0f, 0.0f);
    CovilsEspecificacoes[4].TipoGeometria = "Circulo";
    CovilsEspecificacoes[4].Parametro1 = 400.0f;

    CovilsEspecificacoes[5].Centro = FVector(-3000.0f, -2400.0f, 0.0f);
    CovilsEspecificacoes[5].TipoGeometria = "Circulo";
    CovilsEspecificacoes[5].Parametro1 = 400.0f;
}

void AImplementacaoAutomatizada::BeginPlay()
{
    Super::BeginPlay();

    // Verificar precisão ao iniciar
    if (!VerificarPrecisaoCompleta())
    {
        UE_LOG(LogTemp, Warning, TEXT("Precisão matemática comprometida! Executando correções..."));
        CorrigirDesviosPrecisao();
    }
}

// ========== FUNÇÕES DE VALIDAÇÃO GEOMÉTRICA ==========

bool AImplementacaoAutomatizada::ValidarPontoNaLane(float X, float Y, int32 LaneIndex)
{
    if (LaneIndex < 0 || LaneIndex >= 3) return false;

    float YEsperado;
    float LarguraLane;

    switch (LaneIndex)
    {
        case 0: // Lane Superior
            YEsperado = CALCULAR_Y_LANE_SUPERIOR(X);
            LarguraLane = 600.0f;
            break;
        case 1: // Lane Central
            YEsperado = 0.0f;
            LarguraLane = 800.0f;
            // Verificar se está dentro dos limites X da lane central
            if (FMath::Abs(X) > 4800.0f) return false;
            break;
        case 2: // Lane Inferior
            YEsperado = CALCULAR_Y_LANE_INFERIOR(X);
            LarguraLane = 600.0f;
            break;
        default:
            return false;
    }

    return FMath::Abs(Y - YEsperado) <= (LarguraLane / 2.0f);
}

bool AImplementacaoAutomatizada::ValidarPontoNoRio(float X, float Y)
{
    // Verificar se X está dentro dos limites do rio
    if (FMath::Abs(X) > 4800.0f) return false;

    // Calcular Y central do rio (função senoidal)
    float YCentralRio = CALCULAR_Y_RIO_SENOIDAL(X);

    // Calcular largura variável do rio
    float LarguraRio = CALCULAR_LARGURA_RIO(X);

    // Verificar se o ponto está dentro da largura do rio
    return FMath::Abs(Y - YCentralRio) <= (LarguraRio / 2.0f);
}

bool AImplementacaoAutomatizada::ValidarPontoEmHexagono(float X, float Y, FVector2D Centro, float Raio)
{
    float DX = X - Centro.X;
    float DY = Y - Centro.Y;
    float Distancia = FMath::Sqrt(DX * DX + DY * DY);

    // Verificação rápida: se está fora do círculo circunscrito
    if (Distancia > Raio) return false;

    // Calcular ângulo do ponto em relação ao centro
    float Angulo = FMath::Atan2(DY, DX);

    // Normalizar ângulo para o primeiro sextante do hexágono
    float AnguloHex = FMath::Fmod(Angulo + AImplementacaoAutomatizada::PI_PRECISO / 6.0f, AImplementacaoAutomatizada::PI_PRECISO / 3.0f) - AImplementacaoAutomatizada::PI_PRECISO / 6.0f;

    // Calcular distância máxima permitida neste ângulo
    float DistanciaMaxima = Raio * FMath::Cos(AImplementacaoAutomatizada::PI_PRECISO / 6.0f) / FMath::Cos(AnguloHex);

    return Distancia <= DistanciaMaxima;
}

bool AImplementacaoAutomatizada::ValidarPontoEmElipse(float X, float Y, FVector2D Centro, float SemiEixoA, float SemiEixoB)
{
    float DX = X - Centro.X;
    float DY = Y - Centro.Y;

    // Equação da elipse: (x/a)² + (y/b)² <= 1
    float TermoX = (DX * DX) / (SemiEixoA * SemiEixoA);
    float TermoY = (DY * DY) / (SemiEixoB * SemiEixoB);

    return (TermoX + TermoY) <= 1.0f;
}

// ========== FUNÇÕES DE CÁLCULO AUTOMÁTICO ==========

FVector AImplementacaoAutomatizada::CalcularPosicaoTorre(int32 LaneIndex, int32 TorreIndex, bool IsTeamAzul)
{
    if (LaneIndex < 0 || LaneIndex >= 3) return FVector::ZeroVector;

    // Distâncias das torres ao longo da lane
    TArray<float> DistanciasTorres = {1200.0f, 2400.0f, 3600.0f, 4800.0f}; // Da base para frente

    if (TorreIndex < 0 || TorreIndex >= DistanciasTorres.Num()) return FVector::ZeroVector;

    float DistanciaBase = DistanciasTorres[TorreIndex];
    if (!IsTeamAzul) DistanciaBase = -DistanciaBase; // Time vermelho: direção oposta

    FVector PosicaoTorre;

    switch (LaneIndex)
    {
        case 0: // Lane Superior
        {
            // Calcular posição ao longo da lane superior
            float X = IsTeamAzul ? (-6000.0f + DistanciaBase * 0.866f) : (6000.0f + DistanciaBase * 0.866f);
            float Y = CALCULAR_Y_LANE_SUPERIOR(X);
            PosicaoTorre = FVector(X, Y, 0.0f);
            break;
        }
        case 1: // Lane Central
        {
            float X = IsTeamAzul ? (-4800.0f + DistanciaBase) : (4800.0f + DistanciaBase);
            PosicaoTorre = FVector(X, 0.0f, 0.0f);
            break;
        }
        case 2: // Lane Inferior
        {
            float X = IsTeamAzul ? (-6000.0f + DistanciaBase * 0.866f) : (6000.0f + DistanciaBase * 0.866f);
            float Y = CALCULAR_Y_LANE_INFERIOR(X);
            PosicaoTorre = FVector(X, Y, 0.0f);
            break;
        }
    }

    return PosicaoTorre;
}

TArray<FVector> AImplementacaoAutomatizada::CalcularVerticesHexagono(FVector2D Centro, float Raio, float RotacaoGraus)
{
    TArray<FVector> Vertices;
    Vertices.SetNum(6);

    float RotacaoRad = FMath::DegreesToRadians(RotacaoGraus);

    for (int32 i = 0; i < 6; i++)
    {
        float Angulo = (AImplementacaoAutomatizada::PI_PRECISO / 3.0f) * i + RotacaoRad;
        float X = Centro.X + Raio * FMath::Cos(Angulo);
        float Y = Centro.Y + Raio * FMath::Sin(Angulo);
        Vertices[i] = FVector(X, Y, 0.0f);
    }

    return Vertices;
}

TArray<FVector> AImplementacaoAutomatizada::CalcularPontosRioSenoidal(int32 NumPontos)
{
    TArray<FVector> PontosRio;
    PontosRio.SetNum(NumPontos);

    float DeltaX = 9600.0f / (NumPontos - 1); // Comprimento total do rio

    for (int32 i = 0; i < NumPontos; i++)
    {
        float X = -4800.0f + i * DeltaX;
        float Y = CALCULAR_Y_RIO_SENOIDAL(X);
        float LarguraLocal = CALCULAR_LARGURA_RIO(X);

        PontosRio[i] = FVector(X, Y, LarguraLocal); // Z armazena a largura
    }

    return PontosRio;
}

FVector AImplementacaoAutomatizada::CalcularPosicaoMinion(int32 LaneIndex, float DistanciaPercorrida, bool IsTeamAzul)
{
    if (LaneIndex < 0 || LaneIndex >= 3) return FVector::ZeroVector;

    // Resolver mundo atual de forma robusta (UE 5.6)
    UWorld* World = nullptr;
    if (GEngine)
    {
        const TIndirectArray<FWorldContext>& Contexts = GEngine->GetWorldContexts();
        for (const FWorldContext& Ctx : Contexts)
        {
            if (Ctx.World() && (Ctx.WorldType == EWorldType::Editor || Ctx.WorldType == EWorldType::Game || Ctx.WorldType == EWorldType::PIE))
            {
                World = Ctx.World();
                break;
            }
        }
    }
    if (!World)
    {
        return FVector::ZeroVector;
    }

    FVector PosicaoInicial = FVector::ZeroVector;
    FVector PosicaoFinal = FVector::ZeroVector;

    // Procurar uma instância ativa para acessar LanesEspecificacoes
    for (TActorIterator<AImplementacaoAutomatizada> It(World); It; ++It)
    {
        const AImplementacaoAutomatizada* Impl = *It;
        if (Impl && Impl->LanesEspecificacoes.IsValidIndex(LaneIndex))
        {
            const FLaneEspecificacao& Lane = Impl->LanesEspecificacoes[LaneIndex];
            PosicaoInicial = IsTeamAzul ? Lane.PontoInicial : Lane.PontoFinal;
            PosicaoFinal   = IsTeamAzul ? Lane.PontoFinal   : Lane.PontoInicial;
            break;
        }
    }

    if (PosicaoInicial.IsZero() && PosicaoFinal.IsZero())
    {
        return FVector::ZeroVector;
    }

    // Calcular comprimento total da lane
    const float ComprimentoTotal = FVector::Dist2D(PosicaoInicial, PosicaoFinal);
    if (ComprimentoTotal <= KINDA_SMALL_NUMBER)
    {
        return PosicaoInicial;
    }

    // Calcular parâmetro t (0 a 1) baseado na distância percorrida
    const float T = FMath::Clamp(DistanciaPercorrida / ComprimentoTotal, 0.0f, 1.0f);

    // Interpolação linear ao longo da lane
    return FMath::Lerp(PosicaoInicial, PosicaoFinal, T);
}

// ========== SISTEMA DE CRIAÇÃO AUTOMÁTICA ==========

void AImplementacaoAutomatizada::CriarMapaCompleto()
{
    UE_LOG(LogTemp, Log, TEXT("Iniciando criação automática do mapa Aura..."));

    CriarLanes();
    CriarRioPrismal();
    CriarBases();
    CriarCovils();
    CriarTorres();
    CriarParedes();
    CriarIlhaCentral();

    // Verificação final
    if (VerificarPrecisaoCompleta())
    {
        UE_LOG(LogTemp, Log, TEXT("Mapa criado com 100%% de precisão matemática!"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Erro na precisão do mapa! Executando correções..."));
        CorrigirDesviosPrecisao();
    }
}

void AImplementacaoAutomatizada::CriarLanes()
{
    UE_LOG(LogTemp, Log, TEXT("Criando lanes com especificações matemáticas precisas..."));

    for (int32 i = 0; i < LanesEspecificacoes.Num(); i++)
    {
        const FLaneEspecificacao& Lane = LanesEspecificacoes[i];

        // Criar geometria da lane baseada na função linear
        // Implementação específica dependeria do sistema de mesh do projeto

        UE_LOG(LogTemp, Log, TEXT("Lane %d: Y = %.6fX + %.2f, Largura: %.2f"),
               i, Lane.CoeficienteAngular, Lane.CoeficienteLinear, Lane.Largura);
    }
}

void AImplementacaoAutomatizada::CriarRioPrismal()
{
    UE_LOG(LogTemp, Log, TEXT("Criando Rio Prismal com função senoidal..."));

    TArray<FVector> PontosRio = CalcularPontosRioSenoidal(200);

    // Criar mesh do rio baseado nos pontos calculados
    // Implementação específica dependeria do sistema de mesh do projeto

    UE_LOG(LogTemp, Log, TEXT("Rio criado com %d pontos senoidais"), PontosRio.Num());
}

void AImplementacaoAutomatizada::CriarBases()
{
    UE_LOG(LogTemp, Log, TEXT("Criando bases hexagonais..."));

    // Base Azul
    FVector2D CentroBaseAzul(-6000.0f, -6000.0f);
    TArray<FVector> VerticesBaseAzul = CalcularVerticesHexagono(CentroBaseAzul, 1200.0f, 90.0f);

    // Base Vermelha
    FVector2D CentroBaseVermelha(6000.0f, 6000.0f);
    TArray<FVector> VerticesBaseVermelha = CalcularVerticesHexagono(CentroBaseVermelha, 1200.0f, 90.0f);

    UE_LOG(LogTemp, Log, TEXT("Bases criadas: Azul em (%.0f, %.0f), Vermelha em (%.0f, %.0f)"),
           CentroBaseAzul.X, CentroBaseAzul.Y, CentroBaseVermelha.X, CentroBaseVermelha.Y);
}

void AImplementacaoAutomatizada::CriarCovils()
{
    UE_LOG(LogTemp, Log, TEXT("Criando covils com geometrias específicas..."));

    for (int32 i = 0; i < CovilsEspecificacoes.Num(); i++)
    {
        const FCovilEspecificacao& Covil = CovilsEspecificacoes[i];

        if (Covil.TipoGeometria == "Hexagono")
        {
            TArray<FVector> Vertices = CalcularVerticesHexagono(
                FVector2D(Covil.Centro.X, Covil.Centro.Y),
                Covil.Parametro1,
                Covil.RotacaoGraus
            );
        }
        else if (Covil.TipoGeometria == "Elipse")
        {
            // Criar elipse com semi-eixos Parametro1 e Parametro2
        }
        else if (Covil.TipoGeometria == "Circulo")
        {
            // Criar círculo com raio Parametro1
        }

        UE_LOG(LogTemp, Log, TEXT("Covil %d (%s) criado em (%.0f, %.0f)"),
               i, *Covil.TipoGeometria, Covil.Centro.X, Covil.Centro.Y);
    }
}

void AImplementacaoAutomatizada::CriarTorres()
{
    UE_LOG(LogTemp, Log, TEXT("Criando torres com posicionamento matemático..."));

    for (int32 Lane = 0; Lane < 3; Lane++)
    {
        for (int32 Torre = 0; Torre < 4; Torre++)
        {
            // Torres do time azul
            FVector PosAzul = CalcularPosicaoTorre(Lane, Torre, true);

            // Torres do time vermelho
            FVector PosVermelho = CalcularPosicaoTorre(Lane, Torre, false);

            UE_LOG(LogTemp, Log, TEXT("Torre L%d T%d: Azul(%.1f, %.1f), Vermelho(%.1f, %.1f)"),
                   Lane, Torre, PosAzul.X, PosAzul.Y, PosVermelho.X, PosVermelho.Y);
        }
    }
}

void AImplementacaoAutomatizada::CriarParedes()
{
    UE_LOG(LogTemp, Log, TEXT("Criando sistema de paredes e colisões..."));

    // Paredes externas do mapa
    float LimiteX = MAPA_LARGURA / 2.0f;
    float LimiteY = MAPA_ALTURA / 2.0f;

    // Criar paredes das lanes baseadas nas funções lineares
    for (int32 i = 0; i < LanesEspecificacoes.Num(); i++)
    {
        const FLaneEspecificacao& Lane = LanesEspecificacoes[i];
        float MeiaLargura = Lane.Largura / 2.0f;

        // Paredes paralelas à lane
        // Implementação específica dependeria do sistema de colisão do projeto
    }
}

void AImplementacaoAutomatizada::CriarIlhaCentral()
{
    UE_LOG(LogTemp, Log, TEXT("Criando Ilha Central Auracron (hexágono)..."));

    FVector2D CentroIlha(0.0f, 0.0f);
    TArray<FVector> VerticesIlha = CalcularVerticesHexagono(CentroIlha, 600.0f, 0.0f);

    // Calcular área para verificação
    float AreaCalculada = (3.0f * SQRT_3 / 2.0f) * 600.0f * 600.0f;

    UE_LOG(LogTemp, Log, TEXT("Ilha Central criada: Raio 600 UU, Área %.2f UU²"), AreaCalculada);
}

// ========== SISTEMA DE VERIFICAÇÃO CONTÍNUA ==========

bool AImplementacaoAutomatizada::VerificarPrecisaoCompleta()
{
    bool PrecisaoOK = true;

    // Verificar especificações das lanes
    for (int32 i = 0; i < LanesEspecificacoes.Num(); i++)
    {
        const FLaneEspecificacao& Lane = LanesEspecificacoes[i];

        // Verificar se os pontos inicial e final estão na função da lane
        float YInicialCalculado = Lane.CoeficienteAngular * Lane.PontoInicial.X + Lane.CoeficienteLinear;
        float YFinalCalculado = Lane.CoeficienteAngular * Lane.PontoFinal.X + Lane.CoeficienteLinear;

        if (FMath::Abs(YInicialCalculado - Lane.PontoInicial.Y) > TOLERANCIA_COORDENADA ||
            FMath::Abs(YFinalCalculado - Lane.PontoFinal.Y) > TOLERANCIA_COORDENADA)
        {
            UE_LOG(LogTemp, Warning, TEXT("Imprecisão detectada na Lane %d"), i);
            PrecisaoOK = false;
        }
    }

    // Verificar covils
    for (int32 i = 0; i < CovilsEspecificacoes.Num(); i++)
    {
        const FCovilEspecificacao& Covil = CovilsEspecificacoes[i];

        if (Covil.TipoGeometria == "Hexagono")
        {
            TArray<FVector> Vertices = CalcularVerticesHexagono(
                FVector2D(Covil.Centro.X, Covil.Centro.Y),
                Covil.Parametro1
            );

            // Verificar se todos os vértices estão à distância correta do centro
            for (const FVector& Vertice : Vertices)
            {
                float Distancia = FVector2D::Distance(
                    FVector2D(Vertice.X, Vertice.Y),
                    FVector2D(Covil.Centro.X, Covil.Centro.Y)
                );

                if (FMath::Abs(Distancia - Covil.Parametro1) > TOLERANCIA_DISTANCIA)
                {
                    UE_LOG(LogTemp, Warning, TEXT("Imprecisão no hexágono do Covil %d"), i);
                    PrecisaoOK = false;
                }
            }
        }
    }

    return PrecisaoOK;
}

FString AImplementacaoAutomatizada::GerarRelatorioValidacao()
{
    FString Relatorio = TEXT("=== RELATÓRIO DE VALIDAÇÃO GEOMÉTRICA ===\n\n");

    // Verificar lanes
    Relatorio += TEXT("LANES:\n");
    for (int32 i = 0; i < LanesEspecificacoes.Num(); i++)
    {
        const FLaneEspecificacao& Lane = LanesEspecificacoes[i];
        Relatorio += FString::Printf(TEXT("  Lane %d: Y = %.6fX + %.2f (Largura: %.0f UU)\n"),
                                   i, Lane.CoeficienteAngular, Lane.CoeficienteLinear, Lane.Largura);
    }

    // Verificar rio
    Relatorio += TEXT("\nRIO PRISMAL:\n");
    Relatorio += TEXT("  Função: Y = 200 × sin(πX/4800)\n");
    Relatorio += TEXT("  Largura: 1200 ± 200 × |sin(πX/2400)| UU\n");

    // Verificar covils
    Relatorio += TEXT("\nCOVILS:\n");
    for (int32 i = 0; i < CovilsEspecificacoes.Num(); i++)
    {
        const FCovilEspecificacao& Covil = CovilsEspecificacoes[i];
        Relatorio += FString::Printf(TEXT("  Covil %d (%s): Centro(%.0f, %.0f), Param1=%.0f, Param2=%.0f\n"),
                                   i, *Covil.TipoGeometria, Covil.Centro.X, Covil.Centro.Y,
                                   Covil.Parametro1, Covil.Parametro2);
    }

    Relatorio += TEXT("\n=== FIM DO RELATÓRIO ===\n");

    return Relatorio;
}

void AImplementacaoAutomatizada::CorrigirDesviosPrecisao()
{
    UE_LOG(LogTemp, Log, TEXT("Executando correções de precisão..."));

    // Recalcular pontos das lanes baseados nas funções matemáticas
    for (int32 i = 0; i < LanesEspecificacoes.Num(); i++)
    {
        FLaneEspecificacao& Lane = LanesEspecificacoes[i];

        // Recalcular ponto final baseado no inicial e na função
        float YFinalCorreto = Lane.CoeficienteAngular * Lane.PontoFinal.X + Lane.CoeficienteLinear;
        Lane.PontoFinal.Y = YFinalCorreto;

        // Recalcular ponto inicial baseado no final e na função
        float YInicialCorreto = Lane.CoeficienteAngular * Lane.PontoInicial.X + Lane.CoeficienteLinear;
        Lane.PontoInicial.Y = YInicialCorreto;
    }

    UE_LOG(LogTemp, Log, TEXT("Correções de precisão concluídas."));
}

// ========== FUNÇÕES AUXILIARES MATEMÁTICAS ==========

float AImplementacaoAutomatizada::CalcularDistanciaEuclidiana(FVector2D P1, FVector2D P2)
{
    float DX = P2.X - P1.X;
    float DY = P2.Y - P1.Y;
    return FMath::Sqrt(DX * DX + DY * DY);
}

float AImplementacaoAutomatizada::CalcularAnguloEntrePontos(FVector2D P1, FVector2D P2)
{
    float DX = P2.X - P1.X;
    float DY = P2.Y - P1.Y;
    return FMath::Atan2(DY, DX);
}

FVector2D AImplementacaoAutomatizada::CalcularPontoNaLinha(FVector2D Inicio, FVector2D Fim, float Parametro)
{
    return FVector2D(
        Inicio.X + Parametro * (Fim.X - Inicio.X),
        Inicio.Y + Parametro * (Fim.Y - Inicio.Y)
    );
}

bool AImplementacaoAutomatizada::PontoEstaEmPoligono(FVector2D Ponto, const TArray<FVector2D>& Vertices)
{
    int32 NumVertices = Vertices.Num();
    bool Dentro = false;

    for (int32 i = 0, j = NumVertices - 1; i < NumVertices; j = i++)
    {
        if (((Vertices[i].Y > Ponto.Y) != (Vertices[j].Y > Ponto.Y)) &&
            (Ponto.X < (Vertices[j].X - Vertices[i].X) * (Ponto.Y - Vertices[i].Y) / (Vertices[j].Y - Vertices[i].Y) + Vertices[i].X))
        {
            Dentro = !Dentro;
        }
    }

    return Dentro;
}