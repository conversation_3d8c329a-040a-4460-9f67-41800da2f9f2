// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "APCGChaosIntegrator.h"

#ifdef AURA_APCGChaosIntegrator_generated_h
#error "APCGChaosIntegrator.generated.h already included, missing '#pragma once' in APCGChaosIntegrator.h"
#endif
#define AURA_APCGChaosIntegrator_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class APCGCacheManager;
class APCGLumenIntegrator;
class APCGNaniteOptimizer;
class APCGStreamingManager;
class APCGWorldPartitionManager;
class UPCGPerformanceProfiler;
class UStaticMesh;
enum class EPCGPhysicsBodyType : uint8;
enum class EPCGPhysicsConstraintType : uint8;
enum class EPCGPhysicsQuality : uint8;
struct FPCGPhysicsBodyData;
struct FPCGPhysicsConstraintData;
struct FPCGPhysicsEventData;
struct FPCGPhysicsPerformanceStats;

// ********** Begin ScriptStruct FPCGPhysicsConfig *************************************************
#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_111_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGPhysicsConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGPhysicsConfig;
// ********** End ScriptStruct FPCGPhysicsConfig ***************************************************

// ********** Begin ScriptStruct FPCGPhysicsBodyData ***********************************************
#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_197_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGPhysicsBodyData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGPhysicsBodyData;
// ********** End ScriptStruct FPCGPhysicsBodyData *************************************************

// ********** Begin ScriptStruct FPCGPhysicsConstraintData *****************************************
#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_282_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGPhysicsConstraintData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGPhysicsConstraintData;
// ********** End ScriptStruct FPCGPhysicsConstraintData *******************************************

// ********** Begin ScriptStruct FPCGPhysicsPerformanceStats ***************************************
#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_361_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGPhysicsPerformanceStats_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGPhysicsPerformanceStats;
// ********** End ScriptStruct FPCGPhysicsPerformanceStats *****************************************

// ********** Begin ScriptStruct FPCGPhysicsEventData **********************************************
#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_429_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGPhysicsEventData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGPhysicsEventData;
// ********** End ScriptStruct FPCGPhysicsEventData ************************************************

// ********** Begin Delegate FOnPhysicsBodyCreated *************************************************
#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_459_DELEGATE \
AURA_API void FOnPhysicsBodyCreated_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsBodyCreated, FPCGPhysicsBodyData const& BodyData);


// ********** End Delegate FOnPhysicsBodyCreated ***************************************************

// ********** Begin Delegate FOnPhysicsBodyDestroyed ***********************************************
#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_460_DELEGATE \
AURA_API void FOnPhysicsBodyDestroyed_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsBodyDestroyed, const FString& BodyID);


// ********** End Delegate FOnPhysicsBodyDestroyed *************************************************

// ********** Begin Delegate FOnPhysicsConstraintCreated *******************************************
#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_461_DELEGATE \
AURA_API void FOnPhysicsConstraintCreated_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsConstraintCreated, FPCGPhysicsConstraintData const& ConstraintData);


// ********** End Delegate FOnPhysicsConstraintCreated *********************************************

// ********** Begin Delegate FOnPhysicsConstraintBroken ********************************************
#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_462_DELEGATE \
AURA_API void FOnPhysicsConstraintBroken_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsConstraintBroken, const FString& ConstraintID);


// ********** End Delegate FOnPhysicsConstraintBroken **********************************************

// ********** Begin Delegate FOnPhysicsEvent *******************************************************
#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_463_DELEGATE \
AURA_API void FOnPhysicsEvent_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsEvent, FPCGPhysicsEventData const& EventData);


// ********** End Delegate FOnPhysicsEvent *********************************************************

// ********** Begin Delegate FOnPhysicsPerformanceUpdated ******************************************
#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_464_DELEGATE \
AURA_API void FOnPhysicsPerformanceUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsPerformanceUpdated, FPCGPhysicsPerformanceStats const& Stats);


// ********** End Delegate FOnPhysicsPerformanceUpdated ********************************************

// ********** Begin Class APCGChaosIntegrator ******************************************************
#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_482_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetGravity); \
	DECLARE_FUNCTION(execSetGravity); \
	DECLARE_FUNCTION(execDestroyPhysicsIsland); \
	DECLARE_FUNCTION(execCreatePhysicsIsland); \
	DECLARE_FUNCTION(execSetPhysicsQuality); \
	DECLARE_FUNCTION(execEnableAsyncPhysics); \
	DECLARE_FUNCTION(execIntegrateWithProfiler); \
	DECLARE_FUNCTION(execIntegrateWithCache); \
	DECLARE_FUNCTION(execIntegrateWithStreaming); \
	DECLARE_FUNCTION(execIntegrateWithLumen); \
	DECLARE_FUNCTION(execIntegrateWithNanite); \
	DECLARE_FUNCTION(execIntegrateWithWorldPartition); \
	DECLARE_FUNCTION(execGeneratePhysicsFromPCGData); \
	DECLARE_FUNCTION(execGeneratePhysicsCluster); \
	DECLARE_FUNCTION(execGeneratePhysicsChain); \
	DECLARE_FUNCTION(execGeneratePhysicsBodyFromGeometry); \
	DECLARE_FUNCTION(execGeneratePhysicsBodiesFromMesh); \
	DECLARE_FUNCTION(execUpdatePhysicsLOD); \
	DECLARE_FUNCTION(execSetCullingDistance); \
	DECLARE_FUNCTION(execEnablePhysicsCulling); \
	DECLARE_FUNCTION(execSetPhysicsLOD); \
	DECLARE_FUNCTION(execOptimizePhysicsPerformance); \
	DECLARE_FUNCTION(execGetPerformanceStatistics); \
	DECLARE_FUNCTION(execAreOverlapping); \
	DECLARE_FUNCTION(execGetOverlappingBodies); \
	DECLARE_FUNCTION(execBoxTrace); \
	DECLARE_FUNCTION(execSphereTrace); \
	DECLARE_FUNCTION(execLineTrace); \
	DECLARE_FUNCTION(execGetConstraintsForBody); \
	DECLARE_FUNCTION(execGetAllPhysicsConstraints); \
	DECLARE_FUNCTION(execGetPhysicsConstraintData); \
	DECLARE_FUNCTION(execUpdatePhysicsConstraint); \
	DECLARE_FUNCTION(execDestroyPhysicsConstraint); \
	DECLARE_FUNCTION(execCreatePhysicsConstraint); \
	DECLARE_FUNCTION(execPutBodyToSleep); \
	DECLARE_FUNCTION(execWakeBody); \
	DECLARE_FUNCTION(execIsBodyAwake); \
	DECLARE_FUNCTION(execSetBodyAngularVelocity); \
	DECLARE_FUNCTION(execSetBodyLinearVelocity); \
	DECLARE_FUNCTION(execGetBodyAngularVelocity); \
	DECLARE_FUNCTION(execGetBodyLinearVelocity); \
	DECLARE_FUNCTION(execAddAngularImpulseToBody); \
	DECLARE_FUNCTION(execAddImpulseAtLocation); \
	DECLARE_FUNCTION(execAddImpulseToBody); \
	DECLARE_FUNCTION(execAddTorqueToBody); \
	DECLARE_FUNCTION(execAddForceAtLocation); \
	DECLARE_FUNCTION(execAddForceToBody); \
	DECLARE_FUNCTION(execSetBodyKinematic); \
	DECLARE_FUNCTION(execSetBodyAngularDamping); \
	DECLARE_FUNCTION(execSetBodyLinearDamping); \
	DECLARE_FUNCTION(execSetBodyRestitution); \
	DECLARE_FUNCTION(execSetBodyFriction); \
	DECLARE_FUNCTION(execSetBodyDensity); \
	DECLARE_FUNCTION(execSetBodyMass); \
	DECLARE_FUNCTION(execGetPhysicsBodiesByType); \
	DECLARE_FUNCTION(execGetAllPhysicsBodies); \
	DECLARE_FUNCTION(execGetPhysicsBodyData); \
	DECLARE_FUNCTION(execUpdatePhysicsBody); \
	DECLARE_FUNCTION(execDestroyPhysicsBody); \
	DECLARE_FUNCTION(execCreatePhysicsBody); \
	DECLARE_FUNCTION(execResetSimulation); \
	DECLARE_FUNCTION(execPauseSimulation); \
	DECLARE_FUNCTION(execStopSimulation); \
	DECLARE_FUNCTION(execStartSimulation); \
	DECLARE_FUNCTION(execEnablePhysics); \
	DECLARE_FUNCTION(execShutdownPhysics); \
	DECLARE_FUNCTION(execInitializePhysics);


AURA_API UClass* Z_Construct_UClass_APCGChaosIntegrator_NoRegister();

#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_482_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAPCGChaosIntegrator(); \
	friend struct Z_Construct_UClass_APCGChaosIntegrator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_APCGChaosIntegrator_NoRegister(); \
public: \
	DECLARE_CLASS2(APCGChaosIntegrator, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_APCGChaosIntegrator_NoRegister) \
	DECLARE_SERIALIZER(APCGChaosIntegrator)


#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_482_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	APCGChaosIntegrator(APCGChaosIntegrator&&) = delete; \
	APCGChaosIntegrator(const APCGChaosIntegrator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, APCGChaosIntegrator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(APCGChaosIntegrator); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(APCGChaosIntegrator) \
	NO_API virtual ~APCGChaosIntegrator();


#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_479_PROLOG
#define FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_482_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_482_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_482_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h_482_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class APCGChaosIntegrator;

// ********** End Class APCGChaosIntegrator ********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_APCGChaosIntegrator_h

// ********** Begin Enum EPCGPhysicsSimulationMode *************************************************
#define FOREACH_ENUM_EPCGPHYSICSSIMULATIONMODE(op) \
	op(EPCGPhysicsSimulationMode::Static) \
	op(EPCGPhysicsSimulationMode::Kinematic) \
	op(EPCGPhysicsSimulationMode::Dynamic) \
	op(EPCGPhysicsSimulationMode::Procedural) \
	op(EPCGPhysicsSimulationMode::Hybrid) 

enum class EPCGPhysicsSimulationMode : uint8;
template<> struct TIsUEnumClass<EPCGPhysicsSimulationMode> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGPhysicsSimulationMode>();
// ********** End Enum EPCGPhysicsSimulationMode ***************************************************

// ********** Begin Enum EPCGPhysicsBodyType *******************************************************
#define FOREACH_ENUM_EPCGPHYSICSBODYTYPE(op) \
	op(EPCGPhysicsBodyType::Box) \
	op(EPCGPhysicsBodyType::Sphere) \
	op(EPCGPhysicsBodyType::Capsule) \
	op(EPCGPhysicsBodyType::Convex) \
	op(EPCGPhysicsBodyType::TriangleMesh) \
	op(EPCGPhysicsBodyType::Heightfield) \
	op(EPCGPhysicsBodyType::Custom) 

enum class EPCGPhysicsBodyType : uint8;
template<> struct TIsUEnumClass<EPCGPhysicsBodyType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGPhysicsBodyType>();
// ********** End Enum EPCGPhysicsBodyType *********************************************************

// ********** Begin Enum EPCGPhysicsConstraintType *************************************************
#define FOREACH_ENUM_EPCGPHYSICSCONSTRAINTTYPE(op) \
	op(EPCGPhysicsConstraintType::Fixed) \
	op(EPCGPhysicsConstraintType::Hinge) \
	op(EPCGPhysicsConstraintType::Prismatic) \
	op(EPCGPhysicsConstraintType::Spherical) \
	op(EPCGPhysicsConstraintType::Universal) \
	op(EPCGPhysicsConstraintType::Distance) \
	op(EPCGPhysicsConstraintType::Spring) 

enum class EPCGPhysicsConstraintType : uint8;
template<> struct TIsUEnumClass<EPCGPhysicsConstraintType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGPhysicsConstraintType>();
// ********** End Enum EPCGPhysicsConstraintType ***************************************************

// ********** Begin Enum EPCGPhysicsQuality ********************************************************
#define FOREACH_ENUM_EPCGPHYSICSQUALITY(op) \
	op(EPCGPhysicsQuality::Low) \
	op(EPCGPhysicsQuality::Medium) \
	op(EPCGPhysicsQuality::High) \
	op(EPCGPhysicsQuality::Ultra) \
	op(EPCGPhysicsQuality::Adaptive) 

enum class EPCGPhysicsQuality : uint8;
template<> struct TIsUEnumClass<EPCGPhysicsQuality> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGPhysicsQuality>();
// ********** End Enum EPCGPhysicsQuality **********************************************************

// ********** Begin Enum EPCGPhysicsOptimization ***************************************************
#define FOREACH_ENUM_EPCGPHYSICSOPTIMIZATION(op) \
	op(EPCGPhysicsOptimization::None) \
	op(EPCGPhysicsOptimization::LOD) \
	op(EPCGPhysicsOptimization::Culling) \
	op(EPCGPhysicsOptimization::Clustering) \
	op(EPCGPhysicsOptimization::Instancing) \
	op(EPCGPhysicsOptimization::Streaming) \
	op(EPCGPhysicsOptimization::Hybrid) 

enum class EPCGPhysicsOptimization : uint8;
template<> struct TIsUEnumClass<EPCGPhysicsOptimization> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGPhysicsOptimization>();
// ********** End Enum EPCGPhysicsOptimization *****************************************************

// ********** Begin Enum EPCGPhysicsEventType ******************************************************
#define FOREACH_ENUM_EPCGPHYSICSEVENTTYPE(op) \
	op(EPCGPhysicsEventType::Collision) \
	op(EPCGPhysicsEventType::Overlap) \
	op(EPCGPhysicsEventType::Break) \
	op(EPCGPhysicsEventType::Sleep) \
	op(EPCGPhysicsEventType::Wake) \
	op(EPCGPhysicsEventType::Constraint) \
	op(EPCGPhysicsEventType::Destruction) 

enum class EPCGPhysicsEventType : uint8;
template<> struct TIsUEnumClass<EPCGPhysicsEventType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGPhysicsEventType>();
// ********** End Enum EPCGPhysicsEventType ********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
