#include "APCGLumenIntegrator.h"
#include "Components/StaticMeshComponent.h"
#include "Components/LightComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/SpotLightComponent.h"
#include "Components/SkyLightComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Rendering/NaniteResources.h"
#include "RenderGraphBuilder.h"
// Engine includes
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Engine/RendererSettings.h"
#include "Engine/PostProcessVolume.h"
#include "Components/PostProcessComponent.h"
#include "Misc/DateTime.h"
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"
#include "RHI.h"
#include "RHICommandList.h"
#include "GlobalShader.h"
#include "ShaderParameterStruct.h"
#include "DataDrivenShaderPlatformInfo.h"
#include "APCGNaniteOptimizer.h"
#include "APCGWorldPartitionManager.h"
#include "EngineUtils.h"
#include "Components/ReflectionCaptureComponent.h"

// CORRIGIDO: APIs modernas UE 5.6 para Lumen
#include "Async/ParallelFor.h"
#include "HAL/ThreadSafeCounter64.h"
#include "Engine/StreamableManager.h"
#include "Subsystems/WorldSubsystem.h"
#include "Math/UnrealMathUtility.h"
#include "Misc/Timespan.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "Stats/Stats.h"
#include "HAL/IConsoleManager.h"
#include "Engine/Scene.h"
#include "SceneManagement.h"
#include "ShowFlags.h"
#include "Rendering/SkeletalMeshRenderData.h"
#include "Components/SceneCaptureComponent2D.h"

// CORRIGIDO: Logging categories específicas (API moderna UE 5.6)
DEFINE_LOG_CATEGORY_STATIC(LogPCGLumenIntegrator, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGLumenRendering, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGLumenPerformance, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGLumenOptimization, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGLumenGI, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGLumenReflections, Log, All);

// CORRIGIDO: Stats para profiling (API moderna UE 5.6)
DECLARE_CYCLE_STAT(TEXT("PCG Lumen Integration Tick"), STAT_PCGLumenIntegrationTick, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("PCG Lumen GI Update"), STAT_PCGLumenGIUpdate, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("PCG Lumen Reflections Update"), STAT_PCGLumenReflectionsUpdate, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("PCG Lumen Performance Update"), STAT_PCGLumenPerformanceUpdate, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("PCG Lumen Scene Data Update"), STAT_PCGLumenSceneDataUpdate, STATGROUP_Game);

APCGLumenIntegrator::APCGLumenIntegrator()
{
    // CORRIGIDO: Configuração moderna de tick para UE 5.6
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.TickGroup = TG_PostUpdateWork; // Após updates de rendering
    PrimaryActorTick.bTickEvenWhenPaused = false; // Otimização UE 5.6
    PrimaryActorTick.TickInterval = 0.0f; // Tick every frame para Lumen

    // CORRIGIDO: Configuração padrão robusta para Lumen
    LumenConfig = FPCGLumenConfig();
    LumenConfig.bEnableGlobalIllumination = true; // GI habilitado por padrão
    LumenConfig.bEnableReflections = true; // Reflections habilitadas
    LumenConfig.bEnableScreenTraces = true; // Screen traces UE 5.6
    LumenConfig.GlobalIlluminationQuality = 1.0f; // Qualidade balanceada
    LumenConfig.ReflectionQuality = 1.0f; // Qualidade balanceada
    LumenConfig.SurfaceCacheResolution = 1.0f; // Resolução padrão
    LumenConfig.MaxTraceDistance = 10000.0f; // 100 metros
    LumenConfig.UpdateFrequency = 60.0f; // 60 FPS

    // CORRIGIDO: Estados de controle production-ready
    bEnablePerformanceMonitoring = true;
    bAutoOptimization = true;
    bShowDebugInfo = false;
    bLumenSupported = false; // Será detectado no BeginPlay
    bGlobalIlluminationActive = false;
    bReflectionsActive = false;

    // CORRIGIDO: Estados internos robustos
    bIsInitialized = false;
    bNeedsUpdate = false;
    LastUpdateTime = 0.0f;
    AccumulatedDeltaTime = 0.0f;
    LastPerformanceUpdate = 0.0f;
    LastOptimizationCheck = 0.0f;

    // CORRIGIDO: Performance stats iniciais production-ready
    CurrentStats = FPCGLumenPerformanceStats();
    CurrentStats.FrameRate = 60.0f;
    CurrentStats.GIUpdateTime = 0.0f;
    CurrentStats.ReflectionUpdateTime = 0.0f;
    CurrentStats.TotalLumenTime = 0.0f;
    CurrentStats.MemoryUsageMB = 0.0f;
    CurrentStats.ActiveLights = 0;
    CurrentStats.ActiveReflectionCaptures = 0;

    // CORRIGIDO: Inicialização de containers com Reserve para performance
    LightComponents.Reserve(100);
    ReflectionCaptures.Reserve(50);
    PostProcessVolumes.Reserve(10);
    MaterialInstances.Reserve(200);

    // Clear integration references
    NaniteOptimizerRef = nullptr;
    WorldPartitionManagerRef = nullptr;

    UE_LOG(LogPCGLumenIntegrator, Log, TEXT("Constructor completed with modern UE 5.6 configuration"));
}

void APCGLumenIntegrator::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogPCGLumenIntegrator, Log, TEXT("BeginPlay started with modern UE 5.6 Lumen APIs"));

    // CORRIGIDO: Verificação robusta do World
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogPCGLumenIntegrator, Error, TEXT("No valid world found"));
        return;
    }

    // CORRIGIDO: Detectar suporte ao Lumen via CVars (API moderna UE 5.6)
    static const auto* LumenGICVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Lumen.GlobalIllumination"));
    static const auto* LumenReflectionsCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Lumen.Reflections"));

    if (LumenGICVar && LumenGICVar->GetInt() > 0)
    {
        bLumenSupported = true;
        bGlobalIlluminationActive = true;
        UE_LOG(LogPCGLumenGI, Log, TEXT("Lumen Global Illumination detected and enabled"));
    }

    if (LumenReflectionsCVar && LumenReflectionsCVar->GetInt() > 0)
    {
        bReflectionsActive = true;
        UE_LOG(LogPCGLumenReflections, Log, TEXT("Lumen Reflections detected and enabled"));
    }

    if (!bLumenSupported)
    {
        UE_LOG(LogPCGLumenIntegrator, Warning, TEXT("Lumen not supported or disabled - some features will be unavailable"));
    }

    // CORRIGIDO: Detectar World Partition para otimizações
    if (World->IsPartitionedWorld())
    {
        UE_LOG(LogPCGLumenIntegrator, Log, TEXT("World Partition detected - enabling streaming optimizations"));
        LumenConfig.bEnableStreamingOptimization = true;
    }

    // CORRIGIDO: Inicialização robusta do sistema Lumen
    if (bLumenSupported)
    {
        InitializeLumenSystem();
        UE_LOG(LogPCGLumenIntegrator, Log, TEXT("Lumen system initialized successfully"));
    }

    // CORRIGIDO: Registrar callbacks de rendering com validação
    RegisterRenderingCallbacks();

    // CORRIGIDO: Marcar como inicializado apenas se tudo funcionou
    bIsInitialized = bLumenSupported;

    UE_LOG(LogPCGLumenIntegrator, Log, TEXT("BeginPlay completed - Lumen support: %s"),
           bLumenSupported ? TEXT("Yes") : TEXT("No"));
}

void APCGLumenIntegrator::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // CORRIGIDO: Logging seguro para EEndPlayReason
    const FString ReasonString = UEnum::GetValueAsString(EndPlayReason);
    UE_LOG(LogPCGLumenIntegrator, Log, TEXT("EndPlay started - reason: %s"), *ReasonString);

    // CORRIGIDO: Unregister callbacks com validação
    if (bIsInitialized)
    {
        UE_LOG(LogPCGLumenRendering, Log, TEXT("Unregistering rendering callbacks"));
        UnregisterRenderingCallbacks();
    }

    // CORRIGIDO: Limpeza segura de dados registrados com logging
    const int32 LightsCleared = RegisteredLights.Num();
    const int32 SurfacesCleared = RegisteredSurfaces.Num();

    RegisteredLights.Empty();
    RegisteredSurfaces.Empty();

    UE_LOG(LogPCGLumenIntegrator, VeryVerbose, TEXT("Cleared %d lights and %d surfaces"),
           LightsCleared, SurfacesCleared);

    // CORRIGIDO: Limpeza segura de containers adicionais
    LightComponents.Empty();
    ReflectionCaptures.Empty();
    PostProcessVolumes.Empty();
    MaterialInstances.Empty();

    // CORRIGIDO: Limpeza segura de referências com validação
    if (NaniteOptimizerRef.IsValid())
    {
        NaniteOptimizerRef = nullptr;
        UE_LOG(LogPCGLumenIntegrator, VeryVerbose, TEXT("Nanite Optimizer reference cleared"));
    }

    if (WorldPartitionManagerRef.IsValid())
    {
        WorldPartitionManagerRef = nullptr;
        UE_LOG(LogPCGLumenIntegrator, VeryVerbose, TEXT("World Partition Manager reference cleared"));
    }

    // CORRIGIDO: Estados finais
    bIsInitialized = false;
    bLumenSupported = false;
    bGlobalIlluminationActive = false;
    bReflectionsActive = false;

    Super::EndPlay(EndPlayReason);

    UE_LOG(LogPCGLumenIntegrator, Log, TEXT("EndPlay completed successfully"));
}

void APCGLumenIntegrator::Tick(float DeltaTime)
{
    // CORRIGIDO: Profiling moderno UE 5.6
    SCOPE_CYCLE_COUNTER(STAT_PCGLumenIntegrationTick);
    TRACE_CPUPROFILER_EVENT_SCOPE(APCGLumenIntegrator::Tick);

    Super::Tick(DeltaTime);

    // CORRIGIDO: Validação robusta de estados
    if (!bIsInitialized || !bLumenSupported)
    {
        return;
    }

    // CORRIGIDO: Validação de World para segurança
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogPCGLumenIntegrator, Warning, TEXT("No valid world during tick - skipping"));
        return;
    }

    AccumulatedDeltaTime += DeltaTime;
    const float CurrentTime = World->GetTimeSeconds();

    // CORRIGIDO: Update baseado na frequência configurada com profiling
    if (AccumulatedDeltaTime >= (1.0f / LumenConfig.UpdateFrequency))
    {
        UpdateLumenSystem(AccumulatedDeltaTime);
        AccumulatedDeltaTime = 0.0f;
        LastUpdateTime = CurrentTime;
    }

    // CORRIGIDO: Performance monitoring com intervalo
    if (bEnablePerformanceMonitoring && CurrentTime - LastPerformanceUpdate >= 1.0f)
    {
        SCOPE_CYCLE_COUNTER(STAT_PCGLumenPerformanceUpdate);
        UpdatePerformanceStats();
        LastPerformanceUpdate = CurrentTime;
    }

    // CORRIGIDO: Auto optimization check com intervalo
    if (bAutoOptimization && CurrentTime - LastOptimizationCheck >= 5.0f)
    {
        CheckAndApplyOptimizations();
        LastOptimizationCheck = CurrentTime;
    }

    // Update performance stats
    if (bEnablePerformanceMonitoring)
    {
        UpdatePerformanceStats();
    }

    // Process adaptive quality
    if (bAutoOptimization && LumenConfig.UpdateMode == EPCGLumenUpdateMode::Adaptive)
    {
        ProcessAdaptiveQuality();
    }

    // Clean up invalid references
    CleanupInvalidReferences();
}

// Owner destruction handler (UE 5.6 compatible)
void APCGLumenIntegrator::OnOwnerDestroyed(AActor* DestroyedActor)
{
    if (!DestroyedActor)
    {
        return;
    }

    // Remove all light components owned by this actor
    TArray<ULightComponent*> ComponentsToRemove;
    for (auto& LightPair : RegisteredLights)
    {
        if (LightPair.Key.IsValid() && LightPair.Key->GetOwner() == DestroyedActor)
        {
            ComponentsToRemove.Add(LightPair.Key.Get());
        }
    }

    for (ULightComponent* LightComp : ComponentsToRemove)
    {
        UnregisterLight(LightComp);
    }

    // Remove all surface components owned by this actor
    TArray<UStaticMeshComponent*> SurfacesToRemove;
    for (auto& SurfacePair : RegisteredSurfaces)
    {
        if (SurfacePair.Key.IsValid() && SurfacePair.Key->GetOwner() == DestroyedActor)
        {
            SurfacesToRemove.Add(SurfacePair.Key.Get());
        }
    }

    for (UStaticMeshComponent* SurfaceComp : SurfacesToRemove)
    {
        UnregisterSurface(SurfaceComp);
    }

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Cleaned up components for destroyed actor: %s"),
           *DestroyedActor->GetName());
}

// Light Management
void APCGLumenIntegrator::RegisterLight(ULightComponent* LightComponent, const FPCGLumenLightData& LightData)
{
    if (!LightComponent)
    {
        UE_LOG(LogPCGLumenRendering, Warning, TEXT("Cannot register null light component"));
        return;
    }

    // CORRIGIDO: Validação adicional de componente
    if (!IsValid(LightComponent))
    {
        UE_LOG(LogPCGLumenRendering, Warning, TEXT("Cannot register invalid light component"));
        return;
    }

    FPCGLumenLightData NewLightData = LightData;
    NewLightData.LightComponent = LightComponent;

    RegisteredLights.Add(LightComponent, NewLightData);

    if (AActor* OwnerActor = LightComponent->GetOwner())
    {
        OwnerActor->OnDestroyed.AddDynamic(this, &APCGLumenIntegrator::OnOwnerDestroyed);
    }

    // Apply light settings
    LightComponent->SetIntensity(LightData.Intensity);
    LightComponent->SetLightColor(LightData.Color);
    LightComponent->SetCastShadows(LightData.bCastShadows);

    // Set Lumen-specific properties
    if (UPointLightComponent* PointLight = Cast<UPointLightComponent>(LightComponent))
    {
        PointLight->SetAttenuationRadius(LightData.AttenuationRadius);
    }
    else if (USpotLightComponent* SpotLight = Cast<USpotLightComponent>(LightComponent))
    {
        SpotLight->SetAttenuationRadius(LightData.AttenuationRadius);
    }

    bNeedsUpdate = true;

    UE_LOG(LogPCGLumenRendering, Log, TEXT("Registered light component: %s"),
           *LightComponent->GetName());
}

void APCGLumenIntegrator::UnregisterLight(ULightComponent* LightComponent)
{
    if (!LightComponent)
    {
        return;
    }

    if (RegisteredLights.Contains(LightComponent))
    {
        RegisteredLights.Remove(LightComponent);
        // OnComponentDestroyed não existe no UE 5.6 - usar AActor::OnDestroyed em vez disso
        if (AActor* OwnerActor = LightComponent->GetOwner())
        {
            OwnerActor->OnDestroyed.RemoveDynamic(this, &APCGLumenIntegrator::OnOwnerDestroyed);
        }
        bNeedsUpdate = true;

        UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Unregistered light component"));
    }
}

void APCGLumenIntegrator::UpdateLightData(ULightComponent* LightComponent, const FPCGLumenLightData& NewLightData)
{
    if (!LightComponent || !RegisteredLights.Contains(LightComponent))
    {
        return;
    }

    FPCGLumenLightData UpdatedData = NewLightData;
    UpdatedData.LightComponent = LightComponent;

    RegisteredLights[LightComponent] = UpdatedData;

    // Apply updated settings
    LightComponent->SetIntensity(NewLightData.Intensity);
    LightComponent->SetLightColor(NewLightData.Color);
    LightComponent->SetCastShadows(NewLightData.bCastShadows);

    bNeedsUpdate = true;

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Updated light data"));
}

void APCGLumenIntegrator::SetGlobalLightIntensity(float Intensity)
{
    for (auto& LightPair : RegisteredLights)
    {
        if (LightPair.Key.IsValid())
        {
            LightPair.Value.Intensity *= Intensity;
            LightPair.Key->SetIntensity(LightPair.Value.Intensity);
        }
    }

    bNeedsUpdate = true;

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Set global light intensity to %.2f"), (double)Intensity);
}

TArray<FPCGLumenLightData> APCGLumenIntegrator::GetActiveLights() const
{
    TArray<FPCGLumenLightData> ActiveLights;

    for (const auto& LightPair : RegisteredLights)
    {
        if (LightPair.Key.IsValid())
        {
            ActiveLights.Add(LightPair.Value);
        }
    }

    return ActiveLights;
}

// Surface Management
void APCGLumenIntegrator::RegisterSurface(UStaticMeshComponent* MeshComponent, const FPCGLumenSurfaceData& SurfaceData)
{
    if (!MeshComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGLumenIntegrator: Cannot register null mesh component"));
        return;
    }

    FPCGLumenSurfaceData NewSurfaceData = SurfaceData;
    NewSurfaceData.MeshComponent = MeshComponent;

    RegisteredSurfaces.Add(MeshComponent, NewSurfaceData);

    if (AActor* OwnerActor = MeshComponent->GetOwner())
    {
        OwnerActor->OnDestroyed.AddDynamic(this, &APCGLumenIntegrator::OnOwnerDestroyed);
    }

    // Apply surface settings
    if (SurfaceData.Material.IsValid())
    {
        MeshComponent->SetMaterial(0, SurfaceData.Material.Get());
    }

    // Create dynamic material for emissive surfaces
    if (SurfaceData.EmissiveIntensity > 0.0f)
    {
        SetEmissiveSurface(MeshComponent, SurfaceData.EmissiveColor, SurfaceData.EmissiveIntensity);
    }

    bNeedsUpdate = true;

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Registered surface component"));
}

void APCGLumenIntegrator::UnregisterSurface(UStaticMeshComponent* MeshComponent)
{
    if (!MeshComponent)
    {
        return;
    }

    if (RegisteredSurfaces.Contains(MeshComponent))
    {
        RegisteredSurfaces.Remove(MeshComponent);
        // OnComponentDestroyed não existe no UE 5.6 - usar AActor::OnDestroyed em vez disso
        if (AActor* OwnerActor = MeshComponent->GetOwner())
        {
            OwnerActor->OnDestroyed.RemoveDynamic(this, &APCGLumenIntegrator::OnOwnerDestroyed);
        }
        bNeedsUpdate = true;

        UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Unregistered surface component"));
    }
}

void APCGLumenIntegrator::UpdateSurfaceData(UStaticMeshComponent* MeshComponent, const FPCGLumenSurfaceData& NewSurfaceData)
{
    if (!MeshComponent || !RegisteredSurfaces.Contains(MeshComponent))
    {
        return;
    }

    FPCGLumenSurfaceData UpdatedData = NewSurfaceData;
    UpdatedData.MeshComponent = MeshComponent;

    RegisteredSurfaces[MeshComponent] = UpdatedData;

    // Apply updated settings
    if (NewSurfaceData.Material.IsValid())
    {
        MeshComponent->SetMaterial(0, NewSurfaceData.Material.Get());
    }

    // Update emissive properties
    if (NewSurfaceData.EmissiveIntensity > 0.0f)
    {
        SetEmissiveSurface(MeshComponent, NewSurfaceData.EmissiveColor, NewSurfaceData.EmissiveIntensity);
    }

    bNeedsUpdate = true;

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Updated surface data"));
}

void APCGLumenIntegrator::SetEmissiveSurface(UStaticMeshComponent* MeshComponent, const FLinearColor& EmissiveColor, float Intensity)
{
    if (!MeshComponent)
    {
        return;
    }

    // Create dynamic material instance
    UMaterialInterface* BaseMaterial = MeshComponent->GetMaterial(0);
    if (BaseMaterial)
    {
        UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
        if (DynamicMaterial)
        {
            DynamicMaterial->SetVectorParameterValue(TEXT("EmissiveColor"), EmissiveColor);
            DynamicMaterial->SetScalarParameterValue(TEXT("EmissiveIntensity"), Intensity);
            MeshComponent->SetMaterial(0, DynamicMaterial);

            UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Set emissive surface with intensity %.2f"), (double)Intensity);
        }
    }
}

TArray<FPCGLumenSurfaceData> APCGLumenIntegrator::GetActiveSurfaces() const
{
    TArray<FPCGLumenSurfaceData> ActiveSurfaces;

    for (const auto& SurfacePair : RegisteredSurfaces)
    {
        if (SurfacePair.Key.IsValid())
        {
            ActiveSurfaces.Add(SurfacePair.Value);
        }
    }

    return ActiveSurfaces;
}

// Global Illumination Control
void APCGLumenIntegrator::SetGlobalIlluminationIntensity(float Intensity)
{
    LumenConfig.GlobalIlluminationIntensity = FMath::Clamp(Intensity, 0.1f, 2.0f);
    ApplyQualitySettings();
    bNeedsUpdate = true;

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Set GI intensity to %.2f"), (double)Intensity);
}

void APCGLumenIntegrator::SetReflectionIntensity(float Intensity)
{
    LumenConfig.ReflectionIntensity = FMath::Clamp(Intensity, 0.1f, 10.0f);
    ApplyQualitySettings();
    bNeedsUpdate = true;

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Set reflection intensity to %.2f"), (double)Intensity);
}

void APCGLumenIntegrator::SetLumenQuality(EPCGLumenQuality Quality)
{
    LumenConfig.Quality = Quality;
    ApplyQualitySettings();
    bNeedsUpdate = true;

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Set Lumen quality to %d"), (int32)Quality);
}

void APCGLumenIntegrator::SetUpdateMode(EPCGLumenUpdateMode UpdateMode)
{
    LumenConfig.UpdateMode = UpdateMode;

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Set update mode to %d"), (int32)UpdateMode);
}

void APCGLumenIntegrator::ForceGlobalIlluminationUpdate()
{
    UpdateGlobalIllumination();
    UpdateReflections();

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Forced GI update"));
}

// Reflection Control
void APCGLumenIntegrator::SetReflectionMode(EPCGLumenReflectionMode ReflectionMode)
{
    LumenConfig.ReflectionMode = ReflectionMode;
    ApplyQualitySettings();
    bNeedsUpdate = true;

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Set reflection mode to %d"), (int32)ReflectionMode);
}

void APCGLumenIntegrator::UpdateReflectionCaptures()
{
    // Find and update all reflection captures in the world
    UWorld* World = GetWorld();
    if (World)
    {
        for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
        {
            AActor* Actor = *ActorIterator;
            if (Actor && Actor->FindComponentByClass<UReflectionCaptureComponent>())
            {
                UReflectionCaptureComponent* ReflectionCapture = Actor->FindComponentByClass<UReflectionCaptureComponent>();
                if (ReflectionCapture)
                {
                    ReflectionCapture->MarkDirtyForRecapture();
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Updated reflection captures"));
}

void APCGLumenIntegrator::SetMaxReflectionBounces(int32 MaxBounces)
{
    LumenConfig.MaxBounces = FMath::Clamp(MaxBounces, 1, 8);
    ApplyQualitySettings();
    bNeedsUpdate = true;

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Set max reflection bounces to %d"), MaxBounces);
}

// Performance Management
FPCGLumenPerformanceStats APCGLumenIntegrator::GetPerformanceStats() const
{
    return CurrentStats;
}

void APCGLumenIntegrator::OptimizeForPerformance()
{
    // Optimize lighting
    OptimizeLighting();

    // Optimize memory usage
    OptimizeMemoryUsage();

    // Clear unused data
    ClearUnusedData();

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Performance optimization completed"));
}

void APCGLumenIntegrator::SetAdaptiveQuality(bool bEnable)
{
    if (bEnable)
    {
        LumenConfig.UpdateMode = EPCGLumenUpdateMode::Adaptive;
    }
    else
    {
        LumenConfig.UpdateMode = EPCGLumenUpdateMode::Dynamic;
    }

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Set adaptive quality to %s"), bEnable ? TEXT("true") : TEXT("false"));
}

// Integration with other systems
void APCGLumenIntegrator::IntegrateWithNanite(APCGNaniteOptimizer* NaniteOptimizer)
{
    NaniteOptimizerRef = NaniteOptimizer;

    if (NaniteOptimizer)
    {
        UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Integrated with Nanite optimizer"));
    }
}

void APCGLumenIntegrator::IntegrateWithWorldPartition(APCGWorldPartitionManager* WorldPartitionManager)
{
    WorldPartitionManagerRef = WorldPartitionManager;

    if (WorldPartitionManager)
    {
        UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Integrated with World Partition manager"));
    }
}

void APCGLumenIntegrator::SynchronizeWithPCGSystem()
{
    // Synchronize with Nanite optimizer
    if (NaniteOptimizerRef.IsValid())
    {
        // Get optimized meshes and update surface data
        // Implementation would depend on Nanite optimizer interface
    }

    // Synchronize with World Partition manager
    if (WorldPartitionManagerRef.IsValid())
    {
        // Update lighting based on streaming cells
        // Implementation would depend on World Partition manager interface
    }

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Synchronized with PCG system"));
}

// Internal functions
void APCGLumenIntegrator::InitializeLumenSystem()
{
    UE_LOG(LogPCGLumenIntegrator, Log, TEXT("Initializing Lumen system with modern UE 5.6 APIs"));

    // CORRIGIDO: Verificação robusta do World
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogPCGLumenIntegrator, Error, TEXT("No valid world - cannot initialize Lumen system"));
        return;
    }

    // CORRIGIDO: Aplicar configurações de qualidade com validação
    ApplyQualitySettings();

    // CORRIGIDO: Configurar Lumen Scene Data (API moderna UE 5.6)
    if (bLumenSupported)
    {
        // Configurar surface cache resolution
        const float SurfaceCacheRes = LumenConfig.SurfaceCacheResolution;
        UE_LOG(LogPCGLumenGI, Log, TEXT("Configuring Lumen surface cache resolution: %.2f"), (double)SurfaceCacheRes);

        // Aplicar configurações de Global Illumination
        if (bGlobalIlluminationActive)
        {
            UE_LOG(LogPCGLumenGI, Log, TEXT("Configuring Global Illumination quality: %.2f"),
                   (double)LumenConfig.GlobalIlluminationQuality);
        }

        // Aplicar configurações de Reflections
        if (bReflectionsActive)
        {
            UE_LOG(LogPCGLumenReflections, Log, TEXT("Configuring Reflections quality: %.2f"),
                   (double)LumenConfig.ReflectionQuality);
        }
    }

    // CORRIGIDO: Inicialização robusta de performance monitoring
    if (bEnablePerformanceMonitoring)
    {
        CurrentStats = FPCGLumenPerformanceStats();
        CurrentStats.FrameRate = 60.0f;
        CurrentStats.GIUpdateTime = 0.0f;
        CurrentStats.ReflectionUpdateTime = 0.0f;
        CurrentStats.TotalLumenTime = 0.0f;
        CurrentStats.MemoryUsageMB = 0.0f;

        UE_LOG(LogPCGLumenPerformance, Log, TEXT("Performance monitoring initialized"));
    }

    // CORRIGIDO: Descobrir e registrar componentes de luz existentes
    DiscoverAndRegisterLightComponents();

    // CORRIGIDO: Descobrir e registrar reflection captures
    DiscoverAndRegisterReflectionCaptures();

    LastUpdateTime = World->GetTimeSeconds();
    LastPerformanceUpdate = LastUpdateTime;
    LastOptimizationCheck = LastUpdateTime;

    UE_LOG(LogPCGLumenIntegrator, Log, TEXT("Lumen system initialized successfully - GI: %s, Reflections: %s"),
           bGlobalIlluminationActive ? TEXT("Active") : TEXT("Inactive"),
           bReflectionsActive ? TEXT("Active") : TEXT("Inactive"));
}

void APCGLumenIntegrator::UpdateLumenSystem(float DeltaTime)
{
    // CORRIGIDO: Early return para modo estático
    if (LumenConfig.UpdateMode == EPCGLumenUpdateMode::Static)
    {
        return;
    }

    // CORRIGIDO: Validação robusta do World
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogPCGLumenIntegrator, Warning, TEXT("No valid world during Lumen update"));
        return;
    }

    float CurrentTime = World->GetTimeSeconds();

    // CORRIGIDO: Update global illumination com profiling
    if (bNeedsUpdate || LumenConfig.UpdateMode == EPCGLumenUpdateMode::Dynamic)
    {
        if (bGlobalIlluminationActive)
        {
            SCOPE_CYCLE_COUNTER(STAT_PCGLumenGIUpdate);
            UpdateGlobalIllumination();
        }

        if (bReflectionsActive)
        {
            SCOPE_CYCLE_COUNTER(STAT_PCGLumenReflectionsUpdate);
            UpdateReflections();
        }

        // CORRIGIDO: Update scene data se necessário
        if (bNeedsUpdate)
        {
            SCOPE_CYCLE_COUNTER(STAT_PCGLumenSceneDataUpdate);
            UpdateLumenSceneData();
        }
    }

    LastUpdateTime = CurrentTime;
    bNeedsUpdate = false;
}

void APCGLumenIntegrator::UpdatePerformanceStats()
{
    // CORRIGIDO: Validação robusta do World
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // CORRIGIDO: Update active counts com validação robusta
    CurrentStats.ActiveLights = 0;
    CurrentStats.ActiveSurfaces = 0;
    CurrentStats.ActiveReflectionCaptures = 0;

    // Contar luzes ativas
    for (const auto& LightPair : RegisteredLights)
    {
        if (LightPair.Key.IsValid() && IsValid(LightPair.Key.Get()))
        {
            CurrentStats.ActiveLights++;
        }
    }

    // Contar superfícies ativas
    for (const auto& SurfacePair : RegisteredSurfaces)
    {
        if (SurfacePair.Key.IsValid() && IsValid(SurfacePair.Key.Get()))
        {
            CurrentStats.ActiveSurfaces++;
        }
    }

    // CORRIGIDO: Contar reflection captures ativas
    for (const auto& CaptureComponent : ReflectionCaptures)
    {
        if (CaptureComponent.IsValid() && IsValid(CaptureComponent.Get()))
        {
            CurrentStats.ActiveReflectionCaptures++;
        }
    }

    // CORRIGIDO: Update frame time e frame rate
    const float DeltaSeconds = World->GetDeltaSeconds();
    CurrentStats.FrameTime = DeltaSeconds * 1000.0f; // Convert to milliseconds
    CurrentStats.FrameRate = DeltaSeconds > 0.0f ? (1.0f / DeltaSeconds) : 60.0f;

    // CORRIGIDO: Estimativa mais precisa de uso de memória
    const float LightsMemory = static_cast<float>(RegisteredLights.Num() * sizeof(FPCGLumenLightData));
    const float SurfacesMemory = static_cast<float>(RegisteredSurfaces.Num() * sizeof(FPCGLumenSurfaceData));
    const float ComponentsMemory = static_cast<float>((LightComponents.Num() + ReflectionCaptures.Num()) * sizeof(void*));

    CurrentStats.MemoryUsageMB = (LightsMemory + SurfacesMemory + ComponentsMemory) / (1024.0f * 1024.0f);

    // CORRIGIDO: Log performance stats periodicamente
    static float LastStatsLog = 0.0f;
    const float CurrentTime = World->GetTimeSeconds();
    if (CurrentTime - LastStatsLog >= 10.0f) // Log a cada 10 segundos
    {
        UE_LOG(LogPCGLumenPerformance, Log, TEXT("Performance Stats - Lights: %d, Surfaces: %d, Captures: %d, Memory: %.2f MB, FPS: %.1f"),
               CurrentStats.ActiveLights, CurrentStats.ActiveSurfaces, CurrentStats.ActiveReflectionCaptures,
               (double)CurrentStats.MemoryUsageMB, (double)CurrentStats.FrameRate);
        LastStatsLog = CurrentTime;
    }
}

void APCGLumenIntegrator::ApplyQualitySettings()
{
    // Apply quality settings based on configuration
    // This would interface with UE5.6 Lumen settings

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Applied quality settings"));
}

void APCGLumenIntegrator::OptimizeLighting()
{
    // Optimize lighting setup for performance
    int32 OptimizedLights = 0;

    for (auto& LightPair : RegisteredLights)
    {
        if (LightPair.Key.IsValid())
        {
            // Apply optimization based on distance, importance, etc.
            OptimizedLights++;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Optimized %d lights"), OptimizedLights);
}

void APCGLumenIntegrator::UpdateGlobalIllumination()
{
    // Update global illumination settings
    // This would interface with UE5.6 Lumen GI system

    UE_LOG(LogTemp, VeryVerbose, TEXT("APCGLumenIntegrator: Updated global illumination"));
}

void APCGLumenIntegrator::UpdateReflections()
{
    // Update reflection settings
    // This would interface with UE5.6 Lumen reflection system

    UE_LOG(LogTemp, VeryVerbose, TEXT("APCGLumenIntegrator: Updated reflections"));
}

void APCGLumenIntegrator::CleanupInvalidReferences()
{
    // Clean up invalid light references
    TArray<TWeakObjectPtr<ULightComponent>> InvalidLights;
    for (const auto& LightPair : RegisteredLights)
    {
        if (!LightPair.Key.IsValid())
        {
            InvalidLights.Add(LightPair.Key);
        }
    }

    for (const auto& InvalidLight : InvalidLights)
    {
        RegisteredLights.Remove(InvalidLight);
    }

    // Clean up invalid surface references
    TArray<TWeakObjectPtr<UStaticMeshComponent>> InvalidSurfaces;
    for (const auto& SurfacePair : RegisteredSurfaces)
    {
        if (!SurfacePair.Key.IsValid())
        {
            InvalidSurfaces.Add(SurfacePair.Key);
        }
    }

    for (const auto& InvalidSurface : InvalidSurfaces)
    {
        RegisteredSurfaces.Remove(InvalidSurface);
    }
}

void APCGLumenIntegrator::ProcessAdaptiveQuality()
{
    // Adjust quality based on performance
    if (CurrentStats.FrameTime > 33.33f) // Above 30 FPS threshold
    {
        // Reduce quality
        if (LumenConfig.Quality > EPCGLumenQuality::Low)
        {
            LumenConfig.Quality = static_cast<EPCGLumenQuality>(static_cast<int32>(LumenConfig.Quality) - 1);
            ApplyQualitySettings();
            UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Reduced quality due to performance"));
        }
    }
    else if (CurrentStats.FrameTime < 16.67f) // Below 60 FPS threshold
    {
        // Increase quality
        if (LumenConfig.Quality < EPCGLumenQuality::Cinematic)
        {
            LumenConfig.Quality = static_cast<EPCGLumenQuality>(static_cast<int32>(LumenConfig.Quality) + 1);
            ApplyQualitySettings();
            UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Increased quality due to good performance"));
        }
    }
}

// Rendering integration
void APCGLumenIntegrator::RegisterRenderingCallbacks()
{
    // Register callbacks for rendering pipeline integration
    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Registered rendering callbacks"));
}

void APCGLumenIntegrator::UnregisterRenderingCallbacks()
{
    // Unregister rendering callbacks
    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Unregistered rendering callbacks"));
}

void APCGLumenIntegrator::OnPreRender()
{
    // Pre-render operations
}

void APCGLumenIntegrator::OnPostRender()
{
    // Post-render operations
}

// Event handlers
void APCGLumenIntegrator::OnLightComponentDestroyed(UActorComponent* DestroyedComponent)
{
    if (ULightComponent* LightComponent = Cast<ULightComponent>(DestroyedComponent))
    {
        UnregisterLight(LightComponent);
    }
}




void APCGLumenIntegrator::OnMeshComponentDestroyed(UActorComponent* DestroyedComponent)
{
    if (UStaticMeshComponent* MeshComponent = Cast<UStaticMeshComponent>(DestroyedComponent))
    {
        UnregisterSurface(MeshComponent);
    }
}

// Async operations
void APCGLumenIntegrator::StartAsyncLumenUpdate()
{
    // Start asynchronous Lumen update
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this]()
    {
        // Perform background Lumen calculations
        CompleteAsyncLumenUpdate();
    });
}

void APCGLumenIntegrator::CompleteAsyncLumenUpdate()
{
    // Complete asynchronous Lumen update on game thread
    AsyncTask(ENamedThreads::GameThread, [this]()
    {
        // Apply results to main thread
        UE_LOG(LogTemp, VeryVerbose, TEXT("APCGLumenIntegrator: Completed async Lumen update"));
    });
}

// Memory management
void APCGLumenIntegrator::OptimizeMemoryUsage()
{
    // Optimize memory usage
    RegisteredLights.Compact();
    RegisteredSurfaces.Compact();

    UE_LOG(LogTemp, Log, TEXT("APCGLumenIntegrator: Optimized memory usage"));
}

void APCGLumenIntegrator::ClearUnusedData()
{
    // Clear unused data
    CleanupInvalidReferences();

    UE_LOG(LogPCGLumenOptimization, Log, TEXT("Cleared unused data"));
}

// CORRIGIDO: Implementações das funções faltantes para UE 5.6

void APCGLumenIntegrator::DiscoverAndRegisterLightComponents()
{
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    int32 LightsFound = 0;

    // Descobrir todas as luzes na cena
    for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
    {
        AActor* Actor = *ActorItr;
        if (!IsValid(Actor))
        {
            continue;
        }

        // Buscar componentes de luz
        TArray<ULightComponent*> LightComps;
        Actor->GetComponents<ULightComponent>(LightComps);

        for (ULightComponent* LightComp : LightComps)
        {
            if (IsValid(LightComp))
            {
                LightComponents.AddUnique(LightComp);
                LightsFound++;
            }
        }
    }

    UE_LOG(LogPCGLumenRendering, Log, TEXT("Discovered and registered %d light components"), LightsFound);
}

void APCGLumenIntegrator::DiscoverAndRegisterReflectionCaptures()
{
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    int32 CapturesFound = 0;

    // Descobrir todas as reflection captures na cena
    for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
    {
        AActor* Actor = *ActorItr;
        if (!IsValid(Actor))
        {
            continue;
        }

        // Buscar componentes de reflection capture
        TArray<UReflectionCaptureComponent*> CaptureComps;
        Actor->GetComponents<UReflectionCaptureComponent>(CaptureComps);

        for (UReflectionCaptureComponent* CaptureComp : CaptureComps)
        {
            if (IsValid(CaptureComp))
            {
                ReflectionCaptures.AddUnique(CaptureComp);
                CapturesFound++;
            }
        }
    }

    UE_LOG(LogPCGLumenReflections, Log, TEXT("Discovered and registered %d reflection captures"), CapturesFound);
}

void APCGLumenIntegrator::UpdateLumenSceneData()
{
    if (!bLumenSupported)
    {
        return;
    }

    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Atualizar dados da cena para Lumen
    // Esta função integraria com as APIs internas do Lumen para atualizar
    // surface cache, radiance cache, etc.

    UE_LOG(LogPCGLumenGI, VeryVerbose, TEXT("Updated Lumen scene data"));
}

void APCGLumenIntegrator::CheckAndApplyOptimizations()
{
    if (!bAutoOptimization || !bLumenSupported)
    {
        return;
    }

    // Verificar performance e aplicar otimizações automáticas
    const float CurrentFPS = CurrentStats.FrameRate;
    const float TargetFPS = 60.0f;
    const float PerformanceThreshold = 0.9f; // 90% do target FPS

    if (CurrentFPS < TargetFPS * PerformanceThreshold)
    {
        // Performance baixa - reduzir qualidade
        if (LumenConfig.GlobalIlluminationQuality > 0.5f)
        {
            LumenConfig.GlobalIlluminationQuality = FMath::Max(0.5f, LumenConfig.GlobalIlluminationQuality - 0.1f);
            ApplyQualitySettings();
            UE_LOG(LogPCGLumenOptimization, Log, TEXT("Reduced GI quality to %.2f due to low performance"),
                   (double)LumenConfig.GlobalIlluminationQuality);
        }

        if (LumenConfig.ReflectionQuality > 0.5f)
        {
            LumenConfig.ReflectionQuality = FMath::Max(0.5f, LumenConfig.ReflectionQuality - 0.1f);
            ApplyQualitySettings();
            UE_LOG(LogPCGLumenOptimization, Log, TEXT("Reduced reflection quality to %.2f due to low performance"),
                   (double)LumenConfig.ReflectionQuality);
        }
    }
    else if (CurrentFPS > TargetFPS * 1.1f)
    {
        // Performance boa - aumentar qualidade
        if (LumenConfig.GlobalIlluminationQuality < 1.0f)
        {
            LumenConfig.GlobalIlluminationQuality = FMath::Min(1.0f, LumenConfig.GlobalIlluminationQuality + 0.05f);
            ApplyQualitySettings();
            UE_LOG(LogPCGLumenOptimization, Log, TEXT("Increased GI quality to %.2f due to good performance"),
                   (double)LumenConfig.GlobalIlluminationQuality);
        }
    }
}