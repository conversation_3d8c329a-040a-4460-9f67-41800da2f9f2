#include "UPCGQualityValidator.h"
#include "APCGWorldPartitionManager.h"
#include "APCGNaniteOptimizer.h"
#include "APCGLumenIntegrator.h"
// Editor includes removidos para evitar problemas de compilação
#include "Materials/MaterialInterface.h"
#include "Materials/Material.h"
#include "Engine/Light.h"
#include "Components/LightComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/SpotLightComponent.h"
#include "PCGEdge.h"
#include "PCGPin.h"
#include "APCGStreamingManager.h"
#include "UPCGPerformanceProfiler.h"
#include "APCGCacheManager.h"
#include "APCGChaosIntegrator.h"
#include "EngineUtils.h"
#include "Kismet/GameplayStatics.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "Misc/Guid.h"
#include "TimerManager.h"
#include "Stats/Stats.h"
#include "RenderingThread.h"
#include "RHI.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/Material.h"
#include "Engine/Texture2D.h"
#include "LandscapeProxy.h"
#include "WorldPartition/WorldPartition.h"
#include "NaniteSceneProxy.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "PhysicsEngine/BodySetup.h"
#include "Chaos/ChaosEngineInterface.h"
#include "Engine/RendererSettings.h"
#include "Async/ParallelFor.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

UPCGQualityValidator::UPCGQualityValidator()
{
	// Initialize default configuration
	ValidationConfig = FPCGValidationConfig();
	bAutomaticValidationEnabled = false;

	// Initialize integration references
	InitializeIntegrations();
}

// Core Validation Functions
FString UPCGQualityValidator::StartValidation(const FPCGValidationConfig& Config)
{
	FString ValidationID = FGuid::NewGuid().ToString();
	ValidationConfig = Config;

	// Create new validation report
	FPCGValidationReport NewReport;
	NewReport.ReportID = ValidationID;
	NewReport.GenerationTime = FDateTime::Now();

	// Store active validation
	ActiveValidations.Add(ValidationID, NewReport);

	// Broadcast validation started
	OnValidationStarted.Broadcast(ValidationID);

	// Start validation process asynchronously
	AsyncTask(ENamedThreads::GameThread, [this, ValidationID]()
	{
		PerformValidation(ValidationID);
	});

	return ValidationID;
}

bool UPCGQualityValidator::StopValidation(const FString& ValidationID)
{
	if (!ActiveValidations.Contains(ValidationID))
	{
		return false;
	}

	// Move to history and remove from active
	FPCGValidationReport Report = ActiveValidations[ValidationID];
	ValidationHistory.Add(Report);
	ActiveValidations.Remove(ValidationID);

	return true;
}

FPCGValidationReport UPCGQualityValidator::GetValidationReport(const FString& ValidationID)
{
	if (ActiveValidations.Contains(ValidationID))
	{
		return ActiveValidations[ValidationID];
	}

	// Search in history
	for (const FPCGValidationReport& Report : ValidationHistory)
	{
		if (Report.ReportID == ValidationID)
		{
			return Report;
		}
	}

	return FPCGValidationReport();
}

TArray<FPCGValidationReport> UPCGQualityValidator::GetAllReports()
{
	TArray<FPCGValidationReport> AllReports;

	// Add active validations
	for (const auto& Pair : ActiveValidations)
	{
		AllReports.Add(Pair.Value);
	}

	// Add historical reports
	AllReports.Append(ValidationHistory);

	return AllReports;
}

// Issue Management
TArray<FPCGValidationIssue> UPCGQualityValidator::GetIssuesByCategory(EPCGValidationCategory Category)
{
	TArray<FPCGValidationIssue> FilteredIssues;

	for (const FPCGValidationIssue& Issue : CurrentIssues)
	{
		if (Issue.Category == Category)
		{
			FilteredIssues.Add(Issue);
		}
	}

	return FilteredIssues;
}

TArray<FPCGValidationIssue> UPCGQualityValidator::GetIssuesBySeverity(EPCGValidationSeverity Severity)
{
	TArray<FPCGValidationIssue> FilteredIssues;

	for (const FPCGValidationIssue& Issue : CurrentIssues)
	{
		if (Issue.Severity == Severity)
		{
			FilteredIssues.Add(Issue);
		}
	}

	return FilteredIssues;
}

bool UPCGQualityValidator::FixIssue(const FString& IssueID)
{
	for (FPCGValidationIssue& Issue : CurrentIssues)
	{
		if (Issue.IssueID == IssueID && Issue.bCanAutoFix && !Issue.bIsFixed)
		{
			bool bFixed = false;

			// Attempt auto-fix based on category
			switch (Issue.Category)
			{
			case EPCGValidationCategory::Geometry:
				bFixed = AutoFixLODIssue(Issue);
				break;
			case EPCGValidationCategory::Performance:
				bFixed = AutoFixPerformanceIssue(Issue);
				break;
			case EPCGValidationCategory::Memory:
				bFixed = AutoFixMemoryIssue(Issue);
				break;
			case EPCGValidationCategory::Rendering:
				bFixed = AutoFixRenderingIssue(Issue);
				break;
			case EPCGValidationCategory::Streaming:
				bFixed = AutoFixStreamingIssue(Issue);
				break;
			default:
				bFixed = false;
				break;
			}

			if (bFixed)
			{
				Issue.bIsFixed = true;
				OnIssueFixed.Broadcast(FString(), Issue);
				return true;
			}
		}
	}

	return false;
}

int32 UPCGQualityValidator::FixAllAutoFixableIssues()
{
	int32 FixedCount = 0;

	for (FPCGValidationIssue& Issue : CurrentIssues)
	{
		if (Issue.bCanAutoFix && !Issue.bIsFixed)
		{
			if (FixIssue(Issue.IssueID))
			{
				FixedCount++;
			}
		}
	}

	return FixedCount;
}

// Quality Assessment
EPCGQualityLevel UPCGQualityValidator::AssessOverallQuality()
{
	float QualityScore = CalculateQualityScore();
	return DetermineQualityLevel(QualityScore);
}

float UPCGQualityValidator::CalculateQualityScore()
{
	float TotalScore = 0.0f;
	int32 CategoryCount = 0;

	// Calculate score for each enabled category
	for (EPCGValidationCategory Category : ValidationConfig.EnabledCategories)
	{
		TotalScore += CalculateCategoryScore(Category);
		CategoryCount++;
	}

	return CategoryCount > 0 ? TotalScore / CategoryCount : 0.0f;
}

FPCGQualityMetrics UPCGQualityValidator::GetCurrentMetrics()
{
	FPCGQualityMetrics Metrics;

	// Update performance metrics
	if (PerformanceProfiler.IsValid())
	{
		// Get metrics from performance profiler
		Metrics.FrameTime = PerformanceProfiler->GetAverageFrameTime();
		Metrics.MemoryUsage = PerformanceProfiler->GetMemoryUsage();
		Metrics.DrawCalls = PerformanceProfiler->GetDrawCallCount();
		Metrics.TriangleCount = PerformanceProfiler->GetTriangleCount();
	}

	// Update quality metrics
	Metrics.LODCoverage = CalculateLODCoverage();
	Metrics.TextureQuality = CalculateTextureQuality();
	Metrics.GeometryComplexity = CalculateGeometryComplexity();
	Metrics.LightingQuality = CalculateLightingQuality();

	// Update streaming metrics
	if (StreamingManager.IsValid())
	{
		Metrics.StreamingEfficiency = StreamingManager->GetStreamingEfficiency();
	}

	if (CacheManager.IsValid())
	{
		Metrics.CacheHitRate = CacheManager->GetCacheHitRate();
	}

	return Metrics;
}

bool UPCGQualityValidator::MeetsQualityStandards(EPCGQualityLevel RequiredLevel)
{
	EPCGQualityLevel CurrentLevel = AssessOverallQuality();
	return static_cast<int32>(CurrentLevel) >= static_cast<int32>(RequiredLevel);
}

// Configuration
void UPCGQualityValidator::SetValidationConfig(const FPCGValidationConfig& NewConfig)
{
	ValidationConfig = NewConfig;
}

FPCGValidationConfig UPCGQualityValidator::GetValidationConfig() const
{
	return ValidationConfig;
}

void UPCGQualityValidator::EnableCategory(EPCGValidationCategory Category)
{
	ValidationConfig.EnabledCategories.AddUnique(Category);
}

void UPCGQualityValidator::DisableCategory(EPCGValidationCategory Category)
{
	ValidationConfig.EnabledCategories.Remove(Category);
}

// Automation
void UPCGQualityValidator::StartAutomaticValidation()
{
	if (!bAutomaticValidationEnabled)
	{
		bAutomaticValidationEnabled = true;

		if (UWorld* World = GetWorld())
		{
			World->GetTimerManager().SetTimer(
				AutoValidationTimer,
				this,
				&UPCGQualityValidator::OnAutomaticValidationTimer,
				ValidationConfig.ValidationInterval,
				true
			);
		}
	}
}

void UPCGQualityValidator::StopAutomaticValidation()
{
	if (bAutomaticValidationEnabled)
	{
		bAutomaticValidationEnabled = false;

		if (UWorld* World = GetWorld())
		{
			World->GetTimerManager().ClearTimer(AutoValidationTimer);
		}
	}
}

bool UPCGQualityValidator::IsAutomaticValidationRunning() const
{
	return bAutomaticValidationEnabled;
}

// Reporting
bool UPCGQualityValidator::ExportReport(const FString& ValidationID, const FString& FilePath)
{
	FPCGValidationReport Report = GetValidationReport(ValidationID);
	if (Report.ReportID.IsEmpty())
	{
		return false;
	}

	FString JSONContent = GenerateJSONReport(ValidationID);
	return FFileHelper::SaveStringToFile(JSONContent, *FilePath);
}

FString UPCGQualityValidator::GenerateHTMLReport(const FString& ValidationID)
{
	FPCGValidationReport Report = GetValidationReport(ValidationID);
	if (Report.ReportID.IsEmpty())
	{
		return FString();
	}

	FString HTMLContent = TEXT("<!DOCTYPE html><html><head><title>PCG Quality Validation Report</title></head><body>");
	HTMLContent += FString::Printf(TEXT("<h1>Validation Report: %s</h1>"), *Report.ReportID);
	HTMLContent += FString::Printf(TEXT("<p>Generated: %s</p>"), *Report.GenerationTime.ToString());
	HTMLContent += FString::Printf(TEXT("<p>Duration: %.2f seconds</p>"), Report.ValidationDuration);
	HTMLContent += FString::Printf(TEXT("<p>Quality Score: %.1f/100</p>"), Report.QualityScore);
	HTMLContent += FString::Printf(TEXT("<p>Overall Quality: %s</p>"), *UEnum::GetValueAsString(Report.OverallQuality));

	// Add issues section
	HTMLContent += TEXT("<h2>Issues Found</h2><ul>");
	for (const FPCGValidationIssue& Issue : Report.Issues)
	{
		HTMLContent += FString::Printf(TEXT("<li><strong>%s</strong> [%s]: %s</li>"),
			*Issue.Title,
			*UEnum::GetValueAsString(Issue.Severity),
			*Issue.Description
		);
	}
	HTMLContent += TEXT("</ul></body></html>");

	return HTMLContent;
}

FString UPCGQualityValidator::GenerateJSONReport(const FString& ValidationID)
{
	FPCGValidationReport Report = GetValidationReport(ValidationID);
	if (Report.ReportID.IsEmpty())
	{
		return FString();
	}

	TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
	JsonObject->SetStringField(TEXT("reportId"), Report.ReportID);
	JsonObject->SetStringField(TEXT("generationTime"), Report.GenerationTime.ToString());
	JsonObject->SetNumberField(TEXT("validationDuration"), Report.ValidationDuration);
	JsonObject->SetNumberField(TEXT("qualityScore"), Report.QualityScore);
	JsonObject->SetStringField(TEXT("overallQuality"), UEnum::GetValueAsString(Report.OverallQuality));
	JsonObject->SetNumberField(TEXT("totalChecks"), Report.TotalChecks);
	JsonObject->SetNumberField(TEXT("passedChecks"), Report.PassedChecks);
	JsonObject->SetNumberField(TEXT("failedChecks"), Report.FailedChecks);

	// Add issues array
	TArray<TSharedPtr<FJsonValue>> IssuesArray;
	for (const FPCGValidationIssue& Issue : Report.Issues)
	{
		TSharedPtr<FJsonObject> IssueObject = MakeShareable(new FJsonObject);
		IssueObject->SetStringField(TEXT("id"), Issue.IssueID);
		IssueObject->SetStringField(TEXT("title"), Issue.Title);
		IssueObject->SetStringField(TEXT("description"), Issue.Description);
		IssueObject->SetStringField(TEXT("severity"), UEnum::GetValueAsString(Issue.Severity));
		IssueObject->SetStringField(TEXT("category"), UEnum::GetValueAsString(Issue.Category));
		IssueObject->SetBoolField(TEXT("canAutoFix"), Issue.bCanAutoFix);
		IssueObject->SetBoolField(TEXT("isFixed"), Issue.bIsFixed);

		IssuesArray.Add(MakeShareable(new FJsonValueObject(IssueObject)));
	}
	JsonObject->SetArrayField(TEXT("issues"), IssuesArray);

	FString OutputString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
	FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

	return OutputString;
}

// Integration with PCG Systems
void UPCGQualityValidator::ValidatePCGComponent(UPCGComponent* Component)
{
	if (!Component)
	{
		return;
	}

	// Create validation report for component
	FPCGValidationReport ComponentReport;
	ComponentReport.ReportID = FGuid::NewGuid().ToString();
	ComponentReport.GenerationTime = FDateTime::Now();

	// Validate component-specific aspects
	ValidateComponentGeometry(Component, ComponentReport);
	ValidateComponentPerformance(Component, ComponentReport);
	ValidateComponentSettings(Component, ComponentReport);

	// Store report
	ValidationHistory.Add(ComponentReport);
}

void UPCGQualityValidator::ValidatePCGGraph(UPCGGraph* Graph)
{
	if (!Graph)
	{
		return;
	}

	// Create validation report for graph
	FPCGValidationReport GraphReport;
	GraphReport.ReportID = FGuid::NewGuid().ToString();
	GraphReport.GenerationTime = FDateTime::Now();

	// Validate graph-specific aspects
	ValidateGraphComplexity(Graph, GraphReport);
	ValidateGraphConnections(Graph, GraphReport);
	ValidateGraphSettings(Graph, GraphReport);

	// Store report
	ValidationHistory.Add(GraphReport);
}

void UPCGQualityValidator::ValidateWorldPartition()
{
	if (WorldPartitionManager.IsValid())
	{
		FPCGValidationReport WPReport;
		WPReport.ReportID = FGuid::NewGuid().ToString();
		WPReport.GenerationTime = FDateTime::Now();

		// Validate World Partition setup
		ValidateWorldPartitionConfiguration(WPReport);
		ValidateWorldPartitionPerformance(WPReport);

		ValidationHistory.Add(WPReport);
	}
}

void UPCGQualityValidator::ValidateNaniteContent()
{
	if (NaniteOptimizer.IsValid())
	{
		FPCGValidationReport NaniteReport;
		NaniteReport.ReportID = FGuid::NewGuid().ToString();
		NaniteReport.GenerationTime = FDateTime::Now();

		// Validate Nanite setup
		ValidateNaniteConfiguration(NaniteReport);
		ValidateNanitePerformance(NaniteReport);

		ValidationHistory.Add(NaniteReport);
	}
}

void UPCGQualityValidator::ValidateLumenSetup()
{
	if (LumenIntegrator.IsValid())
	{
		FPCGValidationReport LumenReport;
		LumenReport.ReportID = FGuid::NewGuid().ToString();
		LumenReport.GenerationTime = FDateTime::Now();

		// Validate Lumen setup
		ValidateLumenConfiguration(LumenReport);
		ValidateLumenPerformance(LumenReport);

		ValidationHistory.Add(LumenReport);
	}
}

// Internal Validation Functions
void UPCGQualityValidator::PerformValidation(const FString& ValidationID)
{
	if (!ActiveValidations.Contains(ValidationID))
	{
		return;
	}

	FPCGValidationReport& Report = ActiveValidations[ValidationID];
	FDateTime StartTime = FDateTime::Now();

	// Clear current issues
	CurrentIssues.Empty();

	// Perform validation for each enabled category
	for (EPCGValidationCategory Category : ValidationConfig.EnabledCategories)
	{
		switch (Category)
		{
		case EPCGValidationCategory::Geometry:
			PerformGeometryValidation(Report);
			break;
		case EPCGValidationCategory::Performance:
			PerformPerformanceValidation(Report);
			break;
		case EPCGValidationCategory::Memory:
			PerformMemoryValidation(Report);
			break;
		case EPCGValidationCategory::Rendering:
			PerformRenderingValidation(Report);
			break;
		case EPCGValidationCategory::Physics:
			PerformPhysicsValidation(Report);
			break;
		case EPCGValidationCategory::Streaming:
			PerformStreamingValidation(Report);
			break;
		case EPCGValidationCategory::Cache:
			PerformCacheValidation(Report);
			break;
		case EPCGValidationCategory::Quality:
			PerformQualityValidation(Report);
			break;
		case EPCGValidationCategory::Compliance:
			PerformComplianceValidation(Report);
			break;
		}
	}

	// Calculate final metrics
	Report.ValidationDuration = (FDateTime::Now() - StartTime).GetTotalSeconds();
	Report.QualityScore = CalculateQualityScore();
	Report.OverallQuality = DetermineQualityLevel(Report.QualityScore);
	Report.Issues = CurrentIssues;

	// Update counters
	Report.TotalChecks = Report.PassedChecks + Report.FailedChecks + Report.SkippedChecks;

	// Broadcast completion
	OnValidationCompleted.Broadcast(ValidationID, Report);

	// Move to history
	ValidationHistory.Add(Report);
	ActiveValidations.Remove(ValidationID);

	// Clean up old reports
	CleanupOldReports();
}

void UPCGQualityValidator::PerformGeometryValidation(FPCGValidationReport& Report)
{
	// Detect LOD issues
	DetectLODIssues(Report);

	// Validate mesh complexity
	ValidateMeshComplexity(Report);

	// Check for degenerate geometry
	DetectDegenerateGeometry(Report);

	// Validate UV mapping
	ValidateUVMapping(Report);

	Report.PassedChecks += 4; // Assuming all checks pass for now
}

void UPCGQualityValidator::PerformPerformanceValidation(FPCGValidationReport& Report)
{
	// Detect performance bottlenecks
	DetectPerformanceBottlenecks(Report);

	// Validate frame rate
	ValidateFrameRate(Report);

	// Check draw call count
	ValidateDrawCalls(Report);

	// Validate triangle count
	ValidateTriangleCount(Report);

	Report.PassedChecks += 4;
}

void UPCGQualityValidator::PerformMemoryValidation(FPCGValidationReport& Report)
{
	// Detect memory leaks
	DetectMemoryLeaks(Report);

	// Validate memory usage
	ValidateMemoryUsage(Report);

	// Check for memory fragmentation
	DetectMemoryFragmentation(Report);

	Report.PassedChecks += 3;
}

void UPCGQualityValidator::PerformRenderingValidation(FPCGValidationReport& Report)
{
	// Detect rendering issues
	DetectRenderingIssues(Report);

	// Validate material setup
	ValidateMaterialSetup(Report);

	// Check lighting setup
	ValidateLightingSetup(Report);

	// Validate shadow quality
	ValidateShadowQuality(Report);

	Report.PassedChecks += 4;
}

void UPCGQualityValidator::PerformPhysicsValidation(FPCGValidationReport& Report)
{
	// Detect physics problems
	DetectPhysicsProblems(Report);

	// Validate collision setup
	ValidateCollisionSetup(Report);

	// Check physics performance
	ValidatePhysicsPerformance(Report);

	Report.PassedChecks += 3;
}

void UPCGQualityValidator::PerformStreamingValidation(FPCGValidationReport& Report)
{
	// Detect streaming issues
	DetectStreamingIssues(Report);

	// Validate streaming setup
	ValidateStreamingSetup(Report);

	// Check streaming performance
	ValidateStreamingPerformance(Report);

	Report.PassedChecks += 3;
}

void UPCGQualityValidator::PerformCacheValidation(FPCGValidationReport& Report)
{
	// Detect cache problems
	DetectCacheProblems(Report);

	// Validate cache efficiency
	ValidateCacheEfficiency(Report);

	// Check cache memory usage
	ValidateCacheMemoryUsage(Report);

	Report.PassedChecks += 3;
}

void UPCGQualityValidator::PerformQualityValidation(FPCGValidationReport& Report)
{
	// Validate overall quality metrics
	ValidateQualityMetrics(Report);

	// Check visual quality
	ValidateVisualQuality(Report);

	// Validate consistency
	ValidateConsistency(Report);

	Report.PassedChecks += 3;
}

void UPCGQualityValidator::PerformComplianceValidation(FPCGValidationReport& Report)
{
	// Check platform compliance
	ValidatePlatformCompliance(Report);

	// Validate naming conventions
	ValidateNamingConventions(Report);

	// Check asset organization
	ValidateAssetOrganization(Report);

	Report.PassedChecks += 3;
}

// Issue Detection Functions
void UPCGQualityValidator::DetectLODIssues(FPCGValidationReport& Report)
{
	if (UWorld* World = GetWorld())
	{
		// Iterate through all static mesh components
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (!Actor) continue;

			TArray<UStaticMeshComponent*> MeshComponents;
			Actor->GetComponents<UStaticMeshComponent>(MeshComponents);

			for (UStaticMeshComponent* MeshComp : MeshComponents)
			{
				if (!MeshComp || !MeshComp->GetStaticMesh()) continue;

				UStaticMesh* StaticMesh = MeshComp->GetStaticMesh();
				int32 LODCount = StaticMesh->GetNumLODs();

				// Check for missing LODs
				if (LODCount < 3)
				{
					FPCGValidationIssue Issue;
					Issue.Title = TEXT("Insufficient LOD Levels");
					Issue.Description = FString::Printf(TEXT("Mesh %s has only %d LOD levels, recommended minimum is 3"), 
						*StaticMesh->GetName(), LODCount);
					Issue.Severity = EPCGValidationSeverity::Warning;
					Issue.Category = EPCGValidationCategory::Geometry;
					Issue.Location = Actor->GetActorLocation();
					Report.Issues.Add(Issue);
				}

				// Check LOD transition distances
				for (int32 LODIndex = 0; LODIndex < LODCount - 1; ++LODIndex)
				{
					float CurrentDistance = StaticMesh->GetRenderData()->LODResources[LODIndex].MaxDeviation;
					float NextDistance = StaticMesh->GetRenderData()->LODResources[LODIndex + 1].MaxDeviation;
					
					if (NextDistance <= CurrentDistance)
					{
						FPCGValidationIssue Issue;
						Issue.Title = TEXT("Invalid LOD Transition");
						Issue.Description = FString::Printf(TEXT("LOD %d to %d transition distance is invalid in mesh %s"), 
							LODIndex, LODIndex + 1, *StaticMesh->GetName());
						Issue.Severity = EPCGValidationSeverity::Error;
						Issue.Category = EPCGValidationCategory::Geometry;
						Issue.Location = Actor->GetActorLocation();
						Report.Issues.Add(Issue);
					}
				}
			}
		}
	}
}

void UPCGQualityValidator::DetectPerformanceBottlenecks(FPCGValidationReport& Report)
{
	// Check frame rate performance
	float CurrentFPS = 1.0f / FApp::GetDeltaTime();
	if (CurrentFPS < 30.0f)
	{
		FPCGValidationIssue Issue;
		Issue.Title = TEXT("Low Frame Rate");
		Issue.Description = FString::Printf(TEXT("Current FPS is %.1f, below recommended 30 FPS"), CurrentFPS);
		Issue.Severity = EPCGValidationSeverity::Critical;
		Issue.Category = EPCGValidationCategory::Performance;
		Report.Issues.Add(Issue);
	}

	// Check draw call count
	if (GEngine && GEngine->GameViewport)
        {
                FViewport* Viewport = GEngine->GameViewport->Viewport;
		if (Viewport)
		{
			// Get render stats
			// FRenderStats não existe no UE 5.6 - usar alternativa
			// Usar sistema de estatísticas nativo do UE 5.6 para obter draw calls
			int32 DrawCallCount = 0;

#if STATS
			// UE 5.6: Usar estimativa baseada em componentes visíveis
			if (UWorld* World = GetWorld())
			{
				for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
				{
					AActor* Actor = *ActorItr;
					if (Actor && Actor->GetRootComponent())
					{
						TArray<UStaticMeshComponent*> StaticMeshComponents;
						Actor->GetComponents<UStaticMeshComponent>(StaticMeshComponents);
						DrawCallCount += StaticMeshComponents.Num();
					}
				}
			}
#endif

			if (DrawCallCount > 2000)
			{
				FPCGValidationIssue Issue;
				Issue.Title = TEXT("High Draw Call Count");
				Issue.Description = FString::Printf(TEXT("Draw calls: %d, recommended maximum is 2000"), DrawCallCount);
				Issue.Severity = EPCGValidationSeverity::Warning;
				Issue.Category = EPCGValidationCategory::Performance;
				Report.Issues.Add(Issue);
			}
		}
	}

	// Check memory usage
	FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
	float MemoryUsageGB = MemStats.UsedPhysical / (1024.0f * 1024.0f * 1024.0f);
	if (MemoryUsageGB > 8.0f)
	{
		FPCGValidationIssue Issue;
		Issue.Title = TEXT("High Memory Usage");
		Issue.Description = FString::Printf(TEXT("Memory usage: %.2f GB, recommended maximum is 8 GB"), MemoryUsageGB);
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Category = EPCGValidationCategory::Performance;
		Report.Issues.Add(Issue);
	}
}

void UPCGQualityValidator::DetectMemoryLeaks(FPCGValidationReport& Report)
{
	// Get current memory statistics
	FPlatformMemoryStats CurrentStats = FPlatformMemory::GetStats();
	static FPlatformMemoryStats PreviousStats = CurrentStats;
	static double LastCheckTime = FPlatformTime::Seconds();
	
	double CurrentTime = FPlatformTime::Seconds();
	double TimeDelta = CurrentTime - LastCheckTime;
	
	if (TimeDelta > 5.0) // Check every 5 seconds
	{
		// Calculate memory growth rate
		int64 MemoryGrowth = CurrentStats.UsedPhysical - PreviousStats.UsedPhysical;
		float GrowthRateMB = (MemoryGrowth / (1024.0f * 1024.0f)) / TimeDelta;
		
		// Detect potential memory leaks
		if (GrowthRateMB > 10.0f) // More than 10MB/second growth
		{
			FPCGValidationIssue Issue;
			Issue.Title = TEXT("Potential Memory Leak");
			Issue.Description = FString::Printf(TEXT("Memory growing at %.2f MB/s, potential leak detected"), GrowthRateMB);
			Issue.Severity = EPCGValidationSeverity::Critical;
			Issue.Category = EPCGValidationCategory::Performance;
			Report.Issues.Add(Issue);
		}
		
		// Check for excessive allocations
		if (CurrentStats.TotalPhysical > 0)
		{
			float MemoryUsagePercent = (float)CurrentStats.UsedPhysical / CurrentStats.TotalPhysical * 100.0f;
			if (MemoryUsagePercent > 90.0f)
			{
				FPCGValidationIssue Issue;
				Issue.Title = TEXT("Critical Memory Usage");
				Issue.Description = FString::Printf(TEXT("Memory usage at %.1f%%, system may become unstable"), MemoryUsagePercent);
				Issue.Severity = EPCGValidationSeverity::Critical;
				Issue.Category = EPCGValidationCategory::Performance;
				Report.Issues.Add(Issue);
			}
		}
		
		PreviousStats = CurrentStats;
		LastCheckTime = CurrentTime;
	}
}

void UPCGQualityValidator::DetectRenderingIssues(FPCGValidationReport& Report)
{
	if (UWorld* World = GetWorld())
	{
		// Check for materials without proper setup
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (!Actor) continue;

			TArray<UMeshComponent*> MeshComponents;
			Actor->GetComponents<UMeshComponent>(MeshComponents);

			for (UMeshComponent* MeshComp : MeshComponents)
			{
				if (!MeshComp) continue;

				// Check for missing materials
				int32 MaterialCount = MeshComp->GetNumMaterials();
				for (int32 i = 0; i < MaterialCount; ++i)
				{
					UMaterialInterface* Material = MeshComp->GetMaterial(i);
					if (!Material)
					{
						FPCGValidationIssue Issue;
						Issue.Title = TEXT("Missing Material");
						Issue.Description = FString::Printf(TEXT("Material slot %d is empty on %s"), i, *Actor->GetName());
						Issue.Severity = EPCGValidationSeverity::Error;
						Issue.Category = EPCGValidationCategory::Rendering;
						Issue.Location = Actor->GetActorLocation();
						Report.Issues.Add(Issue);
					}
					else if (Material->GetMaterial() && Material->GetMaterial()->IsDefaultMaterial())
					{
						FPCGValidationIssue Issue;
						Issue.Title = TEXT("Default Material Used");
						Issue.Description = FString::Printf(TEXT("Default material used on %s, slot %d"), *Actor->GetName(), i);
						Issue.Severity = EPCGValidationSeverity::Warning;
						Issue.Category = EPCGValidationCategory::Rendering;
						Issue.Location = Actor->GetActorLocation();
						Report.Issues.Add(Issue);
					}
				}

				// Check for excessive material complexity
				if (UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(MeshComp))
				{
					if (StaticMeshComp->GetStaticMesh())
					{
						int32 TriangleCount = StaticMeshComp->GetStaticMesh()->GetNumTriangles(0);
						if (TriangleCount > 10000 && MaterialCount > 4)
						{
							FPCGValidationIssue Issue;
							Issue.Title = TEXT("High Complexity Rendering");
							Issue.Description = FString::Printf(TEXT("High poly mesh (%d triangles) with %d materials on %s"), 
								TriangleCount, MaterialCount, *Actor->GetName());
							Issue.Severity = EPCGValidationSeverity::Warning;
							Issue.Category = EPCGValidationCategory::Performance;
							Issue.Location = Actor->GetActorLocation();
							Report.Issues.Add(Issue);
						}
					}
				}
			}
		}

		// Check lighting setup
		TArray<AActor*> LightActors;
		UGameplayStatics::GetAllActorsOfClass(World, ALight::StaticClass(), LightActors);
		
		if (LightActors.Num() == 0)
		{
			FPCGValidationIssue Issue;
			Issue.Title = TEXT("No Lighting");
			Issue.Description = TEXT("No light actors found in the scene");
			Issue.Severity = EPCGValidationSeverity::Warning;
			Issue.Category = EPCGValidationCategory::Rendering;
			Report.Issues.Add(Issue);
		}
	}
}

void UPCGQualityValidator::DetectPhysicsProblems(FPCGValidationReport& Report)
{
	if (UWorld* World = GetWorld())
	{
		// Check for physics bodies without collision
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (!Actor) continue;

			TArray<UPrimitiveComponent*> PrimitiveComponents;
			Actor->GetComponents<UPrimitiveComponent>(PrimitiveComponents);

			for (UPrimitiveComponent* PrimComp : PrimitiveComponents)
			{
				if (!PrimComp) continue;

				// Check for physics simulation without collision
				if (PrimComp->IsSimulatingPhysics() && PrimComp->GetCollisionEnabled() == ECollisionEnabled::NoCollision)
				{
					FPCGValidationIssue Issue;
					Issue.Title = TEXT("Physics Without Collision");
					Issue.Description = FString::Printf(TEXT("Component %s on %s simulates physics but has no collision"), 
						*PrimComp->GetName(), *Actor->GetName());
					Issue.Severity = EPCGValidationSeverity::Error;
					Issue.Category = EPCGValidationCategory::Physics;
					Issue.Location = Actor->GetActorLocation();
					Report.Issues.Add(Issue);
				}

				// Check for excessive collision complexity
				if (UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(PrimComp))
				{
					if (StaticMeshComp->GetCollisionEnabled() != ECollisionEnabled::NoCollision)
					{
						// UE 5.6: Usar GetBodySetup para verificar collision
						ECollisionTraceFlag TraceFlag = CTF_UseDefault;
						if (UStaticMesh* StaticMesh = StaticMeshComp->GetStaticMesh())
						{
							// Verificar se tem collision mesh complexa usando BodySetup
							if (UBodySetup* BodySetup = StaticMesh->GetBodySetup())
							{
								TraceFlag = BodySetup->CollisionTraceFlag;
							}
						}
						if (TraceFlag == ECollisionTraceFlag::CTF_UseComplexAsSimple)
						{
							if (StaticMeshComp->GetStaticMesh() && StaticMeshComp->GetStaticMesh()->GetNumTriangles(0) > 1000)
							{
								FPCGValidationIssue Issue;
								Issue.Title = TEXT("Complex Collision Performance");
								Issue.Description = FString::Printf(TEXT("High poly mesh using complex collision on %s"), *Actor->GetName());
								Issue.Severity = EPCGValidationSeverity::Warning;
								Issue.Category = EPCGValidationCategory::Performance;
								Issue.Location = Actor->GetActorLocation();
								Report.Issues.Add(Issue);
							}
						}
					}
				}

				// Check for missing physics materials
				// UE 5.6: GetPhysicalMaterial não existe mais em UPrimitiveComponent - usar GetBodyInstance
				UPhysicalMaterial* PhysMaterial = nullptr;
				if (FBodyInstance* BodyInstance = PrimComp->GetBodyInstance())
				{
					PhysMaterial = BodyInstance->GetSimplePhysicalMaterial();
				}
				if (PrimComp->IsSimulatingPhysics() && !PhysMaterial)
				{
					FPCGValidationIssue Issue;
					Issue.Title = TEXT("Missing Physics Material");
					Issue.Description = FString::Printf(TEXT("Physics component %s on %s has no physics material"), 
						*PrimComp->GetName(), *Actor->GetName());
					Issue.Severity = EPCGValidationSeverity::Warning;
					Issue.Category = EPCGValidationCategory::Physics;
					Issue.Location = Actor->GetActorLocation();
					Report.Issues.Add(Issue);
				}
			}
		}

		// Check world physics settings
		if (UPhysicsSettings* PhysicsSettings = UPhysicsSettings::Get())
		{
			if (PhysicsSettings->DefaultGravityZ > -980.0f) // Standard gravity is -980 cm/s²
			{
				FPCGValidationIssue Issue;
				Issue.Title = TEXT("Non-Standard Gravity");
				Issue.Description = FString::Printf(TEXT("Gravity is set to %.1f, standard is -980"), PhysicsSettings->DefaultGravityZ);
				Issue.Severity = EPCGValidationSeverity::Info;
				Issue.Category = EPCGValidationCategory::Physics;
				Report.Issues.Add(Issue);
			}
		}
	}
}

void UPCGQualityValidator::DetectStreamingIssues(FPCGValidationReport& Report)
{
	if (UWorld* World = GetWorld())
	{
		// Check for world partition setup
		if (World->IsPartitionedWorld())
		{
			if (UWorldPartition* WorldPartition = World->GetWorldPartition())
			{
				// Check streaming source setup
				// UE 5.6: GetStreamingSources retorna const reference
				const TArray<FWorldPartitionStreamingSource>& StreamingSources = WorldPartition->GetStreamingSources();

				if (StreamingSources.Num() == 0)
				{
					FPCGValidationIssue Issue;
					Issue.Title = TEXT("No Streaming Sources");
					Issue.Description = TEXT("World partition enabled but no streaming sources configured");
					Issue.Severity = EPCGValidationSeverity::Warning;
					Issue.Category = EPCGValidationCategory::Streaming;
					Report.Issues.Add(Issue);
				}

				// Check for excessive streaming distance
				for (const FWorldPartitionStreamingSource& StreamingSource : StreamingSources)
				{
					// UE 5.6: FWorldPartitionStreamingSource não tem Radius - usar Shapes
					float MaxRadius = 0.0f;
					for (const FStreamingSourceShape& Shape : StreamingSource.Shapes)
					{
						if (Shape.bUseGridLoadingRange)
						{
							// UE 5.6: Usar ForEachShape para obter o raio máximo
							StreamingSource.ForEachShape(10000.0f, false, [&MaxRadius](const FSphericalSector& Sector)
							{
								MaxRadius = FMath::Max(MaxRadius, Sector.GetRadius());
							});
						}
						else
						{
							MaxRadius = FMath::Max(MaxRadius, Shape.Radius);
						}
					}

					// Se não tem shapes, usar ForEachShape que retorna um shape padrão
					if (StreamingSource.Shapes.IsEmpty())
					{
						StreamingSource.ForEachShape(10000.0f, false, [&MaxRadius](const FSphericalSector& Sector)
						{
							MaxRadius = FMath::Max(MaxRadius, Sector.GetRadius());
						});
					}

					if (MaxRadius > 50000.0f) // 500m in UE units
					{
						FPCGValidationIssue Issue;
						Issue.Title = TEXT("Excessive Streaming Distance");
						Issue.Description = FString::Printf(TEXT("Streaming radius %.1f cm is very large, may impact performance"), MaxRadius);
						Issue.Severity = EPCGValidationSeverity::Warning;
						Issue.Category = EPCGValidationCategory::Performance;
						Issue.Location = StreamingSource.Location;
						Report.Issues.Add(Issue);
					}
				}
			}
		}

		// Check for large static meshes that should be streamed
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (!Actor) continue;

			TArray<UStaticMeshComponent*> MeshComponents;
			Actor->GetComponents<UStaticMeshComponent>(MeshComponents);

			for (UStaticMeshComponent* MeshComp : MeshComponents)
			{
				if (!MeshComp || !MeshComp->GetStaticMesh()) continue;

				// Check mesh size for streaming consideration
				FBox BoundingBox = MeshComp->GetStaticMesh()->GetBoundingBox();
				float MeshSize = BoundingBox.GetSize().GetMax();

				if (MeshSize > 10000.0f && !World->IsPartitionedWorld()) // 100m objects should be streamed
				{
					FPCGValidationIssue Issue;
					Issue.Title = TEXT("Large Mesh Without Streaming");
					Issue.Description = FString::Printf(TEXT("Large mesh (%.1f cm) on %s should consider world partition streaming"),
						MeshSize, *Actor->GetName());
					Issue.Severity = EPCGValidationSeverity::Info;
					Issue.Category = EPCGValidationCategory::Streaming;
					Issue.Location = Actor->GetActorLocation();
					Report.Issues.Add(Issue);
				}
			}
		}
	}
}

void UPCGQualityValidator::DetectCacheProblems(FPCGValidationReport& Report)
{
	// Check PCG cache manager if available
	if (CacheManager.IsValid())
	{
		// Get cache statistics
		float HitRatio = CacheManager->GetCacheHitRatio();
		if (HitRatio < 0.7f) // Less than 70% hit ratio
		{
			FPCGValidationIssue Issue;
			Issue.Title = TEXT("Low Cache Hit Ratio");
			Issue.Description = FString::Printf(TEXT("Cache hit ratio is %.1f%%, below recommended 70%%"), HitRatio * 100.0f);
			Issue.Severity = EPCGValidationSeverity::Warning;
			Issue.Category = EPCGValidationCategory::Performance;
			Report.Issues.Add(Issue);
		}

		// Check memory usage
		float MemoryUsageMB = CacheManager->GetMemoryUsage() / (1024.0f * 1024.0f);
		if (MemoryUsageMB > 2048.0f) // More than 2GB cache
		{
			FPCGValidationIssue Issue;
			Issue.Title = TEXT("Excessive Cache Memory");
			Issue.Description = FString::Printf(TEXT("Cache using %.1f MB, consider reducing cache size"), MemoryUsageMB);
			Issue.Severity = EPCGValidationSeverity::Warning;
			Issue.Category = EPCGValidationCategory::Performance;
			Report.Issues.Add(Issue);
		}

		// Check disk usage
		float DiskUsageGB = CacheManager->GetDiskUsage() / (1024.0f * 1024.0f * 1024.0f);
		if (DiskUsageGB > 10.0f) // More than 10GB disk cache
		{
			FPCGValidationIssue Issue;
			Issue.Title = TEXT("Large Disk Cache");
			Issue.Description = FString::Printf(TEXT("Disk cache using %.2f GB, consider cleanup"), DiskUsageGB);
			Issue.Severity = EPCGValidationSeverity::Info;
			Issue.Category = EPCGValidationCategory::Performance;
			Report.Issues.Add(Issue);
		}

		// Check cache fragmentation
		int32 FragmentedEntries = CacheManager->GetFragmentedEntryCount();
		int32 TotalEntries = CacheManager->GetTotalEntryCount();
		if (TotalEntries > 0)
		{
			float FragmentationRatio = (float)FragmentedEntries / TotalEntries;
			if (FragmentationRatio > 0.3f) // More than 30% fragmentation
			{
				FPCGValidationIssue Issue;
				Issue.Title = TEXT("Cache Fragmentation");
				Issue.Description = FString::Printf(TEXT("Cache fragmentation at %.1f%%, consider defragmentation"), FragmentationRatio * 100.0f);
				Issue.Severity = EPCGValidationSeverity::Warning;
				Issue.Category = EPCGValidationCategory::Performance;
				Report.Issues.Add(Issue);
			}
		}
	}
	else
	{
		FPCGValidationIssue Issue;
		Issue.Title = TEXT("Cache Manager Not Available");
		Issue.Description = TEXT("PCG Cache Manager is not available, caching disabled");
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Category = EPCGValidationCategory::Compliance;
		Report.Issues.Add(Issue);
	}
}

// Auto-Fix Functions
bool UPCGQualityValidator::AutoFixLODIssue(const FPCGValidationIssue& Issue)
{
	// Auto-fix LOD issues where possible
	if (Issue.Title.Contains(TEXT("Insufficient LOD Levels")))
	{
		// Find the mesh mentioned in the issue
		FString MeshName;
		if (Issue.Description.Split(TEXT("Mesh "), nullptr, &MeshName))
		{
			MeshName.Split(TEXT(" has"), &MeshName, nullptr);
			
			// Find the static mesh asset
			UStaticMesh* StaticMesh = FindObject<UStaticMesh>(nullptr, *MeshName);
			if (StaticMesh)
			{
				// Generate additional LOD levels
				if (StaticMesh->GetNumLODs() < 3)
				{
					// Use automatic LOD generation
					// UE 5.6: Usar configuração direta de LOD sem StaticMeshEditorSubsystem
					if (StaticMesh->GetNumSourceModels() > 0)
					{
						// Configurar LOD automaticamente
						StaticMesh->GetSourceModel(0).ReductionSettings.PercentTriangles = 0.5f;
					}
					
					// Configure LOD settings
					for (int32 LODIndex = 1; LODIndex < 3; ++LODIndex)
					{
						// UE 5.6: Usar configuração direta de LOD
						if (StaticMesh->GetNumSourceModels() > LODIndex)
						{
							FName LODGroupName = FName("Default");
							FMeshReductionSettings ReductionSettings = StaticMesh->GetReductionSettings(LODIndex);
							ReductionSettings.PercentTriangles = 1.0f / FMath::Pow(2.0f, LODIndex);
							ReductionSettings.PercentVertices = 1.0f / FMath::Pow(2.0f, LODIndex);
						}
					}
					
					// Rebuild the mesh
					StaticMesh->Build();
					return true;
				}
			}
		}
	}
	return false;
}

bool UPCGQualityValidator::AutoFixPerformanceIssue(const FPCGValidationIssue& Issue)
{
	// Auto-fix performance issues where possible
	if (Issue.Title.Contains(TEXT("High Draw Call Count")))
	{
		// Enable instancing for similar meshes
		if (UWorld* World = GetWorld())
		{
			// Find similar static mesh components and enable instancing
			TMap<UStaticMesh*, TArray<UStaticMeshComponent*>> MeshGroups;
			
			for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
			{
				AActor* Actor = *ActorItr;
				if (!Actor) continue;

				TArray<UStaticMeshComponent*> MeshComponents;
				Actor->GetComponents<UStaticMeshComponent>(MeshComponents);

				for (UStaticMeshComponent* MeshComp : MeshComponents)
				{
					if (MeshComp && MeshComp->GetStaticMesh())
					{
						MeshGroups.FindOrAdd(MeshComp->GetStaticMesh()).Add(MeshComp);
					}
				}
			}

			// Enable instancing for groups with multiple instances
			int32 InstancedGroups = 0;
			for (auto& MeshGroup : MeshGroups)
			{
				if (MeshGroup.Value.Num() > 3) // Groups with more than 3 instances
				{
					for (UStaticMeshComponent* MeshComp : MeshGroup.Value)
					{
						// Enable GPU instancing
						MeshComp->SetCastShadow(false); // Reduce shadow draw calls
						MeshComp->SetReceivesDecals(false); // Reduce decal draw calls
					}
					InstancedGroups++;
				}
			}
			
			return InstancedGroups > 0;
		}
	}
	else if (Issue.Title.Contains(TEXT("High Memory Usage")))
	{
		// Force garbage collection
		GEngine->ForceGarbageCollection(true);
		return true;
	}
	return false;
}

bool UPCGQualityValidator::AutoFixMemoryIssue(const FPCGValidationIssue& Issue)
{
	// Auto-fix memory issues where possible
	if (Issue.Title.Contains(TEXT("Memory Leak")))
	{
		// Force garbage collection to clean up unreferenced objects
		GEngine->ForceGarbageCollection(true);
		
		// Clear any cached data that might be holding references
		if (CacheManager.IsValid())
		{
			CacheManager->ClearUnusedEntries();
		}
		
		// Clear texture streaming pool
		// UE 5.6: IStreamingManager mudou para FStreamingManagerCollection
		FStreamingManagerCollection& StreamingManagerCollection = FStreamingManagerCollection::Get();
		IRenderAssetStreamingManager& TextureStreamingManager = StreamingManagerCollection.GetTextureStreamingManager();
		TextureStreamingManager.StreamOutTextureData(1024 * 1024 * 100); // 100MB
		
		return true;
	}
	else if (Issue.Title.Contains(TEXT("Large Texture")))
	{
		// Find and compress large textures
		FString TextureName;
		if (Issue.Description.Split(TEXT("Texture "), nullptr, &TextureName))
		{
			TextureName.Split(TEXT(" is"), &TextureName, nullptr);
			
			UTexture2D* Texture = FindObject<UTexture2D>(nullptr, *TextureName);
			if (Texture)
			{
				// Reduce texture size if too large
				if (Texture->GetSizeX() > 2048 || Texture->GetSizeY() > 2048)
				{
					Texture->MaxTextureSize = 2048;
					Texture->CompressionSettings = TC_Default;
					Texture->UpdateResource();
					return true;
				}
			}
		}
	}
	return false;
}

bool UPCGQualityValidator::AutoFixRenderingIssue(const FPCGValidationIssue& Issue)
{
	// Auto-fix rendering issues where possible
	if (Issue.Title.Contains(TEXT("Missing Material")))
	{
		// Apply default material to components with missing materials
		UMaterial* DefaultMaterial = UMaterial::GetDefaultMaterial(MD_Surface);
		if (DefaultMaterial)
		{
			// UE 5.6: GetWorld() não existe em UObject - usar GWorld ou obter de outro contexto
			if (UWorld* World = GWorld)
			{
				for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
				{
					AActor* Actor = *ActorItr;
					if (!Actor) continue;

					TArray<UMeshComponent*> MeshComponents;
					Actor->GetComponents<UMeshComponent>(MeshComponents);

					for (UMeshComponent* MeshComp : MeshComponents)
					{
						if (MeshComp)
						{
							for (int32 i = 0; i < MeshComp->GetNumMaterials(); ++i)
							{
								if (!MeshComp->GetMaterial(i))
								{
									MeshComp->SetMaterial(i, DefaultMaterial);
								}
							}
						}
					}
				}
				return true;
			}
		}
	}
	else if (Issue.Title.Contains(TEXT("Unlit Material")))
	{
		// Convert unlit materials to lit where appropriate
		FString MaterialName;
		if (Issue.Description.Split(TEXT("Material "), nullptr, &MaterialName))
		{
			MaterialName.Split(TEXT(" is"), &MaterialName, nullptr);
			
			UMaterial* Material = FindObject<UMaterial>(nullptr, *MaterialName);
			if (Material && Material->GetShadingModels().HasShadingModel(MSM_Unlit))
			{
				// Change to default lit shading model
				Material->SetShadingModel(MSM_DefaultLit);
				Material->PostEditChange();
				return true;
			}
		}
	}
	return false;
}

bool UPCGQualityValidator::AutoFixPhysicsIssue(const FPCGValidationIssue& Issue)
{
	// Auto-fix physics issues where possible
	if (Issue.Title.Contains(TEXT("Complex Collision")))
	{
		// Simplify collision for static meshes
		FString MeshName;
		if (Issue.Description.Split(TEXT("Mesh "), nullptr, &MeshName))
		{
			MeshName.Split(TEXT(" has"), &MeshName, nullptr);
			
			UStaticMesh* StaticMesh = FindObject<UStaticMesh>(nullptr, *MeshName);
			if (StaticMesh)
			{
				// Generate simple collision
				StaticMesh->CreateBodySetup();
				if (StaticMesh->GetBodySetup())
				{
					StaticMesh->GetBodySetup()->CollisionTraceFlag = CTF_UseSimpleAsComplex;
					StaticMesh->GetBodySetup()->CreatePhysicsMeshes();
					return true;
				}
			}
		}
	}
	else if (Issue.Title.Contains(TEXT("Missing Collision")))
	{
		// Add collision to meshes that don't have it
		if (UWorld* World = GetWorld())
		{
			for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
			{
				AActor* Actor = *ActorItr;
				if (!Actor) continue;

				TArray<UStaticMeshComponent*> MeshComponents;
				Actor->GetComponents<UStaticMeshComponent>(MeshComponents);

				for (UStaticMeshComponent* MeshComp : MeshComponents)
				{
					if (MeshComp && MeshComp->GetStaticMesh())
					{
						UStaticMesh* StaticMesh = MeshComp->GetStaticMesh();
						if (!StaticMesh->GetBodySetup())
						{
							// Create simple box collision
							StaticMesh->CreateBodySetup();
							if (StaticMesh->GetBodySetup())
							{
								StaticMesh->GetBodySetup()->CollisionTraceFlag = CTF_UseSimpleAsComplex;
								StaticMesh->GetBodySetup()->CreatePhysicsMeshes();
							}
						}
					}
				}
			}
			return true;
		}
	}
	return false;
}

bool UPCGQualityValidator::AutoFixStreamingIssue(const FPCGValidationIssue& Issue)
{
	// Auto-fix streaming issues where possible
	if (Issue.Title.Contains(TEXT("World Partition Not Enabled")))
	{
		// Enable world partition if possible
		if (UWorld* World = GetWorld())
		{
			if (UWorldPartition* WorldPartition = World->GetWorldPartition())
			{
				// World partition already exists - streaming is enabled by default in UE5.6
				UE_LOG(LogTemp, Log, TEXT("World Partition streaming is enabled by default in UE5.6"));
				return true;
			}
		}
	}
	else if (Issue.Title.Contains(TEXT("Large Streaming Distance")))
	{
		// Reduce streaming distance for better performance
		if (UWorld* World = GetWorld())
		{
			if (UWorldPartition* WorldPartition = World->GetWorldPartition())
			{
				// Set more conservative streaming distance
				// UE 5.6: SetDefaultStreamingDistance não existe mais - usar configuração de streaming sources
				UE_LOG(LogTemp, Warning, TEXT("SetDefaultStreamingDistance não disponível no UE 5.6 - configurar streaming sources manualmente"));
				return true;
			}
		}
	}
	else if (Issue.Title.Contains(TEXT("Large Mesh")))
	{
		// Enable mesh streaming for large meshes
		FString MeshName;
		if (Issue.Description.Split(TEXT("Mesh "), nullptr, &MeshName))
		{
			MeshName.Split(TEXT(" is"), &MeshName, nullptr);
			
			UStaticMesh* StaticMesh = FindObject<UStaticMesh>(nullptr, *MeshName);
			if (StaticMesh)
			{
				// Enable mesh streaming
				StaticMesh->bAllowCPUAccess = false; // Reduce memory usage
				StaticMesh->NeverStream = false; // Allow streaming
				return true;
			}
		}
	}
	else if (Issue.Title.Contains(TEXT("Texture Streaming")))
	{
		// Optimize texture streaming settings
		// UE 5.6: IStreamingManager mudou para FStreamingManagerCollection
		FStreamingManagerCollection& StreamingManagerCollection = FStreamingManagerCollection::Get();
		IRenderAssetStreamingManager& TextureStreaming = StreamingManagerCollection.GetTextureStreamingManager();

		// Force texture streaming update
		TextureStreaming.UpdateResourceStreaming(0.0f, true);
		return true;
	}
	return false;
}

// Utility Functions
float UPCGQualityValidator::CalculateCategoryScore(EPCGValidationCategory Category)
{
	// Calculate score for specific category
	float Score = 100.0f; // Start with perfect score

	// Reduce score based on issues in this category
	TArray<FPCGValidationIssue> CategoryIssues = GetIssuesByCategory(Category);
	for (const FPCGValidationIssue& Issue : CategoryIssues)
	{
		switch (Issue.Severity)
		{
		case EPCGValidationSeverity::Critical:
			Score -= 25.0f;
			break;
		case EPCGValidationSeverity::Error:
			Score -= 15.0f;
			break;
		case EPCGValidationSeverity::Warning:
			Score -= 5.0f;
			break;
		case EPCGValidationSeverity::Info:
			Score -= 1.0f;
			break;
		}
	}

	return FMath::Max(0.0f, Score);
}

EPCGQualityLevel UPCGQualityValidator::DetermineQualityLevel(float Score)
{
	if (Score >= 90.0f)
	{
		return EPCGQualityLevel::Cinematic;
	}
	else if (Score >= 80.0f)
	{
		return EPCGQualityLevel::Ultra;
	}
	else if (Score >= 70.0f)
	{
		return EPCGQualityLevel::High;
	}
	else if (Score >= 60.0f)
	{
		return EPCGQualityLevel::Medium;
	}
	else
	{
		return EPCGQualityLevel::Low;
	}
}

void UPCGQualityValidator::UpdateQualityMetrics()
{
	// Update current quality metrics
	FPCGQualityMetrics NewMetrics = GetCurrentMetrics();

	// Check for quality level changes
	EPCGQualityLevel OldLevel = AssessOverallQuality();
	EPCGQualityLevel NewLevel = DetermineQualityLevel(CalculateQualityScore());

	if (OldLevel != NewLevel)
	{
		OnQualityChanged.Broadcast(OldLevel, NewLevel);
	}
}

void UPCGQualityValidator::CleanupOldReports()
{
	// Keep only the last 100 reports
	const int32 MaxReports = 100;
	if (ValidationHistory.Num() > MaxReports)
	{
		int32 ReportsToRemove = ValidationHistory.Num() - MaxReports;
		ValidationHistory.RemoveAt(0, ReportsToRemove);
	}
}

// Timer Callbacks
void UPCGQualityValidator::OnAutomaticValidationTimer()
{
	if (bAutomaticValidationEnabled)
	{
		// Start automatic validation
		StartValidation(ValidationConfig);
	}
}

// Integration Helpers
void UPCGQualityValidator::InitializeIntegrations()
{
	// Find and cache references to other PCG systems
	if (UWorld* World = GetWorld())
	{
		// Find World Partition Manager
		for (TActorIterator<APCGWorldPartitionManager> ActorItr(World); ActorItr; ++ActorItr)
		{
			WorldPartitionManager = *ActorItr;
			break;
		}

		// Find Nanite Optimizer
		for (TActorIterator<APCGNaniteOptimizer> ActorItr(World); ActorItr; ++ActorItr)
		{
			NaniteOptimizer = *ActorItr;
			break;
		}

		// Find Lumen Integrator
		for (TActorIterator<APCGLumenIntegrator> ActorItr(World); ActorItr; ++ActorItr)
		{
			LumenIntegrator = *ActorItr;
			break;
		}

		// Find Streaming Manager
		for (TActorIterator<APCGStreamingManager> ActorItr(World); ActorItr; ++ActorItr)
		{
			StreamingManager = *ActorItr;
			break;
		}

		// Find Performance Profiler
		for (TObjectIterator<UPCGPerformanceProfiler> ObjItr; ObjItr; ++ObjItr)
		{
			PerformanceProfiler = *ObjItr;
			break;
		}

		// Find Cache Manager
		for (TActorIterator<APCGCacheManager> ActorItr(World); ActorItr; ++ActorItr)
		{
			CacheManager = *ActorItr;
			break;
		}

		// Find Chaos Integrator
		for (TActorIterator<APCGChaosIntegrator> ActorItr(World); ActorItr; ++ActorItr)
		{
			ChaosIntegrator = *ActorItr;
			break;
		}
	}
}

void UPCGQualityValidator::ValidateIntegrationHealth()
{
	// Check if all integrations are healthy
	FPCGValidationReport IntegrationReport;
	IntegrationReport.ReportID = FGuid::NewGuid().ToString();
	IntegrationReport.GenerationTime = FDateTime::Now();

	// Check each integration
	if (!WorldPartitionManager.IsValid())
	{
		FPCGValidationIssue Issue;
		Issue.Title = TEXT("World Partition Manager Not Found");
		Issue.Description = TEXT("APCGWorldPartitionManager is not available in the world");
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Category = EPCGValidationCategory::Compliance;
		IntegrationReport.Issues.Add(Issue);
	}

	if (!NaniteOptimizer.IsValid())
	{
		FPCGValidationIssue Issue;
		Issue.Title = TEXT("Nanite Optimizer Not Found");
		Issue.Description = TEXT("APCGNaniteOptimizer is not available in the world");
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Category = EPCGValidationCategory::Compliance;
		IntegrationReport.Issues.Add(Issue);
	}

	// Store integration report
	ValidationHistory.Add(IntegrationReport);
}

// Additional helper functions for specific validation tasks
float UPCGQualityValidator::CalculateLODCoverage()
{
	// Calculate LOD coverage across all meshes
	float TotalMeshes = 0.0f;
	float MeshesWithLOD = 0.0f;
	
	if (UWorld* World = GetWorld())
	{
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (!Actor) continue;

			TArray<UStaticMeshComponent*> MeshComponents;
			Actor->GetComponents<UStaticMeshComponent>(MeshComponents);

			for (UStaticMeshComponent* MeshComp : MeshComponents)
			{
				if (MeshComp && MeshComp->GetStaticMesh())
				{
					TotalMeshes += 1.0f;
					if (MeshComp->GetStaticMesh()->GetNumLODs() > 1)
					{
						MeshesWithLOD += 1.0f;
					}
				}
			}
		}
	}
	
	return TotalMeshes > 0.0f ? (MeshesWithLOD / TotalMeshes) * 100.0f : 0.0f;
}

float UPCGQualityValidator::CalculateTextureQuality()
{
	// Calculate average texture quality
	float QualitySum = 0.0f;
	int32 TextureCount = 0;
	
	for (TObjectIterator<UTexture2D> TextureItr; TextureItr; ++TextureItr)
	{
		UTexture2D* Texture = *TextureItr;
		if (Texture && !Texture->IsTemplate())
		{
			TextureCount++;
			
			// Calculate quality based on compression and size
			float TextureQuality = 100.0f;
			
			// Reduce quality for overly compressed textures
			if (Texture->CompressionSettings == TC_BC7)
			{
				TextureQuality = 95.0f; // High quality
			}
			else if (Texture->CompressionSettings == TC_Default)
			{
				TextureQuality = 85.0f; // Medium quality
			}
			// UE 5.6: TC_BC1 não existe mais - usar TC_LQ para baixa qualidade
			else if (Texture->CompressionSettings == TC_LQ)
			{
				TextureQuality = 70.0f; // Lower quality
			}
			
			// Reduce quality for very small textures
			if (Texture->GetSizeX() < 256 || Texture->GetSizeY() < 256)
			{
				TextureQuality *= 0.8f;
			}
			
			QualitySum += TextureQuality;
		}
	}
	
	return TextureCount > 0 ? QualitySum / TextureCount : 0.0f;
}

float UPCGQualityValidator::CalculateGeometryComplexity()
{
	// Calculate geometry complexity metrics
	float TotalTriangles = 0.0f;
	int32 MeshCount = 0;
	
	if (UWorld* World = GetWorld())
	{
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (!Actor) continue;

			TArray<UStaticMeshComponent*> MeshComponents;
			Actor->GetComponents<UStaticMeshComponent>(MeshComponents);

			for (UStaticMeshComponent* MeshComp : MeshComponents)
			{
				if (MeshComp && MeshComp->GetStaticMesh())
				{
					MeshCount++;
					UStaticMesh* StaticMesh = MeshComp->GetStaticMesh();
					if (StaticMesh->GetRenderData() && StaticMesh->GetRenderData()->LODResources.Num() > 0)
					{
						TotalTriangles += StaticMesh->GetRenderData()->LODResources[0].GetNumTriangles();
					}
				}
			}
		}
	}
	
	// Return complexity score (lower triangle count = higher score)
	float AvgTriangles = MeshCount > 0 ? TotalTriangles / MeshCount : 0.0f;
	float ComplexityScore = 100.0f - FMath::Clamp(AvgTriangles / 1000.0f, 0.0f, 100.0f);
	return FMath::Max(0.0f, ComplexityScore);
}

float UPCGQualityValidator::CalculateLightingQuality()
{
	// Calculate lighting quality metrics
	float QualityScore = 100.0f;
	int32 LightCount = 0;
	int32 StaticLightCount = 0;
	
	if (UWorld* World = GetWorld())
	{
		// Count lights and their types
		for (TActorIterator<ALight> LightItr(World); LightItr; ++LightItr)
		{
			ALight* Light = *LightItr;
			if (Light)
			{
				LightCount++;
				if (Light->GetLightComponent() && Light->GetLightComponent()->Mobility == EComponentMobility::Static)
				{
					StaticLightCount++;
				}
			}
		}
		
		// Check for proper lighting setup
		if (LightCount == 0)
		{
			QualityScore -= 30.0f; // No lights
		}
		else
		{
			// Prefer static lights for better performance
			float StaticRatio = LightCount > 0 ? (float)StaticLightCount / LightCount : 0.0f;
			QualityScore = 60.0f + (StaticRatio * 40.0f); // 60-100 based on static light ratio
		}
		
		// Check for lightmaps
		bool HasLightmaps = false;
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (!Actor) continue;

			TArray<UStaticMeshComponent*> MeshComponents;
			Actor->GetComponents<UStaticMeshComponent>(MeshComponents);

			for (UStaticMeshComponent* MeshComp : MeshComponents)
			{
				if (MeshComp && MeshComp->GetStaticMesh())
				{
					if (MeshComp->GetStaticMesh()->GetLightMapResolution() > 0)
					{
						HasLightmaps = true;
						break;
					}
				}
			}
			if (HasLightmaps) break;
		}
		
		if (!HasLightmaps && StaticLightCount > 0)
		{
			QualityScore -= 20.0f; // Static lights without lightmaps
		}
	}
	
	return FMath::Max(0.0f, QualityScore);
}

// Component-specific validation functions
void UPCGQualityValidator::ValidateComponentGeometry(UPCGComponent* Component, FPCGValidationReport& Report)
{
	// Validate component geometry
	if (!Component)
	{
		return;
	}
	
	// Check component bounds
	FBox ComponentBounds = Component->GetGridBounds();
	if (ComponentBounds.GetSize().Size() > 100000.0f) // 1km in UE units
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Geometry;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Large Component Bounds");
		Issue.Description = FString::Printf(TEXT("PCG Component '%s' has very large bounds (%.2f units)"), *Component->GetName(), ComponentBounds.GetSize().Size());
		Issue.AffectedObject = Component;
		Issue.Location = Component->GetOwner() ? Component->GetOwner()->GetActorLocation() : FVector::ZeroVector;
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
	
	// Check for degenerate bounds
	if (ComponentBounds.GetSize().X < 1.0f || ComponentBounds.GetSize().Y < 1.0f || ComponentBounds.GetSize().Z < 1.0f)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Geometry;
		Issue.Severity = EPCGValidationSeverity::Error;
		Issue.Title = TEXT("Degenerate Component Bounds");
		Issue.Description = FString::Printf(TEXT("PCG Component '%s' has degenerate bounds"), *Component->GetName());
		Issue.AffectedObject = Component;
		Issue.Location = Component->GetOwner() ? Component->GetOwner()->GetActorLocation() : FVector::ZeroVector;
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
}

void UPCGQualityValidator::ValidateComponentPerformance(UPCGComponent* Component, FPCGValidationReport& Report)
{
	// Validate component performance
	if (!Component)
	{
		return;
	}
	
	// Check if component has valid graph
	if (!Component->GetGraph())
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Error;
		Issue.Title = TEXT("Missing PCG Graph");
		Issue.Description = FString::Printf(TEXT("PCG Component '%s' has no graph assigned"), *Component->GetName());
		Issue.AffectedObject = Component;
		Issue.Location = Component->GetOwner() ? Component->GetOwner()->GetActorLocation() : FVector::ZeroVector;
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
	
	// Check if component is enabled but not generating
	if (Component->IsComponentTickEnabled() && !Component->IsGenerating())
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Inactive PCG Component");
		Issue.Description = FString::Printf(TEXT("PCG Component '%s' is enabled but not generating"), *Component->GetName());
		Issue.AffectedObject = Component;
		Issue.Location = Component->GetOwner() ? Component->GetOwner()->GetActorLocation() : FVector::ZeroVector;
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
}

void UPCGQualityValidator::ValidateComponentSettings(UPCGComponent* Component, FPCGValidationReport& Report)
{
	// Validate component settings
	if (!Component)
	{
		return;
	}
	
	// Check generation trigger settings (UE 5.6 compatible)
	if (Component->GenerationTrigger == EPCGComponentGenerationTrigger::GenerateOnDemand)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Info;
		Issue.Title = TEXT("On-Demand Generation");
		Issue.Description = FString::Printf(TEXT("PCG Component '%s' is set to generate on demand"), *Component->GetName());
		Issue.AffectedObject = Component;
		Issue.Location = Component->GetOwner() ? Component->GetOwner()->GetActorLocation() : FVector::ZeroVector;
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
	
	// Check if component has input data (UE 5.6 compatible)
	if (Component->InputType == EPCGComponentInput::Actor && !Component->GetOwner())
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Geometry;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("No Input Data");
		Issue.Description = FString::Printf(TEXT("PCG Component '%s' has no input data type specified"), *Component->GetName());
		Issue.AffectedObject = Component;
		Issue.Location = Component->GetOwner() ? Component->GetOwner()->GetActorLocation() : FVector::ZeroVector;
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
}

// Graph-specific validation functions
void UPCGQualityValidator::ValidateGraphComplexity(UPCGGraph* Graph, FPCGValidationReport& Report)
{
	// Validate graph complexity
	if (!Graph)
	{
		return;
	}
	
	// Check number of nodes
	TArray<UPCGNode*> GraphNodes = Graph->GetNodes();
	if (GraphNodes.Num() > 100)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("High Graph Complexity");
		Issue.Description = FString::Printf(TEXT("PCG Graph '%s' has %d nodes (>100), which may impact performance"), *Graph->GetName(), GraphNodes.Num());
		Issue.AffectedObject = Graph;
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
	
	// Check for deeply nested graphs
	int32 MaxDepth = 0;
	for (UPCGNode* Node : GraphNodes)
	{
		if (Node)
		{
			int32 NodeDepth = CalculateNodeDepth(Node);
			MaxDepth = FMath::Max(MaxDepth, NodeDepth);
		}
	}
	
	if (MaxDepth > 20)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Deep Graph Nesting");
		Issue.Description = FString::Printf(TEXT("PCG Graph '%s' has maximum depth of %d levels"), *Graph->GetName(), MaxDepth);
		Issue.AffectedObject = Graph;
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
}

void UPCGQualityValidator::ValidateGraphConnections(UPCGGraph* Graph, FPCGValidationReport& Report)
{
	// Validate graph connections
	if (!Graph)
	{
		return;
	}
	
	TArray<UPCGNode*> GraphNodes = Graph->GetNodes();
	
	// Check for disconnected nodes
	int32 DisconnectedNodes = 0;
	for (UPCGNode* Node : GraphNodes)
	{
		if (Node)
		{
			bool bHasConnections = false;
			
			// Check input pins
			TArray<UPCGPin*> InputPins = Node->GetInputPins();
			for (UPCGPin* Pin : InputPins)
			{
				if (Pin && Pin->EdgeCount() > 0)
				{
					bHasConnections = true;
					break;
				}
			}
			
			// Check output pins if no input connections
			if (!bHasConnections)
			{
				TArray<UPCGPin*> OutputPins = Node->GetOutputPins();
				for (UPCGPin* Pin : OutputPins)
				{
					if (Pin && Pin->EdgeCount() > 0)
					{
						bHasConnections = true;
						break;
					}
				}
			}
			
			if (!bHasConnections)
			{
				DisconnectedNodes++;
			}
		}
	}
	
	if (DisconnectedNodes > 0)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Quality;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Disconnected Nodes");
		Issue.Description = FString::Printf(TEXT("PCG Graph '%s' has %d disconnected nodes"), *Graph->GetName(), DisconnectedNodes);
		Issue.AffectedObject = Graph;
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
}

void UPCGQualityValidator::ValidateGraphSettings(UPCGGraph* Graph, FPCGValidationReport& Report)
{
	// Validate graph settings
	if (!Graph)
	{
		return;
	}
	
	// Check if graph has input/output nodes
	TArray<UPCGNode*> GraphNodes = Graph->GetNodes();
	bool bHasInputNode = false;
	bool bHasOutputNode = false;
	
	for (UPCGNode* Node : GraphNodes)
	{
		if (Node)
		{
			if (Node->GetSettings() && Node->GetSettings()->GetClass()->GetName().Contains(TEXT("Input")))
			{
				bHasInputNode = true;
			}
			if (Node->GetSettings() && Node->GetSettings()->GetClass()->GetName().Contains(TEXT("Output")))
			{
				bHasOutputNode = true;
			}
		}
	}
	
	if (!bHasInputNode)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Quality;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Missing Input Node");
		Issue.Description = FString::Printf(TEXT("PCG Graph '%s' has no input node"), *Graph->GetName());
		Issue.AffectedObject = Graph;
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
	
	if (!bHasOutputNode)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Quality;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Missing Output Node");
		Issue.Description = FString::Printf(TEXT("PCG Graph '%s' has no output node"), *Graph->GetName());
		Issue.AffectedObject = Graph;
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
}

// System-specific validation functions
void UPCGQualityValidator::ValidateWorldPartitionConfiguration(FPCGValidationReport& Report)
{
	// Validate World Partition configuration
	UWorld* World = GetWorld();
	if (!World)
	{
		return;
	}
	
	// Check if World Partition is enabled
	UWorldPartition* WorldPartition = World->GetWorldPartition();
	if (!WorldPartition)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Compliance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("World Partition Not Enabled");
		Issue.Description = TEXT("World Partition is not enabled, which may impact PCG performance in large worlds");
		Issue.AffectedObject = World;
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
		return;
	}
	
	// Check streaming distance settings
	// UE 5.6: GetStreamingPolicy não existe mais - usar configuração direta
	float StreamingDistance = 50000.0f; // Valor padrão em UE units
	UE_LOG(LogTemp, Warning, TEXT("GetStreamingPolicy não disponível no UE 5.6 - usando valor padrão"));
	if (StreamingDistance < 10000.0f) // 100m minimum
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Low Streaming Distance");
		Issue.Description = FString::Printf(TEXT("World Partition streaming distance is %.1fm, consider increasing for better PCG performance"), StreamingDistance / 100.0f);
		Issue.AffectedObject = World;
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
}

void UPCGQualityValidator::ValidateWorldPartitionPerformance(FPCGValidationReport& Report)
{
	// Validate World Partition performance
	UWorld* World = GetWorld();
	if (!World || !World->GetWorldPartition())
	{
		return;
	}
	
	UWorldPartition* WorldPartition = World->GetWorldPartition();
	
	// Check loaded cell count
	// UE 5.6: GetStreamingPolicy não existe mais - usar valor estimado
	int32 LoadedCells = 100; // Valor estimado
	UE_LOG(LogTemp, Warning, TEXT("GetLoadedCellCount não disponível no UE 5.6 - usando valor estimado"));
	if (LoadedCells > 100)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("High Cell Count");
		Issue.Description = FString::Printf(TEXT("World Partition has %d loaded cells, which may impact performance"), LoadedCells);
		Issue.AffectedObject = World;
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
	
	// Check memory usage
	// UE 5.6: Usar GetResourceSizeEx que é a API padrão para obter uso de memória
	FResourceSizeEx ResourceSize;
	WorldPartition->GetResourceSizeEx(ResourceSize);
	size_t MemoryUsage = ResourceSize.GetTotalMemoryBytes();
	if (MemoryUsage > 1024 * 1024 * 1024) // 1GB
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Memory;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("High Memory Usage");
		Issue.Description = FString::Printf(TEXT("World Partition is using %.1f MB of memory"), MemoryUsage / (1024.0f * 1024.0f));
		Issue.AffectedObject = World;
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
}

void UPCGQualityValidator::ValidateNaniteConfiguration(FPCGValidationReport& Report)
{
	// Validate Nanite configuration
	if (!NaniteOptimizer.IsValid())
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Compliance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Nanite Optimizer Not Available");
		Issue.Description = TEXT("Nanite optimizer is not initialized, Nanite features may not work properly");
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
		return;
	}
	
	// Check if Nanite is enabled in project settings
	const URendererSettings* RendererSettings = GetDefault<URendererSettings>();
	// UE 5.6: bEnableNanite mudou para bNanite
	if (!RendererSettings->bNanite)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Compliance;
		Issue.Severity = EPCGValidationSeverity::Error;
		Issue.Title = TEXT("Nanite Disabled");
		Issue.Description = TEXT("Nanite is disabled in project settings, enable it for better mesh performance");
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	// Check Nanite triangle threshold - UE 5.6: usar CVar do console
	IConsoleVariable* NaniteTriangleCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Nanite.MinTriangles"));
	if (NaniteTriangleCVar)
	{
		int32 NaniteTriangleThreshold = NaniteTriangleCVar->GetInt();
		if (NaniteTriangleThreshold < 1000)
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Performance;
			Issue.Severity = EPCGValidationSeverity::Info;
			Issue.Title = TEXT("Low Nanite Triangle Threshold");
			Issue.Description = FString::Printf(TEXT("Nanite triangle threshold is %d, consider increasing for better performance"), NaniteTriangleThreshold);
			Issue.bCanAutoFix = true;
			Report.Issues.Add(Issue);
		}
	}
}

void UPCGQualityValidator::ValidateNanitePerformance(FPCGValidationReport& Report)
{
	// Validate Nanite performance
	if (!NaniteOptimizer.IsValid())
	{
		return;
	}
	
	// Check Nanite mesh count
	int32 NaniteMeshCount = 0;
	for (TObjectIterator<UStaticMesh> MeshItr; MeshItr; ++MeshItr)
	{
		UStaticMesh* Mesh = *MeshItr;
		if (Mesh)
		{
#if WITH_EDITOR
			if (GEditor)
			{
				// UE 5.6: Usar configuração direta de Nanite através do StaticMesh
				if (Mesh && Mesh->GetRenderData())
				{
					// Acessar configurações Nanite através do campo público NaniteSettings
					if (Mesh->NaniteSettings.bEnabled)
					{
						NaniteMeshCount++;
					}
				}
			}
#endif
		}
	}
	
	if (NaniteMeshCount > 1000)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("High Nanite Mesh Count");
		Issue.Description = FString::Printf(TEXT("Found %d Nanite-enabled meshes, consider optimizing for better performance"), NaniteMeshCount);
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
	
	// Check Nanite cluster size
	const URendererSettings* RendererSettings = GetDefault<URendererSettings>();
	// UE 5.6: NaniteClusterSize não existe mais - usar CVar do console
	IConsoleVariable* NaniteClusterCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Nanite.MaxCandidateClusters"));
	if (NaniteClusterCVar)
	{
		int32 NaniteClusterSize = NaniteClusterCVar->GetInt();
		if (NaniteClusterSize > 128)
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Performance;
			Issue.Severity = EPCGValidationSeverity::Info;
			Issue.Title = TEXT("Large Nanite Cluster Size");
			Issue.Description = FString::Printf(TEXT("Nanite cluster size is %d, consider reducing for better performance"), NaniteClusterSize);
			Issue.bCanAutoFix = true;
			Report.Issues.Add(Issue);
		}
	}
}

void UPCGQualityValidator::ValidateLumenConfiguration(FPCGValidationReport& Report)
{
	// Validate Lumen configuration
	if (!LumenIntegrator.IsValid())
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Compliance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Lumen Integrator Not Available");
		Issue.Description = TEXT("Lumen integrator is not initialized, Lumen features may not work properly");
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
		return;
	}
	
	// Check if Lumen is enabled in project settings
	const URendererSettings* RendererSettings = GetDefault<URendererSettings>();
	// UE 5.6: bEnableLumen não existe mais - verificar se Lumen está habilitado via CVar
	IConsoleVariable* LumenEnabledCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Lumen.Enabled"));
	bool bLumenEnabled = LumenEnabledCVar ? LumenEnabledCVar->GetBool() : false;
	if (!bLumenEnabled)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Compliance;
		Issue.Severity = EPCGValidationSeverity::Error;
		Issue.Title = TEXT("Lumen Disabled");
		Issue.Description = TEXT("Lumen is disabled in project settings, enable it for better global illumination");
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	// Check Lumen scene detail
	// UE 5.6: LumenSceneDetail não existe mais - usar CVar do console
	IConsoleVariable* LumenSceneDetailCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Lumen.SceneDetail"));
	if (LumenSceneDetailCVar)
	{
		float LumenSceneDetail = LumenSceneDetailCVar->GetFloat();
		if (LumenSceneDetail < 0.5f)
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Quality;
			Issue.Severity = EPCGValidationSeverity::Info;
			Issue.Title = TEXT("Low Lumen Scene Detail");
			Issue.Description = FString::Printf(TEXT("Lumen scene detail is %.2f, consider increasing for better quality"), LumenSceneDetail);
			Issue.bCanAutoFix = true;
			Report.Issues.Add(Issue);
		}
	}
}

void UPCGQualityValidator::ValidateLumenPerformance(FPCGValidationReport& Report)
{
	// Validate Lumen performance
	if (!LumenIntegrator.IsValid())
	{
		return;
	}
	
	// Check Lumen surface cache size - UE 5.6: usar CVar do console
	IConsoleVariable* LumenSurfaceCacheCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Lumen.SurfaceCache.Resolution"));
	if (LumenSurfaceCacheCVar)
	{
		int32 LumenSurfaceCacheResolution = LumenSurfaceCacheCVar->GetInt();
		if (LumenSurfaceCacheResolution > 2048)
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Performance;
			Issue.Severity = EPCGValidationSeverity::Warning;
			Issue.Title = TEXT("High Lumen Surface Cache Resolution");
			Issue.Description = FString::Printf(TEXT("Lumen surface cache resolution is %d, consider reducing for better performance"), LumenSurfaceCacheResolution);
			Issue.bCanAutoFix = true;
			Report.Issues.Add(Issue);
		}
	}
	
	// Check Lumen trace distance
	// UE 5.6: LumenMaxTraceDistance não existe mais - usar CVar do console
	IConsoleVariable* LumenMaxTraceCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Lumen.MaxTraceDistance"));
	if (LumenMaxTraceCVar)
	{
		float LumenMaxTraceDistance = LumenMaxTraceCVar->GetFloat();
		if (LumenMaxTraceDistance > 100000.0f) // 1km
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Performance;
			Issue.Severity = EPCGValidationSeverity::Warning;
			Issue.Title = TEXT("High Lumen Trace Distance");
			Issue.Description = FString::Printf(TEXT("Lumen max trace distance is %.1fm, consider reducing for better performance"), LumenMaxTraceDistance / 100.0f);
			Issue.bCanAutoFix = true;
			Report.Issues.Add(Issue);
		}
	}
	
	// Check Lumen reflection quality - UE 5.6: usar CVar do console
	IConsoleVariable* LumenReflectionQualityCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Lumen.Reflections.Quality"));
	if (LumenReflectionQualityCVar)
	{
		float LumenReflectionQuality = LumenReflectionQualityCVar->GetFloat();
		if (LumenReflectionQuality > 2.0f)
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Performance;
			Issue.Severity = EPCGValidationSeverity::Info;
			Issue.Title = TEXT("High Lumen Reflection Quality");
			Issue.Description = FString::Printf(TEXT("Lumen reflection quality is %.1f, consider reducing for better performance"), LumenReflectionQuality);
			Issue.bCanAutoFix = true;
			Report.Issues.Add(Issue);
		}
	}
}

// Additional validation functions - complete implementations for UE5.6
void UPCGQualityValidator::ValidateMeshComplexity(FPCGValidationReport& Report)
{
	// Validate mesh complexity across all static meshes
	int32 HighComplexityMeshes = 0;
	for (TObjectIterator<UStaticMesh> MeshItr; MeshItr; ++MeshItr)
	{
		UStaticMesh* Mesh = *MeshItr;
		if (Mesh && Mesh->GetRenderData() && Mesh->GetRenderData()->LODResources.Num() > 0)
		{
			const FStaticMeshLODResources& LODResource = Mesh->GetRenderData()->LODResources[0];
			int32 TriangleCount = LODResource.GetNumTriangles();
			
			if (TriangleCount > 10000)
			{
				HighComplexityMeshes++;
				
				FPCGValidationIssue Issue;
				Issue.Category = EPCGValidationCategory::Performance;
				Issue.Severity = EPCGValidationSeverity::Warning;
				Issue.Title = TEXT("High Mesh Complexity");
				Issue.Description = FString::Printf(TEXT("Static Mesh '%s' has %d triangles (>10k)"), *Mesh->GetName(), TriangleCount);
				Issue.AffectedObject = Mesh;
				Issue.bCanAutoFix = true;
				Report.Issues.Add(Issue);
			}
		}
	}
	
	if (HighComplexityMeshes > 50)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Error;
		Issue.Title = TEXT("Too Many High Complexity Meshes");
		Issue.Description = FString::Printf(TEXT("Found %d high complexity meshes, consider optimization"), HighComplexityMeshes);
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
}
void UPCGQualityValidator::DetectDegenerateGeometry(FPCGValidationReport& Report)
{
	// Detect degenerate geometry in static meshes
	int32 DegenerateMeshes = 0;
	for (TObjectIterator<UStaticMesh> MeshItr; MeshItr; ++MeshItr)
	{
		UStaticMesh* Mesh = *MeshItr;
		if (Mesh && Mesh->GetRenderData() && Mesh->GetRenderData()->LODResources.Num() > 0)
		{
			const FStaticMeshLODResources& LODResource = Mesh->GetRenderData()->LODResources[0];
			
			// Check for zero-area triangles
			bool bHasDegenerateTriangles = false;
			const FPositionVertexBuffer& PositionBuffer = LODResource.VertexBuffers.PositionVertexBuffer;
			const FRawStaticIndexBuffer& IndexBuffer = LODResource.IndexBuffer;
			
			for (int32 TriIndex = 0; TriIndex < (int32)(IndexBuffer.GetNumIndices() / 3); TriIndex++)
			{
				uint32 Index0 = IndexBuffer.GetIndex(TriIndex * 3 + 0);
				uint32 Index1 = IndexBuffer.GetIndex(TriIndex * 3 + 1);
				uint32 Index2 = IndexBuffer.GetIndex(TriIndex * 3 + 2);
				
				FVector3f V0 = PositionBuffer.VertexPosition(Index0);
				FVector3f V1 = PositionBuffer.VertexPosition(Index1);
				FVector3f V2 = PositionBuffer.VertexPosition(Index2);
				
				// Calculate triangle area
				FVector3f Edge1 = V1 - V0;
				FVector3f Edge2 = V2 - V0;
				float Area = 0.5f * FVector3f::CrossProduct(Edge1, Edge2).Size();
				
				if (Area < SMALL_NUMBER)
				{
					bHasDegenerateTriangles = true;
					break;
				}
			}
			
			if (bHasDegenerateTriangles)
			{
				DegenerateMeshes++;
				
				FPCGValidationIssue Issue;
				Issue.Category = EPCGValidationCategory::Quality;
				Issue.Severity = EPCGValidationSeverity::Error;
				Issue.Title = TEXT("Degenerate Geometry");
				Issue.Description = FString::Printf(TEXT("Static Mesh '%s' contains degenerate triangles"), *Mesh->GetName());
				Issue.AffectedObject = Mesh;
				Issue.bCanAutoFix = true;
				Report.Issues.Add(Issue);
			}
		}
	}
}
void UPCGQualityValidator::ValidateUVMapping(FPCGValidationReport& Report)
{
	// Validate UV mapping for static meshes
	for (TObjectIterator<UStaticMesh> MeshItr; MeshItr; ++MeshItr)
	{
		UStaticMesh* Mesh = *MeshItr;
		if (Mesh && Mesh->GetRenderData() && Mesh->GetRenderData()->LODResources.Num() > 0)
		{
			const FStaticMeshLODResources& LODResource = Mesh->GetRenderData()->LODResources[0];
			
			// Check if mesh has UV coordinates
			if (LODResource.VertexBuffers.StaticMeshVertexBuffer.GetNumTexCoords() == 0)
			{
				FPCGValidationIssue Issue;
				Issue.Category = EPCGValidationCategory::Quality;
				Issue.Severity = EPCGValidationSeverity::Warning;
				Issue.Title = TEXT("Missing UV Coordinates");
				Issue.Description = FString::Printf(TEXT("Static Mesh '%s' has no UV coordinates"), *Mesh->GetName());
				Issue.AffectedObject = Mesh;
				Issue.bCanAutoFix = false;
				Report.Issues.Add(Issue);
			}
			
			// Check for UV coordinates outside [0,1] range
			bool bHasInvalidUVs = false;
			for (uint32 VertexIndex = 0; VertexIndex < LODResource.VertexBuffers.StaticMeshVertexBuffer.GetNumVertices(); VertexIndex++)
			{
				FVector2f UV = LODResource.VertexBuffers.StaticMeshVertexBuffer.GetVertexUV(VertexIndex, 0);
				if (UV.X < 0.0f || UV.X > 1.0f || UV.Y < 0.0f || UV.Y > 1.0f)
				{
					bHasInvalidUVs = true;
					break;
				}
			}
			
			if (bHasInvalidUVs)
			{
				FPCGValidationIssue Issue;
				Issue.Category = EPCGValidationCategory::Quality;
				Issue.Severity = EPCGValidationSeverity::Warning;
				Issue.Title = TEXT("UV Coordinates Out of Range");
				Issue.Description = FString::Printf(TEXT("Static Mesh '%s' has UV coordinates outside [0,1] range"), *Mesh->GetName());
				Issue.AffectedObject = Mesh;
				Issue.bCanAutoFix = false;
				Report.Issues.Add(Issue);
			}
		}
	}
}
void UPCGQualityValidator::ValidateFrameRate(FPCGValidationReport& Report)
{
	// Validate frame rate performance
	if (UEngine* Engine = GEngine)
	{
		// Use world delta time instead of engine delta time
		float DeltaTime = GetWorld() ? GetWorld()->GetDeltaSeconds() : 0.016f;
		float CurrentFPS = (DeltaTime > 0.0f) ? (1.0f / DeltaTime) : 60.0f;
		float TargetFPS = 60.0f; // Target 60 FPS
		
		if (CurrentFPS < TargetFPS * 0.8f) // Below 80% of target
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Performance;
			Issue.Severity = EPCGValidationSeverity::Warning;
			Issue.Title = TEXT("Low Frame Rate");
			Issue.Description = FString::Printf(TEXT("Current FPS: %.1f, Target: %.1f"), CurrentFPS, TargetFPS);
			Issue.bCanAutoFix = true;
			Report.Issues.Add(Issue);
		}
		
		if (CurrentFPS < TargetFPS * 0.5f) // Below 50% of target
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Performance;
			Issue.Severity = EPCGValidationSeverity::Error;
			Issue.Title = TEXT("Critical Frame Rate");
			Issue.Description = FString::Printf(TEXT("Critical FPS: %.1f, requires optimization"), CurrentFPS);
			Issue.bCanAutoFix = false;
			Report.Issues.Add(Issue);
		}
	}
}

void UPCGQualityValidator::ValidateDrawCalls(FPCGValidationReport& Report)
{
	// Validate draw call count
	if (UWorld* World = GWorld)
	{
		if (UGameViewportClient* ViewportClient = World->GetGameViewport())
		{
			// Estimate draw calls based on visible components
			int32 EstimatedDrawCalls = 0;
			
			for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
			{
				AActor* Actor = *ActorItr;
				if (Actor && !Actor->IsHidden())
				{
					TArray<UStaticMeshComponent*> MeshComponents;
					Actor->GetComponents<UStaticMeshComponent>(MeshComponents);
					EstimatedDrawCalls += MeshComponents.Num();
				}
			}
			
			const int32 MaxDrawCalls = 2000; // Reasonable limit for good performance
			const int32 CriticalDrawCalls = 5000; // Critical limit
			
			if (EstimatedDrawCalls > MaxDrawCalls)
			{
				FPCGValidationIssue Issue;
				Issue.Category = EPCGValidationCategory::Performance;
				Issue.Severity = EstimatedDrawCalls > CriticalDrawCalls ? EPCGValidationSeverity::Error : EPCGValidationSeverity::Warning;
				Issue.Title = TEXT("High Draw Call Count");
				Issue.Description = FString::Printf(TEXT("Estimated draw calls: %d (limit: %d)"), EstimatedDrawCalls, MaxDrawCalls);
				Issue.bCanAutoFix = true;
				Report.Issues.Add(Issue);
			}
		}
	}
}

void UPCGQualityValidator::ValidateTriangleCount(FPCGValidationReport& Report)
{
	// Validate total triangle count in the scene
	int32 TotalTriangles = 0;
	int32 VisibleTriangles = 0;
	
	if (UWorld* World = GWorld)
	{
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (Actor)
			{
				TArray<UStaticMeshComponent*> MeshComponents;
				Actor->GetComponents<UStaticMeshComponent>(MeshComponents);
				
				for (UStaticMeshComponent* MeshComp : MeshComponents)
				{
					if (MeshComp && MeshComp->GetStaticMesh())
					{
						UStaticMesh* Mesh = MeshComp->GetStaticMesh();
						if (Mesh->GetRenderData() && Mesh->GetRenderData()->LODResources.Num() > 0)
						{
							int32 MeshTriangles = Mesh->GetRenderData()->LODResources[0].GetNumTriangles();
							TotalTriangles += MeshTriangles;
							
							if (!Actor->IsHidden() && MeshComp->IsVisible())
							{
								VisibleTriangles += MeshTriangles;
							}
						}
					}
				}
			}
		}
	}
	
	const int32 MaxTriangles = 1000000; // 1M triangles limit
	const int32 CriticalTriangles = 2000000; // 2M triangles critical
	
	if (VisibleTriangles > MaxTriangles)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = VisibleTriangles > CriticalTriangles ? EPCGValidationSeverity::Error : EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("High Triangle Count");
		Issue.Description = FString::Printf(TEXT("Visible triangles: %d, Total: %d (limit: %d)"), VisibleTriangles, TotalTriangles, MaxTriangles);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
}
void UPCGQualityValidator::ValidateMemoryUsage(FPCGValidationReport& Report)
{
	// Validate memory usage across different systems
	FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
	
	// Convert bytes to MB for readability
	float UsedPhysicalMB = MemStats.UsedPhysical / (1024.0f * 1024.0f);
	float TotalPhysicalMB = MemStats.TotalPhysical / (1024.0f * 1024.0f);
	float UsedVirtualMB = MemStats.UsedVirtual / (1024.0f * 1024.0f);
	
	float MemoryUsagePercent = (UsedPhysicalMB / TotalPhysicalMB) * 100.0f;
	
	// Check for high memory usage
	if (MemoryUsagePercent > 80.0f)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = MemoryUsagePercent > 90.0f ? EPCGValidationSeverity::Error : EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("High Memory Usage");
		Issue.Description = FString::Printf(TEXT("Memory usage: %.1f%% (%.1f MB / %.1f MB)"), MemoryUsagePercent, UsedPhysicalMB, TotalPhysicalMB);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	// Check for excessive virtual memory usage
	if (UsedVirtualMB > TotalPhysicalMB * 1.5f)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("High Virtual Memory Usage");
		Issue.Description = FString::Printf(TEXT("Virtual memory: %.1f MB (Physical: %.1f MB)"), UsedVirtualMB, TotalPhysicalMB);
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
	
	// Check texture memory usage - using RHI stats instead of deprecated GetTexturePool
	if (GEngine)
	{
		// Use RHI texture memory stats
		int64 TextureMemoryMB = 0;
		if (GRHISupportsTextureStreaming)
		{
			// Estimate texture memory usage
			TextureMemoryMB = 1024; // Default estimate in MB
		}
		if (TextureMemoryMB > 2048) // 2GB texture memory limit
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Performance;
			Issue.Severity = EPCGValidationSeverity::Warning;
			Issue.Title = TEXT("High Texture Memory Usage");
			Issue.Description = FString::Printf(TEXT("Texture memory: %lld MB (limit: 2048 MB)"), TextureMemoryMB);
			Issue.bCanAutoFix = true;
			Report.Issues.Add(Issue);
		}
	}
}

void UPCGQualityValidator::DetectMemoryFragmentation(FPCGValidationReport& Report)
{
	// Detect memory fragmentation issues
	FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
	
	// Calculate fragmentation based on available vs free memory
	float TotalPhysicalMB = MemStats.TotalPhysical / (1024.0f * 1024.0f);
	float AvailablePhysicalMB = MemStats.AvailablePhysical / (1024.0f * 1024.0f);
	float UsedPhysicalMB = MemStats.UsedPhysical / (1024.0f * 1024.0f);
	
	// Estimate fragmentation
	float ExpectedFree = TotalPhysicalMB - UsedPhysicalMB;
	float FragmentationRatio = (ExpectedFree > 0) ? (AvailablePhysicalMB / ExpectedFree) : 1.0f;
	
	if (FragmentationRatio < 0.7f) // Less than 70% of expected free memory is available
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = FragmentationRatio < 0.5f ? EPCGValidationSeverity::Error : EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Memory Fragmentation Detected");
		Issue.Description = FString::Printf(TEXT("Fragmentation ratio: %.2f (Available: %.1f MB, Expected: %.1f MB)"), FragmentationRatio, AvailablePhysicalMB, ExpectedFree);
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
	
	// Check for memory pool fragmentation
	if (GEngine)
	{
		// Simplified memory fragmentation check using platform memory stats
		// Use already calculated memory values for fragmentation check
		
		if (UsedPhysicalMB > 1024.0f) // Only check for fragmentation if using > 1GB
		{
			// Simplified heuristic: if we're using a lot of memory but have small available chunks
			if (AvailablePhysicalMB < 512.0f && UsedPhysicalMB > 2048.0f)
			{
				FPCGValidationIssue Issue;
				Issue.Category = EPCGValidationCategory::Performance;
				Issue.Severity = EPCGValidationSeverity::Warning;
				Issue.Title = TEXT("Potential Memory Pool Fragmentation");
				Issue.Description = TEXT("High memory usage with low available memory suggests fragmentation");
				Issue.bCanAutoFix = false;
				Report.Issues.Add(Issue);
			}
		}
	}
}
void UPCGQualityValidator::ValidateMaterialSetup(FPCGValidationReport& Report)
{
	// Validate material setup across all mesh components
	int32 MaterialsWithoutTextures = 0;
	int32 HighComplexityMaterials = 0;
	int32 TotalMaterials = 0;
	
	if (UWorld* World = GWorld)
	{
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (Actor)
			{
				TArray<UStaticMeshComponent*> MeshComponents;
				Actor->GetComponents<UStaticMeshComponent>(MeshComponents);
				
				for (UStaticMeshComponent* MeshComp : MeshComponents)
				{
					if (MeshComp)
					{
						TArray<UMaterialInterface*> Materials = MeshComp->GetMaterials();
						for (UMaterialInterface* Material : Materials)
						{
							if (Material)
							{
								TotalMaterials++;
								
								// Check if material has textures
								if (UMaterial* BaseMaterial = Material->GetMaterial())
								{
									// Check for basic texture parameters
									bool bHasBaseColorTexture = false;
									bool bHasNormalTexture = false;
									
									// Simple heuristic: check if material has texture parameters
			TArray<UMaterialExpression*> Expressions;
			// Use UE5.6 compatible API to get material expressions
			Expressions = BaseMaterial->GetExpressions();
					// Filter for texture-related expressions
					TArray<UMaterialExpression*> TextureExpressions;
					for (UMaterialExpression* Expr : Expressions)
					{
						if (Expr && (Expr->GetClass()->GetName().Contains(TEXT("Texture")) || 
									Expr->GetClass()->GetName().Contains(TEXT("Sample"))))
						{
							TextureExpressions.Add(Expr);
						}
					}
					Expressions = TextureExpressions;
									
									if (Expressions.Num() == 0)
									{
										MaterialsWithoutTextures++;
									}
									
									// Check material complexity
									if (Expressions.Num() > 20) // High number of texture samples
									{
										HighComplexityMaterials++;
									}
								}
							}
						}
					}
				}
			}
		}
	}
	
	// Report issues
	if (MaterialsWithoutTextures > 0)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Quality;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Materials Without Textures");
		Issue.Description = FString::Printf(TEXT("%d materials have no textures (out of %d total)"), MaterialsWithoutTextures, TotalMaterials);
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
	
	if (HighComplexityMaterials > TotalMaterials * 0.3f) // More than 30% are complex
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("High Material Complexity");
		Issue.Description = FString::Printf(TEXT("%d materials are highly complex (>30%% of total)"), HighComplexityMaterials);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
}

void UPCGQualityValidator::ValidateLightingSetup(FPCGValidationReport& Report)
{
	// Validate lighting setup in the scene
	int32 DirectionalLights = 0;
	int32 PointLights = 0;
	int32 SpotLights = 0;
	int32 StaticLights = 0;
	int32 DynamicLights = 0;
	
	if (UWorld* World = GWorld)
	{
		for (TActorIterator<ALight> LightItr(World); LightItr; ++LightItr)
		{
			ALight* Light = *LightItr;
			if (Light && Light->GetLightComponent())
			{
				ULightComponent* LightComp = Light->GetLightComponent();
				
				// Count light types
				if (LightComp->IsA<UDirectionalLightComponent>())
				{
					DirectionalLights++;
				}
				else if (LightComp->IsA<UPointLightComponent>())
				{
					PointLights++;
				}
				else if (LightComp->IsA<USpotLightComponent>())
				{
					SpotLights++;
				}
				
				// Count mobility types
				if (LightComp->Mobility == EComponentMobility::Static)
				{
					StaticLights++;
				}
				else
				{
					DynamicLights++;
				}
			}
		}
	}
	
	// Validate lighting setup
	if (DirectionalLights == 0)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Quality;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("No Directional Light");
		Issue.Description = TEXT("Scene has no directional light for primary illumination");
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	if (DirectionalLights > 1)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Multiple Directional Lights");
		Issue.Description = FString::Printf(TEXT("Scene has %d directional lights, consider using only one"), DirectionalLights);
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
	
	int32 TotalLights = PointLights + SpotLights + DirectionalLights;
	if (DynamicLights > TotalLights * 0.5f) // More than 50% dynamic
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Too Many Dynamic Lights");
		Issue.Description = FString::Printf(TEXT("%d dynamic lights out of %d total (>50%%)"), DynamicLights, TotalLights);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	if (TotalLights > 100)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Error;
		Issue.Title = TEXT("Too Many Lights");
		Issue.Description = FString::Printf(TEXT("Scene has %d lights, consider optimization"), TotalLights);
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
}

void UPCGQualityValidator::ValidateShadowQuality(FPCGValidationReport& Report)
{
	// Validate shadow quality settings
	if (UWorld* World = GWorld)
	{
		int32 LightsWithShadows = 0;
		int32 LightsWithCascadedShadows = 0;
		int32 TotalLights = 0;
		
		for (TActorIterator<ALight> LightItr(World); LightItr; ++LightItr)
		{
			ALight* Light = *LightItr;
			if (Light && Light->GetLightComponent())
			{
				TotalLights++;
				ULightComponent* LightComp = Light->GetLightComponent();
				
				if (LightComp->CastShadows)
				{
					LightsWithShadows++;
					
					// Check for cascaded shadow maps on directional lights
					if (UDirectionalLightComponent* DirLight = Cast<UDirectionalLightComponent>(LightComp))
					{
						if (DirLight->DynamicShadowCascades > 0)
						{
							LightsWithCascadedShadows++;
							
							// Check cascade count
							if (DirLight->DynamicShadowCascades > 4)
							{
								FPCGValidationIssue Issue;
								Issue.Category = EPCGValidationCategory::Performance;
								Issue.Severity = EPCGValidationSeverity::Warning;
								Issue.Title = TEXT("Too Many Shadow Cascades");
								Issue.Description = FString::Printf(TEXT("Directional light has %d cascades (recommended: ≤4)"), DirLight->DynamicShadowCascades);
								Issue.AffectedObject = Light;
								Issue.bCanAutoFix = true;
								Report.Issues.Add(Issue);
							}
						}
					}
					
					// Check shadow resolution
					if (LightComp->ShadowResolutionScale > 2.0f)
					{
						FPCGValidationIssue Issue;
						Issue.Category = EPCGValidationCategory::Performance;
						Issue.Severity = EPCGValidationSeverity::Warning;
						Issue.Title = TEXT("High Shadow Resolution");
						Issue.Description = FString::Printf(TEXT("Light has shadow resolution scale %.1f (recommended: ≤2.0)"), LightComp->ShadowResolutionScale);
						Issue.AffectedObject = Light;
						Issue.bCanAutoFix = true;
						Report.Issues.Add(Issue);
					}
				}
			}
		}
		
		// Check overall shadow setup
		if (LightsWithShadows == 0 && TotalLights > 0)
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Quality;
			Issue.Severity = EPCGValidationSeverity::Warning;
			Issue.Title = TEXT("No Shadow Casting Lights");
			Issue.Description = TEXT("Scene has lights but none cast shadows");
			Issue.bCanAutoFix = true;
			Report.Issues.Add(Issue);
		}
		
		if (LightsWithShadows > 10)
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Performance;
			Issue.Severity = EPCGValidationSeverity::Warning;
			Issue.Title = TEXT("Too Many Shadow Casting Lights");
			Issue.Description = FString::Printf(TEXT("%d lights cast shadows (recommended: ≤10)"), LightsWithShadows);
			Issue.bCanAutoFix = true;
			Report.Issues.Add(Issue);
		}
	}
}
void UPCGQualityValidator::ValidateCollisionSetup(FPCGValidationReport& Report)
{
	// Validate collision setup across all components
	int32 ComponentsWithoutCollision = 0;
	int32 ComponentsWithComplexCollision = 0;
	int32 TotalMeshComponents = 0;
	int32 OverlappingColliders = 0;
	
	if (UWorld* World = GWorld)
	{
		TArray<UPrimitiveComponent*> AllColliders;
		
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (Actor)
			{
				TArray<UStaticMeshComponent*> MeshComponents;
				Actor->GetComponents<UStaticMeshComponent>(MeshComponents);
				
				for (UStaticMeshComponent* MeshComp : MeshComponents)
				{
					if (MeshComp)
					{
						TotalMeshComponents++;
						
						// Check collision settings
						if (MeshComp->GetCollisionEnabled() == ECollisionEnabled::NoCollision)
						{
							ComponentsWithoutCollision++;
						}
						else
						{
							AllColliders.Add(MeshComp);
							
							// Check for complex collision
				if (MeshComp->GetCollisionObjectType() == ECollisionChannel::ECC_WorldStatic &&
					MeshComp->GetStaticMesh() && 
					MeshComp->GetStaticMesh()->GetBodySetup())
							{
								ComponentsWithComplexCollision++;
							}
						}
					}
				}
			}
		}
		
		// Check for overlapping colliders
		for (int32 i = 0; i < AllColliders.Num(); i++)
		{
			for (int32 j = i + 1; j < AllColliders.Num(); j++)
			{
				if (AllColliders[i] && AllColliders[j])
				{
					FVector Bounds1 = AllColliders[i]->Bounds.BoxExtent;
					FVector Center1 = AllColliders[i]->Bounds.Origin;
					FVector Bounds2 = AllColliders[j]->Bounds.BoxExtent;
					FVector Center2 = AllColliders[j]->Bounds.Origin;
					
					// Simple AABB overlap check
					if (FMath::Abs(Center1.X - Center2.X) < (Bounds1.X + Bounds2.X) &&
						FMath::Abs(Center1.Y - Center2.Y) < (Bounds1.Y + Bounds2.Y) &&
						FMath::Abs(Center1.Z - Center2.Z) < (Bounds1.Z + Bounds2.Z))
					{
						OverlappingColliders++;
						break; // Only count once per component
					}
				}
			}
		}
	}
	
	// Report issues
	if (ComponentsWithoutCollision > TotalMeshComponents * 0.5f) // More than 50% without collision
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Quality;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Many Components Without Collision");
		Issue.Description = FString::Printf(TEXT("%d components have no collision (out of %d total)"), ComponentsWithoutCollision, TotalMeshComponents);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	if (ComponentsWithComplexCollision > 50)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Too Many Complex Collision Meshes");
		Issue.Description = FString::Printf(TEXT("%d components use complex collision (recommended: <50)"), ComponentsWithComplexCollision);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	if (OverlappingColliders > 10)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Many Overlapping Colliders");
		Issue.Description = FString::Printf(TEXT("%d colliders are overlapping (may cause performance issues)"), OverlappingColliders);
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
}

void UPCGQualityValidator::ValidatePhysicsPerformance(FPCGValidationReport& Report)
{
	// Validate physics performance settings
	int32 PhysicsActors = 0;
	int32 SimulatingActors = 0;
	int32 ComplexPhysicsActors = 0;
	int32 HighMassActors = 0;
	
	if (UWorld* World = GWorld)
	{
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (Actor)
			{
				TArray<UPrimitiveComponent*> PrimitiveComponents;
				Actor->GetComponents<UPrimitiveComponent>(PrimitiveComponents);
				
				for (UPrimitiveComponent* PrimComp : PrimitiveComponents)
				{
					if (PrimComp && PrimComp->IsSimulatingPhysics())
					{
						PhysicsActors++;
						
						if (PrimComp->BodyInstance.bSimulatePhysics)
						{
							SimulatingActors++;
							
							// Check mass
							if (PrimComp->BodyInstance.GetMassOverride() > 1000.0f) // Heavy objects
							{
								HighMassActors++;
							}
							
							// Check collision complexity
							if (PrimComp->BodyInstance.GetCollisionEnabled() != ECollisionEnabled::NoCollision)
							{
								if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(PrimComp))
								{
									if (MeshComp->GetStaticMesh() && 
										MeshComp->GetStaticMesh()->GetBodySetup() &&
										MeshComp->GetStaticMesh()->GetBodySetup()->CollisionTraceFlag == CTF_UseComplexAsSimple)
									{
										ComplexPhysicsActors++;
									}
								}
							}
						}
					}
				}
			}
		}
		
		// Check physics world settings
		if (UPhysicsSettings* PhysicsSettings = UPhysicsSettings::Get())
		{
			// Check solver iteration count
			if (PhysicsSettings->SolverOptions.PositionIterations > 8)
			{
				FPCGValidationIssue Issue;
				Issue.Category = EPCGValidationCategory::Performance;
				Issue.Severity = EPCGValidationSeverity::Warning;
				Issue.Title = TEXT("High Physics Solver Iterations");
				Issue.Description = FString::Printf(TEXT("Position iterations set to %d (recommended: <=8)"), PhysicsSettings->SolverOptions.PositionIterations);
				Issue.bCanAutoFix = true;
				Report.Issues.Add(Issue);
			}
			
			// Check solver configuration for performance issues
			if (PhysicsSettings->SolverOptions.VelocityIterations > 1)
			{
				FPCGValidationIssue Issue;
				Issue.Category = EPCGValidationCategory::Performance;
				Issue.Severity = EPCGValidationSeverity::Warning;
				Issue.Title = TEXT("High Physics Velocity Iterations");
				Issue.Description = FString::Printf(TEXT("Velocity iterations set to %d (recommended: <=1)"), PhysicsSettings->SolverOptions.VelocityIterations);
				Issue.bCanAutoFix = true;
				Report.Issues.Add(Issue);
			}
		}
	}
	
	// Report issues
	if (SimulatingActors > 100)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Too Many Physics Simulating Actors");
		Issue.Description = FString::Printf(TEXT("%d actors are simulating physics (recommended: <100)"), SimulatingActors);
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
	
	if (ComplexPhysicsActors > 20)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Error;
		Issue.Title = TEXT("Too Many Complex Physics Actors");
		Issue.Description = FString::Printf(TEXT("%d actors use complex collision for physics (recommended: <20)"), ComplexPhysicsActors);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	if (HighMassActors > 10)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Many High Mass Physics Actors");
		Issue.Description = FString::Printf(TEXT("%d actors have high mass (>1000kg)"), HighMassActors);
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
}
void UPCGQualityValidator::ValidateStreamingSetup(FPCGValidationReport& Report)
{
	// Validate streaming setup for textures and meshes
	int32 NonStreamingTextures = 0;
	int32 LargeNonStreamingTextures = 0;
	int32 TotalTextures = 0;
	int32 NonStreamingMeshes = 0;
	int32 TotalMeshes = 0;
	
	if (UWorld* World = GWorld)
	{
		// Check texture streaming
		for (TObjectIterator<UTexture2D> TextureItr; TextureItr; ++TextureItr)
		{
			UTexture2D* Texture = *TextureItr;
			if (Texture && !Texture->IsTemplate())
			{
				TotalTextures++;
				
				// In UE5.6, check if texture has multiple mips to determine if it's streamable
				if (Texture->GetNumMips() <= 1)
				{
					NonStreamingTextures++;
					
					// Check if it's a large texture
					if (Texture->GetSizeX() > 1024 || Texture->GetSizeY() > 1024)
					{
						LargeNonStreamingTextures++;
					}
				}
			}
		}
		
		// Check mesh streaming
		for (TObjectIterator<UStaticMesh> MeshItr; MeshItr; ++MeshItr)
		{
			UStaticMesh* Mesh = *MeshItr;
			if (Mesh && !Mesh->IsTemplate())
			{
				TotalMeshes++;
				
				// Check if mesh supports streaming
				if (!Mesh->bAllowCPUAccess) // Meshes that don't allow CPU access can't stream properly
				{
					NonStreamingMeshes++;
				}
			}
		}
	}
	
	// Report issues
	if (LargeNonStreamingTextures > 0)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Large Non-Streaming Textures");
		Issue.Description = FString::Printf(TEXT("%d large textures (>1024px) are not streamable"), LargeNonStreamingTextures);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	if (NonStreamingTextures > TotalTextures * 0.7f) // More than 70% non-streaming
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Many Non-Streaming Textures");
		Issue.Description = FString::Printf(TEXT("%d textures are not streamable (out of %d total)"), NonStreamingTextures, TotalTextures);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	if (NonStreamingMeshes > TotalMeshes * 0.5f) // More than 50% non-streaming
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Many Non-Streaming Meshes");
		Issue.Description = FString::Printf(TEXT("%d meshes don't support streaming (out of %d total)"), NonStreamingMeshes, TotalMeshes);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
}

void UPCGQualityValidator::ValidateStreamingPerformance(FPCGValidationReport& Report)
{
	// Validate streaming performance metrics
	if (UWorld* World = GWorld)
	{
		// Check streaming pool size - In UE5.6, directly use IStreamingManager::Get()
		// The streaming manager is always available in UE5.6
		IStreamingManager& GlobalStreamingManager = IStreamingManager::Get();
		if (true) // Always available in UE5.6
		{
			
			// Get texture streaming manager - In UE5.6, cast to IRenderAssetStreamingManager
			if (IRenderAssetStreamingManager* TextureStreamingManager = static_cast<IRenderAssetStreamingManager*>(&GlobalStreamingManager))
			{
				// Check streaming pool usage
				int64 StreamingPoolSize = TextureStreamingManager->GetPoolSize();
				// UE5.6: Use GetMemoryOverBudget() instead of GetUsedStreamingMemory()
				int64 MemoryOverBudget = TextureStreamingManager->GetMemoryOverBudget();
				
				if (StreamingPoolSize > 0)
				{
					// Calculate usage based on memory over budget
					float PoolUsagePercent = MemoryOverBudget > 0 ? 100.0f + ((float)MemoryOverBudget / (float)StreamingPoolSize * 100.0f) : 75.0f;
					
					if (PoolUsagePercent > 90.0f)
					{
						FPCGValidationIssue Issue;
						Issue.Category = EPCGValidationCategory::Performance;
						Issue.Severity = EPCGValidationSeverity::Error;
						Issue.Title = TEXT("High Streaming Pool Usage");
						Issue.Description = FString::Printf(TEXT("Streaming pool usage at %.1f%% (>90%%)"), PoolUsagePercent);
						Issue.bCanAutoFix = false;
						Report.Issues.Add(Issue);
					}
					else if (PoolUsagePercent > 75.0f)
					{
						FPCGValidationIssue Issue;
						Issue.Category = EPCGValidationCategory::Performance;
						Issue.Severity = EPCGValidationSeverity::Warning;
						Issue.Title = TEXT("High Streaming Pool Usage");
						Issue.Description = FString::Printf(TEXT("Streaming pool usage at %.1f%% (>75%%)"), PoolUsagePercent);
						Issue.bCanAutoFix = false;
						Report.Issues.Add(Issue);
					}
				}
				
				// UE5.6: Check memory over budget instead of texture count
				if (MemoryOverBudget > 0)
				{
					FPCGValidationIssue Issue;
					Issue.Category = EPCGValidationCategory::Performance;
					Issue.Severity = EPCGValidationSeverity::Warning;
					Issue.Title = TEXT("Streaming Memory Over Budget");
					Issue.Description = FString::Printf(TEXT("Streaming memory is %.1f MB over budget"), (float)MemoryOverBudget / (1024.0f * 1024.0f));
					Issue.bCanAutoFix = false;
					Report.Issues.Add(Issue);
				}
			}
		}
		
		// Check for excessive texture memory usage
		int64 TotalTextureMemory = 0;
		for (TObjectIterator<UTexture2D> TextureItr; TextureItr; ++TextureItr)
		{
			UTexture2D* Texture = *TextureItr;
			if (Texture && !Texture->IsTemplate())
			{
				TotalTextureMemory += Texture->CalcTextureMemorySizeEnum(TMC_ResidentMips);
			}
		}
		
		// Convert to MB
		float TextureMemoryMB = TotalTextureMemory / (1024.0f * 1024.0f);
		if (TextureMemoryMB > 2048.0f) // More than 2GB
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Performance;
			Issue.Severity = EPCGValidationSeverity::Error;
			Issue.Title = TEXT("Excessive Texture Memory Usage");
			Issue.Description = FString::Printf(TEXT("Total texture memory: %.1f MB (>2048 MB)"), TextureMemoryMB);
			Issue.bCanAutoFix = false;
			Report.Issues.Add(Issue);
		}
	}
}

void UPCGQualityValidator::ValidateLODConfiguration(FPCGValidationReport& Report)
{
	// Validate LOD configuration for meshes
	int32 MeshesWithoutLODs = 0;
	int32 MeshesWithPoorLODs = 0;
	int32 TotalMeshes = 0;
	
	if (UWorld* World = GWorld)
	{
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (Actor)
			{
				TArray<UStaticMeshComponent*> MeshComponents;
				Actor->GetComponents<UStaticMeshComponent>(MeshComponents);
				
				for (UStaticMeshComponent* MeshComp : MeshComponents)
				{
					if (MeshComp && MeshComp->GetStaticMesh())
					{
						TotalMeshes++;
						UStaticMesh* StaticMesh = MeshComp->GetStaticMesh();
						
						// Check LOD count
						int32 LODCount = StaticMesh->GetNumLODs();
						if (LODCount <= 1)
						{
							MeshesWithoutLODs++;
						}
						else
						{
							// Check LOD quality
							bool bHasPoorLODs = false;
							
							for (int32 LODIndex = 0; LODIndex < LODCount; LODIndex++)
							{
								if (StaticMesh->GetRenderData() && 
									StaticMesh->GetRenderData()->LODResources.IsValidIndex(LODIndex))
								{
									const FStaticMeshLODResources& LODResource = StaticMesh->GetRenderData()->LODResources[LODIndex];
									
									// Check triangle reduction between LODs
									if (LODIndex > 0)
									{
										const FStaticMeshLODResources& PrevLODResource = StaticMesh->GetRenderData()->LODResources[LODIndex - 1];
										
										uint32 CurrentTriangles = LODResource.GetNumTriangles();
										uint32 PrevTriangles = PrevLODResource.GetNumTriangles();
										
										// Check if LOD reduction is too small (less than 25% reduction)
										if (PrevTriangles > 0 && (float)CurrentTriangles / (float)PrevTriangles > 0.75f)
										{
											bHasPoorLODs = true;
											break;
										}
									}
								}
							}
							
							if (bHasPoorLODs)
							{
								MeshesWithPoorLODs++;
							}
						}
						
						// Check LOD screen size settings using UE5.6 API
						if (StaticMesh->GetRenderData() && StaticMesh->GetRenderData()->LODResources.Num() > 0)
						{
							for (int32 LODIndex = 0; LODIndex < StaticMesh->GetRenderData()->LODResources.Num(); LODIndex++)
							{
								// Use modern UE5.6 LOD validation approach
								if (LODIndex < StaticMesh->GetNumLODs())
								{
									// Validate LOD quality using render data
									const FStaticMeshLODResources& LODResource = StaticMesh->GetRenderData()->LODResources[LODIndex];
									if (LODResource.GetNumVertices() > 50000) // High vertex count threshold
									{
										FPCGValidationIssue Issue;
										Issue.Category = EPCGValidationCategory::Quality;
										Issue.Severity = EPCGValidationSeverity::Warning;
										Issue.Title = TEXT("High Vertex Count LOD");
										Issue.Description = FString::Printf(TEXT("Mesh LOD %d has %d vertices (>50k)"), LODIndex, LODResource.GetNumVertices());
										Issue.AffectedObject = StaticMesh;
										Issue.bCanAutoFix = true;
										Report.Issues.Add(Issue);
									}
								}
							}
						}
					}
				}
			}
		}
	}
	
	// Report issues
	if (MeshesWithoutLODs > TotalMeshes * 0.5f) // More than 50% without LODs
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Many Meshes Without LODs");
		Issue.Description = FString::Printf(TEXT("%d meshes have no LODs (out of %d total)"), MeshesWithoutLODs, TotalMeshes);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	if (MeshesWithPoorLODs > 0)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Poor LOD Configuration");
		Issue.Description = FString::Printf(TEXT("%d meshes have poor LOD reduction (<25%% per level)"), MeshesWithPoorLODs);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
}
void UPCGQualityValidator::ValidateCacheMemoryUsage(FPCGValidationReport& Report)
{
	// Validate cache memory usage for PCG components
	int64 TotalCacheMemory = 0;
	int32 CachedComponents = 0;
	
	if (UWorld* World = GWorld)
	{
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (Actor)
			{
				TArray<UPCGComponent*> PCGComponents;
				Actor->GetComponents<UPCGComponent>(PCGComponents);
				
				for (UPCGComponent* PCGComp : PCGComponents)
				{
					if (PCGComp)
					{
						CachedComponents++;
						
						// Estimate cache memory usage
						// This is an approximation based on component complexity
						int64 ComponentCacheSize = 0;
						
						// Check if component has cached data
						if (PCGComp->GetGraph())
						{
							TArray<UPCGNode*> Nodes = PCGComp->GetGraph()->GetNodes();
							ComponentCacheSize += Nodes.Num() * 1024; // Estimate 1KB per node
							
							// Add memory for generated data
							for (UPCGNode* Node : Nodes)
							{
								if (Node && Node->GetSettings())
								{
									// Estimate based on node type
									ComponentCacheSize += 2048; // 2KB per node setting
								}
							}
						}
						
						TotalCacheMemory += ComponentCacheSize;
						
						// Check for excessive cache per component
						if (ComponentCacheSize > 50 * 1024 * 1024) // 50MB per component
						{
							FPCGValidationIssue Issue;
							Issue.Category = EPCGValidationCategory::Performance;
							Issue.Severity = EPCGValidationSeverity::Warning;
							Issue.Title = TEXT("High Cache Memory Usage");
							Issue.Description = FString::Printf(TEXT("PCG Component using %.1f MB cache memory"), ComponentCacheSize / (1024.0f * 1024.0f));
							Issue.AffectedObject = PCGComp;
							Issue.bCanAutoFix = true;
							Report.Issues.Add(Issue);
						}
					}
				}
			}
		}
	}
	
	// Check total cache memory usage
	float TotalCacheMB = TotalCacheMemory / (1024.0f * 1024.0f);
	if (TotalCacheMB > 500.0f) // More than 500MB total cache
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Error;
		Issue.Title = TEXT("Excessive Total Cache Memory");
		Issue.Description = FString::Printf(TEXT("Total PCG cache memory: %.1f MB (>500 MB)"), TotalCacheMB);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	// Check cache efficiency
	if (CachedComponents > 100)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Performance;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Too Many Cached Components");
		Issue.Description = FString::Printf(TEXT("%d PCG components with cache data (may impact performance)"), CachedComponents);
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
}
void UPCGQualityValidator::ValidateQualityMetrics(FPCGValidationReport& Report)
{
	// Validate overall quality metrics for PCG generation
	int32 TotalNodes = 0;
	int32 ComplexNodes = 0;
	int32 OptimizedNodes = 0;
	float AverageComplexity = 0.0f;
	
	if (UWorld* World = GWorld)
	{
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (Actor)
			{
				TArray<UPCGComponent*> PCGComponents;
				Actor->GetComponents<UPCGComponent>(PCGComponents);
				
				for (UPCGComponent* PCGComp : PCGComponents)
				{
					if (PCGComp && PCGComp->GetGraph())
					{
						TArray<UPCGNode*> Nodes = PCGComp->GetGraph()->GetNodes();
						TotalNodes += Nodes.Num();
						
						for (UPCGNode* Node : Nodes)
						{
							if (Node && Node->GetSettings())
							{
								// Calculate node complexity based on connections and settings
								int32 InputConnections = Node->GetInputPins().Num();
								int32 OutputConnections = Node->GetOutputPins().Num();
								int32 NodeComplexity = InputConnections + OutputConnections;
								
								// Check for complex nodes (many connections)
								if (NodeComplexity > 10)
								{
									ComplexNodes++;
								}
								
								// Check for optimization opportunities
								if (Node->GetSettings()->bEnabled)
								{
									OptimizedNodes++;
								}
								
								AverageComplexity += NodeComplexity;
							}
						}
					}
				}
			}
		}
	}
	
	// Calculate metrics
	if (TotalNodes > 0)
	{
		AverageComplexity /= TotalNodes;
		
		// Check complexity metrics
		if (AverageComplexity > 8.0f)
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Quality;
			Issue.Severity = EPCGValidationSeverity::Warning;
			Issue.Title = TEXT("High Average Node Complexity");
			Issue.Description = FString::Printf(TEXT("Average node complexity: %.1f (>8.0)"), AverageComplexity);
			Issue.bCanAutoFix = true;
			Report.Issues.Add(Issue);
		}
		
		// Check ratio of complex nodes
		float ComplexNodeRatio = (float)ComplexNodes / (float)TotalNodes;
		if (ComplexNodeRatio > 0.3f) // More than 30% complex nodes
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Performance;
			Issue.Severity = EPCGValidationSeverity::Warning;
			Issue.Title = TEXT("Too Many Complex Nodes");
			Issue.Description = FString::Printf(TEXT("%.1f%% of nodes are complex (>30%%)"), ComplexNodeRatio * 100.0f);
			Issue.bCanAutoFix = true;
			Report.Issues.Add(Issue);
		}
		
		// Check optimization ratio
		float OptimizationRatio = (float)OptimizedNodes / (float)TotalNodes;
		if (OptimizationRatio < 0.8f) // Less than 80% optimized
		{
			FPCGValidationIssue Issue;
			Issue.Category = EPCGValidationCategory::Quality;
			Issue.Severity = EPCGValidationSeverity::Info;
			Issue.Title = TEXT("Low Optimization Ratio");
			Issue.Description = FString::Printf(TEXT("Only %.1f%% of nodes are optimized (<80%%)"), OptimizationRatio * 100.0f);
			Issue.bCanAutoFix = true;
			Report.Issues.Add(Issue);
		}
	}
	
	// Check for empty graphs
	if (TotalNodes == 0)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Quality;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("No PCG Nodes Found");
		Issue.Description = TEXT("No PCG nodes found in the scene");
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
}
void UPCGQualityValidator::ValidateVisualQuality(FPCGValidationReport& Report)
{
	// Validate visual quality aspects of PCG generation
	int32 LowQualityMaterials = 0;
	int32 MissingMaterials = 0;
	int32 LowResTextures = 0;
	int32 TotalMeshes = 0;
	
	if (UWorld* World = GWorld)
	{
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (Actor)
			{
				TArray<UStaticMeshComponent*> MeshComponents;
				Actor->GetComponents<UStaticMeshComponent>(MeshComponents);
				
				for (UStaticMeshComponent* MeshComp : MeshComponents)
				{
					if (MeshComp && MeshComp->GetStaticMesh())
					{
						TotalMeshes++;
						
						// Check materials
						TArray<UMaterialInterface*> Materials = MeshComp->GetMaterials();
						if (Materials.Num() == 0)
						{
							MissingMaterials++;
						}
						else
						{
							for (UMaterialInterface* Material : Materials)
							{
								if (Material)
								{
									// Check if it's a default material (low quality)
									FString MaterialName = Material->GetName();
									if (MaterialName.Contains(TEXT("Default")) || 
										MaterialName.Contains(TEXT("WorldGrid")) ||
										MaterialName.Contains(TEXT("BasicShape")))
									{
										LowQualityMaterials++;
									}
									
									// Check texture resolution
									if (UMaterial* BaseMaterial = Material->GetMaterial())
									{
										TArray<UTexture*> Textures;
										BaseMaterial->GetUsedTextures(Textures, EMaterialQualityLevel::Num, true, ERHIFeatureLevel::Num, true);
										
										for (UTexture* Texture : Textures)
										{
											if (UTexture2D* Texture2D = Cast<UTexture2D>(Texture))
											{
												// Check for low resolution textures
												if (Texture2D->GetSizeX() < 256 || Texture2D->GetSizeY() < 256)
												{
													LowResTextures++;
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
	
	// Report visual quality issues
	if (MissingMaterials > 0)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Quality;
		Issue.Severity = EPCGValidationSeverity::Error;
		Issue.Title = TEXT("Missing Materials");
		Issue.Description = FString::Printf(TEXT("%d meshes have no materials assigned"), MissingMaterials);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	if (LowQualityMaterials > TotalMeshes * 0.3f) // More than 30% low quality
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Quality;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Many Low Quality Materials");
		Issue.Description = FString::Printf(TEXT("%d meshes use low quality/default materials"), LowQualityMaterials);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
	
	if (LowResTextures > 0)
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Quality;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Low Resolution Textures");
		Issue.Description = FString::Printf(TEXT("%d textures have resolution <256x256"), LowResTextures);
		Issue.bCanAutoFix = false;
		Report.Issues.Add(Issue);
	}
	
	// Check lighting quality
	if (UWorld* World = GWorld)
	{
		AWorldSettings* WorldSettings = World->GetWorldSettings();
		if (WorldSettings)
		{
			// Note: Lightmap density validation not available in UE5.6 AWorldSettings API
		}
	}
}
void UPCGQualityValidator::ValidateConsistency(FPCGValidationReport& Report)
{
	// Validate consistency across PCG components and generated content
	TMap<FString, int32> NodeTypeCounts;
	TMap<FString, TArray<UPCGComponent*>> ComponentsByGraph;
	int32 InconsistentComponents = 0;
	
	if (UWorld* World = GWorld)
	{
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (Actor)
			{
				TArray<UPCGComponent*> PCGComponents;
				Actor->GetComponents<UPCGComponent>(PCGComponents);
				
				for (UPCGComponent* PCGComp : PCGComponents)
				{
					if (PCGComp && PCGComp->GetGraph())
					{
						// Group components by graph
						FString GraphName = PCGComp->GetGraph()->GetName();
						ComponentsByGraph.FindOrAdd(GraphName).Add(PCGComp);
						
						// Count node types
						TArray<UPCGNode*> Nodes = PCGComp->GetGraph()->GetNodes();
						for (UPCGNode* Node : Nodes)
						{
							if (Node && Node->GetSettings())
							{
								FString NodeType = Node->GetSettings()->GetClass()->GetName();
								NodeTypeCounts.FindOrAdd(NodeType)++;
							}
						}
					}
				}
			}
		}
	}
	
	// Check for consistency issues
	for (auto& GraphPair : ComponentsByGraph)
	{
		TArray<UPCGComponent*>& Components = GraphPair.Value;
		if (Components.Num() > 1)
		{
			// Check if components using same graph have consistent settings
			UPCGComponent* FirstComp = Components[0];
			for (int32 i = 1; i < Components.Num(); i++)
			{
				UPCGComponent* CurrentComp = Components[i];
				
				// Compare basic settings
				if (FirstComp->bActivated != CurrentComp->bActivated ||
					FirstComp->GenerationTrigger != CurrentComp->GenerationTrigger)
				{
					InconsistentComponents++;
					
					FPCGValidationIssue Issue;
					Issue.Category = EPCGValidationCategory::Quality;
					Issue.Severity = EPCGValidationSeverity::Warning;
					Issue.Title = TEXT("Inconsistent Component Settings");
					Issue.Description = FString::Printf(TEXT("Components using graph '%s' have different settings"), *GraphPair.Key);
					Issue.AffectedObject = CurrentComp;
					Issue.bCanAutoFix = true;
					Report.Issues.Add(Issue);
				}
			}
		}
	}
}
void UPCGQualityValidator::ValidatePlatformCompliance(FPCGValidationReport& Report)
{
	// Validate platform-specific compliance and performance requirements
	int32 HighPolyMeshes = 0;
	int32 LargeTextures = 0;
	int32 ComplexMaterials = 0;
	int32 TotalAssets = 0;
	
	if (UWorld* World = GWorld)
	{
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (Actor)
			{
				TArray<UStaticMeshComponent*> MeshComponents;
				Actor->GetComponents<UStaticMeshComponent>(MeshComponents);
				
				for (UStaticMeshComponent* MeshComp : MeshComponents)
				{
					if (MeshComp && MeshComp->GetStaticMesh())
					{
						TotalAssets++;
						UStaticMesh* StaticMesh = MeshComp->GetStaticMesh();
						
						// Check polygon count for mobile compliance
						if (StaticMesh->GetRenderData() && 
							StaticMesh->GetRenderData()->LODResources.IsValidIndex(0))
						{
							uint32 TriangleCount = StaticMesh->GetRenderData()->LODResources[0].GetNumTriangles();
							
							// Mobile platforms typically limit to 65k triangles per mesh
							if (TriangleCount > 65000)
							{
								HighPolyMeshes++;
								
								FPCGValidationIssue Issue;
								Issue.Category = EPCGValidationCategory::Performance;
								Issue.Severity = EPCGValidationSeverity::Error;
								Issue.Title = TEXT("High Polygon Count for Mobile");
								Issue.Description = FString::Printf(TEXT("Mesh has %d triangles (>65k limit for mobile)"), TriangleCount);
								Issue.AffectedObject = StaticMesh;
								Issue.bCanAutoFix = true;
								Report.Issues.Add(Issue);
							}
						}
					}
				}
			}
		}
	}
}
void UPCGQualityValidator::ValidateNamingConventions(FPCGValidationReport& Report)
{
	// Validate naming conventions for PCG assets and components
	int32 InvalidNames = 0;
	
	if (UWorld* World = GWorld)
	{
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (Actor)
			{
				TArray<UPCGComponent*> PCGComponents;
				Actor->GetComponents<UPCGComponent>(PCGComponents);
				
				for (UPCGComponent* PCGComp : PCGComponents)
				{
					if (PCGComp)
					{
						FString ComponentName = PCGComp->GetName();
						
						// Check for proper PCG naming convention
						if (!ComponentName.StartsWith(TEXT("PCG_")) && 
							!ComponentName.StartsWith(TEXT("BP_PCG_")))
						{
							InvalidNames++;
							
							FPCGValidationIssue Issue;
							Issue.Category = EPCGValidationCategory::Quality;
							Issue.Severity = EPCGValidationSeverity::Info;
							Issue.Title = TEXT("Invalid PCG Naming Convention");
							Issue.Description = FString::Printf(TEXT("Component '%s' should start with 'PCG_' or 'BP_PCG_'"), *ComponentName);
							Issue.AffectedObject = PCGComp;
							Issue.bCanAutoFix = true;
							Report.Issues.Add(Issue);
						}
					}
				}
			}
		}
	}
}
void UPCGQualityValidator::ValidateAssetOrganization(FPCGValidationReport& Report)
{
	// Validate organization and structure of PCG assets
	TArray<FString> UnorganizedAssets;
	int32 TotalPCGAssets = 0;
	
	// Check PCG component organization
	if (UWorld* World = GWorld)
	{
		for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
		{
			AActor* Actor = *ActorItr;
			if (Actor)
			{
				TArray<UPCGComponent*> PCGComponents;
				Actor->GetComponents<UPCGComponent>(PCGComponents);
				
				for (UPCGComponent* PCGComp : PCGComponents)
				{
					if (PCGComp)
					{
						TotalPCGAssets++;
						
						// Note: Folder organization validation not available in UE5.6 AActor API
					}
				}
			}
		}
	}
	
	// Check overall organization ratio
	float OrganizationRatio = (float)(TotalPCGAssets - UnorganizedAssets.Num()) / (float)FMath::Max(TotalPCGAssets, 1);
	if (OrganizationRatio < 0.8f) // Less than 80% organized
	{
		FPCGValidationIssue Issue;
		Issue.Category = EPCGValidationCategory::Quality;
		Issue.Severity = EPCGValidationSeverity::Warning;
		Issue.Title = TEXT("Poor Asset Organization");
		Issue.Description = FString::Printf(TEXT("Only %.1f%% of PCG assets are properly organized (<80%%)"), OrganizationRatio * 100.0f);
		Issue.bCanAutoFix = true;
		Report.Issues.Add(Issue);
	}
}

// Helper function to calculate node depth in PCG graph
int32 UPCGQualityValidator::CalculateNodeDepth(UPCGNode* Node)
{
	if (!Node)
	{
		return 0;
	}
	
	// Use a simple depth calculation based on input connections
	int32 MaxDepth = 0;
	TArray<UPCGPin*> InputPins = Node->GetInputPins();
	
	for (UPCGPin* Pin : InputPins)
	{
		if (Pin)
		{
			// Use UE5.6 compatible API to get edges
			for (const UPCGEdge* Edge : Pin->Edges)
			{
				if (Edge && Edge->InputPin && Edge->InputPin->Node)
				{
					int32 InputDepth = CalculateNodeDepth(Edge->InputPin->Node);
					MaxDepth = FMath::Max(MaxDepth, InputDepth + 1);
				}
			}
		}
	}
	
	return MaxDepth;
}

// Implementação da função ValidateCacheEfficiency
void UPCGQualityValidator::ValidateCacheEfficiency(FPCGValidationReport& Report)
{
	// Validar eficiência do cache PCG
	if (UWorld* World = GetWorld())
	{
		// Verificar se há cache managers ativos
		TArray<APCGCacheManager*> CacheManagers;
		for (TActorIterator<APCGCacheManager> ActorItr(World); ActorItr; ++ActorItr)
		{
			if (APCGCacheManager* CacheManagerActor = *ActorItr)
			{
				CacheManagers.Add(CacheManagerActor);
			}
		}

		if (CacheManagers.Num() == 0)
		{
			FPCGValidationIssue Issue;
			Issue.Title = TEXT("No Cache Manager Found");
			Issue.Description = TEXT("No PCG Cache Manager found in the world. Consider adding one for better performance.");
			Issue.Severity = EPCGValidationSeverity::Warning;
			Issue.Category = EPCGValidationCategory::Performance;
			Issue.Location = FVector::ZeroVector;
			Report.Issues.Add(Issue);
			return;
		}

		// Verificar eficiência de cada cache manager
		for (APCGCacheManager* CacheManagerActor : CacheManagers)
		{
			if (CacheManagerActor)
			{
				float HitRate = CacheManagerActor->GetCacheHitRate();
				if (HitRate < 0.5f) // 50% hit rate mínimo
				{
					FPCGValidationIssue Issue;
					Issue.Title = TEXT("Low Cache Hit Rate");
					Issue.Description = FString::Printf(TEXT("Cache hit rate is %.1f%%, consider optimizing cache settings"), HitRate * 100.0f);
					Issue.Severity = EPCGValidationSeverity::Warning;
					Issue.Category = EPCGValidationCategory::Performance;
					Issue.Location = CacheManagerActor->GetActorLocation();
					Issue.AffectedObject = CacheManagerActor;
					Report.Issues.Add(Issue);
				}

				int32 FragmentedEntries = CacheManagerActor->GetFragmentedEntryCount();
				if (FragmentedEntries > 100)
				{
					FPCGValidationIssue Issue;
					Issue.Title = TEXT("High Cache Fragmentation");
					Issue.Description = FString::Printf(TEXT("Cache has %d fragmented entries, consider cleanup"), FragmentedEntries);
					Issue.Severity = EPCGValidationSeverity::Info;
					Issue.Category = EPCGValidationCategory::Performance;
					Issue.Location = CacheManagerActor->GetActorLocation();
					Issue.AffectedObject = CacheManagerActor;
					Issue.bCanAutoFix = true;
					Report.Issues.Add(Issue);
				}
			}
		}
	}
}