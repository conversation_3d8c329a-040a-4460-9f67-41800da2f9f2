#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "HAL/FileManager.h"
#include "Misc/DateTime.h"
#include "Serialization/Archive.h"
#include "UPCGVersionManager.generated.h"

// Forward Declarations
class ALaneManager;
class ABaronAuracronManager;
class ADragonPrismalManager;
class ARiverPrismalManager;
class AWallCollisionManager;
class AMinionWaveManager;
class AMapManager;

/**
 * Enums para sistema de versionamento
 */
UENUM(BlueprintType)
enum class EPCGVersionType : uint8
{
	None = 0,
	Minor = 1,
	Major = 2,
	Hotfix = 3,
	Snapshot = 4
};

UENUM(BlueprintType)
enum class EPCGBackupStatus : uint8
{
	Pending = 0,
	InProgress = 1,
	Completed = 2,
	Failed = 3,
	Corrupted = 4
};

UENUM(BlueprintType)
enum class EPCGRestoreMode : uint8
{
	Full = 0,
	Partial = 1,
	GeometryOnly = 2,
	ConfigOnly = 3
};

/**
 * Estruturas de dados para versionamento
 */
USTRUCT(BlueprintType)
struct AURA_API FPCGVersionInfo
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Version")
	int32 MajorVersion = 1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Version")
	int32 MinorVersion = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Version")
	int32 PatchVersion = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Version")
	FString BuildNumber;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Version")
	FDateTime CreationTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Version")
	FString Author;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Version")
	FString Description;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Version")
	TArray<FString> ChangedFiles;

	FPCGVersionInfo()
	{
		CreationTime = FDateTime::Now();
		Author = TEXT("PCG System");
		BuildNumber = FString::Printf(TEXT("%d%02d%02d_%02d%02d"), 
			CreationTime.GetYear(), CreationTime.GetMonth(), CreationTime.GetDay(),
			CreationTime.GetHour(), CreationTime.GetMinute());
	}

	FString GetVersionString() const
	{
		return FString::Printf(TEXT("%d.%d.%d-%s"), MajorVersion, MinorVersion, PatchVersion, *BuildNumber);
	}
};

USTRUCT(BlueprintType)
struct AURA_API FPCGBackupData
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Backup")
	FPCGVersionInfo VersionInfo;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Backup")
	FString BackupPath;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Backup")
	int64 BackupSize = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Backup")
	EPCGBackupStatus Status = EPCGBackupStatus::Pending;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Backup")
	FString Checksum;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Backup")
	TMap<FString, FString> ComponentData;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Backup")
	TArray<uint8> CompressedData;

	FPCGBackupData()
	{
		BackupPath = TEXT("");
		Checksum = TEXT("");
	}
};

USTRUCT(BlueprintType)
struct AURA_API FPCGVersionConfig
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Config")
	bool bAutoBackupEnabled = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Config")
	float AutoBackupInterval = 300.0f; // 5 minutos

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Config")
	int32 MaxBackupCount = 50;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Config")
	bool bCompressBackups = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Config")
	bool bValidateChecksums = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Config")
	FString BackupDirectory;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Config")
	TArray<FString> ExcludedPaths;

	FPCGVersionConfig()
	{
		BackupDirectory = TEXT("Saved/Backups/PCG");
		ExcludedPaths.Add(TEXT("Temp"));
		ExcludedPaths.Add(TEXT("Cache"));
	}
};

/**
 * Delegates para eventos de versionamento
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnBackupCompleted, bool, bSuccess, const FPCGVersionInfo&, VersionInfo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnRestoreCompleted, bool, bSuccess, const FPCGVersionInfo&, VersionInfo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnVersionCreated, const FPCGVersionInfo&, VersionInfo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnBackupProgress, float, Progress, const FString&, CurrentFile);

/**
 * Classe principal para gerenciamento de versões e backup do mapa MOBA
 */
UCLASS(BlueprintType, Blueprintable)
class AURA_API UPCGVersionManager : public UObject
{
	GENERATED_BODY()

public:
	UPCGVersionManager();

	// === Inicialização e Configuração ===
	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	void Initialize(const FPCGVersionConfig& InConfig);

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	void Shutdown();

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	void SetConfiguration(const FPCGVersionConfig& InConfig);

	UFUNCTION(BlueprintPure, Category = "PCG Version Manager")
	FPCGVersionConfig GetConfiguration() const { return Config; }

	// === Gerenciamento de Versões ===
	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	FPCGVersionInfo CreateVersion(EPCGVersionType VersionType, const FString& Description, const FString& Author = TEXT(""));

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	bool CreateBackup(const FPCGVersionInfo& VersionInfo);

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	bool RestoreVersion(const FPCGVersionInfo& VersionInfo, EPCGRestoreMode RestoreMode = EPCGRestoreMode::Full);

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	bool DeleteVersion(const FPCGVersionInfo& VersionInfo);

	// === Consulta de Versões ===
	UFUNCTION(BlueprintPure, Category = "PCG Version Manager")
	TArray<FPCGVersionInfo> GetAllVersions() const;

	UFUNCTION(BlueprintPure, Category = "PCG Version Manager")
	FPCGVersionInfo GetCurrentVersion() const { return CurrentVersion; }

	UFUNCTION(BlueprintPure, Category = "PCG Version Manager")
	FPCGVersionInfo GetLatestVersion() const;

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	TArray<FPCGVersionInfo> FindVersionsByAuthor(const FString& Author) const;

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	TArray<FPCGVersionInfo> FindVersionsByDateRange(const FDateTime& StartDate, const FDateTime& EndDate) const;

	// === Backup e Restore ===
	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	void StartAutoBackup();

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	void StopAutoBackup();

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	bool IsAutoBackupRunning() const { return bAutoBackupActive; }

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	void ForceBackup(const FString& Description = TEXT("Manual Backup"));

	// === Validação e Integridade ===
	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	bool ValidateBackup(const FPCGVersionInfo& VersionInfo);

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	bool RepairCorruptedBackup(const FPCGVersionInfo& VersionInfo);

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	void CleanupOldBackups();

	// === Comparação de Versões ===
	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	TArray<FString> CompareVersions(const FPCGVersionInfo& VersionA, const FPCGVersionInfo& VersionB);

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	FString GenerateVersionDiff(const FPCGVersionInfo& VersionA, const FPCGVersionInfo& VersionB);

	// === Exportação e Importação ===
	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	bool ExportVersion(const FPCGVersionInfo& VersionInfo, const FString& ExportPath);

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	bool ImportVersion(const FString& ImportPath);

	// === Integração com Componentes MOBA ===
	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	void RegisterMOBAComponent(const FString& ComponentName, UObject* Component);

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	void UnregisterMOBAComponent(const FString& ComponentName);

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	bool BackupMOBAComponent(const FString& ComponentName);

	UFUNCTION(BlueprintCallable, Category = "PCG Version Manager")
	bool RestoreMOBAComponent(const FString& ComponentName, const FPCGVersionInfo& VersionInfo);

	// === Delegates ===
	UPROPERTY(BlueprintAssignable, Category = "PCG Version Manager")
	FOnBackupCompleted OnBackupCompleted;

	UPROPERTY(BlueprintAssignable, Category = "PCG Version Manager")
	FOnRestoreCompleted OnRestoreCompleted;

	UPROPERTY(BlueprintAssignable, Category = "PCG Version Manager")
	FOnVersionCreated OnVersionCreated;

	UPROPERTY(BlueprintAssignable, Category = "PCG Version Manager")
	FOnBackupProgress OnBackupProgress;

protected:
	// === Configuração ===
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Config")
	FPCGVersionConfig Config;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Version")
	FPCGVersionInfo CurrentVersion;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Version")
	TArray<FPCGVersionInfo> VersionHistory;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Backup")
	TMap<FString, FPCGBackupData> BackupDatabase;

	// === Estado do Sistema ===
	UPROPERTY(BlueprintReadOnly, Category = "State")
	bool bIsInitialized = false;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	bool bAutoBackupActive = false;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	float LastBackupTime = 0.0f;

	// === Componentes Registrados ===
	UPROPERTY()
	TMap<FString, TWeakObjectPtr<UObject>> RegisteredComponents;

	// === Timer Handle ===
	FTimerHandle AutoBackupTimerHandle;

private:
	// === Funções Internas ===
	void PerformAutoBackup();
	bool SerializeComponentData(UObject* Component, TArray<uint8>& OutData);
	bool DeserializeComponentData(UObject* Component, const TArray<uint8>& InData);
	FString CalculateChecksum(const TArray<uint8>& Data);
	bool CompressData(const TArray<uint8>& InData, TArray<uint8>& OutCompressedData);
	bool DecompressData(const TArray<uint8>& InCompressedData, TArray<uint8>& OutData);
	void SaveVersionDatabase();
	void LoadVersionDatabase();
	bool CreateBackupDirectory();
	FString GetBackupFilePath(const FPCGVersionInfo& VersionInfo);
	void UpdateBackupProgress(float Progress, const FString& CurrentFile);

	// === Integração com Sistemas MOBA ===
	bool BackupLaneGeometry();
	bool BackupObjectiveData();
	bool BackupRiverConfiguration();
	bool BackupWallCollisions();
	bool BackupMinionWaveSettings();

	bool RestoreLaneGeometry(const FPCGVersionInfo& VersionInfo);
	bool RestoreObjectiveData(const FPCGVersionInfo& VersionInfo);
	bool RestoreRiverConfiguration(const FPCGVersionInfo& VersionInfo);
	bool RestoreWallCollisions(const FPCGVersionInfo& VersionInfo);
	bool RestoreMinionWaveSettings(const FPCGVersionInfo& VersionInfo);

	// === Validação Específica MOBA ===
	bool ValidateMOBAGeometry(const FPCGVersionInfo& VersionInfo);
	bool ValidateLaneIntegrity(const FPCGVersionInfo& VersionInfo);
	bool ValidateObjectivePositions(const FPCGVersionInfo& VersionInfo);
	bool ValidateRiverFlow(const FPCGVersionInfo& VersionInfo);
};