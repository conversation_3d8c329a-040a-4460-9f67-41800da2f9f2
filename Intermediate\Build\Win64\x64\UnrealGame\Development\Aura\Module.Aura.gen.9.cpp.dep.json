{"Version": "1.2", "Data": {"Source": "c:\\aura\\intermediate\\build\\win64\\x64\\unrealgame\\development\\aura\\module.aura.gen.9.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\intermediate\\build\\win64\\x64\\aura\\development\\engine\\sharedpch.engine.project.rtti.exceptions.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\intermediate\\build\\win64\\x64\\unrealgame\\development\\aura\\definitions.aura.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\upcgqualityvalidator.gen.cpp", "c:\\aura\\source\\aura\\public\\upcgqualityvalidator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\noexporttypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\serialization\\testundeclaredscriptstructobjectreferences.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\polyglottextdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\assetregistry\\arfilter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgcommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgcommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcggraphexecutioninspection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgcrc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgcrc.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\data\\pcgdataptrwrapper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgdataptrwrapper.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgattributepropertyselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgpoint.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\helpers\\pcgpointhelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgpoint.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgattributepropertyselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgmetadatacommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgmetadatacommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgnode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\graph\\pcgstackcontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\utils\\pcgextracapture.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgstackcontext.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcggraphexecutionstateinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgdebug.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgdebug.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgelement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgpin.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgpin.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\elements\\pcgactorselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgactorselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\utils\\pcgpreconfiguration.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgmetadataattributetraits.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\defaultvaluehelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgmetadataattributetraits.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgpreconfiguration.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\tests\\determinism\\pcgdeterminismsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgdeterminismsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\algo\\allof.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcggraphexecutionstateinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\grid\\pcggriddescriptor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerasset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayertype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\datalayertype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\datalayerasset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\hlod\\hlodlayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\meshmerge\\meshmergingsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\meshmergingsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\meshmerge\\meshproxysettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\meshproxysettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\meshmerge\\meshapproximationsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\meshapproximationsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\hlod\\hlodbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\hlodbuilder.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\hlodlayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcggriddescriptor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealgame\\inc\\pcg\\uht\\pcgcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscape.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapephysicalmaterial.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapeinfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\ilandscapesplineinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\landscape\\uht\\ilandscapesplineinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\landscape\\uht\\landscapeinfo.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapeweightmapusage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\landscape\\uht\\landscapeweightmapusage.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\texture2darray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\texture2darray.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\landscape\\uht\\landscapecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapeproxy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapenanitecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapedataaccess.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\staticmeshdescription\\public\\staticmeshattributes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\meshdescription\\public\\meshattributes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\landscape\\uht\\landscapenanitecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapeheightfieldcollisioncomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\landscape\\uht\\landscapeheightfieldcollisioncomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\actorpartition\\partitionactor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\partitionactor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\landscape\\uht\\landscapeproxy.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeblueprintbrushbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeedittypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\landscape\\uht\\landscapeedittypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeeditlayerrenderer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapetexturehash.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\landscape\\uht\\landscapetexturehash.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphevent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphevent.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphblackboard.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\generatedtypename.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphpass.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphparameter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphresources.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rhi\\public\\rhitransientresourceallocator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphresources.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphparameters.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphtrace.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphvalidation.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\experimental\\containers\\robinhoodhashtable.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphbuilder.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeeditlayerrendererstate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeeditlayertargettypestate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeeditlayertypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\landscape\\uht\\landscapeeditlayerrenderer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\landscape\\uht\\landscapeblueprintbrushbase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapelayerinfoobject.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\landscape\\uht\\landscapelayerinfoobject.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\landscape\\uht\\landscape.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\instancedstaticmeshcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\elements\\sminstance\\sminstancemanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\elements\\sminstance\\sminstanceelementid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\instancedstaticmeshdelegates.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\sminstanceelementid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\sminstancemanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\instancedatasceneproxy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\rendering\\renderingspatialhash.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\instancedstaticmesh\\isminstancedatamanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\instancedstaticmesh\\isminstancedatasceneproxy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\instancedstaticmesh\\instanceattributetracker.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\instancedstaticmeshcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\texturerendertarget2d.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\texturerendertarget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\texturerendertarget.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\texturerendertarget2d.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendertargetpool.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\upcgqualityvalidator.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\upcgversionmanager.gen.cpp", "c:\\aura\\source\\aura\\public\\upcgversionmanager.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\upcgversionmanager.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}