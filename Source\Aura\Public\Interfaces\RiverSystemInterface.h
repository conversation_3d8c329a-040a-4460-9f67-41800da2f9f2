#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "Engine/Engine.h"
#include "Components/SplineComponent.h"
#include "RiverSystemInterface.generated.h"

USTRUCT(BlueprintType)
struct AURA_API FRiverGenerationParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River")
    FVector StartPosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River")
    FVector EndPosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River")
    float Width;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River")
    float Depth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River")
    float FlowSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River")
    bool bGenerateIsland;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River")
    float IslandRadius;

    FRiverGenerationParams()
    {
        StartPosition = FVector(-5000.0f, 0.0f, 0.0f);
        EndPosition = FVector(5000.0f, 0.0f, 0.0f);
        Width = 800.0f;
        Depth = 200.0f;
        FlowSpeed = 100.0f;
        bGenerateIsland = true;
        IslandRadius = 400.0f;
    }
};

UINTERFACE(MinimalAPI, BlueprintType)
class URiverSystemInterface : public UInterface
{
    GENERATED_BODY()
};

/**
 * Interface moderna do UE 5.6 para sistemas de rio
 * Implementa padrões robustos de configuração de rios
 */
class AURA_API IRiverSystemInterface
{
    GENERATED_BODY()

public:
    /**
     * Configura o rio com parâmetros específicos
     * @param Params - Parâmetros de configuração do rio
     * @return true se configurado com sucesso
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "River System")
    bool ConfigureRiver(const FRiverGenerationParams& Params);

    /**
     * Define pontos do spline do rio
     * @param SplinePoints - Array de pontos do spline
     * @return true se configurado com sucesso
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "River System")
    bool SetSplinePoints(const TArray<FVector>& SplinePoints);

    /**
     * Gera o mesh do rio baseado na configuração atual
     * @return true se gerado com sucesso
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "River System")
    bool GenerateRiverMesh();

    /**
     * Obtém informações do rio em uma posição específica
     * @param Position - Posição para consultar
     * @return Profundidade da água na posição
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "River System")
    float GetWaterDepthAtPosition(const FVector& Position);

    /**
     * Verifica se uma posição está na água
     * @param Position - Posição para verificar
     * @return true se está na água
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "River System")
    bool IsPositionInWater(const FVector& Position);

    /**
     * Obtém a velocidade do fluxo em uma posição
     * @param Position - Posição para consultar
     * @return Vetor de velocidade do fluxo
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "River System")
    FVector GetFlowVelocityAtPosition(const FVector& Position);

    /**
     * Obtém a posição da superfície da água
     * @param Position - Posição de referência
     * @return Posição na superfície da água
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "River System")
    FVector GetWaterSurfacePosition(const FVector& Position);

    /**
     * Configura uma ponte sobre o rio
     * @param BridgePosition - Posição da ponte
     * @param BridgeLength - Comprimento da ponte
     * @param BridgeWidth - Largura da ponte
     * @return true se ponte criada com sucesso
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "River System")
    bool CreateBridge(const FVector& BridgePosition, float BridgeLength, float BridgeWidth);

    /**
     * Remove uma ponte do rio
     * @param BridgeIndex - Índice da ponte para remover
     * @return true se removida com sucesso
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "River System")
    bool RemoveBridge(int32 BridgeIndex);

    /**
     * Obtém todas as pontes configuradas
     * @return Array com posições das pontes
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "River System")
    TArray<FVector> GetAllBridgePositions();

    /**
     * Valida a configuração atual do rio
     * @return true se configuração é válida
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "River System")
    bool ValidateRiverConfiguration();

    /**
     * Limpa todos os dados do rio
     * @return true se limpeza bem-sucedida
     */
    UFUNCTION(BlueprintImplementableEvent, Category = "River System")
    bool ClearRiverData();
};
