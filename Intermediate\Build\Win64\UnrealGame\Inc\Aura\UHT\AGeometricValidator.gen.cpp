// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AGeometricValidator.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAGeometricValidator() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_AGeometricValidator();
AURA_API UClass* Z_Construct_UClass_AGeometricValidator_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EGeometricShape();
AURA_API UEnum* Z_Construct_UEnum_Aura_EValidationSeverity();
AURA_API UEnum* Z_Construct_UEnum_Aura_EValidationType();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FGeometricShape();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FValidationConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FValidationResult();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EValidationType ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EValidationType;
static UEnum* EValidationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EValidationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EValidationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EValidationType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EValidationType"));
	}
	return Z_Registration_Info_UEnum_EValidationType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EValidationType>()
{
	return EValidationType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EValidationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Angle.DisplayName", "Angle" },
		{ "Angle.Name", "EValidationType::Angle" },
		{ "Area.DisplayName", "Area" },
		{ "Area.Name", "EValidationType::Area" },
		{ "BlueprintType", "true" },
		{ "Collinearity.DisplayName", "Collinearity" },
		{ "Collinearity.Name", "EValidationType::Collinearity" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums para tipos de valida\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "Containment.DisplayName", "Containment" },
		{ "Containment.Name", "EValidationType::Containment" },
		{ "Distance.DisplayName", "Distance" },
		{ "Distance.Name", "EValidationType::Distance" },
		{ "Intersection.DisplayName", "Intersection" },
		{ "Intersection.Name", "EValidationType::Intersection" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
		{ "Parallelism.DisplayName", "Parallelism" },
		{ "Parallelism.Name", "EValidationType::Parallelism" },
		{ "Perpendicularity.DisplayName", "Perpendicularity" },
		{ "Perpendicularity.Name", "EValidationType::Perpendicularity" },
		{ "Symmetry.DisplayName", "Symmetry" },
		{ "Symmetry.Name", "EValidationType::Symmetry" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums para tipos de valida\xc3\xa7\xc3\xa3o" },
#endif
		{ "Volume.DisplayName", "Volume" },
		{ "Volume.Name", "EValidationType::Volume" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EValidationType::Distance", (int64)EValidationType::Distance },
		{ "EValidationType::Angle", (int64)EValidationType::Angle },
		{ "EValidationType::Area", (int64)EValidationType::Area },
		{ "EValidationType::Volume", (int64)EValidationType::Volume },
		{ "EValidationType::Intersection", (int64)EValidationType::Intersection },
		{ "EValidationType::Containment", (int64)EValidationType::Containment },
		{ "EValidationType::Symmetry", (int64)EValidationType::Symmetry },
		{ "EValidationType::Parallelism", (int64)EValidationType::Parallelism },
		{ "EValidationType::Perpendicularity", (int64)EValidationType::Perpendicularity },
		{ "EValidationType::Collinearity", (int64)EValidationType::Collinearity },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EValidationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EValidationType",
	"EValidationType",
	Z_Construct_UEnum_Aura_EValidationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EValidationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EValidationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EValidationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EValidationType()
{
	if (!Z_Registration_Info_UEnum_EValidationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EValidationType.InnerSingleton, Z_Construct_UEnum_Aura_EValidationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EValidationType.InnerSingleton;
}
// ********** End Enum EValidationType *************************************************************

// ********** Begin Enum EValidationSeverity *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EValidationSeverity;
static UEnum* EValidationSeverity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EValidationSeverity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EValidationSeverity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EValidationSeverity, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EValidationSeverity"));
	}
	return Z_Registration_Info_UEnum_EValidationSeverity.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EValidationSeverity>()
{
	return EValidationSeverity_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EValidationSeverity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EValidationSeverity::Critical" },
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EValidationSeverity::Error" },
		{ "Info.DisplayName", "Info" },
		{ "Info.Name", "EValidationSeverity::Info" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
		{ "Warning.DisplayName", "Warning" },
		{ "Warning.Name", "EValidationSeverity::Warning" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EValidationSeverity::Info", (int64)EValidationSeverity::Info },
		{ "EValidationSeverity::Warning", (int64)EValidationSeverity::Warning },
		{ "EValidationSeverity::Error", (int64)EValidationSeverity::Error },
		{ "EValidationSeverity::Critical", (int64)EValidationSeverity::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EValidationSeverity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EValidationSeverity",
	"EValidationSeverity",
	Z_Construct_UEnum_Aura_EValidationSeverity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EValidationSeverity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EValidationSeverity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EValidationSeverity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EValidationSeverity()
{
	if (!Z_Registration_Info_UEnum_EValidationSeverity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EValidationSeverity.InnerSingleton, Z_Construct_UEnum_Aura_EValidationSeverity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EValidationSeverity.InnerSingleton;
}
// ********** End Enum EValidationSeverity *********************************************************

// ********** Begin Enum EGeometricShape ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EGeometricShape;
static UEnum* EGeometricShape_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EGeometricShape.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EGeometricShape.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EGeometricShape, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EGeometricShape"));
	}
	return Z_Registration_Info_UEnum_EGeometricShape.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EGeometricShape>()
{
	return EGeometricShape_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EGeometricShape_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Box.DisplayName", "Box" },
		{ "Box.Name", "EGeometricShape::Box" },
		{ "Circle.DisplayName", "Circle" },
		{ "Circle.Name", "EGeometricShape::Circle" },
		{ "Cylinder.DisplayName", "Cylinder" },
		{ "Cylinder.Name", "EGeometricShape::Cylinder" },
		{ "Ellipse.DisplayName", "Ellipse" },
		{ "Ellipse.Name", "EGeometricShape::Ellipse" },
		{ "Hexagon.DisplayName", "Hexagon" },
		{ "Hexagon.Name", "EGeometricShape::Hexagon" },
		{ "Line.DisplayName", "Line" },
		{ "Line.Name", "EGeometricShape::Line" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
		{ "Plane.DisplayName", "Plane" },
		{ "Plane.Name", "EGeometricShape::Plane" },
		{ "Point.DisplayName", "Point" },
		{ "Point.Name", "EGeometricShape::Point" },
		{ "Polygon.DisplayName", "Polygon" },
		{ "Polygon.Name", "EGeometricShape::Polygon" },
		{ "Ray.DisplayName", "Ray" },
		{ "Ray.Name", "EGeometricShape::Ray" },
		{ "Rectangle.DisplayName", "Rectangle" },
		{ "Rectangle.Name", "EGeometricShape::Rectangle" },
		{ "Segment.DisplayName", "Segment" },
		{ "Segment.Name", "EGeometricShape::Segment" },
		{ "Sphere.DisplayName", "Sphere" },
		{ "Sphere.Name", "EGeometricShape::Sphere" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EGeometricShape::Point", (int64)EGeometricShape::Point },
		{ "EGeometricShape::Line", (int64)EGeometricShape::Line },
		{ "EGeometricShape::Ray", (int64)EGeometricShape::Ray },
		{ "EGeometricShape::Segment", (int64)EGeometricShape::Segment },
		{ "EGeometricShape::Circle", (int64)EGeometricShape::Circle },
		{ "EGeometricShape::Ellipse", (int64)EGeometricShape::Ellipse },
		{ "EGeometricShape::Rectangle", (int64)EGeometricShape::Rectangle },
		{ "EGeometricShape::Polygon", (int64)EGeometricShape::Polygon },
		{ "EGeometricShape::Hexagon", (int64)EGeometricShape::Hexagon },
		{ "EGeometricShape::Sphere", (int64)EGeometricShape::Sphere },
		{ "EGeometricShape::Box", (int64)EGeometricShape::Box },
		{ "EGeometricShape::Cylinder", (int64)EGeometricShape::Cylinder },
		{ "EGeometricShape::Plane", (int64)EGeometricShape::Plane },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EGeometricShape_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EGeometricShape",
	"EGeometricShape",
	Z_Construct_UEnum_Aura_EGeometricShape_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EGeometricShape_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EGeometricShape_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EGeometricShape_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EGeometricShape()
{
	if (!Z_Registration_Info_UEnum_EGeometricShape.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EGeometricShape.InnerSingleton, Z_Construct_UEnum_Aura_EGeometricShape_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EGeometricShape.InnerSingleton;
}
// ********** End Enum EGeometricShape *************************************************************

// ********** Begin ScriptStruct FValidationResult *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FValidationResult;
class UScriptStruct* FValidationResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FValidationResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FValidationResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FValidationResult, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("ValidationResult"));
	}
	return Z_Registration_Info_UScriptStruct_FValidationResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FValidationResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estruturas de dados para valida\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estruturas de dados para valida\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValid_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationType_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Severity_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExpectedValue_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActualValue_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tolerance_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Deviation_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValid;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ValidationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ValidationType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Severity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExpectedValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActualValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Deviation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FValidationResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_bIsValid_SetBit(void* Obj)
{
	((FValidationResult*)Obj)->bIsValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_bIsValid = { "bIsValid", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FValidationResult), &Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_bIsValid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValid_MetaData), NewProp_bIsValid_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_ValidationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_ValidationType = { "ValidationType", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationResult, ValidationType), Z_Construct_UEnum_Aura_EValidationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationType_MetaData), NewProp_ValidationType_MetaData) }; // 320457599
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Severity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationResult, Severity), Z_Construct_UEnum_Aura_EValidationSeverity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Severity_MetaData), NewProp_Severity_MetaData) }; // 258873626
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationResult, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationResult, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_ExpectedValue = { "ExpectedValue", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationResult, ExpectedValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExpectedValue_MetaData), NewProp_ExpectedValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_ActualValue = { "ActualValue", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationResult, ActualValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActualValue_MetaData), NewProp_ActualValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationResult, Tolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tolerance_MetaData), NewProp_Tolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Deviation = { "Deviation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationResult, Deviation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Deviation_MetaData), NewProp_Deviation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationResult, Timestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FValidationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_bIsValid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_ValidationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_ValidationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Severity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Severity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_ExpectedValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_ActualValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Deviation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationResult_Statics::NewProp_Timestamp,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FValidationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FValidationResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"ValidationResult",
	Z_Construct_UScriptStruct_FValidationResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FValidationResult_Statics::PropPointers),
	sizeof(FValidationResult),
	alignof(FValidationResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FValidationResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FValidationResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FValidationResult()
{
	if (!Z_Registration_Info_UScriptStruct_FValidationResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FValidationResult.InnerSingleton, Z_Construct_UScriptStruct_FValidationResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FValidationResult.InnerSingleton;
}
// ********** End ScriptStruct FValidationResult ***************************************************

// ********** Begin ScriptStruct FGeometricShape ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FGeometricShape;
class UScriptStruct* FGeometricShape::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FGeometricShape.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FGeometricShape.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FGeometricShape, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("GeometricShape"));
	}
	return Z_Registration_Info_UScriptStruct_FGeometricShape.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FGeometricShape_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShapeType_MetaData[] = {
		{ "Category", "Shape" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "Category", "Shape" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Shape" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "Category", "Shape" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Points_MetaData[] = {
		{ "Category", "Shape" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "Category", "Shape" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Dimensions_MetaData[] = {
		{ "Category", "Shape" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShapeID_MetaData[] = {
		{ "Category", "Shape" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ShapeType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ShapeType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Points_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Points;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Dimensions;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ShapeID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FGeometricShape>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_ShapeType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_ShapeType = { "ShapeType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeometricShape, ShapeType), Z_Construct_UEnum_Aura_EGeometricShape, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShapeType_MetaData), NewProp_ShapeType_MetaData) }; // 1638157365
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeometricShape, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeometricShape, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeometricShape, Scale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Points_Inner = { "Points", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeometricShape, Points), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Points_MetaData), NewProp_Points_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeometricShape, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Dimensions = { "Dimensions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeometricShape, Dimensions), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Dimensions_MetaData), NewProp_Dimensions_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_ShapeID = { "ShapeID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGeometricShape, ShapeID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShapeID_MetaData), NewProp_ShapeID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FGeometricShape_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_ShapeType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_ShapeType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Scale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Points_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_Dimensions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGeometricShape_Statics::NewProp_ShapeID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGeometricShape_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FGeometricShape_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"GeometricShape",
	Z_Construct_UScriptStruct_FGeometricShape_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGeometricShape_Statics::PropPointers),
	sizeof(FGeometricShape),
	alignof(FGeometricShape),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGeometricShape_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FGeometricShape_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FGeometricShape()
{
	if (!Z_Registration_Info_UScriptStruct_FGeometricShape.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FGeometricShape.InnerSingleton, Z_Construct_UScriptStruct_FGeometricShape_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FGeometricShape.InnerSingleton;
}
// ********** End ScriptStruct FGeometricShape *****************************************************

// ********** Begin ScriptStruct FValidationConfig *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FValidationConfig;
class UScriptStruct* FValidationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FValidationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FValidationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FValidationConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("ValidationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FValidationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FValidationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRealTimeValidation_MetaData[] = {
		{ "Category", "Validation Config" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationInterval_MetaData[] = {
		{ "Category", "Validation Config" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceTolerance_MetaData[] = {
		{ "Category", "Validation Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// segundos\n" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngleTolerance_MetaData[] = {
		{ "Category", "Validation Config" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AreaTolerance_MetaData[] = {
		{ "Category", "Validation Config" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VolumeTolerance_MetaData[] = {
		{ "Category", "Validation Config" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDebugVisualization_MetaData[] = {
		{ "Category", "Validation Config" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogValidationResults_MetaData[] = {
		{ "Category", "Validation Config" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDebugText_MetaData[] = {
		{ "Category", "Validation Config" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxValidationHistory_MetaData[] = {
		{ "Category", "Validation Config" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableRealTimeValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRealTimeValidation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValidationInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AngleTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AreaTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VolumeTolerance;
	static void NewProp_bShowDebugVisualization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDebugVisualization;
	static void NewProp_bLogValidationResults_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogValidationResults;
	static void NewProp_bShowDebugText_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDebugText;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxValidationHistory;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FValidationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bEnableRealTimeValidation_SetBit(void* Obj)
{
	((FValidationConfig*)Obj)->bEnableRealTimeValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bEnableRealTimeValidation = { "bEnableRealTimeValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FValidationConfig), &Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bEnableRealTimeValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRealTimeValidation_MetaData), NewProp_bEnableRealTimeValidation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_ValidationInterval = { "ValidationInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationConfig, ValidationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationInterval_MetaData), NewProp_ValidationInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_DistanceTolerance = { "DistanceTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationConfig, DistanceTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceTolerance_MetaData), NewProp_DistanceTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_AngleTolerance = { "AngleTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationConfig, AngleTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngleTolerance_MetaData), NewProp_AngleTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_AreaTolerance = { "AreaTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationConfig, AreaTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AreaTolerance_MetaData), NewProp_AreaTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_VolumeTolerance = { "VolumeTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationConfig, VolumeTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VolumeTolerance_MetaData), NewProp_VolumeTolerance_MetaData) };
void Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bShowDebugVisualization_SetBit(void* Obj)
{
	((FValidationConfig*)Obj)->bShowDebugVisualization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bShowDebugVisualization = { "bShowDebugVisualization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FValidationConfig), &Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bShowDebugVisualization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDebugVisualization_MetaData), NewProp_bShowDebugVisualization_MetaData) };
void Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bLogValidationResults_SetBit(void* Obj)
{
	((FValidationConfig*)Obj)->bLogValidationResults = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bLogValidationResults = { "bLogValidationResults", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FValidationConfig), &Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bLogValidationResults_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogValidationResults_MetaData), NewProp_bLogValidationResults_MetaData) };
void Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bShowDebugText_SetBit(void* Obj)
{
	((FValidationConfig*)Obj)->bShowDebugText = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bShowDebugText = { "bShowDebugText", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FValidationConfig), &Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bShowDebugText_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDebugText_MetaData), NewProp_bShowDebugText_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_MaxValidationHistory = { "MaxValidationHistory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FValidationConfig, MaxValidationHistory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxValidationHistory_MetaData), NewProp_MaxValidationHistory_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FValidationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bEnableRealTimeValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_ValidationInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_DistanceTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_AngleTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_AreaTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_VolumeTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bShowDebugVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bLogValidationResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_bShowDebugText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FValidationConfig_Statics::NewProp_MaxValidationHistory,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FValidationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FValidationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"ValidationConfig",
	Z_Construct_UScriptStruct_FValidationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FValidationConfig_Statics::PropPointers),
	sizeof(FValidationConfig),
	alignof(FValidationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FValidationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FValidationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FValidationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FValidationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FValidationConfig.InnerSingleton, Z_Construct_UScriptStruct_FValidationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FValidationConfig.InnerSingleton;
}
// ********** End ScriptStruct FValidationConfig ***************************************************

// ********** Begin Delegate FOnValidationResult ***************************************************
struct Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnValidationResult_Parms
	{
		FValidationResult Result;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegates para eventos de valida\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates para eventos de valida\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Result_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnValidationResult_Parms, Result), Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Result_MetaData), NewProp_Result_MetaData) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature_Statics::NewProp_Result,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnValidationResult__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature_Statics::_Script_Aura_eventOnValidationResult_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature_Statics::_Script_Aura_eventOnValidationResult_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnValidationResult_DelegateWrapper(const FMulticastScriptDelegate& OnValidationResult, FValidationResult const& Result)
{
	struct _Script_Aura_eventOnValidationResult_Parms
	{
		FValidationResult Result;
	};
	_Script_Aura_eventOnValidationResult_Parms Parms;
	Parms.Result=Result;
	OnValidationResult.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnValidationResult *****************************************************

// ********** Begin Delegate FOnValidationError ****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnValidationError_Parms
	{
		FString ErrorMessage;
		EValidationSeverity Severity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Severity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnValidationError_Parms, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::NewProp_Severity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnValidationError_Parms, Severity), Z_Construct_UEnum_Aura_EValidationSeverity, METADATA_PARAMS(0, nullptr) }; // 258873626
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::NewProp_Severity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::NewProp_Severity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnValidationError__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::_Script_Aura_eventOnValidationError_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::_Script_Aura_eventOnValidationError_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnValidationError_DelegateWrapper(const FMulticastScriptDelegate& OnValidationError, const FString& ErrorMessage, EValidationSeverity Severity)
{
	struct _Script_Aura_eventOnValidationError_Parms
	{
		FString ErrorMessage;
		EValidationSeverity Severity;
	};
	_Script_Aura_eventOnValidationError_Parms Parms;
	Parms.ErrorMessage=ErrorMessage;
	Parms.Severity=Severity;
	OnValidationError.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnValidationError ******************************************************

// ********** Begin Delegate FOnValidationComplete *************************************************
struct Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnValidationComplete_Parms
	{
		TArray<FValidationResult> Results;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Results_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Results_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Results;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::NewProp_Results_Inner = { "Results", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::NewProp_Results = { "Results", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnValidationComplete_Parms, Results), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Results_MetaData), NewProp_Results_MetaData) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::NewProp_Results_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::NewProp_Results,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnValidationComplete__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::_Script_Aura_eventOnValidationComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::_Script_Aura_eventOnValidationComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnValidationComplete_DelegateWrapper(const FMulticastScriptDelegate& OnValidationComplete, TArray<FValidationResult> const& Results)
{
	struct _Script_Aura_eventOnValidationComplete_Parms
	{
		TArray<FValidationResult> Results;
	};
	_Script_Aura_eventOnValidationComplete_Parms Parms;
	Parms.Results=Results;
	OnValidationComplete.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnValidationComplete ***************************************************

// ********** Begin Class AGeometricValidator Function ClearRegisteredShapes ***********************
struct Z_Construct_UFunction_AGeometricValidator_ClearRegisteredShapes_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Limpa todas as formas registradas\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpa todas as formas registradas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ClearRegisteredShapes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ClearRegisteredShapes", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ClearRegisteredShapes_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ClearRegisteredShapes_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AGeometricValidator_ClearRegisteredShapes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ClearRegisteredShapes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execClearRegisteredShapes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearRegisteredShapes();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ClearRegisteredShapes *************************

// ********** Begin Class AGeometricValidator Function ClearValidationHistory **********************
struct Z_Construct_UFunction_AGeometricValidator_ClearValidationHistory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Limpa hist\xc3\xb3rico de valida\xc3\xa7\xc3\xb5""es\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpa hist\xc3\xb3rico de valida\xc3\xa7\xc3\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ClearValidationHistory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ClearValidationHistory", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ClearValidationHistory_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ClearValidationHistory_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AGeometricValidator_ClearValidationHistory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ClearValidationHistory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execClearValidationHistory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearValidationHistory();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ClearValidationHistory ************************

// ********** Begin Class AGeometricValidator Function ExportValidationReport **********************
struct Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics
{
	struct GeometricValidator_eventExportValidationReport_Parms
	{
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Report" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Exporta relat\xc3\xb3rio de valida\xc3\xa7\xc3\xa3o para arquivo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Exporta relat\xc3\xb3rio de valida\xc3\xa7\xc3\xa3o para arquivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventExportValidationReport_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((GeometricValidator_eventExportValidationReport_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(GeometricValidator_eventExportValidationReport_Parms), &Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ExportValidationReport", Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::GeometricValidator_eventExportValidationReport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::GeometricValidator_eventExportValidationReport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ExportValidationReport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ExportValidationReport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execExportValidationReport)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExportValidationReport(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ExportValidationReport ************************

// ********** Begin Class AGeometricValidator Function GetErrorCount *******************************
struct Z_Construct_UFunction_AGeometricValidator_GetErrorCount_Statics
{
	struct GeometricValidator_eventGetErrorCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Status" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGeometricValidator_GetErrorCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetErrorCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_GetErrorCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetErrorCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetErrorCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_GetErrorCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "GetErrorCount", Z_Construct_UFunction_AGeometricValidator_GetErrorCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetErrorCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_GetErrorCount_Statics::GeometricValidator_eventGetErrorCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetErrorCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_GetErrorCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_GetErrorCount_Statics::GeometricValidator_eventGetErrorCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_GetErrorCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_GetErrorCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execGetErrorCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetErrorCount();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function GetErrorCount *********************************

// ********** Begin Class AGeometricValidator Function GetRegisteredShape **************************
struct Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics
{
	struct GeometricValidator_eventGetRegisteredShape_Parms
	{
		FString ShapeID;
		FGeometricShape ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Status" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m forma registrada por ID\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m forma registrada por ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShapeID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ShapeID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::NewProp_ShapeID = { "ShapeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetRegisteredShape_Parms, ShapeID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShapeID_MetaData), NewProp_ShapeID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetRegisteredShape_Parms, ReturnValue), Z_Construct_UScriptStruct_FGeometricShape, METADATA_PARAMS(0, nullptr) }; // 1180479940
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::NewProp_ShapeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "GetRegisteredShape", Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::GeometricValidator_eventGetRegisteredShape_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::GeometricValidator_eventGetRegisteredShape_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execGetRegisteredShape)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ShapeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FGeometricShape*)Z_Param__Result=P_THIS->GetRegisteredShape(Z_Param_ShapeID);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function GetRegisteredShape ****************************

// ********** Begin Class AGeometricValidator Function GetRegisteredShapes *************************
struct Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics
{
	struct GeometricValidator_eventGetRegisteredShapes_Parms
	{
		TArray<FGeometricShape> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Status" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGeometricShape, METADATA_PARAMS(0, nullptr) }; // 1180479940
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetRegisteredShapes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1180479940
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "GetRegisteredShapes", Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::GeometricValidator_eventGetRegisteredShapes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::GeometricValidator_eventGetRegisteredShapes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execGetRegisteredShapes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FGeometricShape>*)Z_Param__Result=P_THIS->GetRegisteredShapes();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function GetRegisteredShapes ***************************

// ********** Begin Class AGeometricValidator Function GetValidationConfig *************************
struct Z_Construct_UFunction_AGeometricValidator_GetValidationConfig_Statics
{
	struct GeometricValidator_eventGetValidationConfig_Parms
	{
		FValidationConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m configura\xc3\xa7\xc3\xa3o atual de valida\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m configura\xc3\xa7\xc3\xa3o atual de valida\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetValidationConfig_Parms, ReturnValue), Z_Construct_UScriptStruct_FValidationConfig, METADATA_PARAMS(0, nullptr) }; // 1341900035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_GetValidationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_GetValidationConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "GetValidationConfig", Z_Construct_UFunction_AGeometricValidator_GetValidationConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationConfig_Statics::GeometricValidator_eventGetValidationConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_GetValidationConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationConfig_Statics::GeometricValidator_eventGetValidationConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_GetValidationConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_GetValidationConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execGetValidationConfig)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FValidationConfig*)Z_Param__Result=P_THIS->GetValidationConfig();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function GetValidationConfig ***************************

// ********** Begin Class AGeometricValidator Function GetValidationCount **************************
struct Z_Construct_UFunction_AGeometricValidator_GetValidationCount_Statics
{
	struct GeometricValidator_eventGetValidationCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Status" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetValidationCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_GetValidationCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_GetValidationCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "GetValidationCount", Z_Construct_UFunction_AGeometricValidator_GetValidationCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationCount_Statics::GeometricValidator_eventGetValidationCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_GetValidationCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationCount_Statics::GeometricValidator_eventGetValidationCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_GetValidationCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_GetValidationCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execGetValidationCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetValidationCount();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function GetValidationCount ****************************

// ********** Begin Class AGeometricValidator Function GetValidationHistory ************************
struct Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics
{
	struct GeometricValidator_eventGetValidationHistory_Parms
	{
		TArray<FValidationResult> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Status" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetValidationHistory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "GetValidationHistory", Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::GeometricValidator_eventGetValidationHistory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::GeometricValidator_eventGetValidationHistory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_GetValidationHistory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_GetValidationHistory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execGetValidationHistory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FValidationResult>*)Z_Param__Result=P_THIS->GetValidationHistory();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function GetValidationHistory **************************

// ********** Begin Class AGeometricValidator Function GetValidationHistoryBySeverity **************
struct Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics
{
	struct GeometricValidator_eventGetValidationHistoryBySeverity_Parms
	{
		EValidationSeverity Severity;
		TArray<FValidationResult> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Status" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m hist\xc3\xb3rico de valida\xc3\xa7\xc3\xb5""es por severidade\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m hist\xc3\xb3rico de valida\xc3\xa7\xc3\xb5""es por severidade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Severity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::NewProp_Severity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetValidationHistoryBySeverity_Parms, Severity), Z_Construct_UEnum_Aura_EValidationSeverity, METADATA_PARAMS(0, nullptr) }; // 258873626
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetValidationHistoryBySeverity_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::NewProp_Severity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::NewProp_Severity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "GetValidationHistoryBySeverity", Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::GeometricValidator_eventGetValidationHistoryBySeverity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::GeometricValidator_eventGetValidationHistoryBySeverity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execGetValidationHistoryBySeverity)
{
	P_GET_ENUM(EValidationSeverity,Z_Param_Severity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FValidationResult>*)Z_Param__Result=P_THIS->GetValidationHistoryBySeverity(EValidationSeverity(Z_Param_Severity));
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function GetValidationHistoryBySeverity ****************

// ********** Begin Class AGeometricValidator Function GetValidationHistoryByType ******************
struct Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics
{
	struct GeometricValidator_eventGetValidationHistoryByType_Parms
	{
		EValidationType Type;
		TArray<FValidationResult> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Status" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m hist\xc3\xb3rico de valida\xc3\xa7\xc3\xb5""es por tipo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m hist\xc3\xb3rico de valida\xc3\xa7\xc3\xb5""es por tipo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetValidationHistoryByType_Parms, Type), Z_Construct_UEnum_Aura_EValidationType, METADATA_PARAMS(0, nullptr) }; // 320457599
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetValidationHistoryByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "GetValidationHistoryByType", Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::GeometricValidator_eventGetValidationHistoryByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::GeometricValidator_eventGetValidationHistoryByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execGetValidationHistoryByType)
{
	P_GET_ENUM(EValidationType,Z_Param_Type);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FValidationResult>*)Z_Param__Result=P_THIS->GetValidationHistoryByType(EValidationType(Z_Param_Type));
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function GetValidationHistoryByType ********************

// ********** Begin Class AGeometricValidator Function GetValidationProgress ***********************
struct Z_Construct_UFunction_AGeometricValidator_GetValidationProgress_Statics
{
	struct GeometricValidator_eventGetValidationProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Status" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m progresso da valida\xc3\xa7\xc3\xa3o atual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m progresso da valida\xc3\xa7\xc3\xa3o atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetValidationProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_GetValidationProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_GetValidationProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "GetValidationProgress", Z_Construct_UFunction_AGeometricValidator_GetValidationProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationProgress_Statics::GeometricValidator_eventGetValidationProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_GetValidationProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationProgress_Statics::GeometricValidator_eventGetValidationProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_GetValidationProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_GetValidationProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execGetValidationProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetValidationProgress();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function GetValidationProgress *************************

// ********** Begin Class AGeometricValidator Function GetValidationStatistics *********************
struct Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics
{
	struct GeometricValidator_eventGetValidationStatistics_Parms
	{
		TMap<FString,int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m estat\xc3\xadsticas de valida\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m estat\xc3\xadsticas de valida\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetValidationStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "GetValidationStatistics", Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::GeometricValidator_eventGetValidationStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::GeometricValidator_eventGetValidationStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execGetValidationStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,int32>*)Z_Param__Result=P_THIS->GetValidationStatistics();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function GetValidationStatistics ***********************

// ********** Begin Class AGeometricValidator Function GetValidationSummary ************************
struct Z_Construct_UFunction_AGeometricValidator_GetValidationSummary_Statics
{
	struct GeometricValidator_eventGetValidationSummary_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Status" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m resumo das valida\xc3\xa7\xc3\xb5""es\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m resumo das valida\xc3\xa7\xc3\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGeometricValidator_GetValidationSummary_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetValidationSummary_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_GetValidationSummary_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetValidationSummary_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationSummary_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_GetValidationSummary_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "GetValidationSummary", Z_Construct_UFunction_AGeometricValidator_GetValidationSummary_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationSummary_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationSummary_Statics::GeometricValidator_eventGetValidationSummary_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetValidationSummary_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_GetValidationSummary_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_GetValidationSummary_Statics::GeometricValidator_eventGetValidationSummary_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_GetValidationSummary()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_GetValidationSummary_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execGetValidationSummary)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetValidationSummary();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function GetValidationSummary **************************

// ********** Begin Class AGeometricValidator Function GetWarningCount *****************************
struct Z_Construct_UFunction_AGeometricValidator_GetWarningCount_Statics
{
	struct GeometricValidator_eventGetWarningCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Status" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGeometricValidator_GetWarningCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventGetWarningCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_GetWarningCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_GetWarningCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetWarningCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_GetWarningCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "GetWarningCount", Z_Construct_UFunction_AGeometricValidator_GetWarningCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetWarningCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_GetWarningCount_Statics::GeometricValidator_eventGetWarningCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_GetWarningCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_GetWarningCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_GetWarningCount_Statics::GeometricValidator_eventGetWarningCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_GetWarningCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_GetWarningCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execGetWarningCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetWarningCount();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function GetWarningCount *******************************

// ********** Begin Class AGeometricValidator Function IsValidating ********************************
struct Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics
{
	struct GeometricValidator_eventIsValidating_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Status" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((GeometricValidator_eventIsValidating_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(GeometricValidator_eventIsValidating_Parms), &Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "IsValidating", Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::GeometricValidator_eventIsValidating_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::GeometricValidator_eventIsValidating_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_IsValidating()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_IsValidating_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execIsValidating)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsValidating();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function IsValidating **********************************

// ********** Begin Class AGeometricValidator Function PerformFullValidation ***********************
struct Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics
{
	struct GeometricValidator_eventPerformFullValidation_Parms
	{
		TArray<FValidationResult> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Executa uma valida\xc3\xa7\xc3\xa3o completa \xc3\xbanica\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Executa uma valida\xc3\xa7\xc3\xa3o completa \xc3\xbanica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventPerformFullValidation_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "PerformFullValidation", Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::GeometricValidator_eventPerformFullValidation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::GeometricValidator_eventPerformFullValidation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_PerformFullValidation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_PerformFullValidation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execPerformFullValidation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FValidationResult>*)Z_Param__Result=P_THIS->PerformFullValidation();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function PerformFullValidation *************************

// ********** Begin Class AGeometricValidator Function RegisterShape *******************************
struct Z_Construct_UFunction_AGeometricValidator_RegisterShape_Statics
{
	struct GeometricValidator_eventRegisterShape_Parms
	{
		FGeometricShape Shape;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Registra uma forma geom\xc3\xa9trica para valida\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registra uma forma geom\xc3\xa9trica para valida\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Shape_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Shape;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_RegisterShape_Statics::NewProp_Shape = { "Shape", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventRegisterShape_Parms, Shape), Z_Construct_UScriptStruct_FGeometricShape, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Shape_MetaData), NewProp_Shape_MetaData) }; // 1180479940
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_RegisterShape_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_RegisterShape_Statics::NewProp_Shape,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_RegisterShape_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_RegisterShape_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "RegisterShape", Z_Construct_UFunction_AGeometricValidator_RegisterShape_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_RegisterShape_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_RegisterShape_Statics::GeometricValidator_eventRegisterShape_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_RegisterShape_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_RegisterShape_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_RegisterShape_Statics::GeometricValidator_eventRegisterShape_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_RegisterShape()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_RegisterShape_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execRegisterShape)
{
	P_GET_STRUCT_REF(FGeometricShape,Z_Param_Out_Shape);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterShape(Z_Param_Out_Shape);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function RegisterShape *********************************

// ********** Begin Class AGeometricValidator Function ResetValidator ******************************
struct Z_Construct_UFunction_AGeometricValidator_ResetValidator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reseta completamente o validador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reseta completamente o validador" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ResetValidator_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ResetValidator", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ResetValidator_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ResetValidator_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AGeometricValidator_ResetValidator()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ResetValidator_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execResetValidator)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetValidator();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ResetValidator ********************************

// ********** Begin Class AGeometricValidator Function RunPerformanceBenchmark *********************
struct Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark_Statics
{
	struct GeometricValidator_eventRunPerformanceBenchmark_Parms
	{
		int32 NumIterations;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Executa benchmark de performance\n     */" },
#endif
		{ "CPP_Default_NumIterations", "100" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Executa benchmark de performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumIterations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark_Statics::NewProp_NumIterations = { "NumIterations", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventRunPerformanceBenchmark_Parms, NumIterations), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark_Statics::NewProp_NumIterations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "RunPerformanceBenchmark", Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark_Statics::GeometricValidator_eventRunPerformanceBenchmark_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark_Statics::GeometricValidator_eventRunPerformanceBenchmark_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execRunPerformanceBenchmark)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NumIterations);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RunPerformanceBenchmark(Z_Param_NumIterations);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function RunPerformanceBenchmark ***********************

// ********** Begin Class AGeometricValidator Function SetDebugVisualization ***********************
struct Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics
{
	struct GeometricValidator_eventSetDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativa/desativa visualiza\xc3\xa7\xc3\xa3o de debug\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativa/desativa visualiza\xc3\xa7\xc3\xa3o de debug" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((GeometricValidator_eventSetDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(GeometricValidator_eventSetDebugVisualization_Parms), &Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "SetDebugVisualization", Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::GeometricValidator_eventSetDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::GeometricValidator_eventSetDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execSetDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function SetDebugVisualization *************************

// ********** Begin Class AGeometricValidator Function SetValidationConfig *************************
struct Z_Construct_UFunction_AGeometricValidator_SetValidationConfig_Statics
{
	struct GeometricValidator_eventSetValidationConfig_Parms
	{
		FValidationConfig NewConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Define nova configura\xc3\xa7\xc3\xa3o de valida\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Define nova configura\xc3\xa7\xc3\xa3o de valida\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_SetValidationConfig_Statics::NewProp_NewConfig = { "NewConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventSetValidationConfig_Parms, NewConfig), Z_Construct_UScriptStruct_FValidationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewConfig_MetaData), NewProp_NewConfig_MetaData) }; // 1341900035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_SetValidationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_SetValidationConfig_Statics::NewProp_NewConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_SetValidationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_SetValidationConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "SetValidationConfig", Z_Construct_UFunction_AGeometricValidator_SetValidationConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_SetValidationConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_SetValidationConfig_Statics::GeometricValidator_eventSetValidationConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_SetValidationConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_SetValidationConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_SetValidationConfig_Statics::GeometricValidator_eventSetValidationConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_SetValidationConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_SetValidationConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execSetValidationConfig)
{
	P_GET_STRUCT_REF(FValidationConfig,Z_Param_Out_NewConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetValidationConfig(Z_Param_Out_NewConfig);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function SetValidationConfig ***************************

// ********** Begin Class AGeometricValidator Function StartRealTimeValidation *********************
struct Z_Construct_UFunction_AGeometricValidator_StartRealTimeValidation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Inicia a valida\xc3\xa7\xc3\xa3o em tempo real\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicia a valida\xc3\xa7\xc3\xa3o em tempo real" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_StartRealTimeValidation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "StartRealTimeValidation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_StartRealTimeValidation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_StartRealTimeValidation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AGeometricValidator_StartRealTimeValidation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_StartRealTimeValidation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execStartRealTimeValidation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartRealTimeValidation();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function StartRealTimeValidation ***********************

// ********** Begin Class AGeometricValidator Function StopRealTimeValidation **********************
struct Z_Construct_UFunction_AGeometricValidator_StopRealTimeValidation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Para a valida\xc3\xa7\xc3\xa3o em tempo real\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Para a valida\xc3\xa7\xc3\xa3o em tempo real" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_StopRealTimeValidation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "StopRealTimeValidation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_StopRealTimeValidation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_StopRealTimeValidation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AGeometricValidator_StopRealTimeValidation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_StopRealTimeValidation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execStopRealTimeValidation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopRealTimeValidation();
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function StopRealTimeValidation ************************

// ********** Begin Class AGeometricValidator Function UnregisterShape *****************************
struct Z_Construct_UFunction_AGeometricValidator_UnregisterShape_Statics
{
	struct GeometricValidator_eventUnregisterShape_Parms
	{
		FString ShapeID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remove uma forma geom\xc3\xa9trica da valida\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove uma forma geom\xc3\xa9trica da valida\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShapeID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ShapeID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGeometricValidator_UnregisterShape_Statics::NewProp_ShapeID = { "ShapeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventUnregisterShape_Parms, ShapeID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShapeID_MetaData), NewProp_ShapeID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_UnregisterShape_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_UnregisterShape_Statics::NewProp_ShapeID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_UnregisterShape_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_UnregisterShape_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "UnregisterShape", Z_Construct_UFunction_AGeometricValidator_UnregisterShape_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_UnregisterShape_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_UnregisterShape_Statics::GeometricValidator_eventUnregisterShape_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_UnregisterShape_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_UnregisterShape_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_UnregisterShape_Statics::GeometricValidator_eventUnregisterShape_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_UnregisterShape()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_UnregisterShape_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execUnregisterShape)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ShapeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterShape(Z_Param_ShapeID);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function UnregisterShape *******************************

// ********** Begin Class AGeometricValidator Function ValidateAngle *******************************
struct Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics
{
	struct GeometricValidator_eventValidateAngle_Parms
	{
		FVector Point1;
		FVector Vertex;
		FVector Point2;
		float ExpectedAngle;
		float Tolerance;
		FValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometric Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida \xc3\xa2ngulo entre tr\xc3\xaas pontos\n     */" },
#endif
		{ "CPP_Default_Tolerance", "0.100000" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida \xc3\xa2ngulo entre tr\xc3\xaas pontos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point1_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Vertex_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point2_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point1;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Vertex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point2;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExpectedAngle;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::NewProp_Point1 = { "Point1", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateAngle_Parms, Point1), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point1_MetaData), NewProp_Point1_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::NewProp_Vertex = { "Vertex", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateAngle_Parms, Vertex), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Vertex_MetaData), NewProp_Vertex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::NewProp_Point2 = { "Point2", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateAngle_Parms, Point2), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point2_MetaData), NewProp_Point2_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::NewProp_ExpectedAngle = { "ExpectedAngle", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateAngle_Parms, ExpectedAngle), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateAngle_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateAngle_Parms, ReturnValue), Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::NewProp_Point1,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::NewProp_Vertex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::NewProp_Point2,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::NewProp_ExpectedAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateAngle", Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::GeometricValidator_eventValidateAngle_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::GeometricValidator_eventValidateAngle_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateAngle()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateAngle_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateAngle)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point1);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Vertex);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point2);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ExpectedAngle);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FValidationResult*)Z_Param__Result=P_THIS->ValidateAngle(Z_Param_Out_Point1,Z_Param_Out_Vertex,Z_Param_Out_Point2,Z_Param_ExpectedAngle,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateAngle *********************************

// ********** Begin Class AGeometricValidator Function ValidateBaronHexagonalArea ******************
struct Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics
{
	struct GeometricValidator_eventValidateBaronHexagonalArea_Parms
	{
		FVector Center;
		float Radius;
		TArray<FVector> HexagonPoints;
		TArray<FValidationResult> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Objective Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida \xc3\xa1rea hexagonal do Bar\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida \xc3\xa1rea hexagonal do Bar\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HexagonPoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HexagonPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HexagonPoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateBaronHexagonalArea_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateBaronHexagonalArea_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::NewProp_HexagonPoints_Inner = { "HexagonPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::NewProp_HexagonPoints = { "HexagonPoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateBaronHexagonalArea_Parms, HexagonPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HexagonPoints_MetaData), NewProp_HexagonPoints_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateBaronHexagonalArea_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::NewProp_HexagonPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::NewProp_HexagonPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateBaronHexagonalArea", Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::GeometricValidator_eventValidateBaronHexagonalArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::GeometricValidator_eventValidateBaronHexagonalArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateBaronHexagonalArea)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_TARRAY_REF(FVector,Z_Param_Out_HexagonPoints);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FValidationResult>*)Z_Param__Result=P_THIS->ValidateBaronHexagonalArea(Z_Param_Out_Center,Z_Param_Radius,Z_Param_Out_HexagonPoints);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateBaronHexagonalArea ********************

// ********** Begin Class AGeometricValidator Function ValidateDistance ****************************
struct Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics
{
	struct GeometricValidator_eventValidateDistance_Parms
	{
		FVector Point1;
		FVector Point2;
		float ExpectedDistance;
		float Tolerance;
		FValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometric Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida dist\xc3\xa2ncia entre dois pontos\n     */" },
#endif
		{ "CPP_Default_Tolerance", "1.000000" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida dist\xc3\xa2ncia entre dois pontos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point1_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point2_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point1;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point2;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExpectedDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::NewProp_Point1 = { "Point1", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateDistance_Parms, Point1), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point1_MetaData), NewProp_Point1_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::NewProp_Point2 = { "Point2", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateDistance_Parms, Point2), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point2_MetaData), NewProp_Point2_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::NewProp_ExpectedDistance = { "ExpectedDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateDistance_Parms, ExpectedDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateDistance_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateDistance_Parms, ReturnValue), Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::NewProp_Point1,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::NewProp_Point2,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::NewProp_ExpectedDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateDistance", Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::GeometricValidator_eventValidateDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::GeometricValidator_eventValidateDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateDistance)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point1);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point2);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ExpectedDistance);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FValidationResult*)Z_Param__Result=P_THIS->ValidateDistance(Z_Param_Out_Point1,Z_Param_Out_Point2,Z_Param_ExpectedDistance,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateDistance ******************************

// ********** Begin Class AGeometricValidator Function ValidateDragonEllipticalArea ****************
struct Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics
{
	struct GeometricValidator_eventValidateDragonEllipticalArea_Parms
	{
		FVector Center;
		float SemiMajorAxis;
		float SemiMinorAxis;
		TArray<FVector> EllipsePoints;
		TArray<FValidationResult> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Objective Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida \xc3\xa1rea el\xc3\xadptica do Drag\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida \xc3\xa1rea el\xc3\xadptica do Drag\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EllipsePoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SemiMajorAxis;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SemiMinorAxis;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EllipsePoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EllipsePoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateDragonEllipticalArea_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_SemiMajorAxis = { "SemiMajorAxis", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateDragonEllipticalArea_Parms, SemiMajorAxis), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_SemiMinorAxis = { "SemiMinorAxis", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateDragonEllipticalArea_Parms, SemiMinorAxis), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_EllipsePoints_Inner = { "EllipsePoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_EllipsePoints = { "EllipsePoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateDragonEllipticalArea_Parms, EllipsePoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EllipsePoints_MetaData), NewProp_EllipsePoints_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateDragonEllipticalArea_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_SemiMajorAxis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_SemiMinorAxis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_EllipsePoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_EllipsePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateDragonEllipticalArea", Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::GeometricValidator_eventValidateDragonEllipticalArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::GeometricValidator_eventValidateDragonEllipticalArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateDragonEllipticalArea)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SemiMajorAxis);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SemiMinorAxis);
	P_GET_TARRAY_REF(FVector,Z_Param_Out_EllipsePoints);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FValidationResult>*)Z_Param__Result=P_THIS->ValidateDragonEllipticalArea(Z_Param_Out_Center,Z_Param_SemiMajorAxis,Z_Param_SemiMinorAxis,Z_Param_Out_EllipsePoints);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateDragonEllipticalArea ******************

// ********** Begin Class AGeometricValidator Function ValidateEllipse *****************************
struct Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics
{
	struct GeometricValidator_eventValidateEllipse_Parms
	{
		FVector Center;
		float SemiMajorAxis;
		float SemiMinorAxis;
		TArray<FVector> EllipsePoints;
		float Tolerance;
		FValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometric Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida geometria el\xc3\xadptica\n     */" },
#endif
		{ "CPP_Default_Tolerance", "1.000000" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida geometria el\xc3\xadptica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EllipsePoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SemiMajorAxis;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SemiMinorAxis;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EllipsePoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EllipsePoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateEllipse_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_SemiMajorAxis = { "SemiMajorAxis", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateEllipse_Parms, SemiMajorAxis), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_SemiMinorAxis = { "SemiMinorAxis", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateEllipse_Parms, SemiMinorAxis), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_EllipsePoints_Inner = { "EllipsePoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_EllipsePoints = { "EllipsePoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateEllipse_Parms, EllipsePoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EllipsePoints_MetaData), NewProp_EllipsePoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateEllipse_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateEllipse_Parms, ReturnValue), Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_SemiMajorAxis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_SemiMinorAxis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_EllipsePoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_EllipsePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateEllipse", Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::GeometricValidator_eventValidateEllipse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::GeometricValidator_eventValidateEllipse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateEllipse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateEllipse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateEllipse)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SemiMajorAxis);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SemiMinorAxis);
	P_GET_TARRAY_REF(FVector,Z_Param_Out_EllipsePoints);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FValidationResult*)Z_Param__Result=P_THIS->ValidateEllipse(Z_Param_Out_Center,Z_Param_SemiMajorAxis,Z_Param_SemiMinorAxis,Z_Param_Out_EllipsePoints,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateEllipse *******************************

// ********** Begin Class AGeometricValidator Function ValidateLaneGeometry ************************
struct Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics
{
	struct GeometricValidator_eventValidateLaneGeometry_Parms
	{
		TArray<FVector> LanePoints;
		float ExpectedWidth;
		float ExpectedLength;
		TArray<FValidationResult> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida geometria de uma lane\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida geometria de uma lane" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LanePoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_LanePoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LanePoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExpectedWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExpectedLength;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::NewProp_LanePoints_Inner = { "LanePoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::NewProp_LanePoints = { "LanePoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLaneGeometry_Parms, LanePoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LanePoints_MetaData), NewProp_LanePoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::NewProp_ExpectedWidth = { "ExpectedWidth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLaneGeometry_Parms, ExpectedWidth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::NewProp_ExpectedLength = { "ExpectedLength", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLaneGeometry_Parms, ExpectedLength), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLaneGeometry_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::NewProp_LanePoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::NewProp_LanePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::NewProp_ExpectedWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::NewProp_ExpectedLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateLaneGeometry", Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::GeometricValidator_eventValidateLaneGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::GeometricValidator_eventValidateLaneGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateLaneGeometry)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_LanePoints);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ExpectedWidth);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ExpectedLength);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FValidationResult>*)Z_Param__Result=P_THIS->ValidateLaneGeometry(Z_Param_Out_LanePoints,Z_Param_ExpectedWidth,Z_Param_ExpectedLength);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateLaneGeometry **************************

// ********** Begin Class AGeometricValidator Function ValidateLaneWaypoints ***********************
struct Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics
{
	struct GeometricValidator_eventValidateLaneWaypoints_Parms
	{
		TArray<FVector> Waypoints;
		float ExpectedSpacing;
		TArray<FValidationResult> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida waypoints de uma lane\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida waypoints de uma lane" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Waypoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Waypoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Waypoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExpectedSpacing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::NewProp_Waypoints_Inner = { "Waypoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::NewProp_Waypoints = { "Waypoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLaneWaypoints_Parms, Waypoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Waypoints_MetaData), NewProp_Waypoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::NewProp_ExpectedSpacing = { "ExpectedSpacing", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLaneWaypoints_Parms, ExpectedSpacing), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLaneWaypoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::NewProp_Waypoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::NewProp_Waypoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::NewProp_ExpectedSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateLaneWaypoints", Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::GeometricValidator_eventValidateLaneWaypoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::GeometricValidator_eventValidateLaneWaypoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateLaneWaypoints)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Waypoints);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ExpectedSpacing);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FValidationResult>*)Z_Param__Result=P_THIS->ValidateLaneWaypoints(Z_Param_Out_Waypoints,Z_Param_ExpectedSpacing);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateLaneWaypoints *************************

// ********** Begin Class AGeometricValidator Function ValidateLineIntersection ********************
struct Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics
{
	struct GeometricValidator_eventValidateLineIntersection_Parms
	{
		FVector Line1Start;
		FVector Line1End;
		FVector Line2Start;
		FVector Line2End;
		FVector ExpectedIntersection;
		float Tolerance;
		FValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometric Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida intersec\xc3\xa7\xc3\xa3o entre duas linhas\n     */" },
#endif
		{ "CPP_Default_Tolerance", "1.000000" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida intersec\xc3\xa7\xc3\xa3o entre duas linhas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Line1Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Line1End_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Line2Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Line2End_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExpectedIntersection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Line1Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Line1End;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Line2Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Line2End;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExpectedIntersection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_Line1Start = { "Line1Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLineIntersection_Parms, Line1Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Line1Start_MetaData), NewProp_Line1Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_Line1End = { "Line1End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLineIntersection_Parms, Line1End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Line1End_MetaData), NewProp_Line1End_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_Line2Start = { "Line2Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLineIntersection_Parms, Line2Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Line2Start_MetaData), NewProp_Line2Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_Line2End = { "Line2End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLineIntersection_Parms, Line2End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Line2End_MetaData), NewProp_Line2End_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_ExpectedIntersection = { "ExpectedIntersection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLineIntersection_Parms, ExpectedIntersection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExpectedIntersection_MetaData), NewProp_ExpectedIntersection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLineIntersection_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateLineIntersection_Parms, ReturnValue), Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_Line1Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_Line1End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_Line2Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_Line2End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_ExpectedIntersection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateLineIntersection", Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::GeometricValidator_eventValidateLineIntersection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::GeometricValidator_eventValidateLineIntersection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateLineIntersection)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Line1Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Line1End);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Line2Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Line2End);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ExpectedIntersection);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FValidationResult*)Z_Param__Result=P_THIS->ValidateLineIntersection(Z_Param_Out_Line1Start,Z_Param_Out_Line1End,Z_Param_Out_Line2Start,Z_Param_Out_Line2End,Z_Param_Out_ExpectedIntersection,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateLineIntersection **********************

// ********** Begin Class AGeometricValidator Function ValidateParallelism *************************
struct Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics
{
	struct GeometricValidator_eventValidateParallelism_Parms
	{
		FVector Line1Start;
		FVector Line1End;
		FVector Line2Start;
		FVector Line2End;
		float Tolerance;
		FValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometric Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida paralelismo entre duas linhas\n     */" },
#endif
		{ "CPP_Default_Tolerance", "0.100000" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida paralelismo entre duas linhas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Line1Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Line1End_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Line2Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Line2End_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Line1Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Line1End;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Line2Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Line2End;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::NewProp_Line1Start = { "Line1Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateParallelism_Parms, Line1Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Line1Start_MetaData), NewProp_Line1Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::NewProp_Line1End = { "Line1End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateParallelism_Parms, Line1End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Line1End_MetaData), NewProp_Line1End_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::NewProp_Line2Start = { "Line2Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateParallelism_Parms, Line2Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Line2Start_MetaData), NewProp_Line2Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::NewProp_Line2End = { "Line2End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateParallelism_Parms, Line2End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Line2End_MetaData), NewProp_Line2End_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateParallelism_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateParallelism_Parms, ReturnValue), Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::NewProp_Line1Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::NewProp_Line1End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::NewProp_Line2Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::NewProp_Line2End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateParallelism", Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::GeometricValidator_eventValidateParallelism_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::GeometricValidator_eventValidateParallelism_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateParallelism()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateParallelism_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateParallelism)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Line1Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Line1End);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Line2Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Line2End);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FValidationResult*)Z_Param__Result=P_THIS->ValidateParallelism(Z_Param_Out_Line1Start,Z_Param_Out_Line1End,Z_Param_Out_Line2Start,Z_Param_Out_Line2End,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateParallelism ***************************

// ********** Begin Class AGeometricValidator Function ValidatePerpendicularity ********************
struct Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics
{
	struct GeometricValidator_eventValidatePerpendicularity_Parms
	{
		FVector Line1Start;
		FVector Line1End;
		FVector Line2Start;
		FVector Line2End;
		float Tolerance;
		FValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometric Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida perpendicularidade entre duas linhas\n     */" },
#endif
		{ "CPP_Default_Tolerance", "0.100000" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida perpendicularidade entre duas linhas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Line1Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Line1End_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Line2Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Line2End_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Line1Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Line1End;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Line2Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Line2End;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::NewProp_Line1Start = { "Line1Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidatePerpendicularity_Parms, Line1Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Line1Start_MetaData), NewProp_Line1Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::NewProp_Line1End = { "Line1End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidatePerpendicularity_Parms, Line1End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Line1End_MetaData), NewProp_Line1End_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::NewProp_Line2Start = { "Line2Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidatePerpendicularity_Parms, Line2Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Line2Start_MetaData), NewProp_Line2Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::NewProp_Line2End = { "Line2End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidatePerpendicularity_Parms, Line2End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Line2End_MetaData), NewProp_Line2End_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidatePerpendicularity_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidatePerpendicularity_Parms, ReturnValue), Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::NewProp_Line1Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::NewProp_Line1End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::NewProp_Line2Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::NewProp_Line2End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidatePerpendicularity", Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::GeometricValidator_eventValidatePerpendicularity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::GeometricValidator_eventValidatePerpendicularity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidatePerpendicularity)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Line1Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Line1End);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Line2Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Line2End);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FValidationResult*)Z_Param__Result=P_THIS->ValidatePerpendicularity(Z_Param_Out_Line1Start,Z_Param_Out_Line1End,Z_Param_Out_Line2Start,Z_Param_Out_Line2End,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidatePerpendicularity **********************

// ********** Begin Class AGeometricValidator Function ValidatePointInPolygon **********************
struct Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics
{
	struct GeometricValidator_eventValidatePointInPolygon_Parms
	{
		FVector Point;
		TArray<FVector> PolygonPoints;
		bool bExpectedInside;
		FValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometric Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida se um ponto est\xc3\xa1 dentro de um pol\xc3\xadgono\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida se um ponto est\xc3\xa1 dentro de um pol\xc3\xadgono" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PolygonPoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PolygonPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PolygonPoints;
	static void NewProp_bExpectedInside_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bExpectedInside;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidatePointInPolygon_Parms, Point), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::NewProp_PolygonPoints_Inner = { "PolygonPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::NewProp_PolygonPoints = { "PolygonPoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidatePointInPolygon_Parms, PolygonPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PolygonPoints_MetaData), NewProp_PolygonPoints_MetaData) };
void Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::NewProp_bExpectedInside_SetBit(void* Obj)
{
	((GeometricValidator_eventValidatePointInPolygon_Parms*)Obj)->bExpectedInside = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::NewProp_bExpectedInside = { "bExpectedInside", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(GeometricValidator_eventValidatePointInPolygon_Parms), &Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::NewProp_bExpectedInside_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidatePointInPolygon_Parms, ReturnValue), Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::NewProp_PolygonPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::NewProp_PolygonPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::NewProp_bExpectedInside,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidatePointInPolygon", Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::GeometricValidator_eventValidatePointInPolygon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::GeometricValidator_eventValidatePointInPolygon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidatePointInPolygon)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point);
	P_GET_TARRAY_REF(FVector,Z_Param_Out_PolygonPoints);
	P_GET_UBOOL(Z_Param_bExpectedInside);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FValidationResult*)Z_Param__Result=P_THIS->ValidatePointInPolygon(Z_Param_Out_Point,Z_Param_Out_PolygonPoints,Z_Param_bExpectedInside);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidatePointInPolygon ************************

// ********** Begin Class AGeometricValidator Function ValidatePolygonArea *************************
struct Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics
{
	struct GeometricValidator_eventValidatePolygonArea_Parms
	{
		TArray<FVector> Points;
		float ExpectedArea;
		float Tolerance;
		FValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometric Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida \xc3\xa1rea de um pol\xc3\xadgono\n     */" },
#endif
		{ "CPP_Default_Tolerance", "100.000000" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida \xc3\xa1rea de um pol\xc3\xadgono" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Points_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Points_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Points;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExpectedArea;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::NewProp_Points_Inner = { "Points", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidatePolygonArea_Parms, Points), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Points_MetaData), NewProp_Points_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::NewProp_ExpectedArea = { "ExpectedArea", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidatePolygonArea_Parms, ExpectedArea), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidatePolygonArea_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidatePolygonArea_Parms, ReturnValue), Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::NewProp_Points_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::NewProp_ExpectedArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidatePolygonArea", Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::GeometricValidator_eventValidatePolygonArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::GeometricValidator_eventValidatePolygonArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidatePolygonArea)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Points);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ExpectedArea);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FValidationResult*)Z_Param__Result=P_THIS->ValidatePolygonArea(Z_Param_Out_Points,Z_Param_ExpectedArea,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidatePolygonArea ***************************

// ********** Begin Class AGeometricValidator Function ValidateRegularHexagon **********************
struct Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics
{
	struct GeometricValidator_eventValidateRegularHexagon_Parms
	{
		TArray<FVector> HexagonPoints;
		float ExpectedSideLength;
		float Tolerance;
		FValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometric Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida se um hex\xc3\xa1gono \xc3\xa9 regular\n     */" },
#endif
		{ "CPP_Default_Tolerance", "1.000000" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida se um hex\xc3\xa1gono \xc3\xa9 regular" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HexagonPoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_HexagonPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HexagonPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExpectedSideLength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::NewProp_HexagonPoints_Inner = { "HexagonPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::NewProp_HexagonPoints = { "HexagonPoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateRegularHexagon_Parms, HexagonPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HexagonPoints_MetaData), NewProp_HexagonPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::NewProp_ExpectedSideLength = { "ExpectedSideLength", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateRegularHexagon_Parms, ExpectedSideLength), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateRegularHexagon_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateRegularHexagon_Parms, ReturnValue), Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::NewProp_HexagonPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::NewProp_HexagonPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::NewProp_ExpectedSideLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateRegularHexagon", Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::GeometricValidator_eventValidateRegularHexagon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::GeometricValidator_eventValidateRegularHexagon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateRegularHexagon)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_HexagonPoints);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ExpectedSideLength);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FValidationResult*)Z_Param__Result=P_THIS->ValidateRegularHexagon(Z_Param_Out_HexagonPoints,Z_Param_ExpectedSideLength,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateRegularHexagon ************************

// ********** Begin Class AGeometricValidator Function ValidateRiverGeometry ***********************
struct Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics
{
	struct GeometricValidator_eventValidateRiverGeometry_Parms
	{
		TArray<FVector> RiverPoints;
		float Amplitude;
		float Frequency;
		float Width;
		TArray<FValidationResult> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida geometria senoidal do rio\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida geometria senoidal do rio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverPoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_RiverPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RiverPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Amplitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Frequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_RiverPoints_Inner = { "RiverPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_RiverPoints = { "RiverPoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateRiverGeometry_Parms, RiverPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverPoints_MetaData), NewProp_RiverPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_Amplitude = { "Amplitude", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateRiverGeometry_Parms, Amplitude), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_Frequency = { "Frequency", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateRiverGeometry_Parms, Frequency), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateRiverGeometry_Parms, Width), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateRiverGeometry_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_RiverPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_RiverPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_Amplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_Frequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateRiverGeometry", Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::GeometricValidator_eventValidateRiverGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::GeometricValidator_eventValidateRiverGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateRiverGeometry)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_RiverPoints);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Amplitude);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Frequency);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Width);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FValidationResult>*)Z_Param__Result=P_THIS->ValidateRiverGeometry(Z_Param_Out_RiverPoints,Z_Param_Amplitude,Z_Param_Frequency,Z_Param_Width);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateRiverGeometry *************************

// ********** Begin Class AGeometricValidator Function ValidateRiverIsland *************************
struct Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics
{
	struct GeometricValidator_eventValidateRiverIsland_Parms
	{
		FVector IslandCenter;
		float IslandRadius;
		TArray<FVector> RiverPoints;
		TArray<FValidationResult> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida ilha hexagonal no rio\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida ilha hexagonal no rio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandCenter_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverPoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_IslandCenter;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_IslandRadius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RiverPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RiverPoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::NewProp_IslandCenter = { "IslandCenter", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateRiverIsland_Parms, IslandCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandCenter_MetaData), NewProp_IslandCenter_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::NewProp_IslandRadius = { "IslandRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateRiverIsland_Parms, IslandRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::NewProp_RiverPoints_Inner = { "RiverPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::NewProp_RiverPoints = { "RiverPoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateRiverIsland_Parms, RiverPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverPoints_MetaData), NewProp_RiverPoints_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateRiverIsland_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::NewProp_IslandCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::NewProp_IslandRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::NewProp_RiverPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::NewProp_RiverPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateRiverIsland", Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::GeometricValidator_eventValidateRiverIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::GeometricValidator_eventValidateRiverIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateRiverIsland)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_IslandCenter);
	P_GET_PROPERTY(FFloatProperty,Z_Param_IslandRadius);
	P_GET_TARRAY_REF(FVector,Z_Param_Out_RiverPoints);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FValidationResult>*)Z_Param__Result=P_THIS->ValidateRiverIsland(Z_Param_Out_IslandCenter,Z_Param_IslandRadius,Z_Param_Out_RiverPoints);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateRiverIsland ***************************

// ********** Begin Class AGeometricValidator Function ValidateSinusoidalCurve *********************
struct Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics
{
	struct GeometricValidator_eventValidateSinusoidalCurve_Parms
	{
		TArray<FVector> CurvePoints;
		float Amplitude;
		float Frequency;
		float Phase;
		float Tolerance;
		FValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometric Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida curva senoidal\n     */" },
#endif
		{ "CPP_Default_Tolerance", "1.000000" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida curva senoidal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurvePoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurvePoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CurvePoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Amplitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Frequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Phase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_CurvePoints_Inner = { "CurvePoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_CurvePoints = { "CurvePoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateSinusoidalCurve_Parms, CurvePoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurvePoints_MetaData), NewProp_CurvePoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_Amplitude = { "Amplitude", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateSinusoidalCurve_Parms, Amplitude), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_Frequency = { "Frequency", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateSinusoidalCurve_Parms, Frequency), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_Phase = { "Phase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateSinusoidalCurve_Parms, Phase), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateSinusoidalCurve_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateSinusoidalCurve_Parms, ReturnValue), Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_CurvePoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_CurvePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_Amplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_Frequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_Phase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateSinusoidalCurve", Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::GeometricValidator_eventValidateSinusoidalCurve_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::GeometricValidator_eventValidateSinusoidalCurve_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateSinusoidalCurve)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_CurvePoints);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Amplitude);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Frequency);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Phase);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FValidationResult*)Z_Param__Result=P_THIS->ValidateSinusoidalCurve(Z_Param_Out_CurvePoints,Z_Param_Amplitude,Z_Param_Frequency,Z_Param_Phase,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateSinusoidalCurve ***********************

// ********** Begin Class AGeometricValidator Function ValidateSymmetry ****************************
struct Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics
{
	struct GeometricValidator_eventValidateSymmetry_Parms
	{
		TArray<FVector> Points;
		FVector SymmetryAxis;
		float Tolerance;
		FValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Geometric Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida simetria de pontos em rela\xc3\xa7\xc3\xa3o a um eixo\n     */" },
#endif
		{ "CPP_Default_Tolerance", "1.000000" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida simetria de pontos em rela\xc3\xa7\xc3\xa3o a um eixo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Points_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SymmetryAxis_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Points_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Points;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SymmetryAxis;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::NewProp_Points_Inner = { "Points", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateSymmetry_Parms, Points), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Points_MetaData), NewProp_Points_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::NewProp_SymmetryAxis = { "SymmetryAxis", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateSymmetry_Parms, SymmetryAxis), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SymmetryAxis_MetaData), NewProp_SymmetryAxis_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateSymmetry_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateSymmetry_Parms, ReturnValue), Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::NewProp_Points_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::NewProp_SymmetryAxis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateSymmetry", Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::GeometricValidator_eventValidateSymmetry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::GeometricValidator_eventValidateSymmetry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateSymmetry)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Points);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_SymmetryAxis);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FValidationResult*)Z_Param__Result=P_THIS->ValidateSymmetry(Z_Param_Out_Points,Z_Param_Out_SymmetryAxis,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateSymmetry ******************************

// ********** Begin Class AGeometricValidator Function ValidateTowerPositions **********************
struct Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics
{
	struct GeometricValidator_eventValidateTowerPositions_Parms
	{
		TArray<FVector> TowerPositions;
		TArray<FVector> LanePoints;
		float MinDistanceFromLane;
		TArray<FValidationResult> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida posi\xc3\xa7\xc3\xb5""es das torres\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida posi\xc3\xa7\xc3\xb5""es das torres" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerPositions_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LanePoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TowerPositions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TowerPositions;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LanePoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LanePoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDistanceFromLane;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_TowerPositions_Inner = { "TowerPositions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_TowerPositions = { "TowerPositions", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateTowerPositions_Parms, TowerPositions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerPositions_MetaData), NewProp_TowerPositions_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_LanePoints_Inner = { "LanePoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_LanePoints = { "LanePoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateTowerPositions_Parms, LanePoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LanePoints_MetaData), NewProp_LanePoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_MinDistanceFromLane = { "MinDistanceFromLane", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateTowerPositions_Parms, MinDistanceFromLane), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GeometricValidator_eventValidateTowerPositions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_TowerPositions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_TowerPositions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_LanePoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_LanePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_MinDistanceFromLane,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AGeometricValidator, nullptr, "ValidateTowerPositions", Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::GeometricValidator_eventValidateTowerPositions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::GeometricValidator_eventValidateTowerPositions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGeometricValidator::execValidateTowerPositions)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_TowerPositions);
	P_GET_TARRAY_REF(FVector,Z_Param_Out_LanePoints);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MinDistanceFromLane);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FValidationResult>*)Z_Param__Result=P_THIS->ValidateTowerPositions(Z_Param_Out_TowerPositions,Z_Param_Out_LanePoints,Z_Param_MinDistanceFromLane);
	P_NATIVE_END;
}
// ********** End Class AGeometricValidator Function ValidateTowerPositions ************************

// ********** Begin Class AGeometricValidator ******************************************************
void AGeometricValidator::StaticRegisterNativesAGeometricValidator()
{
	UClass* Class = AGeometricValidator::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearRegisteredShapes", &AGeometricValidator::execClearRegisteredShapes },
		{ "ClearValidationHistory", &AGeometricValidator::execClearValidationHistory },
		{ "ExportValidationReport", &AGeometricValidator::execExportValidationReport },
		{ "GetErrorCount", &AGeometricValidator::execGetErrorCount },
		{ "GetRegisteredShape", &AGeometricValidator::execGetRegisteredShape },
		{ "GetRegisteredShapes", &AGeometricValidator::execGetRegisteredShapes },
		{ "GetValidationConfig", &AGeometricValidator::execGetValidationConfig },
		{ "GetValidationCount", &AGeometricValidator::execGetValidationCount },
		{ "GetValidationHistory", &AGeometricValidator::execGetValidationHistory },
		{ "GetValidationHistoryBySeverity", &AGeometricValidator::execGetValidationHistoryBySeverity },
		{ "GetValidationHistoryByType", &AGeometricValidator::execGetValidationHistoryByType },
		{ "GetValidationProgress", &AGeometricValidator::execGetValidationProgress },
		{ "GetValidationStatistics", &AGeometricValidator::execGetValidationStatistics },
		{ "GetValidationSummary", &AGeometricValidator::execGetValidationSummary },
		{ "GetWarningCount", &AGeometricValidator::execGetWarningCount },
		{ "IsValidating", &AGeometricValidator::execIsValidating },
		{ "PerformFullValidation", &AGeometricValidator::execPerformFullValidation },
		{ "RegisterShape", &AGeometricValidator::execRegisterShape },
		{ "ResetValidator", &AGeometricValidator::execResetValidator },
		{ "RunPerformanceBenchmark", &AGeometricValidator::execRunPerformanceBenchmark },
		{ "SetDebugVisualization", &AGeometricValidator::execSetDebugVisualization },
		{ "SetValidationConfig", &AGeometricValidator::execSetValidationConfig },
		{ "StartRealTimeValidation", &AGeometricValidator::execStartRealTimeValidation },
		{ "StopRealTimeValidation", &AGeometricValidator::execStopRealTimeValidation },
		{ "UnregisterShape", &AGeometricValidator::execUnregisterShape },
		{ "ValidateAngle", &AGeometricValidator::execValidateAngle },
		{ "ValidateBaronHexagonalArea", &AGeometricValidator::execValidateBaronHexagonalArea },
		{ "ValidateDistance", &AGeometricValidator::execValidateDistance },
		{ "ValidateDragonEllipticalArea", &AGeometricValidator::execValidateDragonEllipticalArea },
		{ "ValidateEllipse", &AGeometricValidator::execValidateEllipse },
		{ "ValidateLaneGeometry", &AGeometricValidator::execValidateLaneGeometry },
		{ "ValidateLaneWaypoints", &AGeometricValidator::execValidateLaneWaypoints },
		{ "ValidateLineIntersection", &AGeometricValidator::execValidateLineIntersection },
		{ "ValidateParallelism", &AGeometricValidator::execValidateParallelism },
		{ "ValidatePerpendicularity", &AGeometricValidator::execValidatePerpendicularity },
		{ "ValidatePointInPolygon", &AGeometricValidator::execValidatePointInPolygon },
		{ "ValidatePolygonArea", &AGeometricValidator::execValidatePolygonArea },
		{ "ValidateRegularHexagon", &AGeometricValidator::execValidateRegularHexagon },
		{ "ValidateRiverGeometry", &AGeometricValidator::execValidateRiverGeometry },
		{ "ValidateRiverIsland", &AGeometricValidator::execValidateRiverIsland },
		{ "ValidateSinusoidalCurve", &AGeometricValidator::execValidateSinusoidalCurve },
		{ "ValidateSymmetry", &AGeometricValidator::execValidateSymmetry },
		{ "ValidateTowerPositions", &AGeometricValidator::execValidateTowerPositions },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AGeometricValidator;
UClass* AGeometricValidator::GetPrivateStaticClass()
{
	using TClass = AGeometricValidator;
	if (!Z_Registration_Info_UClass_AGeometricValidator.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("GeometricValidator"),
			Z_Registration_Info_UClass_AGeometricValidator.InnerSingleton,
			StaticRegisterNativesAGeometricValidator,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AGeometricValidator.InnerSingleton;
}
UClass* Z_Construct_UClass_AGeometricValidator_NoRegister()
{
	return AGeometricValidator::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AGeometricValidator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * AGeometricValidator - Sistema de valida\xc3\xa7\xc3\xa3o matem\xc3\xa1tica em tempo real\n * \n * Este sistema valida a precis\xc3\xa3o geom\xc3\xa9trica de todos os elementos do mapa,\n * garantindo que as dimens\xc3\xb5""es, \xc3\xa2ngulos, dist\xc3\xa2ncias e formas estejam corretas\n * de acordo com as especifica\xc3\xa7\xc3\xb5""es matem\xc3\xa1ticas.\n * \n * Responsabilidades:\n * - Validar geometria de lanes (largura, comprimento, \xc3\xa2ngulos)\n * - Validar formas hexagonais e el\xc3\xadpticas dos objetivos\n * - Validar geometria senoidal do rio\n * - Validar colis\xc3\xb5""es e intersec\xc3\xa7\xc3\xb5""es\n * - Validar pathfinding e waypoints\n * - Fornecer feedback em tempo real\n */" },
#endif
		{ "IncludePath", "AGeometricValidator.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AGeometricValidator - Sistema de valida\xc3\xa7\xc3\xa3o matem\xc3\xa1tica em tempo real\n\nEste sistema valida a precis\xc3\xa3o geom\xc3\xa9trica de todos os elementos do mapa,\ngarantindo que as dimens\xc3\xb5""es, \xc3\xa2ngulos, dist\xc3\xa2ncias e formas estejam corretas\nde acordo com as especifica\xc3\xa7\xc3\xb5""es matem\xc3\xa1ticas.\n\nResponsabilidades:\n- Validar geometria de lanes (largura, comprimento, \xc3\xa2ngulos)\n- Validar formas hexagonais e el\xc3\xadpticas dos objetivos\n- Validar geometria senoidal do rio\n- Validar colis\xc3\xb5""es e intersec\xc3\xa7\xc3\xb5""es\n- Validar pathfinding e waypoints\n- Fornecer feedback em tempo real" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationConfig_MetaData[] = {
		{ "Category", "Validation Config" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValidating_MetaData[] = {
		{ "Category", "Validation Status" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationHistory_MetaData[] = {
		{ "Category", "Validation Status" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredShapes_MetaData[] = {
		{ "Category", "Validation Status" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnValidationResult_MetaData[] = {
		{ "Category", "Validation Events" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnValidationError_MetaData[] = {
		{ "Category", "Validation Events" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnValidationComplete_MetaData[] = {
		{ "Category", "Validation Events" },
		{ "ModuleRelativePath", "Public/AGeometricValidator.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ValidationConfig;
	static void NewProp_bIsValidating_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValidating;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ValidationHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValidationHistory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredShapes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RegisteredShapes;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnValidationResult;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnValidationError;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnValidationComplete;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AGeometricValidator_ClearRegisteredShapes, "ClearRegisteredShapes" }, // 95030580
		{ &Z_Construct_UFunction_AGeometricValidator_ClearValidationHistory, "ClearValidationHistory" }, // 4166190652
		{ &Z_Construct_UFunction_AGeometricValidator_ExportValidationReport, "ExportValidationReport" }, // 570524809
		{ &Z_Construct_UFunction_AGeometricValidator_GetErrorCount, "GetErrorCount" }, // 3586699578
		{ &Z_Construct_UFunction_AGeometricValidator_GetRegisteredShape, "GetRegisteredShape" }, // 3146647801
		{ &Z_Construct_UFunction_AGeometricValidator_GetRegisteredShapes, "GetRegisteredShapes" }, // 613369099
		{ &Z_Construct_UFunction_AGeometricValidator_GetValidationConfig, "GetValidationConfig" }, // 2430371231
		{ &Z_Construct_UFunction_AGeometricValidator_GetValidationCount, "GetValidationCount" }, // 1732612023
		{ &Z_Construct_UFunction_AGeometricValidator_GetValidationHistory, "GetValidationHistory" }, // 3525768570
		{ &Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryBySeverity, "GetValidationHistoryBySeverity" }, // 4133704632
		{ &Z_Construct_UFunction_AGeometricValidator_GetValidationHistoryByType, "GetValidationHistoryByType" }, // 986073357
		{ &Z_Construct_UFunction_AGeometricValidator_GetValidationProgress, "GetValidationProgress" }, // 4119037120
		{ &Z_Construct_UFunction_AGeometricValidator_GetValidationStatistics, "GetValidationStatistics" }, // 3238675915
		{ &Z_Construct_UFunction_AGeometricValidator_GetValidationSummary, "GetValidationSummary" }, // 411341332
		{ &Z_Construct_UFunction_AGeometricValidator_GetWarningCount, "GetWarningCount" }, // 796919742
		{ &Z_Construct_UFunction_AGeometricValidator_IsValidating, "IsValidating" }, // 2344254072
		{ &Z_Construct_UFunction_AGeometricValidator_PerformFullValidation, "PerformFullValidation" }, // 3417426101
		{ &Z_Construct_UFunction_AGeometricValidator_RegisterShape, "RegisterShape" }, // 1628584605
		{ &Z_Construct_UFunction_AGeometricValidator_ResetValidator, "ResetValidator" }, // 2892224699
		{ &Z_Construct_UFunction_AGeometricValidator_RunPerformanceBenchmark, "RunPerformanceBenchmark" }, // 3905152769
		{ &Z_Construct_UFunction_AGeometricValidator_SetDebugVisualization, "SetDebugVisualization" }, // 1672970576
		{ &Z_Construct_UFunction_AGeometricValidator_SetValidationConfig, "SetValidationConfig" }, // 3512954279
		{ &Z_Construct_UFunction_AGeometricValidator_StartRealTimeValidation, "StartRealTimeValidation" }, // 372553853
		{ &Z_Construct_UFunction_AGeometricValidator_StopRealTimeValidation, "StopRealTimeValidation" }, // 3915534677
		{ &Z_Construct_UFunction_AGeometricValidator_UnregisterShape, "UnregisterShape" }, // 2038623232
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateAngle, "ValidateAngle" }, // 3008594456
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateBaronHexagonalArea, "ValidateBaronHexagonalArea" }, // 218129867
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateDistance, "ValidateDistance" }, // 2442649819
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateDragonEllipticalArea, "ValidateDragonEllipticalArea" }, // 223924767
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateEllipse, "ValidateEllipse" }, // 1068160210
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateLaneGeometry, "ValidateLaneGeometry" }, // 1305981996
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateLaneWaypoints, "ValidateLaneWaypoints" }, // 3384827448
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateLineIntersection, "ValidateLineIntersection" }, // 3799414783
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateParallelism, "ValidateParallelism" }, // 2770319850
		{ &Z_Construct_UFunction_AGeometricValidator_ValidatePerpendicularity, "ValidatePerpendicularity" }, // 2233077895
		{ &Z_Construct_UFunction_AGeometricValidator_ValidatePointInPolygon, "ValidatePointInPolygon" }, // 3807265483
		{ &Z_Construct_UFunction_AGeometricValidator_ValidatePolygonArea, "ValidatePolygonArea" }, // 2904383183
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateRegularHexagon, "ValidateRegularHexagon" }, // 1896243329
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateRiverGeometry, "ValidateRiverGeometry" }, // 2749885670
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateRiverIsland, "ValidateRiverIsland" }, // 3099205442
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateSinusoidalCurve, "ValidateSinusoidalCurve" }, // 4174089074
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateSymmetry, "ValidateSymmetry" }, // 3378129810
		{ &Z_Construct_UFunction_AGeometricValidator_ValidateTowerPositions, "ValidateTowerPositions" }, // 3466884744
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AGeometricValidator>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AGeometricValidator_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGeometricValidator, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AGeometricValidator_Statics::NewProp_ValidationConfig = { "ValidationConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGeometricValidator, ValidationConfig), Z_Construct_UScriptStruct_FValidationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationConfig_MetaData), NewProp_ValidationConfig_MetaData) }; // 1341900035
void Z_Construct_UClass_AGeometricValidator_Statics::NewProp_bIsValidating_SetBit(void* Obj)
{
	((AGeometricValidator*)Obj)->bIsValidating = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AGeometricValidator_Statics::NewProp_bIsValidating = { "bIsValidating", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AGeometricValidator), &Z_Construct_UClass_AGeometricValidator_Statics::NewProp_bIsValidating_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValidating_MetaData), NewProp_bIsValidating_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AGeometricValidator_Statics::NewProp_ValidationHistory_Inner = { "ValidationHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FValidationResult, METADATA_PARAMS(0, nullptr) }; // 1197292000
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AGeometricValidator_Statics::NewProp_ValidationHistory = { "ValidationHistory", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGeometricValidator, ValidationHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationHistory_MetaData), NewProp_ValidationHistory_MetaData) }; // 1197292000
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AGeometricValidator_Statics::NewProp_RegisteredShapes_Inner = { "RegisteredShapes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGeometricShape, METADATA_PARAMS(0, nullptr) }; // 1180479940
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AGeometricValidator_Statics::NewProp_RegisteredShapes = { "RegisteredShapes", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGeometricValidator, RegisteredShapes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredShapes_MetaData), NewProp_RegisteredShapes_MetaData) }; // 1180479940
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AGeometricValidator_Statics::NewProp_OnValidationResult = { "OnValidationResult", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGeometricValidator, OnValidationResult), Z_Construct_UDelegateFunction_Aura_OnValidationResult__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnValidationResult_MetaData), NewProp_OnValidationResult_MetaData) }; // 1973863849
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AGeometricValidator_Statics::NewProp_OnValidationError = { "OnValidationError", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGeometricValidator, OnValidationError), Z_Construct_UDelegateFunction_Aura_OnValidationError__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnValidationError_MetaData), NewProp_OnValidationError_MetaData) }; // 2655671550
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AGeometricValidator_Statics::NewProp_OnValidationComplete = { "OnValidationComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGeometricValidator, OnValidationComplete), Z_Construct_UDelegateFunction_Aura_OnValidationComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnValidationComplete_MetaData), NewProp_OnValidationComplete_MetaData) }; // 1712873653
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AGeometricValidator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGeometricValidator_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGeometricValidator_Statics::NewProp_ValidationConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGeometricValidator_Statics::NewProp_bIsValidating,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGeometricValidator_Statics::NewProp_ValidationHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGeometricValidator_Statics::NewProp_ValidationHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGeometricValidator_Statics::NewProp_RegisteredShapes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGeometricValidator_Statics::NewProp_RegisteredShapes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGeometricValidator_Statics::NewProp_OnValidationResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGeometricValidator_Statics::NewProp_OnValidationError,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGeometricValidator_Statics::NewProp_OnValidationComplete,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AGeometricValidator_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AGeometricValidator_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AGeometricValidator_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AGeometricValidator_Statics::ClassParams = {
	&AGeometricValidator::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AGeometricValidator_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AGeometricValidator_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AGeometricValidator_Statics::Class_MetaDataParams), Z_Construct_UClass_AGeometricValidator_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AGeometricValidator()
{
	if (!Z_Registration_Info_UClass_AGeometricValidator.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AGeometricValidator.OuterSingleton, Z_Construct_UClass_AGeometricValidator_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AGeometricValidator.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AGeometricValidator);
AGeometricValidator::~AGeometricValidator() {}
// ********** End Class AGeometricValidator ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AGeometricValidator_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EValidationType_StaticEnum, TEXT("EValidationType"), &Z_Registration_Info_UEnum_EValidationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 320457599U) },
		{ EValidationSeverity_StaticEnum, TEXT("EValidationSeverity"), &Z_Registration_Info_UEnum_EValidationSeverity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 258873626U) },
		{ EGeometricShape_StaticEnum, TEXT("EGeometricShape"), &Z_Registration_Info_UEnum_EGeometricShape, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1638157365U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FValidationResult::StaticStruct, Z_Construct_UScriptStruct_FValidationResult_Statics::NewStructOps, TEXT("ValidationResult"), &Z_Registration_Info_UScriptStruct_FValidationResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FValidationResult), 1197292000U) },
		{ FGeometricShape::StaticStruct, Z_Construct_UScriptStruct_FGeometricShape_Statics::NewStructOps, TEXT("GeometricShape"), &Z_Registration_Info_UScriptStruct_FGeometricShape, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FGeometricShape), 1180479940U) },
		{ FValidationConfig::StaticStruct, Z_Construct_UScriptStruct_FValidationConfig_Statics::NewStructOps, TEXT("ValidationConfig"), &Z_Registration_Info_UScriptStruct_FValidationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FValidationConfig), 1341900035U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AGeometricValidator, AGeometricValidator::StaticClass, TEXT("AGeometricValidator"), &Z_Registration_Info_UClass_AGeometricValidator, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AGeometricValidator), 3016052884U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AGeometricValidator_h__Script_Aura_616709845(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AGeometricValidator_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AGeometricValidator_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AGeometricValidator_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AGeometricValidator_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AGeometricValidator_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AGeometricValidator_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
