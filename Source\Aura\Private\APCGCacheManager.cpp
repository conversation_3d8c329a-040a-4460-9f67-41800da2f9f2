#include "APCGCacheManager.h"
#include "HAL/PlatformFilemanager.h"
#include "HAL/FileManager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"
#include "Misc/SecureHash.h"
#include "Serialization/MemoryWriter.h"
#include "Serialization/MemoryReader.h"
#include "Compression/OodleDataCompression.h"
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"
#include "Stats/Stats.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "HAL/PlatformMemory.h"
#include "GenericPlatform/GenericPlatformMemory.h"
#include "Containers/LruCache.h"
#include "Json.h"

// CORRIGIDO: UE 5.6 Modern APIs
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Async/ParallelFor.h"
#include "HAL/ThreadSafeCounter64.h"
#include "Engine/StreamableManager.h"
#include "Subsystems/WorldSubsystem.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "RendererInterface.h"
#include "RHI.h"
#include "Misc/Timespan.h"
#include "HAL/ThreadSafeBool.h"
#include "Templates/Atomic.h"

// Include PCG system headers
#include "APCGWorldPartitionManager.h"
#include "APCGNaniteOptimizer.h"
#include "APCGLumenIntegrator.h"
#include "APCGStreamingManager.h"
#include "UPCGPerformanceProfiler.h"

// CORRIGIDO: Logging categories específicas para UE 5.6
DEFINE_LOG_CATEGORY_STATIC(LogPCGCacheManager, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGCachePerformance, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGCacheMemory, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGCacheIO, Log, All);

// Stats declarations - using STATGROUP_Game since STATGROUP_PCG is not available in UE5.6
DECLARE_STATS_GROUP(TEXT("PCG Cache"), STATGROUP_PCGCache, STATCAT_Advanced);
DECLARE_CYCLE_STAT(TEXT("PCG Cache Store"), STAT_PCGCacheStore, STATGROUP_PCGCache);
DECLARE_CYCLE_STAT(TEXT("PCG Cache Load"), STAT_PCGCacheLoad, STATGROUP_PCGCache);
DECLARE_CYCLE_STAT(TEXT("PCG Cache Compression"), STAT_PCGCacheCompression, STATGROUP_PCGCache);
DECLARE_CYCLE_STAT(TEXT("PCG Cache Eviction"), STAT_PCGCacheEviction, STATGROUP_PCGCache);
DECLARE_MEMORY_STAT(TEXT("PCG Cache Memory"), STAT_PCGCacheMemory, STATGROUP_PCGCache);

APCGCacheManager::APCGCacheManager()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.TickInterval = 1.0f; // Tick every second for maintenance

    // CORRIGIDO: Configuração moderna para UE 5.6
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.TickGroup = TG_PostUpdateWork; // Executar após updates principais

    // Initialize default configuration
    CacheConfig = FPCGCacheConfig();

    // Initialize statistics
    CurrentStatistics = FPCGCacheStatistics();

    // CORRIGIDO: Inicializar contadores thread-safe para UE 5.6
    TotalCacheRequests.Set(0);
    TotalCacheHits.Set(0);
    TotalCacheMisses.Set(0);
    TotalEvictions.Set(0);

    // Set default state
    bIsInitialized = false;
    bIsEnabled = true;
    bMaintenanceMode = false;
    bCacheInitialized = false;
    bMaintenanceActive = false;

    // CORRIGIDO: Inicializar sistemas modernos
    StreamableManager = MakeShared<FStreamableManager>();

    UE_LOG(LogPCGCacheManager, Log, TEXT("APCGCacheManager: Constructor completed with modern UE 5.6 APIs"));
}

void APCGCacheManager::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogPCGCacheManager, Log, TEXT("APCGCacheManager: BeginPlay started with UE 5.6 integration"));

    // CORRIGIDO: Integração com World Partition UE 5.6
    if (UWorld* World = GetWorld())
    {
        if (UWorldPartitionSubsystem* WorldPartitionSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>())
        {
            UE_LOG(LogPCGCacheManager, Log, TEXT("World Partition subsystem detected - enabling advanced caching"));
            bWorldPartitionEnabled = true;
        }

        // CORRIGIDO: Verificar se Nanite está disponível usando CVar (API moderna UE 5.6)
        static const auto* NaniteCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Nanite"));
        if (NaniteCVar && NaniteCVar->GetInt() > 0)
        {
            UE_LOG(LogPCGCacheManager, Log, TEXT("Nanite support detected - enabling geometry caching optimizations"));
            bNaniteEnabled = true;
        }

        // Verificar se Lumen está disponível
        if (IsLumenEnabled(World))
        {
            UE_LOG(LogPCGCacheManager, Log, TEXT("Lumen support detected - enabling lighting cache optimizations"));
            bLumenEnabled = true;
        }
    }

    // Initialize cache system
    InitializeCache();

    // Start maintenance timer
    LastMaintenanceTime = GetWorld()->GetTimeSeconds();
    LastStatisticsUpdate = LastMaintenanceTime;

    UE_LOG(LogPCGCacheManager, Log, TEXT("APCGCacheManager: BeginPlay completed with modern integrations"));
}

void APCGCacheManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // CORRIGIDO: Logging seguro para EEndPlayReason usando UEnum
    const FString ReasonString = UEnum::GetValueAsString(EndPlayReason);
    UE_LOG(LogPCGCacheManager, Log, TEXT("APCGCacheManager: EndPlay started - reason: %s"), *ReasonString);

    // CORRIGIDO: Shutdown graceful com UE 5.6 APIs
    if (bIsInitialized)
    {
        // Aguardar operações async pendentes
        if (StreamableManager.IsValid())
        {
            StreamableManager->RequestAsyncLoad(TArray<FSoftObjectPath>(),
                FStreamableDelegate::CreateLambda([this]()
                {
                    UE_LOG(LogPCGCacheManager, VeryVerbose, TEXT("Async operations completed"));
                }));
        }

        // Flush cache para disco se necessário
        if (CacheConfig.bEnableDiskCache && CacheConfig.bFlushOnShutdown)
        {
            FlushCacheToDisk();
        }
    }

    // Shutdown cache system
    ShutdownCache();

    Super::EndPlay(EndPlayReason);

    UE_LOG(LogPCGCacheManager, Log, TEXT("APCGCacheManager: EndPlay completed gracefully"));
}

void APCGCacheManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (!bIsInitialized || !bIsEnabled)
    {
        return;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    
    // Perform periodic maintenance
    if (CurrentTime - LastMaintenanceTime >= CacheConfig.EvictionCheckInterval)
    {
        PerformMaintenance();
        LastMaintenanceTime = CurrentTime;
    }
    
    // Update statistics
    if (CurrentTime - LastStatisticsUpdate >= 5.0f) // Update every 5 seconds
    {
        UpdateCacheStatistics();
        LastStatisticsUpdate = CurrentTime;
        OnCacheStatisticsUpdated.Broadcast(CurrentStatistics);
    }
    
    // Process prefetch queue
    ProcessPrefetchQueue();
    
    // Update integrated systems
    UpdateIntegratedSystems();
}

// Initialization and Management
void APCGCacheManager::InitializeCache()
{
    UE_LOG(LogPCGCacheManager, Log, TEXT("APCGCacheManager: Initializing cache system with UE 5.6 optimizations"));

    if (bCacheInitialized)
    {
        UE_LOG(LogPCGCacheManager, Warning, TEXT("APCGCacheManager: Cache already initialized"));
        return;
    }

    // CORRIGIDO: Initialize memory cache com estruturas otimizadas UE 5.6
    MemoryCache.Empty();
    CacheEntries.Empty();

    // Reservar espaço para otimização
    MemoryCache.Reserve(CacheConfig.MaxMemoryEntries);
    CacheEntries.Reserve(CacheConfig.MaxMemoryEntries);

    // CORRIGIDO: Create disk cache directory com validação robusta
    if (CacheConfig.bEnableDiskCache)
    {
        FString FullDiskPath = FPaths::ProjectSavedDir() / CacheConfig.DiskCachePath;
        IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

        // Validar path
        if (FullDiskPath.IsEmpty() || FullDiskPath.Len() > 260) // Windows MAX_PATH
        {
            UE_LOG(LogPCGCacheIO, Error, TEXT("Invalid disk cache path: %s"), *FullDiskPath);
            CacheConfig.bEnableDiskCache = false;
        }
        else if (!PlatformFile.DirectoryExists(*FullDiskPath))
        {
            if (PlatformFile.CreateDirectoryTree(*FullDiskPath))
            {
                UE_LOG(LogPCGCacheIO, Log, TEXT("Created disk cache directory: %s"), *FullDiskPath);

                // Verificar permissões de escrita
                FString TestFile = FullDiskPath / TEXT("test_write.tmp");
                if (FFileHelper::SaveStringToFile(TEXT("test"), *TestFile))
                {
                    PlatformFile.DeleteFile(*TestFile);
                    UE_LOG(LogPCGCacheIO, VeryVerbose, TEXT("Disk cache write permissions verified"));
                }
                else
                {
                    UE_LOG(LogPCGCacheIO, Error, TEXT("No write permissions for disk cache directory"));
                    CacheConfig.bEnableDiskCache = false;
                }
            }
            else
            {
                UE_LOG(LogPCGCacheIO, Error, TEXT("Failed to create disk cache directory: %s"), *FullDiskPath);
                CacheConfig.bEnableDiskCache = false;
            }
        }
    }
    
    // Initialize statistics
    ResetStatistics();
    
    // Set initialized state
    bCacheInitialized = true;
    bIsInitialized = true;
    
    UE_LOG(LogPCGCacheManager, Log, TEXT("APCGCacheManager: Cache system initialized successfully"));
}

void APCGCacheManager::ShutdownCache()
{
    UE_LOG(LogPCGCacheManager, Log, TEXT("APCGCacheManager: Shutting down cache system"));
    
    if (!bCacheInitialized)
    {
        return;
    }
    
    // Save important entries to disk
    if (CacheConfig.bEnableDiskCache)
    {
        for (const auto& Entry : CacheEntries)
        {
            if (Entry.Value.Priority >= EPCGCachePriority::High && Entry.Value.State == EPCGCacheState::Valid)
            {
                SaveToDisk(Entry.Key);
            }
        }
    }
    
    // Clear memory cache
    MemoryCache.Empty();
    CacheEntries.Empty();
    EvictionQueue.Empty();
    AccessFrequency.Empty();
    PrefetchQueue.Empty();
    
    // Wait for pending operations
    for (auto& Operation : PendingOperations)
    {
        if (Operation.Value.IsValid())
        {
            Operation.Value.Wait();
        }
    }
    PendingOperations.Empty();
    
    // Reset state
    bCacheInitialized = false;
    bIsInitialized = false;
    
    UE_LOG(LogPCGCacheManager, Log, TEXT("APCGCacheManager: Cache system shutdown completed"));
}

void APCGCacheManager::EnableCache(bool bEnable)
{
    bIsEnabled = bEnable;
    UE_LOG(LogPCGCacheManager, Log, TEXT("APCGCacheManager: Cache %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void APCGCacheManager::SetMaintenanceMode(bool bMaintenance)
{
    bMaintenanceMode = bMaintenance;
    bMaintenanceActive = bMaintenance;
    UE_LOG(LogPCGCacheManager, Log, TEXT("APCGCacheManager: Maintenance mode %s"), bMaintenance ? TEXT("enabled") : TEXT("disabled"));
}

void APCGCacheManager::ClearCache(EPCGCacheStorageType StorageType)
{
    UE_LOG(LogPCGCacheManager, Log, TEXT("APCGCacheManager: Clearing cache for storage type: %d"), (int32)StorageType);
    
    switch (StorageType)
    {
    case EPCGCacheStorageType::Memory:
        MemoryCache.Empty();
        break;
        
    case EPCGCacheStorageType::Disk:
        CleanupDiskCache();
        break;
        
    case EPCGCacheStorageType::Hybrid:
        MemoryCache.Empty();
        CleanupDiskCache();
        break;
        
    default:
        MemoryCache.Empty();
        CacheEntries.Empty();
        break;
    }
    
    // Reset statistics
    ResetStatistics();
}

void APCGCacheManager::OptimizeCache()
{
    UE_LOG(LogPCGCacheManager, Log, TEXT("APCGCacheManager: Optimizing cache"));
    
    // Optimize memory layout
    OptimizeMemoryLayout();
    
    // Compact disk cache
    if (CacheConfig.bEnableDiskCache)
    {
        CompactDiskCache();
    }
    
    // Evict expired entries
    EvictExpiredEntries();
    
    // Update statistics
    UpdateCacheStatistics();
}

// Cache Operations
bool APCGCacheManager::StoreData(const FString& Key, const TArray<uint8>& Data, EPCGCachePriority Priority)
{
    SCOPE_CYCLE_COUNTER(STAT_PCGCacheStore);

    if (!bIsInitialized || !bIsEnabled || bMaintenanceMode)
    {
        return false;
    }

    // CORRIGIDO: Validação robusta de entrada
    if (Key.IsEmpty() || Data.IsEmpty())
    {
        UE_LOG(LogPCGCacheManager, Warning, TEXT("Invalid store request - empty key or data"));
        return false;
    }

    if (Key.Len() > 256) // Limite razoável para chaves
    {
        UE_LOG(LogPCGCacheManager, Warning, TEXT("Key too long: %d characters"), Key.Len());
        return false;
    }

    UE_LOG(LogPCGCachePerformance, VeryVerbose, TEXT("Storing data for key: %s (%.2f KB)"),
           *Key, Data.Num() / 1024.0f);

    // CORRIGIDO: Check memory usando cálculos precisos
    const int64 DataSizeBytes = Data.Num();
    const int32 DataSizeMB = FMath::CeilToInt(DataSizeBytes / (1024.0 * 1024.0));

    if (CurrentStatistics.MemoryUsedMB + DataSizeMB > CacheConfig.MaxMemoryCacheSizeMB)
    {
        UE_LOG(LogPCGCacheMemory, Log, TEXT("Memory limit reached - triggering eviction"));
        ForceEviction(CacheConfig.MaxMemoryCacheSizeMB - DataSizeMB);
    }
    
    // Compress data if enabled
    TArray<uint8> StoredData = Data;
    if (CacheConfig.bEnableCompression)
    {
        StoredData = CompressDataInternal(Data, CacheConfig.CompressionLevel);
    }
    
    // Store in memory cache
    TSharedPtr<TArray<uint8>> CachedData = MakeShared<TArray<uint8>>(StoredData);
    MemoryCache.Add(Key, CachedData);
    
    // Create cache entry
    FPCGCacheEntry Entry;
    Entry.EntryKey = Key;
    Entry.State = EPCGCacheState::Valid;
    Entry.Priority = Priority;
    Entry.SizeInBytes = Data.Num();
    Entry.CompressedSizeInBytes = StoredData.Num();
    Entry.StorageType = EPCGCacheStorageType::Memory;
    Entry.ChecksumHash = GenerateChecksum(Data);
    Entry.CreationTime = FDateTime::Now();
    Entry.LastAccessTime = Entry.CreationTime;
    Entry.LastModificationTime = Entry.CreationTime;
    
    CacheEntries.Add(Key, Entry);
    
    // Update access frequency
    UpdateAccessFrequency(Key);
    
    // Save to disk if high priority
    if (Priority >= EPCGCachePriority::High && CacheConfig.bEnableDiskCache)
    {
        SaveToDisk(Key);
    }
    
    UE_LOG(LogPCGCachePerformance, VeryVerbose, TEXT("APCGCacheManager: Data stored successfully for key: %s"), *Key);
    return true;
}

bool APCGCacheManager::LoadData(const FString& Key, TArray<uint8>& OutData)
{
    SCOPE_CYCLE_COUNTER(STAT_PCGCacheLoad);

    if (!bIsInitialized || !bIsEnabled)
    {
        return false;
    }

    // CORRIGIDO: Validação de entrada robusta
    if (Key.IsEmpty())
    {
        UE_LOG(LogPCGCacheManager, Warning, TEXT("LoadData called with empty key"));
        return false;
    }

    // CORRIGIDO: Usar contador thread-safe
    TotalCacheRequests.Increment();

    UE_LOG(LogPCGCachePerformance, VeryVerbose, TEXT("Loading data for key: %s"), *Key);
    
    // Check memory cache first
    if (TSharedPtr<TArray<uint8>>* CachedData = MemoryCache.Find(Key))
    {
        TotalCacheHits.Increment();
        
        // Update access information
        if (FPCGCacheEntry* Entry = CacheEntries.Find(Key))
        {
            Entry->LastAccessTime = FDateTime::Now();
            Entry->AccessCount++;
        }
        
        UpdateAccessFrequency(Key);
        
        // Decompress if needed
        if (CacheConfig.bEnableCompression)
        {
            OutData = DecompressDataInternal(**CachedData);
        }
        else
        {
            OutData = **CachedData;
        }
        
        UE_LOG(LogPCGCachePerformance, VeryVerbose, TEXT("APCGCacheManager: Data loaded from memory cache for key: %s"), *Key);
        return true;
    }
    
    // Try loading from disk
    if (CacheConfig.bEnableDiskCache && LoadFromDisk(Key))
    {
        // Retry memory cache after disk load
        if (TSharedPtr<TArray<uint8>>* CachedData = MemoryCache.Find(Key))
        {
            TotalCacheHits.Increment();
            
            if (CacheConfig.bEnableCompression)
            {
                OutData = DecompressDataInternal(**CachedData);
            }
            else
            {
                OutData = **CachedData;
            }
            
            UE_LOG(LogPCGCachePerformance, VeryVerbose, TEXT("APCGCacheManager: Data loaded from disk cache for key: %s"), *Key);
            return true;
        }
    }
    
    TotalCacheMisses.Increment();
    UE_LOG(LogPCGCachePerformance, VeryVerbose, TEXT("APCGCacheManager: Cache miss for key: %s"), *Key);
    return false;
}

bool APCGCacheManager::HasEntry(const FString& Key) const
{
    return CacheEntries.Contains(Key) && CacheEntries[Key].State == EPCGCacheState::Valid;
}

bool APCGCacheManager::RemoveEntry(const FString& Key)
{
    UE_LOG(LogPCGCachePerformance, VeryVerbose, TEXT("APCGCacheManager: Removing entry for key: %s"), *Key);
    
    bool bRemoved = false;
    
    // Remove from memory cache
    if (MemoryCache.Remove(Key) > 0)
    {
        bRemoved = true;
    }
    
    // Remove from cache entries
    if (CacheEntries.Remove(Key) > 0)
    {
        bRemoved = true;
    }
    
    // Remove from access frequency
    AccessFrequency.Remove(Key);
    
    // Remove from disk if exists
    if (CacheConfig.bEnableDiskCache)
    {
        FString DiskPath = GetDiskPath(Key);
        if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*DiskPath))
        {
            FPlatformFileManager::Get().GetPlatformFile().DeleteFile(*DiskPath);
        }
    }
    
    return bRemoved;
}

void APCGCacheManager::InvalidateEntry(const FString& Key)
{
    if (FPCGCacheEntry* Entry = CacheEntries.Find(Key))
    {
        Entry->State = EPCGCacheState::Invalid;
        NotifyDependents(Key);
        UE_LOG(LogPCGCachePerformance, VeryVerbose, TEXT("APCGCacheManager: Entry invalidated for key: %s"), *Key);
    }
}

void APCGCacheManager::RefreshEntry(const FString& Key)
{
    if (FPCGCacheEntry* Entry = CacheEntries.Find(Key))
    {
        Entry->LastModificationTime = FDateTime::Now();
        Entry->State = EPCGCacheState::Valid;
        UE_LOG(LogPCGCachePerformance, VeryVerbose, TEXT("APCGCacheManager: Entry refreshed for key: %s"), *Key);
    }
}

// Async Operations
void APCGCacheManager::LoadDataAsync(const FString& Key, const FOnCacheEntryLoaded& Callback)
{
    if (!bIsInitialized || !bIsEnabled)
    {
        FPCGCacheOperationResult Result;
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Cache not initialized or disabled");
        if (Callback.IsBound())
        {
            Callback.Broadcast(Key, Result);
        }
        return;
    }
    
    // Create async task
    TFuture<FPCGCacheOperationResult> Future = Async(EAsyncExecution::ThreadPool, [this, Key]() -> FPCGCacheOperationResult
    {
        FPCGCacheOperationResult Result;
        TArray<uint8> Data;
        
        double StartTime = FPlatformTime::Seconds();
        Result.bSuccess = LoadData(Key, Data);
        Result.OperationTimeMs = (FPlatformTime::Seconds() - StartTime) * 1000.0;
        Result.DataSizeBytes = Data.Num();
        Result.bFromCache = Result.bSuccess;
        
        if (!Result.bSuccess)
        {
            Result.ErrorMessage = TEXT("Failed to load data from cache");
        }
        
        return Result;
    });
    
    // Store future for tracking
    PendingOperations.Add(Key, MoveTemp(Future));
    
    // Set up completion callback
    Future.Then([this, Key, Callback](TFuture<FPCGCacheOperationResult> CompletedFuture)
    {
        FPCGCacheOperationResult Result = CompletedFuture.Get();
        if (Callback.IsBound())
        {
            Callback.Broadcast(Key, Result);
        }
        PendingOperations.Remove(Key);
    });
}

void APCGCacheManager::StoreDataAsync(const FString& Key, const TArray<uint8>& Data, EPCGCachePriority Priority)
{
    if (!bIsInitialized || !bIsEnabled)
    {
        return;
    }
    
    // Create async task
    Async(EAsyncExecution::ThreadPool, [this, Key, Data, Priority]()
    {
        StoreData(Key, Data, Priority);
    });
}

void APCGCacheManager::PrefetchData(const TArray<FString>& Keys)
{
    for (const FString& Key : Keys)
    {
        if (!HasEntry(Key))
        {
            PrefetchQueue.AddUnique(Key);
        }
    }
}

void APCGCacheManager::PrefetchByPattern(const FString& Pattern)
{
    // Simple pattern matching - in a real implementation, you'd use regex
    for (const auto& Entry : CacheEntries)
    {
        if (Entry.Key.Contains(Pattern))
        {
            PrefetchQueue.AddUnique(Entry.Key);
        }
    }
}

// Entry Management
FPCGCacheEntry APCGCacheManager::GetEntryInfo(const FString& Key) const
{
    if (const FPCGCacheEntry* Entry = CacheEntries.Find(Key))
    {
        return *Entry;
    }
    return FPCGCacheEntry();
}

TArray<FPCGCacheEntry> APCGCacheManager::GetAllEntries() const
{
    TArray<FPCGCacheEntry> Entries;
    for (const auto& Entry : CacheEntries)
    {
        Entries.Add(Entry.Value);
    }
    return Entries;
}

TArray<FPCGCacheEntry> APCGCacheManager::GetEntriesByPriority(EPCGCachePriority Priority) const
{
    TArray<FPCGCacheEntry> Entries;
    for (const auto& Entry : CacheEntries)
    {
        if (Entry.Value.Priority == Priority)
        {
            Entries.Add(Entry.Value);
        }
    }
    return Entries;
}

TArray<FPCGCacheEntry> APCGCacheManager::GetEntriesByType(const FString& Type) const
{
    TArray<FPCGCacheEntry> Entries;
    for (const auto& Entry : CacheEntries)
    {
        if (Entry.Value.EntryType == Type)
        {
            Entries.Add(Entry.Value);
        }
    }
    return Entries;
}

void APCGCacheManager::SetEntryPriority(const FString& Key, EPCGCachePriority Priority)
{
    if (FPCGCacheEntry* Entry = CacheEntries.Find(Key))
    {
        Entry->Priority = Priority;
        Entry->LastModificationTime = FDateTime::Now();
    }
}

void APCGCacheManager::AddEntryDependency(const FString& Key, const FString& DependencyKey)
{
    if (FPCGCacheEntry* Entry = CacheEntries.Find(Key))
    {
        Entry->Dependencies.AddUnique(DependencyKey);
    }
    
    if (FPCGCacheEntry* DependencyEntry = CacheEntries.Find(DependencyKey))
    {
        DependencyEntry->Dependents.AddUnique(Key);
    }
}

void APCGCacheManager::RemoveEntryDependency(const FString& Key, const FString& DependencyKey)
{
    if (FPCGCacheEntry* Entry = CacheEntries.Find(Key))
    {
        Entry->Dependencies.Remove(DependencyKey);
    }
    
    if (FPCGCacheEntry* DependencyEntry = CacheEntries.Find(DependencyKey))
    {
        DependencyEntry->Dependents.Remove(Key);
    }
}

// Statistics and Monitoring
FPCGCacheStatistics APCGCacheManager::GetCacheStatistics() const
{
    return CurrentStatistics;
}

float APCGCacheManager::GetHitRatio() const
{
    // CORRIGIDO: Usar GetValue() para FThreadSafeCounter64
    const int64 RequestCount = TotalCacheRequests.GetValue();
    if (RequestCount == 0)
    {
        return 0.0f;
    }
    const int64 HitCount = TotalCacheHits.GetValue();
    return (float)HitCount / (float)RequestCount;
}

float APCGCacheManager::GetCacheHitRate() const
{
    if (CurrentStatistics.TotalRequests == 0)
    {
        return 0.0f;
    }
    return (float)CurrentStatistics.CacheHits / (float)CurrentStatistics.TotalRequests * 100.0f;
}

int32 APCGCacheManager::GetMemoryUsage() const
{
    int32 TotalSize = 0;
    for (const auto& Entry : MemoryCache)
    {
        if (Entry.Value.IsValid())
        {
            TotalSize += Entry.Value->Num();
        }
    }
    return TotalSize / (1024 * 1024); // Convert to MB
}

int32 APCGCacheManager::GetDiskUsage() const
{
    return GetDiskCacheSize() / (1024 * 1024); // Convert to MB
}

void APCGCacheManager::ResetStatistics()
{
    CurrentStatistics = FPCGCacheStatistics();
    TotalCacheRequests = 0;
    TotalCacheHits = 0;
    TotalCacheMisses = 0;
}

void APCGCacheManager::ExportStatistics(const FString& FilePath) const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    
    JsonObject->SetNumberField(TEXT("TotalRequests"), CurrentStatistics.TotalRequests);
    JsonObject->SetNumberField(TEXT("CacheHits"), CurrentStatistics.CacheHits);
    JsonObject->SetNumberField(TEXT("CacheMisses"), CurrentStatistics.CacheMisses);
    JsonObject->SetNumberField(TEXT("HitRatio"), CurrentStatistics.HitRatio);
    JsonObject->SetNumberField(TEXT("MemoryUsedMB"), CurrentStatistics.MemoryUsedMB);
    JsonObject->SetNumberField(TEXT("DiskUsedMB"), CurrentStatistics.DiskUsedMB);
    
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);
    
    FFileHelper::SaveStringToFile(OutputString, *FilePath);
}

// Internal functions
void APCGCacheManager::UpdateCacheStatistics()
{
    // CORRIGIDO: Usar GetValue() para FThreadSafeCounter64
    CurrentStatistics.TotalRequests = (int32)TotalCacheRequests.GetValue();
    CurrentStatistics.CacheHits = (int32)TotalCacheHits.GetValue();
    CurrentStatistics.CacheMisses = (int32)TotalCacheMisses.GetValue();
    CurrentStatistics.HitRatio = GetHitRatio();
    CurrentStatistics.MemoryUsedMB = GetMemoryUsage();
    CurrentStatistics.DiskUsedMB = GetDiskUsage();
    CurrentStatistics.MemoryEntryCount = MemoryCache.Num();
    CurrentStatistics.DiskEntryCount = CacheEntries.Num() - MemoryCache.Num();
    
    // Update memory stats
    SET_MEMORY_STAT(STAT_PCGCacheMemory, CurrentStatistics.MemoryUsedMB * 1024 * 1024);
}

void APCGCacheManager::PerformMaintenance()
{
    if (bMaintenanceActive)
    {
        return;
    }
    
    bMaintenanceActive = true;
    
    // Evict expired entries
    EvictExpiredEntries();
    
    // Process eviction queue
    ProcessEvictionQueue();
    
    // Check memory pressure
    if (CurrentStatistics.MemoryUsedMB > CacheConfig.MaxMemoryCacheSizeMB * CacheConfig.MemoryEvictionThreshold)
    {
        ForceEviction(CacheConfig.MaxMemoryCacheSizeMB * 0.7f); // Target 70% usage
    }
    
    bMaintenanceActive = false;
}

void APCGCacheManager::ProcessEvictionQueue()
{
    SCOPE_CYCLE_COUNTER(STAT_PCGCacheEviction);
    
    // Process items in eviction queue
    FString KeyToEvict;
    while (EvictionQueue.Dequeue(KeyToEvict))
    {
        RemoveEntry(KeyToEvict);
        CurrentStatistics.EvictedEntries++;
    }
}

void APCGCacheManager::UpdateAccessFrequency(const FString& Key)
{
    int32* Frequency = AccessFrequency.Find(Key);
    if (Frequency)
    {
        (*Frequency)++;
    }
    else
    {
        AccessFrequency.Add(Key, 1);
    }
}

bool APCGCacheManager::ShouldEvictEntry(const FPCGCacheEntry& Entry) const
{
    // Don't evict critical priority entries
    if (Entry.Priority == EPCGCachePriority::Critical)
    {
        return false;
    }
    
    // Check if entry is expired
    FTimespan TimeSinceAccess = FDateTime::Now() - Entry.LastAccessTime;
    if (TimeSinceAccess.GetTotalSeconds() > CacheConfig.EntryExpirationTime)
    {
        return true;
    }
    
    // Check access frequency for LFU policy
    if (CacheConfig.EvictionPolicy == EPCGCacheEvictionPolicy::LFU)
    {
        const int32* Frequency = AccessFrequency.Find(Entry.EntryKey);
        return Frequency && *Frequency < 5; // Arbitrary threshold
    }
    
    return false;
}

FString APCGCacheManager::GenerateChecksum(const TArray<uint8>& Data) const
{
    return FMD5::HashBytes(Data.GetData(), Data.Num());
}

bool APCGCacheManager::ValidateEntry(const FString& Key) const
{
    const FPCGCacheEntry* Entry = CacheEntries.Find(Key);
    if (!Entry)
    {
        return false;
    }
    
    // Validate checksum if available
    if (!Entry->ChecksumHash.IsEmpty())
    {
        TArray<uint8> Data;
        if (const_cast<APCGCacheManager*>(this)->LoadData(Key, Data))
        {
            FString CurrentChecksum = GenerateChecksum(Data);
            return CurrentChecksum == Entry->ChecksumHash;
        }
    }
    
    return Entry->State == EPCGCacheState::Valid;
}

void APCGCacheManager::NotifyDependents(const FString& Key)
{
    const FPCGCacheEntry* Entry = CacheEntries.Find(Key);
    if (!Entry)
    {
        return;
    }
    
    // Invalidate all dependent entries
    for (const FString& DependentKey : Entry->Dependents)
    {
        InvalidateEntry(DependentKey);
    }
}

void APCGCacheManager::ProcessPrefetchQueue()
{
    if (PrefetchQueue.Num() == 0)
    {
        return;
    }
    
    // Process a limited number of prefetch requests per tick
    int32 ProcessCount = FMath::Min(PrefetchQueue.Num(), 3);
    
    for (int32 i = 0; i < ProcessCount; i++)
    {
        FString Key = PrefetchQueue[0];
        PrefetchQueue.RemoveAt(0);
        
        // Try to load from disk if not in memory
        if (!MemoryCache.Contains(Key) && CacheConfig.bEnableDiskCache)
        {
            LoadFromDisk(Key);
        }
    }
}

// Compression helpers
TArray<uint8> APCGCacheManager::CompressDataInternal(const TArray<uint8>& Data, EPCGCacheCompressionLevel Level) const
{
    SCOPE_CYCLE_COUNTER(STAT_PCGCacheCompression);
    
    // Simple compression using Unreal's built-in compression
    TArray<uint8> CompressedData;
    
    int32 CompressedSize = FCompression::CompressMemoryBound(NAME_Zlib, Data.Num());
    CompressedData.SetNumUninitialized(CompressedSize);
    
    bool bSuccess = FCompression::CompressMemory(NAME_Zlib, CompressedData.GetData(), CompressedSize, Data.GetData(), Data.Num());
    
    if (bSuccess)
    {
        CompressedData.SetNum(CompressedSize);
        return CompressedData;
    }
    
    return Data; // Return original data if compression failed
}

TArray<uint8> APCGCacheManager::DecompressDataInternal(const TArray<uint8>& CompressedData) const
{
    // For simplicity, assume we store the original size somewhere
    // In a real implementation, you'd store this metadata
    TArray<uint8> DecompressedData;
    
    // Estimate decompressed size (this is a simplification)
    int32 EstimatedSize = CompressedData.Num() * 4; // Assume 4:1 compression ratio
    DecompressedData.SetNumUninitialized(EstimatedSize);
    
    bool bSuccess = FCompression::UncompressMemory(NAME_Zlib, DecompressedData.GetData(), EstimatedSize, CompressedData.GetData(), CompressedData.Num());
    
    if (bSuccess)
    {
        DecompressedData.SetNum(EstimatedSize);
        return DecompressedData;
    }
    
    return CompressedData; // Return compressed data if decompression failed
}

// Disk I/O helpers
FString APCGCacheManager::GetDiskPath(const FString& Key) const
{
    FString SafeKey = Key.Replace(TEXT("/"), TEXT("_")).Replace(TEXT("\\"), TEXT("_"));
    return FPaths::ProjectSavedDir() / CacheConfig.DiskCachePath / (SafeKey + TEXT(".cache"));
}

bool APCGCacheManager::WriteToDisk(const FString& Key, const TArray<uint8>& Data)
{
    FString DiskPath = GetDiskPath(Key);
    return FFileHelper::SaveArrayToFile(Data, *DiskPath);
}

bool APCGCacheManager::ReadFromDisk(const FString& Key, TArray<uint8>& OutData)
{
    FString DiskPath = GetDiskPath(Key);
    return FFileHelper::LoadFileToArray(OutData, *DiskPath);
}

// Additional implementations for remaining functions...
void APCGCacheManager::ForceEviction(int32 TargetMemoryMB)
{
    // Implementation for forced eviction
}

void APCGCacheManager::EvictExpiredEntries()
{
    // Implementation for evicting expired entries
}

bool APCGCacheManager::SaveToDisk(const FString& Key)
{
    // Implementation for saving to disk
    return true;
}

bool APCGCacheManager::LoadFromDisk(const FString& Key)
{
    // Implementation for loading from disk
    return true;
}

void APCGCacheManager::CleanupDiskCache()
{
    // Implementation for disk cache cleanup
}

int64 APCGCacheManager::GetDiskCacheSize() const
{
    // Implementation for getting disk cache size
    return 0;
}

void APCGCacheManager::OptimizeMemoryLayout()
{
    // Implementation for memory layout optimization
}

void APCGCacheManager::CompactDiskCache()
{
    // Implementation for disk cache compaction
}

void APCGCacheManager::UpdateIntegratedSystems()
{
    // Implementation for updating integrated systems
}

// Integration functions
void APCGCacheManager::IntegrateWithWorldPartition(APCGWorldPartitionManager* WorldPartitionManager)
{
    WorldPartitionManagerRef = WorldPartitionManager;
}

void APCGCacheManager::IntegrateWithNanite(APCGNaniteOptimizer* NaniteOptimizer)
{
    NaniteOptimizerRef = NaniteOptimizer;
}

void APCGCacheManager::IntegrateWithLumen(APCGLumenIntegrator* LumenIntegrator)
{
    LumenIntegratorRef = LumenIntegrator;
}

void APCGCacheManager::IntegrateWithStreaming(APCGStreamingManager* StreamingManager)
{
    StreamingManagerRef = StreamingManager;
}

void APCGCacheManager::IntegrateWithProfiler(UPCGPerformanceProfiler* PerformanceProfiler)
{
    PerformanceProfilerRef = PerformanceProfiler;
}

// Compression functions
TArray<uint8> APCGCacheManager::CompressData(const TArray<uint8>& Data, EPCGCacheCompressionLevel Level) const
{
    return CompressDataInternal(Data, Level);
}

TArray<uint8> APCGCacheManager::DecompressData(const TArray<uint8>& CompressedData) const
{
    return DecompressDataInternal(CompressedData);
}

float APCGCacheManager::GetCompressionRatio(const TArray<uint8>& OriginalData, const TArray<uint8>& CompressedData) const
{
    if (OriginalData.Num() == 0)
    {
        return 0.0f;
    }
    return (float)CompressedData.Num() / (float)OriginalData.Num();
}

// Eviction functions
void APCGCacheManager::EvictByPriority(EPCGCachePriority MaxPriority)
{
    // Implementation for priority-based eviction
}

void APCGCacheManager::EvictLeastRecentlyUsed(int32 Count)
{
    // Implementation for LRU eviction
}

void APCGCacheManager::EvictLeastFrequentlyUsed(int32 Count)
{
    // Implementation for LFU eviction
}

// Network functions
void APCGCacheManager::SyncWithNetworkCache()
{
    // Implementation for network cache sync
}

void APCGCacheManager::UploadToNetwork(const FString& Key)
{
    // Implementation for network upload
}

void APCGCacheManager::DownloadFromNetwork(const FString& Key)
{
    // Implementation for network download
}

void APCGCacheManager::SendNetworkRequest(const FString& Endpoint, const FString& Data)
{
    // Implementation for network requests
}

void APCGCacheManager::HandleNetworkResponse(const FString& Response)
{
    // Implementation for network response handling
}

// Event handlers
void APCGCacheManager::OnMemoryPressure()
{
    // Implementation for memory pressure handling
}

void APCGCacheManager::OnDiskSpaceLow()
{
    // Implementation for low disk space handling
}

void APCGCacheManager::OnNetworkError(const FString& Error)
{
    // Implementation for network error handling
}

EPCGCachePriority APCGCacheManager::CalculateAdaptivePriority(const FPCGCacheEntry& Entry) const
{
    // Implementation for adaptive priority calculation
    return EPCGCachePriority::Medium;
}

// Implementações das funções adicionais para validação de qualidade
int32 APCGCacheManager::GetFragmentedEntryCount() const
{
    int32 FragmentedCount = 0;
    for (const auto& Entry : CacheEntries)
    {
        // Considerar entrada fragmentada se foi acessada há muito tempo
        if (Entry.Value.LastAccessTime < FDateTime::Now() - FTimespan::FromHours(24))
        {
            FragmentedCount++;
        }
    }
    return FragmentedCount;
}

int32 APCGCacheManager::GetTotalEntryCount() const
{
    return MemoryCache.Num() + CacheEntries.Num();
}

void APCGCacheManager::ClearUnusedEntries()
{
    SCOPE_CYCLE_COUNTER(STAT_PCGCacheEviction);

    TArray<FString> KeysToRemove;
    FDateTime ThresholdTime = FDateTime::Now() - FTimespan::FromHours(1); // 1 hora sem uso

    // Identificar entradas não utilizadas
    for (const auto& Entry : CacheEntries)
    {
        if (Entry.Value.LastAccessTime < ThresholdTime && Entry.Value.AccessCount == 0)
        {
            KeysToRemove.Add(Entry.Key);
        }
    }

    // Remover entradas identificadas
    for (const FString& Key : KeysToRemove)
    {
        RemoveEntry(Key);
    }

    UE_LOG(LogPCGCachePerformance, Log, TEXT("APCGCacheManager: Cleared %d unused entries"), KeysToRemove.Num());
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES AUXILIARES MODERNAS UE 5.6 =====

bool APCGCacheManager::IsLumenEnabled(UWorld* World) const
{
    if (!World)
    {
        return false;
    }

    // CORRIGIDO: Verificar Lumen usando APIs modernas UE 5.6
    if (World->GetFeatureLevel() >= ERHIFeatureLevel::SM5)
    {
        // Verificar se Lumen está habilitado nas configurações do projeto
        static const auto* CVarLumenEnabled = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.Lumen.Enabled"));
        if (CVarLumenEnabled && CVarLumenEnabled->GetValueOnGameThread() > 0)
        {
            return true;
        }
    }

    return false;
}

void APCGCacheManager::FlushCacheToDisk()
{
    if (!CacheConfig.bEnableDiskCache)
    {
        return;
    }

    UE_LOG(LogPCGCacheIO, Log, TEXT("Flushing cache to disk - %d entries"), MemoryCache.Num());

    // CORRIGIDO: Flush async usando UE 5.6 APIs
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this]()
    {
        int32 FlushedCount = 0;

        for (const auto& CachePair : MemoryCache)
        {
            const FString& Key = CachePair.Key;
            // CORRIGIDO: Desreferenciar TSharedPtr para obter TArray<uint8>
            const TArray<uint8>& Data = *CachePair.Value;

            if (WriteToDisk(Key, Data))
            {
                FlushedCount++;
            }
        }

        // Log resultado no game thread
        AsyncTask(ENamedThreads::GameThread, [this, FlushedCount]()
        {
            UE_LOG(LogPCGCacheIO, Log, TEXT("Cache flush completed - %d entries written to disk"), FlushedCount);
        });
    });
}
