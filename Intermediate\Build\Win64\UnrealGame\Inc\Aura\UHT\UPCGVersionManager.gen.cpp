// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "UPCGVersionManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUPCGVersionManager() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_UPCGVersionManager();
AURA_API UClass* Z_Construct_UClass_UPCGVersionManager_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGBackupStatus();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGRestoreMode();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGVersionType();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGBackupData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGVersionConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGVersionInfo();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPCGVersionType ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGVersionType;
static UEnum* EPCGVersionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGVersionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGVersionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGVersionType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGVersionType"));
	}
	return Z_Registration_Info_UEnum_EPCGVersionType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGVersionType>()
{
	return EPCGVersionType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGVersionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enums para sistema de versionamento\n */" },
#endif
		{ "Hotfix.Name", "EPCGVersionType::Hotfix" },
		{ "Major.Name", "EPCGVersionType::Major" },
		{ "Minor.Name", "EPCGVersionType::Minor" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
		{ "None.Name", "EPCGVersionType::None" },
		{ "Snapshot.Name", "EPCGVersionType::Snapshot" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums para sistema de versionamento" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGVersionType::None", (int64)EPCGVersionType::None },
		{ "EPCGVersionType::Minor", (int64)EPCGVersionType::Minor },
		{ "EPCGVersionType::Major", (int64)EPCGVersionType::Major },
		{ "EPCGVersionType::Hotfix", (int64)EPCGVersionType::Hotfix },
		{ "EPCGVersionType::Snapshot", (int64)EPCGVersionType::Snapshot },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGVersionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGVersionType",
	"EPCGVersionType",
	Z_Construct_UEnum_Aura_EPCGVersionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGVersionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGVersionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGVersionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGVersionType()
{
	if (!Z_Registration_Info_UEnum_EPCGVersionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGVersionType.InnerSingleton, Z_Construct_UEnum_Aura_EPCGVersionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGVersionType.InnerSingleton;
}
// ********** End Enum EPCGVersionType *************************************************************

// ********** Begin Enum EPCGBackupStatus **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGBackupStatus;
static UEnum* EPCGBackupStatus_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGBackupStatus.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGBackupStatus.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGBackupStatus, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGBackupStatus"));
	}
	return Z_Registration_Info_UEnum_EPCGBackupStatus.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGBackupStatus>()
{
	return EPCGBackupStatus_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGBackupStatus_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Completed.Name", "EPCGBackupStatus::Completed" },
		{ "Corrupted.Name", "EPCGBackupStatus::Corrupted" },
		{ "Failed.Name", "EPCGBackupStatus::Failed" },
		{ "InProgress.Name", "EPCGBackupStatus::InProgress" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
		{ "Pending.Name", "EPCGBackupStatus::Pending" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGBackupStatus::Pending", (int64)EPCGBackupStatus::Pending },
		{ "EPCGBackupStatus::InProgress", (int64)EPCGBackupStatus::InProgress },
		{ "EPCGBackupStatus::Completed", (int64)EPCGBackupStatus::Completed },
		{ "EPCGBackupStatus::Failed", (int64)EPCGBackupStatus::Failed },
		{ "EPCGBackupStatus::Corrupted", (int64)EPCGBackupStatus::Corrupted },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGBackupStatus_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGBackupStatus",
	"EPCGBackupStatus",
	Z_Construct_UEnum_Aura_EPCGBackupStatus_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGBackupStatus_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGBackupStatus_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGBackupStatus_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGBackupStatus()
{
	if (!Z_Registration_Info_UEnum_EPCGBackupStatus.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGBackupStatus.InnerSingleton, Z_Construct_UEnum_Aura_EPCGBackupStatus_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGBackupStatus.InnerSingleton;
}
// ********** End Enum EPCGBackupStatus ************************************************************

// ********** Begin Enum EPCGRestoreMode ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGRestoreMode;
static UEnum* EPCGRestoreMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGRestoreMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGRestoreMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGRestoreMode, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGRestoreMode"));
	}
	return Z_Registration_Info_UEnum_EPCGRestoreMode.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGRestoreMode>()
{
	return EPCGRestoreMode_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGRestoreMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ConfigOnly.Name", "EPCGRestoreMode::ConfigOnly" },
		{ "Full.Name", "EPCGRestoreMode::Full" },
		{ "GeometryOnly.Name", "EPCGRestoreMode::GeometryOnly" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
		{ "Partial.Name", "EPCGRestoreMode::Partial" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGRestoreMode::Full", (int64)EPCGRestoreMode::Full },
		{ "EPCGRestoreMode::Partial", (int64)EPCGRestoreMode::Partial },
		{ "EPCGRestoreMode::GeometryOnly", (int64)EPCGRestoreMode::GeometryOnly },
		{ "EPCGRestoreMode::ConfigOnly", (int64)EPCGRestoreMode::ConfigOnly },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGRestoreMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGRestoreMode",
	"EPCGRestoreMode",
	Z_Construct_UEnum_Aura_EPCGRestoreMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGRestoreMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGRestoreMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGRestoreMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGRestoreMode()
{
	if (!Z_Registration_Info_UEnum_EPCGRestoreMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGRestoreMode.InnerSingleton, Z_Construct_UEnum_Aura_EPCGRestoreMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGRestoreMode.InnerSingleton;
}
// ********** End Enum EPCGRestoreMode *************************************************************

// ********** Begin ScriptStruct FPCGVersionInfo ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGVersionInfo;
class UScriptStruct* FPCGVersionInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGVersionInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGVersionInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGVersionInfo, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGVersionInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGVersionInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGVersionInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estruturas de dados para versionamento\n */" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estruturas de dados para versionamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MajorVersion_MetaData[] = {
		{ "Category", "Version" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinorVersion_MetaData[] = {
		{ "Category", "Version" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatchVersion_MetaData[] = {
		{ "Category", "Version" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BuildNumber_MetaData[] = {
		{ "Category", "Version" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Version" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Author_MetaData[] = {
		{ "Category", "Version" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Version" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChangedFiles_MetaData[] = {
		{ "Category", "Version" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MajorVersion;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinorVersion;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PatchVersion;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BuildNumber;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Author;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChangedFiles_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ChangedFiles;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGVersionInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_MajorVersion = { "MajorVersion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGVersionInfo, MajorVersion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MajorVersion_MetaData), NewProp_MajorVersion_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_MinorVersion = { "MinorVersion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGVersionInfo, MinorVersion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinorVersion_MetaData), NewProp_MinorVersion_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_PatchVersion = { "PatchVersion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGVersionInfo, PatchVersion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatchVersion_MetaData), NewProp_PatchVersion_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_BuildNumber = { "BuildNumber", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGVersionInfo, BuildNumber), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BuildNumber_MetaData), NewProp_BuildNumber_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGVersionInfo, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_Author = { "Author", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGVersionInfo, Author), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Author_MetaData), NewProp_Author_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGVersionInfo, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_ChangedFiles_Inner = { "ChangedFiles", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_ChangedFiles = { "ChangedFiles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGVersionInfo, ChangedFiles), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChangedFiles_MetaData), NewProp_ChangedFiles_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_MajorVersion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_MinorVersion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_PatchVersion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_BuildNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_Author,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_ChangedFiles_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewProp_ChangedFiles,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGVersionInfo",
	Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::PropPointers),
	sizeof(FPCGVersionInfo),
	alignof(FPCGVersionInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGVersionInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGVersionInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGVersionInfo.InnerSingleton, Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGVersionInfo.InnerSingleton;
}
// ********** End ScriptStruct FPCGVersionInfo *****************************************************

// ********** Begin ScriptStruct FPCGBackupData ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGBackupData;
class UScriptStruct* FPCGBackupData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGBackupData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGBackupData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGBackupData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGBackupData"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGBackupData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGBackupData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionInfo_MetaData[] = {
		{ "Category", "Backup" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackupPath_MetaData[] = {
		{ "Category", "Backup" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackupSize_MetaData[] = {
		{ "Category", "Backup" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Status_MetaData[] = {
		{ "Category", "Backup" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Checksum_MetaData[] = {
		{ "Category", "Backup" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentData_MetaData[] = {
		{ "Category", "Backup" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompressedData_MetaData[] = {
		{ "Category", "Backup" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionInfo;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BackupPath;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_BackupSize;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Status_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Status;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Checksum;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ComponentData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ComponentData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ComponentData;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CompressedData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompressedData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGBackupData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_VersionInfo = { "VersionInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGBackupData, VersionInfo), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionInfo_MetaData), NewProp_VersionInfo_MetaData) }; // 1371083148
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_BackupPath = { "BackupPath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGBackupData, BackupPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackupPath_MetaData), NewProp_BackupPath_MetaData) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_BackupSize = { "BackupSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGBackupData, BackupSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackupSize_MetaData), NewProp_BackupSize_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_Status_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_Status = { "Status", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGBackupData, Status), Z_Construct_UEnum_Aura_EPCGBackupStatus, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Status_MetaData), NewProp_Status_MetaData) }; // 1646359674
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_Checksum = { "Checksum", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGBackupData, Checksum), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Checksum_MetaData), NewProp_Checksum_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_ComponentData_ValueProp = { "ComponentData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_ComponentData_Key_KeyProp = { "ComponentData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_ComponentData = { "ComponentData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGBackupData, ComponentData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentData_MetaData), NewProp_ComponentData_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_CompressedData_Inner = { "CompressedData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_CompressedData = { "CompressedData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGBackupData, CompressedData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompressedData_MetaData), NewProp_CompressedData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGBackupData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_VersionInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_BackupPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_BackupSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_Status_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_Status,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_Checksum,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_ComponentData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_ComponentData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_ComponentData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_CompressedData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewProp_CompressedData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGBackupData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGBackupData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGBackupData",
	Z_Construct_UScriptStruct_FPCGBackupData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGBackupData_Statics::PropPointers),
	sizeof(FPCGBackupData),
	alignof(FPCGBackupData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGBackupData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGBackupData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGBackupData()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGBackupData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGBackupData.InnerSingleton, Z_Construct_UScriptStruct_FPCGBackupData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGBackupData.InnerSingleton;
}
// ********** End ScriptStruct FPCGBackupData ******************************************************

// ********** Begin ScriptStruct FPCGVersionConfig *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGVersionConfig;
class UScriptStruct* FPCGVersionConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGVersionConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGVersionConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGVersionConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGVersionConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGVersionConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGVersionConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoBackupEnabled_MetaData[] = {
		{ "Category", "Config" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AutoBackupInterval_MetaData[] = {
		{ "Category", "Config" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxBackupCount_MetaData[] = {
		{ "Category", "Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 5 minutos\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "5 minutos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCompressBackups_MetaData[] = {
		{ "Category", "Config" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateChecksums_MetaData[] = {
		{ "Category", "Config" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackupDirectory_MetaData[] = {
		{ "Category", "Config" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExcludedPaths_MetaData[] = {
		{ "Category", "Config" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bAutoBackupEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoBackupEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AutoBackupInterval;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxBackupCount;
	static void NewProp_bCompressBackups_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCompressBackups;
	static void NewProp_bValidateChecksums_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateChecksums;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BackupDirectory;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ExcludedPaths_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExcludedPaths;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGVersionConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_bAutoBackupEnabled_SetBit(void* Obj)
{
	((FPCGVersionConfig*)Obj)->bAutoBackupEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_bAutoBackupEnabled = { "bAutoBackupEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGVersionConfig), &Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_bAutoBackupEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoBackupEnabled_MetaData), NewProp_bAutoBackupEnabled_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_AutoBackupInterval = { "AutoBackupInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGVersionConfig, AutoBackupInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AutoBackupInterval_MetaData), NewProp_AutoBackupInterval_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_MaxBackupCount = { "MaxBackupCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGVersionConfig, MaxBackupCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxBackupCount_MetaData), NewProp_MaxBackupCount_MetaData) };
void Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_bCompressBackups_SetBit(void* Obj)
{
	((FPCGVersionConfig*)Obj)->bCompressBackups = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_bCompressBackups = { "bCompressBackups", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGVersionConfig), &Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_bCompressBackups_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCompressBackups_MetaData), NewProp_bCompressBackups_MetaData) };
void Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_bValidateChecksums_SetBit(void* Obj)
{
	((FPCGVersionConfig*)Obj)->bValidateChecksums = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_bValidateChecksums = { "bValidateChecksums", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGVersionConfig), &Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_bValidateChecksums_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateChecksums_MetaData), NewProp_bValidateChecksums_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_BackupDirectory = { "BackupDirectory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGVersionConfig, BackupDirectory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackupDirectory_MetaData), NewProp_BackupDirectory_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_ExcludedPaths_Inner = { "ExcludedPaths", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_ExcludedPaths = { "ExcludedPaths", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGVersionConfig, ExcludedPaths), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExcludedPaths_MetaData), NewProp_ExcludedPaths_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_bAutoBackupEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_AutoBackupInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_MaxBackupCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_bCompressBackups,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_bValidateChecksums,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_BackupDirectory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_ExcludedPaths_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewProp_ExcludedPaths,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGVersionConfig",
	Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::PropPointers),
	sizeof(FPCGVersionConfig),
	alignof(FPCGVersionConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGVersionConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGVersionConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGVersionConfig.InnerSingleton, Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGVersionConfig.InnerSingleton;
}
// ********** End ScriptStruct FPCGVersionConfig ***************************************************

// ********** Begin Delegate FOnBackupCompleted ****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnBackupCompleted_Parms
	{
		bool bSuccess;
		FPCGVersionInfo VersionInfo;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Delegates para eventos de versionamento\n */" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates para eventos de versionamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionInfo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((_Script_Aura_eventOnBackupCompleted_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_Aura_eventOnBackupCompleted_Parms), &Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::NewProp_VersionInfo = { "VersionInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnBackupCompleted_Parms, VersionInfo), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionInfo_MetaData), NewProp_VersionInfo_MetaData) }; // 1371083148
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::NewProp_VersionInfo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnBackupCompleted__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::_Script_Aura_eventOnBackupCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::_Script_Aura_eventOnBackupCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnBackupCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnBackupCompleted, bool bSuccess, FPCGVersionInfo const& VersionInfo)
{
	struct _Script_Aura_eventOnBackupCompleted_Parms
	{
		bool bSuccess;
		FPCGVersionInfo VersionInfo;
	};
	_Script_Aura_eventOnBackupCompleted_Parms Parms;
	Parms.bSuccess=bSuccess ? true : false;
	Parms.VersionInfo=VersionInfo;
	OnBackupCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBackupCompleted ******************************************************

// ********** Begin Delegate FOnRestoreCompleted ***************************************************
struct Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnRestoreCompleted_Parms
	{
		bool bSuccess;
		FPCGVersionInfo VersionInfo;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionInfo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((_Script_Aura_eventOnRestoreCompleted_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_Aura_eventOnRestoreCompleted_Parms), &Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::NewProp_VersionInfo = { "VersionInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnRestoreCompleted_Parms, VersionInfo), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionInfo_MetaData), NewProp_VersionInfo_MetaData) }; // 1371083148
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::NewProp_VersionInfo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnRestoreCompleted__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::_Script_Aura_eventOnRestoreCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::_Script_Aura_eventOnRestoreCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRestoreCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnRestoreCompleted, bool bSuccess, FPCGVersionInfo const& VersionInfo)
{
	struct _Script_Aura_eventOnRestoreCompleted_Parms
	{
		bool bSuccess;
		FPCGVersionInfo VersionInfo;
	};
	_Script_Aura_eventOnRestoreCompleted_Parms Parms;
	Parms.bSuccess=bSuccess ? true : false;
	Parms.VersionInfo=VersionInfo;
	OnRestoreCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRestoreCompleted *****************************************************

// ********** Begin Delegate FOnVersionCreated *****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnVersionCreated_Parms
	{
		FPCGVersionInfo VersionInfo;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionInfo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature_Statics::NewProp_VersionInfo = { "VersionInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnVersionCreated_Parms, VersionInfo), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionInfo_MetaData), NewProp_VersionInfo_MetaData) }; // 1371083148
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature_Statics::NewProp_VersionInfo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnVersionCreated__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature_Statics::_Script_Aura_eventOnVersionCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature_Statics::_Script_Aura_eventOnVersionCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnVersionCreated_DelegateWrapper(const FMulticastScriptDelegate& OnVersionCreated, FPCGVersionInfo const& VersionInfo)
{
	struct _Script_Aura_eventOnVersionCreated_Parms
	{
		FPCGVersionInfo VersionInfo;
	};
	_Script_Aura_eventOnVersionCreated_Parms Parms;
	Parms.VersionInfo=VersionInfo;
	OnVersionCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnVersionCreated *******************************************************

// ********** Begin Delegate FOnBackupProgress *****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnBackupProgress_Parms
	{
		float Progress;
		FString CurrentFile;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentFile_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentFile;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnBackupProgress_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::NewProp_CurrentFile = { "CurrentFile", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnBackupProgress_Parms, CurrentFile), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentFile_MetaData), NewProp_CurrentFile_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::NewProp_Progress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::NewProp_CurrentFile,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnBackupProgress__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::_Script_Aura_eventOnBackupProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::_Script_Aura_eventOnBackupProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnBackupProgress_DelegateWrapper(const FMulticastScriptDelegate& OnBackupProgress, float Progress, const FString& CurrentFile)
{
	struct _Script_Aura_eventOnBackupProgress_Parms
	{
		float Progress;
		FString CurrentFile;
	};
	_Script_Aura_eventOnBackupProgress_Parms Parms;
	Parms.Progress=Progress;
	Parms.CurrentFile=CurrentFile;
	OnBackupProgress.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBackupProgress *******************************************************

// ********** Begin Class UPCGVersionManager Function BackupMOBAComponent **************************
struct Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics
{
	struct PCGVersionManager_eventBackupMOBAComponent_Parms
	{
		FString ComponentName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ComponentName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::NewProp_ComponentName = { "ComponentName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventBackupMOBAComponent_Parms, ComponentName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentName_MetaData), NewProp_ComponentName_MetaData) };
void Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGVersionManager_eventBackupMOBAComponent_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGVersionManager_eventBackupMOBAComponent_Parms), &Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::NewProp_ComponentName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "BackupMOBAComponent", Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::PCGVersionManager_eventBackupMOBAComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::PCGVersionManager_eventBackupMOBAComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execBackupMOBAComponent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ComponentName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BackupMOBAComponent(Z_Param_ComponentName);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function BackupMOBAComponent ****************************

// ********** Begin Class UPCGVersionManager Function CleanupOldBackups ****************************
struct Z_Construct_UFunction_UPCGVersionManager_CleanupOldBackups_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_CleanupOldBackups_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "CleanupOldBackups", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_CleanupOldBackups_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_CleanupOldBackups_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGVersionManager_CleanupOldBackups()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_CleanupOldBackups_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execCleanupOldBackups)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupOldBackups();
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function CleanupOldBackups ******************************

// ********** Begin Class UPCGVersionManager Function CompareVersions ******************************
struct Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics
{
	struct PCGVersionManager_eventCompareVersions_Parms
	{
		FPCGVersionInfo VersionA;
		FPCGVersionInfo VersionB;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Compara\xc3\xa7\xc3\xa3o de Vers\xc3\xb5""es ===\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Compara\xc3\xa7\xc3\xa3o de Vers\xc3\xb5""es ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionA_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionB_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionA;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionB;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::NewProp_VersionA = { "VersionA", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventCompareVersions_Parms, VersionA), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionA_MetaData), NewProp_VersionA_MetaData) }; // 1371083148
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::NewProp_VersionB = { "VersionB", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventCompareVersions_Parms, VersionB), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionB_MetaData), NewProp_VersionB_MetaData) }; // 1371083148
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventCompareVersions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::NewProp_VersionA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::NewProp_VersionB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "CompareVersions", Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::PCGVersionManager_eventCompareVersions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::PCGVersionManager_eventCompareVersions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_CompareVersions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_CompareVersions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execCompareVersions)
{
	P_GET_STRUCT_REF(FPCGVersionInfo,Z_Param_Out_VersionA);
	P_GET_STRUCT_REF(FPCGVersionInfo,Z_Param_Out_VersionB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->CompareVersions(Z_Param_Out_VersionA,Z_Param_Out_VersionB);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function CompareVersions ********************************

// ********** Begin Class UPCGVersionManager Function CreateBackup *********************************
struct Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics
{
	struct PCGVersionManager_eventCreateBackup_Parms
	{
		FPCGVersionInfo VersionInfo;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionInfo;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::NewProp_VersionInfo = { "VersionInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventCreateBackup_Parms, VersionInfo), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionInfo_MetaData), NewProp_VersionInfo_MetaData) }; // 1371083148
void Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGVersionManager_eventCreateBackup_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGVersionManager_eventCreateBackup_Parms), &Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::NewProp_VersionInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "CreateBackup", Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::PCGVersionManager_eventCreateBackup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::PCGVersionManager_eventCreateBackup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_CreateBackup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_CreateBackup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execCreateBackup)
{
	P_GET_STRUCT_REF(FPCGVersionInfo,Z_Param_Out_VersionInfo);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateBackup(Z_Param_Out_VersionInfo);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function CreateBackup ***********************************

// ********** Begin Class UPCGVersionManager Function CreateVersion ********************************
struct Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics
{
	struct PCGVersionManager_eventCreateVersion_Parms
	{
		EPCGVersionType VersionType;
		FString Description;
		FString Author;
		FPCGVersionInfo ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Gerenciamento de Vers\xc3\xb5""es ===\n" },
#endif
		{ "CPP_Default_Author", "" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Gerenciamento de Vers\xc3\xb5""es ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Author_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_VersionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VersionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Author;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::NewProp_VersionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::NewProp_VersionType = { "VersionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventCreateVersion_Parms, VersionType), Z_Construct_UEnum_Aura_EPCGVersionType, METADATA_PARAMS(0, nullptr) }; // 3150078037
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventCreateVersion_Parms, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::NewProp_Author = { "Author", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventCreateVersion_Parms, Author), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Author_MetaData), NewProp_Author_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventCreateVersion_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(0, nullptr) }; // 1371083148
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::NewProp_VersionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::NewProp_VersionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::NewProp_Author,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "CreateVersion", Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::PCGVersionManager_eventCreateVersion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::PCGVersionManager_eventCreateVersion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_CreateVersion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_CreateVersion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execCreateVersion)
{
	P_GET_ENUM(EPCGVersionType,Z_Param_VersionType);
	P_GET_PROPERTY(FStrProperty,Z_Param_Description);
	P_GET_PROPERTY(FStrProperty,Z_Param_Author);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGVersionInfo*)Z_Param__Result=P_THIS->CreateVersion(EPCGVersionType(Z_Param_VersionType),Z_Param_Description,Z_Param_Author);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function CreateVersion **********************************

// ********** Begin Class UPCGVersionManager Function DeleteVersion ********************************
struct Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics
{
	struct PCGVersionManager_eventDeleteVersion_Parms
	{
		FPCGVersionInfo VersionInfo;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionInfo;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::NewProp_VersionInfo = { "VersionInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventDeleteVersion_Parms, VersionInfo), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionInfo_MetaData), NewProp_VersionInfo_MetaData) }; // 1371083148
void Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGVersionManager_eventDeleteVersion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGVersionManager_eventDeleteVersion_Parms), &Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::NewProp_VersionInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "DeleteVersion", Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::PCGVersionManager_eventDeleteVersion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::PCGVersionManager_eventDeleteVersion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_DeleteVersion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_DeleteVersion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execDeleteVersion)
{
	P_GET_STRUCT_REF(FPCGVersionInfo,Z_Param_Out_VersionInfo);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DeleteVersion(Z_Param_Out_VersionInfo);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function DeleteVersion **********************************

// ********** Begin Class UPCGVersionManager Function ExportVersion ********************************
struct Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics
{
	struct PCGVersionManager_eventExportVersion_Parms
	{
		FPCGVersionInfo VersionInfo;
		FString ExportPath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Exporta\xc3\xa7\xc3\xa3o e Importa\xc3\xa7\xc3\xa3o ===\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Exporta\xc3\xa7\xc3\xa3o e Importa\xc3\xa7\xc3\xa3o ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExportPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionInfo;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ExportPath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::NewProp_VersionInfo = { "VersionInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventExportVersion_Parms, VersionInfo), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionInfo_MetaData), NewProp_VersionInfo_MetaData) }; // 1371083148
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::NewProp_ExportPath = { "ExportPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventExportVersion_Parms, ExportPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExportPath_MetaData), NewProp_ExportPath_MetaData) };
void Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGVersionManager_eventExportVersion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGVersionManager_eventExportVersion_Parms), &Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::NewProp_VersionInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::NewProp_ExportPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "ExportVersion", Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::PCGVersionManager_eventExportVersion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::PCGVersionManager_eventExportVersion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_ExportVersion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_ExportVersion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execExportVersion)
{
	P_GET_STRUCT_REF(FPCGVersionInfo,Z_Param_Out_VersionInfo);
	P_GET_PROPERTY(FStrProperty,Z_Param_ExportPath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExportVersion(Z_Param_Out_VersionInfo,Z_Param_ExportPath);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function ExportVersion **********************************

// ********** Begin Class UPCGVersionManager Function FindVersionsByAuthor *************************
struct Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics
{
	struct PCGVersionManager_eventFindVersionsByAuthor_Parms
	{
		FString Author;
		TArray<FPCGVersionInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Author_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Author;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::NewProp_Author = { "Author", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventFindVersionsByAuthor_Parms, Author), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Author_MetaData), NewProp_Author_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(0, nullptr) }; // 1371083148
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventFindVersionsByAuthor_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1371083148
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::NewProp_Author,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "FindVersionsByAuthor", Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::PCGVersionManager_eventFindVersionsByAuthor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::PCGVersionManager_eventFindVersionsByAuthor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execFindVersionsByAuthor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Author);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGVersionInfo>*)Z_Param__Result=P_THIS->FindVersionsByAuthor(Z_Param_Author);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function FindVersionsByAuthor ***************************

// ********** Begin Class UPCGVersionManager Function FindVersionsByDateRange **********************
struct Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics
{
	struct PCGVersionManager_eventFindVersionsByDateRange_Parms
	{
		FDateTime StartDate;
		FDateTime EndDate;
		TArray<FPCGVersionInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartDate_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndDate_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartDate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndDate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::NewProp_StartDate = { "StartDate", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventFindVersionsByDateRange_Parms, StartDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartDate_MetaData), NewProp_StartDate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::NewProp_EndDate = { "EndDate", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventFindVersionsByDateRange_Parms, EndDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndDate_MetaData), NewProp_EndDate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(0, nullptr) }; // 1371083148
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventFindVersionsByDateRange_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1371083148
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::NewProp_StartDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::NewProp_EndDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "FindVersionsByDateRange", Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::PCGVersionManager_eventFindVersionsByDateRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::PCGVersionManager_eventFindVersionsByDateRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execFindVersionsByDateRange)
{
	P_GET_STRUCT_REF(FDateTime,Z_Param_Out_StartDate);
	P_GET_STRUCT_REF(FDateTime,Z_Param_Out_EndDate);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGVersionInfo>*)Z_Param__Result=P_THIS->FindVersionsByDateRange(Z_Param_Out_StartDate,Z_Param_Out_EndDate);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function FindVersionsByDateRange ************************

// ********** Begin Class UPCGVersionManager Function ForceBackup **********************************
struct Z_Construct_UFunction_UPCGVersionManager_ForceBackup_Statics
{
	struct PCGVersionManager_eventForceBackup_Parms
	{
		FString Description;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "CPP_Default_Description", "Manual Backup" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGVersionManager_ForceBackup_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventForceBackup_Parms, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_ForceBackup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_ForceBackup_Statics::NewProp_Description,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_ForceBackup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_ForceBackup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "ForceBackup", Z_Construct_UFunction_UPCGVersionManager_ForceBackup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_ForceBackup_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_ForceBackup_Statics::PCGVersionManager_eventForceBackup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_ForceBackup_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_ForceBackup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_ForceBackup_Statics::PCGVersionManager_eventForceBackup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_ForceBackup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_ForceBackup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execForceBackup)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Description);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceBackup(Z_Param_Description);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function ForceBackup ************************************

// ********** Begin Class UPCGVersionManager Function GenerateVersionDiff **************************
struct Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics
{
	struct PCGVersionManager_eventGenerateVersionDiff_Parms
	{
		FPCGVersionInfo VersionA;
		FPCGVersionInfo VersionB;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionA_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionB_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionA;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionB;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::NewProp_VersionA = { "VersionA", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventGenerateVersionDiff_Parms, VersionA), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionA_MetaData), NewProp_VersionA_MetaData) }; // 1371083148
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::NewProp_VersionB = { "VersionB", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventGenerateVersionDiff_Parms, VersionB), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionB_MetaData), NewProp_VersionB_MetaData) }; // 1371083148
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventGenerateVersionDiff_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::NewProp_VersionA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::NewProp_VersionB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "GenerateVersionDiff", Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::PCGVersionManager_eventGenerateVersionDiff_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::PCGVersionManager_eventGenerateVersionDiff_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execGenerateVersionDiff)
{
	P_GET_STRUCT_REF(FPCGVersionInfo,Z_Param_Out_VersionA);
	P_GET_STRUCT_REF(FPCGVersionInfo,Z_Param_Out_VersionB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GenerateVersionDiff(Z_Param_Out_VersionA,Z_Param_Out_VersionB);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function GenerateVersionDiff ****************************

// ********** Begin Class UPCGVersionManager Function GetAllVersions *******************************
struct Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics
{
	struct PCGVersionManager_eventGetAllVersions_Parms
	{
		TArray<FPCGVersionInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Consulta de Vers\xc3\xb5""es ===\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Consulta de Vers\xc3\xb5""es ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(0, nullptr) }; // 1371083148
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventGetAllVersions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1371083148
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "GetAllVersions", Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::PCGVersionManager_eventGetAllVersions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::PCGVersionManager_eventGetAllVersions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_GetAllVersions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_GetAllVersions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execGetAllVersions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGVersionInfo>*)Z_Param__Result=P_THIS->GetAllVersions();
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function GetAllVersions *********************************

// ********** Begin Class UPCGVersionManager Function GetConfiguration *****************************
struct Z_Construct_UFunction_UPCGVersionManager_GetConfiguration_Statics
{
	struct PCGVersionManager_eventGetConfiguration_Parms
	{
		FPCGVersionConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGVersionConfig, METADATA_PARAMS(0, nullptr) }; // 2620595296
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UPCGVersionManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_GetConfiguration_Statics::PCGVersionManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_GetConfiguration_Statics::PCGVersionManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGVersionConfig*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function GetConfiguration *******************************

// ********** Begin Class UPCGVersionManager Function GetCurrentVersion ****************************
struct Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion_Statics
{
	struct PCGVersionManager_eventGetCurrentVersion_Parms
	{
		FPCGVersionInfo ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventGetCurrentVersion_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(0, nullptr) }; // 1371083148
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "GetCurrentVersion", Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion_Statics::PCGVersionManager_eventGetCurrentVersion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion_Statics::PCGVersionManager_eventGetCurrentVersion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execGetCurrentVersion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGVersionInfo*)Z_Param__Result=P_THIS->GetCurrentVersion();
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function GetCurrentVersion ******************************

// ********** Begin Class UPCGVersionManager Function GetLatestVersion *****************************
struct Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion_Statics
{
	struct PCGVersionManager_eventGetLatestVersion_Parms
	{
		FPCGVersionInfo ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventGetLatestVersion_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(0, nullptr) }; // 1371083148
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "GetLatestVersion", Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion_Statics::PCGVersionManager_eventGetLatestVersion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion_Statics::PCGVersionManager_eventGetLatestVersion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execGetLatestVersion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGVersionInfo*)Z_Param__Result=P_THIS->GetLatestVersion();
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function GetLatestVersion *******************************

// ********** Begin Class UPCGVersionManager Function ImportVersion ********************************
struct Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics
{
	struct PCGVersionManager_eventImportVersion_Parms
	{
		FString ImportPath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImportPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ImportPath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::NewProp_ImportPath = { "ImportPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventImportVersion_Parms, ImportPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImportPath_MetaData), NewProp_ImportPath_MetaData) };
void Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGVersionManager_eventImportVersion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGVersionManager_eventImportVersion_Parms), &Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::NewProp_ImportPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "ImportVersion", Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::PCGVersionManager_eventImportVersion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::PCGVersionManager_eventImportVersion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_ImportVersion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_ImportVersion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execImportVersion)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ImportPath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ImportVersion(Z_Param_ImportPath);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function ImportVersion **********************************

// ********** Begin Class UPCGVersionManager Function Initialize ***********************************
struct Z_Construct_UFunction_UPCGVersionManager_Initialize_Statics
{
	struct PCGVersionManager_eventInitialize_Parms
	{
		FPCGVersionConfig InConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Inicializa\xc3\xa7\xc3\xa3o e Configura\xc3\xa7\xc3\xa3o ===\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Inicializa\xc3\xa7\xc3\xa3o e Configura\xc3\xa7\xc3\xa3o ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_Initialize_Statics::NewProp_InConfig = { "InConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventInitialize_Parms, InConfig), Z_Construct_UScriptStruct_FPCGVersionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InConfig_MetaData), NewProp_InConfig_MetaData) }; // 2620595296
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_Initialize_Statics::NewProp_InConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "Initialize", Z_Construct_UFunction_UPCGVersionManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_Initialize_Statics::PCGVersionManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_Initialize_Statics::PCGVersionManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execInitialize)
{
	P_GET_STRUCT_REF(FPCGVersionConfig,Z_Param_Out_InConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_InConfig);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function Initialize *************************************

// ********** Begin Class UPCGVersionManager Function IsAutoBackupRunning **************************
struct Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics
{
	struct PCGVersionManager_eventIsAutoBackupRunning_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGVersionManager_eventIsAutoBackupRunning_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGVersionManager_eventIsAutoBackupRunning_Parms), &Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "IsAutoBackupRunning", Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::PCGVersionManager_eventIsAutoBackupRunning_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::PCGVersionManager_eventIsAutoBackupRunning_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execIsAutoBackupRunning)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAutoBackupRunning();
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function IsAutoBackupRunning ****************************

// ********** Begin Class UPCGVersionManager Function RegisterMOBAComponent ************************
struct Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics
{
	struct PCGVersionManager_eventRegisterMOBAComponent_Parms
	{
		FString ComponentName;
		UObject* Component;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Integra\xc3\xa7\xc3\xa3o com Componentes MOBA ===\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Integra\xc3\xa7\xc3\xa3o com Componentes MOBA ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ComponentName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::NewProp_ComponentName = { "ComponentName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventRegisterMOBAComponent_Parms, ComponentName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentName_MetaData), NewProp_ComponentName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventRegisterMOBAComponent_Parms, Component), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::NewProp_ComponentName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::NewProp_Component,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "RegisterMOBAComponent", Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::PCGVersionManager_eventRegisterMOBAComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::PCGVersionManager_eventRegisterMOBAComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execRegisterMOBAComponent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ComponentName);
	P_GET_OBJECT(UObject,Z_Param_Component);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterMOBAComponent(Z_Param_ComponentName,Z_Param_Component);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function RegisterMOBAComponent **************************

// ********** Begin Class UPCGVersionManager Function RepairCorruptedBackup ************************
struct Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics
{
	struct PCGVersionManager_eventRepairCorruptedBackup_Parms
	{
		FPCGVersionInfo VersionInfo;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionInfo;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::NewProp_VersionInfo = { "VersionInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventRepairCorruptedBackup_Parms, VersionInfo), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionInfo_MetaData), NewProp_VersionInfo_MetaData) }; // 1371083148
void Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGVersionManager_eventRepairCorruptedBackup_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGVersionManager_eventRepairCorruptedBackup_Parms), &Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::NewProp_VersionInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "RepairCorruptedBackup", Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::PCGVersionManager_eventRepairCorruptedBackup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::PCGVersionManager_eventRepairCorruptedBackup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execRepairCorruptedBackup)
{
	P_GET_STRUCT_REF(FPCGVersionInfo,Z_Param_Out_VersionInfo);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RepairCorruptedBackup(Z_Param_Out_VersionInfo);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function RepairCorruptedBackup **************************

// ********** Begin Class UPCGVersionManager Function RestoreMOBAComponent *************************
struct Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics
{
	struct PCGVersionManager_eventRestoreMOBAComponent_Parms
	{
		FString ComponentName;
		FPCGVersionInfo VersionInfo;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ComponentName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionInfo;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::NewProp_ComponentName = { "ComponentName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventRestoreMOBAComponent_Parms, ComponentName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentName_MetaData), NewProp_ComponentName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::NewProp_VersionInfo = { "VersionInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventRestoreMOBAComponent_Parms, VersionInfo), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionInfo_MetaData), NewProp_VersionInfo_MetaData) }; // 1371083148
void Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGVersionManager_eventRestoreMOBAComponent_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGVersionManager_eventRestoreMOBAComponent_Parms), &Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::NewProp_ComponentName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::NewProp_VersionInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "RestoreMOBAComponent", Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::PCGVersionManager_eventRestoreMOBAComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::PCGVersionManager_eventRestoreMOBAComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execRestoreMOBAComponent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ComponentName);
	P_GET_STRUCT_REF(FPCGVersionInfo,Z_Param_Out_VersionInfo);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RestoreMOBAComponent(Z_Param_ComponentName,Z_Param_Out_VersionInfo);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function RestoreMOBAComponent ***************************

// ********** Begin Class UPCGVersionManager Function RestoreVersion *******************************
struct Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics
{
	struct PCGVersionManager_eventRestoreVersion_Parms
	{
		FPCGVersionInfo VersionInfo;
		EPCGRestoreMode RestoreMode;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "CPP_Default_RestoreMode", "Full" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionInfo;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RestoreMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RestoreMode;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::NewProp_VersionInfo = { "VersionInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventRestoreVersion_Parms, VersionInfo), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionInfo_MetaData), NewProp_VersionInfo_MetaData) }; // 1371083148
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::NewProp_RestoreMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::NewProp_RestoreMode = { "RestoreMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventRestoreVersion_Parms, RestoreMode), Z_Construct_UEnum_Aura_EPCGRestoreMode, METADATA_PARAMS(0, nullptr) }; // 2369302277
void Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGVersionManager_eventRestoreVersion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGVersionManager_eventRestoreVersion_Parms), &Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::NewProp_VersionInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::NewProp_RestoreMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::NewProp_RestoreMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "RestoreVersion", Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::PCGVersionManager_eventRestoreVersion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::PCGVersionManager_eventRestoreVersion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_RestoreVersion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_RestoreVersion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execRestoreVersion)
{
	P_GET_STRUCT_REF(FPCGVersionInfo,Z_Param_Out_VersionInfo);
	P_GET_ENUM(EPCGRestoreMode,Z_Param_RestoreMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RestoreVersion(Z_Param_Out_VersionInfo,EPCGRestoreMode(Z_Param_RestoreMode));
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function RestoreVersion *********************************

// ********** Begin Class UPCGVersionManager Function SetConfiguration *****************************
struct Z_Construct_UFunction_UPCGVersionManager_SetConfiguration_Statics
{
	struct PCGVersionManager_eventSetConfiguration_Parms
	{
		FPCGVersionConfig InConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_SetConfiguration_Statics::NewProp_InConfig = { "InConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventSetConfiguration_Parms, InConfig), Z_Construct_UScriptStruct_FPCGVersionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InConfig_MetaData), NewProp_InConfig_MetaData) }; // 2620595296
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_SetConfiguration_Statics::NewProp_InConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UPCGVersionManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_SetConfiguration_Statics::PCGVersionManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_SetConfiguration_Statics::PCGVersionManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FPCGVersionConfig,Z_Param_Out_InConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_InConfig);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function SetConfiguration *******************************

// ********** Begin Class UPCGVersionManager Function Shutdown *************************************
struct Z_Construct_UFunction_UPCGVersionManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGVersionManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function Shutdown ***************************************

// ********** Begin Class UPCGVersionManager Function StartAutoBackup ******************************
struct Z_Construct_UFunction_UPCGVersionManager_StartAutoBackup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Backup e Restore ===\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Backup e Restore ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_StartAutoBackup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "StartAutoBackup", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_StartAutoBackup_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_StartAutoBackup_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGVersionManager_StartAutoBackup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_StartAutoBackup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execStartAutoBackup)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartAutoBackup();
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function StartAutoBackup ********************************

// ********** Begin Class UPCGVersionManager Function StopAutoBackup *******************************
struct Z_Construct_UFunction_UPCGVersionManager_StopAutoBackup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_StopAutoBackup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "StopAutoBackup", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_StopAutoBackup_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_StopAutoBackup_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGVersionManager_StopAutoBackup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_StopAutoBackup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execStopAutoBackup)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopAutoBackup();
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function StopAutoBackup *********************************

// ********** Begin Class UPCGVersionManager Function UnregisterMOBAComponent **********************
struct Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent_Statics
{
	struct PCGVersionManager_eventUnregisterMOBAComponent_Parms
	{
		FString ComponentName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ComponentName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent_Statics::NewProp_ComponentName = { "ComponentName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventUnregisterMOBAComponent_Parms, ComponentName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentName_MetaData), NewProp_ComponentName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent_Statics::NewProp_ComponentName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "UnregisterMOBAComponent", Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent_Statics::PCGVersionManager_eventUnregisterMOBAComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent_Statics::PCGVersionManager_eventUnregisterMOBAComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execUnregisterMOBAComponent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ComponentName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterMOBAComponent(Z_Param_ComponentName);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function UnregisterMOBAComponent ************************

// ********** Begin Class UPCGVersionManager Function ValidateBackup *******************************
struct Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics
{
	struct PCGVersionManager_eventValidateBackup_Parms
	{
		FPCGVersionInfo VersionInfo;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Version Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Valida\xc3\xa7\xc3\xa3o e Integridade ===\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Valida\xc3\xa7\xc3\xa3o e Integridade ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionInfo;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::NewProp_VersionInfo = { "VersionInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGVersionManager_eventValidateBackup_Parms, VersionInfo), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionInfo_MetaData), NewProp_VersionInfo_MetaData) }; // 1371083148
void Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGVersionManager_eventValidateBackup_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGVersionManager_eventValidateBackup_Parms), &Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::NewProp_VersionInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGVersionManager, nullptr, "ValidateBackup", Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::PCGVersionManager_eventValidateBackup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::PCGVersionManager_eventValidateBackup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGVersionManager_ValidateBackup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGVersionManager_ValidateBackup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGVersionManager::execValidateBackup)
{
	P_GET_STRUCT_REF(FPCGVersionInfo,Z_Param_Out_VersionInfo);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateBackup(Z_Param_Out_VersionInfo);
	P_NATIVE_END;
}
// ********** End Class UPCGVersionManager Function ValidateBackup *********************************

// ********** Begin Class UPCGVersionManager *******************************************************
void UPCGVersionManager::StaticRegisterNativesUPCGVersionManager()
{
	UClass* Class = UPCGVersionManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "BackupMOBAComponent", &UPCGVersionManager::execBackupMOBAComponent },
		{ "CleanupOldBackups", &UPCGVersionManager::execCleanupOldBackups },
		{ "CompareVersions", &UPCGVersionManager::execCompareVersions },
		{ "CreateBackup", &UPCGVersionManager::execCreateBackup },
		{ "CreateVersion", &UPCGVersionManager::execCreateVersion },
		{ "DeleteVersion", &UPCGVersionManager::execDeleteVersion },
		{ "ExportVersion", &UPCGVersionManager::execExportVersion },
		{ "FindVersionsByAuthor", &UPCGVersionManager::execFindVersionsByAuthor },
		{ "FindVersionsByDateRange", &UPCGVersionManager::execFindVersionsByDateRange },
		{ "ForceBackup", &UPCGVersionManager::execForceBackup },
		{ "GenerateVersionDiff", &UPCGVersionManager::execGenerateVersionDiff },
		{ "GetAllVersions", &UPCGVersionManager::execGetAllVersions },
		{ "GetConfiguration", &UPCGVersionManager::execGetConfiguration },
		{ "GetCurrentVersion", &UPCGVersionManager::execGetCurrentVersion },
		{ "GetLatestVersion", &UPCGVersionManager::execGetLatestVersion },
		{ "ImportVersion", &UPCGVersionManager::execImportVersion },
		{ "Initialize", &UPCGVersionManager::execInitialize },
		{ "IsAutoBackupRunning", &UPCGVersionManager::execIsAutoBackupRunning },
		{ "RegisterMOBAComponent", &UPCGVersionManager::execRegisterMOBAComponent },
		{ "RepairCorruptedBackup", &UPCGVersionManager::execRepairCorruptedBackup },
		{ "RestoreMOBAComponent", &UPCGVersionManager::execRestoreMOBAComponent },
		{ "RestoreVersion", &UPCGVersionManager::execRestoreVersion },
		{ "SetConfiguration", &UPCGVersionManager::execSetConfiguration },
		{ "Shutdown", &UPCGVersionManager::execShutdown },
		{ "StartAutoBackup", &UPCGVersionManager::execStartAutoBackup },
		{ "StopAutoBackup", &UPCGVersionManager::execStopAutoBackup },
		{ "UnregisterMOBAComponent", &UPCGVersionManager::execUnregisterMOBAComponent },
		{ "ValidateBackup", &UPCGVersionManager::execValidateBackup },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPCGVersionManager;
UClass* UPCGVersionManager::GetPrivateStaticClass()
{
	using TClass = UPCGVersionManager;
	if (!Z_Registration_Info_UClass_UPCGVersionManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PCGVersionManager"),
			Z_Registration_Info_UClass_UPCGVersionManager.InnerSingleton,
			StaticRegisterNativesUPCGVersionManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPCGVersionManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UPCGVersionManager_NoRegister()
{
	return UPCGVersionManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPCGVersionManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal para gerenciamento de vers\xc3\xb5""es e backup do mapa MOBA\n */" },
#endif
		{ "IncludePath", "UPCGVersionManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal para gerenciamento de vers\xc3\xb5""es e backup do mapa MOBA" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBackupCompleted_MetaData[] = {
		{ "Category", "PCG Version Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Delegates ===\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Delegates ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRestoreCompleted_MetaData[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnVersionCreated_MetaData[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBackupProgress_MetaData[] = {
		{ "Category", "PCG Version Manager" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "Category", "Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Configura\xc3\xa7\xc3\xa3o ===\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Configura\xc3\xa7\xc3\xa3o ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentVersion_MetaData[] = {
		{ "Category", "Version" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VersionHistory_MetaData[] = {
		{ "Category", "Version" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackupDatabase_MetaData[] = {
		{ "Category", "Backup" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Estado do Sistema ===\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Estado do Sistema ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoBackupActive_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastBackupTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Componentes Registrados ===\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGVersionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Componentes Registrados ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBackupCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRestoreCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnVersionCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBackupProgress;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentVersion;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VersionHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VersionHistory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BackupDatabase_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BackupDatabase_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_BackupDatabase;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static void NewProp_bAutoBackupActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoBackupActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastBackupTime;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_RegisteredComponents_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegisteredComponents_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RegisteredComponents;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPCGVersionManager_BackupMOBAComponent, "BackupMOBAComponent" }, // 2266864057
		{ &Z_Construct_UFunction_UPCGVersionManager_CleanupOldBackups, "CleanupOldBackups" }, // 2113483368
		{ &Z_Construct_UFunction_UPCGVersionManager_CompareVersions, "CompareVersions" }, // 1691436989
		{ &Z_Construct_UFunction_UPCGVersionManager_CreateBackup, "CreateBackup" }, // 3400340021
		{ &Z_Construct_UFunction_UPCGVersionManager_CreateVersion, "CreateVersion" }, // 3113263812
		{ &Z_Construct_UFunction_UPCGVersionManager_DeleteVersion, "DeleteVersion" }, // 1065277622
		{ &Z_Construct_UFunction_UPCGVersionManager_ExportVersion, "ExportVersion" }, // 2801028599
		{ &Z_Construct_UFunction_UPCGVersionManager_FindVersionsByAuthor, "FindVersionsByAuthor" }, // 843636637
		{ &Z_Construct_UFunction_UPCGVersionManager_FindVersionsByDateRange, "FindVersionsByDateRange" }, // 2853282125
		{ &Z_Construct_UFunction_UPCGVersionManager_ForceBackup, "ForceBackup" }, // 758524318
		{ &Z_Construct_UFunction_UPCGVersionManager_GenerateVersionDiff, "GenerateVersionDiff" }, // 369021058
		{ &Z_Construct_UFunction_UPCGVersionManager_GetAllVersions, "GetAllVersions" }, // 3741887955
		{ &Z_Construct_UFunction_UPCGVersionManager_GetConfiguration, "GetConfiguration" }, // 3642893640
		{ &Z_Construct_UFunction_UPCGVersionManager_GetCurrentVersion, "GetCurrentVersion" }, // 3167602086
		{ &Z_Construct_UFunction_UPCGVersionManager_GetLatestVersion, "GetLatestVersion" }, // 4166069709
		{ &Z_Construct_UFunction_UPCGVersionManager_ImportVersion, "ImportVersion" }, // 552902178
		{ &Z_Construct_UFunction_UPCGVersionManager_Initialize, "Initialize" }, // 869587599
		{ &Z_Construct_UFunction_UPCGVersionManager_IsAutoBackupRunning, "IsAutoBackupRunning" }, // 1466762194
		{ &Z_Construct_UFunction_UPCGVersionManager_RegisterMOBAComponent, "RegisterMOBAComponent" }, // 117764427
		{ &Z_Construct_UFunction_UPCGVersionManager_RepairCorruptedBackup, "RepairCorruptedBackup" }, // 1766334715
		{ &Z_Construct_UFunction_UPCGVersionManager_RestoreMOBAComponent, "RestoreMOBAComponent" }, // 2310716858
		{ &Z_Construct_UFunction_UPCGVersionManager_RestoreVersion, "RestoreVersion" }, // 3361874128
		{ &Z_Construct_UFunction_UPCGVersionManager_SetConfiguration, "SetConfiguration" }, // 890798875
		{ &Z_Construct_UFunction_UPCGVersionManager_Shutdown, "Shutdown" }, // 2488515365
		{ &Z_Construct_UFunction_UPCGVersionManager_StartAutoBackup, "StartAutoBackup" }, // 3803885604
		{ &Z_Construct_UFunction_UPCGVersionManager_StopAutoBackup, "StopAutoBackup" }, // 3252132835
		{ &Z_Construct_UFunction_UPCGVersionManager_UnregisterMOBAComponent, "UnregisterMOBAComponent" }, // 2273612117
		{ &Z_Construct_UFunction_UPCGVersionManager_ValidateBackup, "ValidateBackup" }, // 2695076696
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPCGVersionManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_OnBackupCompleted = { "OnBackupCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGVersionManager, OnBackupCompleted), Z_Construct_UDelegateFunction_Aura_OnBackupCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBackupCompleted_MetaData), NewProp_OnBackupCompleted_MetaData) }; // 2009952764
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_OnRestoreCompleted = { "OnRestoreCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGVersionManager, OnRestoreCompleted), Z_Construct_UDelegateFunction_Aura_OnRestoreCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRestoreCompleted_MetaData), NewProp_OnRestoreCompleted_MetaData) }; // 2019108197
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_OnVersionCreated = { "OnVersionCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGVersionManager, OnVersionCreated), Z_Construct_UDelegateFunction_Aura_OnVersionCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnVersionCreated_MetaData), NewProp_OnVersionCreated_MetaData) }; // 2454941950
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_OnBackupProgress = { "OnBackupProgress", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGVersionManager, OnBackupProgress), Z_Construct_UDelegateFunction_Aura_OnBackupProgress__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBackupProgress_MetaData), NewProp_OnBackupProgress_MetaData) }; // 3476601283
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGVersionManager, Config), Z_Construct_UScriptStruct_FPCGVersionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 2620595296
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_CurrentVersion = { "CurrentVersion", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGVersionManager, CurrentVersion), Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentVersion_MetaData), NewProp_CurrentVersion_MetaData) }; // 1371083148
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_VersionHistory_Inner = { "VersionHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGVersionInfo, METADATA_PARAMS(0, nullptr) }; // 1371083148
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_VersionHistory = { "VersionHistory", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGVersionManager, VersionHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VersionHistory_MetaData), NewProp_VersionHistory_MetaData) }; // 1371083148
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_BackupDatabase_ValueProp = { "BackupDatabase", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPCGBackupData, METADATA_PARAMS(0, nullptr) }; // 1563172318
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_BackupDatabase_Key_KeyProp = { "BackupDatabase_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_BackupDatabase = { "BackupDatabase", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGVersionManager, BackupDatabase), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackupDatabase_MetaData), NewProp_BackupDatabase_MetaData) }; // 1563172318
void Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UPCGVersionManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPCGVersionManager), &Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
void Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_bAutoBackupActive_SetBit(void* Obj)
{
	((UPCGVersionManager*)Obj)->bAutoBackupActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_bAutoBackupActive = { "bAutoBackupActive", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPCGVersionManager), &Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_bAutoBackupActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoBackupActive_MetaData), NewProp_bAutoBackupActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_LastBackupTime = { "LastBackupTime", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGVersionManager, LastBackupTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastBackupTime_MetaData), NewProp_LastBackupTime_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_RegisteredComponents_ValueProp = { "RegisteredComponents", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_RegisteredComponents_Key_KeyProp = { "RegisteredComponents_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_RegisteredComponents = { "RegisteredComponents", nullptr, (EPropertyFlags)0x0024080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGVersionManager, RegisteredComponents), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredComponents_MetaData), NewProp_RegisteredComponents_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPCGVersionManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_OnBackupCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_OnRestoreCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_OnVersionCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_OnBackupProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_CurrentVersion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_VersionHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_VersionHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_BackupDatabase_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_BackupDatabase_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_BackupDatabase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_bAutoBackupActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_LastBackupTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_RegisteredComponents_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_RegisteredComponents_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGVersionManager_Statics::NewProp_RegisteredComponents,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPCGVersionManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPCGVersionManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPCGVersionManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPCGVersionManager_Statics::ClassParams = {
	&UPCGVersionManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UPCGVersionManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UPCGVersionManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPCGVersionManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UPCGVersionManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPCGVersionManager()
{
	if (!Z_Registration_Info_UClass_UPCGVersionManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPCGVersionManager.OuterSingleton, Z_Construct_UClass_UPCGVersionManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPCGVersionManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPCGVersionManager);
UPCGVersionManager::~UPCGVersionManager() {}
// ********** End Class UPCGVersionManager *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGVersionManager_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPCGVersionType_StaticEnum, TEXT("EPCGVersionType"), &Z_Registration_Info_UEnum_EPCGVersionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3150078037U) },
		{ EPCGBackupStatus_StaticEnum, TEXT("EPCGBackupStatus"), &Z_Registration_Info_UEnum_EPCGBackupStatus, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1646359674U) },
		{ EPCGRestoreMode_StaticEnum, TEXT("EPCGRestoreMode"), &Z_Registration_Info_UEnum_EPCGRestoreMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2369302277U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPCGVersionInfo::StaticStruct, Z_Construct_UScriptStruct_FPCGVersionInfo_Statics::NewStructOps, TEXT("PCGVersionInfo"), &Z_Registration_Info_UScriptStruct_FPCGVersionInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGVersionInfo), 1371083148U) },
		{ FPCGBackupData::StaticStruct, Z_Construct_UScriptStruct_FPCGBackupData_Statics::NewStructOps, TEXT("PCGBackupData"), &Z_Registration_Info_UScriptStruct_FPCGBackupData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGBackupData), 1563172318U) },
		{ FPCGVersionConfig::StaticStruct, Z_Construct_UScriptStruct_FPCGVersionConfig_Statics::NewStructOps, TEXT("PCGVersionConfig"), &Z_Registration_Info_UScriptStruct_FPCGVersionConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGVersionConfig), 2620595296U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPCGVersionManager, UPCGVersionManager::StaticClass, TEXT("UPCGVersionManager"), &Z_Registration_Info_UClass_UPCGVersionManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPCGVersionManager), 614941394U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGVersionManager_h__Script_Aura_1732509342(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGVersionManager_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGVersionManager_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGVersionManager_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGVersionManager_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGVersionManager_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGVersionManager_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
