// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "ARiverPrismalManager.h"

#ifdef AURA_ARiverPrismalManager_generated_h
#error "ARiverPrismalManager.generated.h already included, missing '#pragma once' in ARiverPrismalManager.h"
#endif
#define AURA_ARiverPrismalManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class UPrimitiveComponent;
struct FHexagonalIsland;
struct FHitResult;
struct FRiverCollisionData;
struct FWaterProperties;

// ********** Begin ScriptStruct FRiverSegment *****************************************************
#define FID_Aura_Source_Aura_Public_ARiverPrismalManager_h_71_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRiverSegment_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FRiverSegment;
// ********** End ScriptStruct FRiverSegment *******************************************************

// ********** Begin ScriptStruct FHexagonalIsland **************************************************
#define FID_Aura_Source_Aura_Public_ARiverPrismalManager_h_120_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHexagonalIsland_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FHexagonalIsland;
// ********** End ScriptStruct FHexagonalIsland ****************************************************

// ********** Begin ScriptStruct FWaterProperties **************************************************
#define FID_Aura_Source_Aura_Public_ARiverPrismalManager_h_168_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWaterProperties_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FWaterProperties;
// ********** End ScriptStruct FWaterProperties ****************************************************

// ********** Begin ScriptStruct FRiverCollisionData ***********************************************
#define FID_Aura_Source_Aura_Public_ARiverPrismalManager_h_230_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRiverCollisionData_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FRiverCollisionData;
// ********** End ScriptStruct FRiverCollisionData *************************************************

// ********** Begin ScriptStruct FBridgeData *******************************************************
#define FID_Aura_Source_Aura_Public_ARiverPrismalManager_h_268_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBridgeData_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FBridgeData;
// ********** End ScriptStruct FBridgeData *********************************************************

// ********** Begin Class ARiverPrismalManager *****************************************************
#define FID_Aura_Source_Aura_Public_ARiverPrismalManager_h_309_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetIslandRadius); \
	DECLARE_FUNCTION(execSetSinusoidalParameters); \
	DECLARE_FUNCTION(execSetRiverDimensions); \
	DECLARE_FUNCTION(execSetWaterProperties); \
	DECLARE_FUNCTION(execGetIslandCenter); \
	DECLARE_FUNCTION(execGetIslandArea); \
	DECLARE_FUNCTION(execGetRiverCenter); \
	DECLARE_FUNCTION(execGetRiverVolume); \
	DECLARE_FUNCTION(execGetTotalRiverLength); \
	DECLARE_FUNCTION(execCalculateWaterNormal); \
	DECLARE_FUNCTION(execInterpolateWaterDepth); \
	DECLARE_FUNCTION(execProjectPointOntoRiver); \
	DECLARE_FUNCTION(execCalculateDistanceToRiver); \
	DECLARE_FUNCTION(execRotateVectorAroundAxis); \
	DECLARE_FUNCTION(execOnActorExitWater); \
	DECLARE_FUNCTION(execOnActorEnterWater); \
	DECLARE_FUNCTION(execOnWaterHit); \
	DECLARE_FUNCTION(execDrawDebugBridges); \
	DECLARE_FUNCTION(execDrawDebugWaterFlow); \
	DECLARE_FUNCTION(execDrawDebugIsland); \
	DECLARE_FUNCTION(execDrawDebugRiver); \
	DECLARE_FUNCTION(execValidateWaterPhysics); \
	DECLARE_FUNCTION(execValidateIslandGeometry); \
	DECLARE_FUNCTION(execValidateRiverGeometry); \
	DECLARE_FUNCTION(execGetSwimmingLanes); \
	DECLARE_FUNCTION(execGetSafeWaterPosition); \
	DECLARE_FUNCTION(execIsWaterPathClear); \
	DECLARE_FUNCTION(execFindWaterPath); \
	DECLARE_FUNCTION(execGetNearestBridgePosition); \
	DECLARE_FUNCTION(execCanUnitCrossBridge); \
	DECLARE_FUNCTION(execRemoveBridge); \
	DECLARE_FUNCTION(execAddBridge); \
	DECLARE_FUNCTION(execCreateBridges); \
	DECLARE_FUNCTION(execApplyWaterCurrent); \
	DECLARE_FUNCTION(execGetWaterSurfacePosition); \
	DECLARE_FUNCTION(execIsPositionOnIsland); \
	DECLARE_FUNCTION(execIsPositionInWater); \
	DECLARE_FUNCTION(execCheckWaterCollision); \
	DECLARE_FUNCTION(execGetClosestPointOnIsland); \
	DECLARE_FUNCTION(execIsPointInsideHexagon); \
	DECLARE_FUNCTION(execCalculateHexagonEdgeLength); \
	DECLARE_FUNCTION(execCalculateHexagonApothem); \
	DECLARE_FUNCTION(execCalculateHexagonEdgeMidpoints); \
	DECLARE_FUNCTION(execCalculateHexagonVertices); \
	DECLARE_FUNCTION(execGenerateHexagonalIsland); \
	DECLARE_FUNCTION(execCalculateWaterDepth); \
	DECLARE_FUNCTION(execCalculateFlowSpeed); \
	DECLARE_FUNCTION(execCalculateFlowDirection); \
	DECLARE_FUNCTION(execGenerateRiverBankPoints); \
	DECLARE_FUNCTION(execCalculateSinusoidalPoint); \
	DECLARE_FUNCTION(execGenerateSinusoidalPath); \
	DECLARE_FUNCTION(execSetupVelocitySystem); \
	DECLARE_FUNCTION(execConfigureBridges); \
	DECLARE_FUNCTION(execCreateHexagonalIsland); \
	DECLARE_FUNCTION(execGenerateSinusoidalGeometry); \
	DECLARE_FUNCTION(execSetupWaterPhysics); \
	DECLARE_FUNCTION(execGenerateRiverGeometry); \
	DECLARE_FUNCTION(execInitializeRiverSystem);


AURA_API UClass* Z_Construct_UClass_ARiverPrismalManager_NoRegister();

#define FID_Aura_Source_Aura_Public_ARiverPrismalManager_h_309_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesARiverPrismalManager(); \
	friend struct Z_Construct_UClass_ARiverPrismalManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_ARiverPrismalManager_NoRegister(); \
public: \
	DECLARE_CLASS2(ARiverPrismalManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_ARiverPrismalManager_NoRegister) \
	DECLARE_SERIALIZER(ARiverPrismalManager)


#define FID_Aura_Source_Aura_Public_ARiverPrismalManager_h_309_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ARiverPrismalManager(ARiverPrismalManager&&) = delete; \
	ARiverPrismalManager(const ARiverPrismalManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ARiverPrismalManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ARiverPrismalManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ARiverPrismalManager) \
	NO_API virtual ~ARiverPrismalManager();


#define FID_Aura_Source_Aura_Public_ARiverPrismalManager_h_306_PROLOG
#define FID_Aura_Source_Aura_Public_ARiverPrismalManager_h_309_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_ARiverPrismalManager_h_309_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_ARiverPrismalManager_h_309_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_ARiverPrismalManager_h_309_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ARiverPrismalManager;

// ********** End Class ARiverPrismalManager *******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_ARiverPrismalManager_h

// ********** Begin Enum ERiverFlowDirection *******************************************************
#define FOREACH_ENUM_ERIVERFLOWDIRECTION(op) \
	op(ERiverFlowDirection::Eastward) \
	op(ERiverFlowDirection::Westward) \
	op(ERiverFlowDirection::Northward) \
	op(ERiverFlowDirection::Southward) \
	op(ERiverFlowDirection::Bidirectional) 

enum class ERiverFlowDirection : uint8;
template<> struct TIsUEnumClass<ERiverFlowDirection> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<ERiverFlowDirection>();
// ********** End Enum ERiverFlowDirection *********************************************************

// ********** Begin Enum ERiverSegmentType *********************************************************
#define FOREACH_ENUM_ERIVERSEGMENTTYPE(op) \
	op(ERiverSegmentType::Straight) \
	op(ERiverSegmentType::Curved) \
	op(ERiverSegmentType::Bend) \
	op(ERiverSegmentType::Island) \
	op(ERiverSegmentType::Bridge) 

enum class ERiverSegmentType : uint8;
template<> struct TIsUEnumClass<ERiverSegmentType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<ERiverSegmentType>();
// ********** End Enum ERiverSegmentType ***********************************************************

// ********** Begin Enum EWaterQuality *************************************************************
#define FOREACH_ENUM_EWATERQUALITY(op) \
	op(EWaterQuality::Pure) \
	op(EWaterQuality::Clear) \
	op(EWaterQuality::Murky) \
	op(EWaterQuality::Polluted) \
	op(EWaterQuality::Magical) 

enum class EWaterQuality : uint8;
template<> struct TIsUEnumClass<EWaterQuality> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EWaterQuality>();
// ********** End Enum EWaterQuality ***************************************************************

// ********** Begin Enum EIslandType ***************************************************************
#define FOREACH_ENUM_EISLANDTYPE(op) \
	op(EIslandType::Hexagonal) \
	op(EIslandType::Circular) \
	op(EIslandType::Irregular) \
	op(EIslandType::Rectangular) 

enum class EIslandType : uint8;
template<> struct TIsUEnumClass<EIslandType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EIslandType>();
// ********** End Enum EIslandType *****************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
