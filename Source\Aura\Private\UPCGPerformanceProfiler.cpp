#include "UPCGPerformanceProfiler.h"
#include "APCGWorldPartitionManager.h"
#include "APCGNaniteOptimizer.h"
#include "APCGLumenIntegrator.h"
#include "APCGStreamingManager.h"
#include "Engine/Canvas.h"
#include "HAL/PlatformFilemanager.h"
#include "HAL/PlatformProcess.h"
#include "HAL/PlatformMemory.h"
#include "Stats/StatsData.h"
#include "Stats/StatsMisc.h"
#include "RenderCore.h"
#include "RHIStats.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "Misc/Paths.h"
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "ProfilingDebugging/MemoryTrace.h"
#include "Logging/LogMacros.h"
#include "Math/UnrealMathUtility.h"
#include "Templates/SharedPointer.h"
#include "Containers/UnrealString.h"
#include "GenericPlatform/GenericPlatformMemory.h"
#include "RHI.h"
#include "GlobalShader.h"
#include "ShaderCore.h"

DEFINE_LOG_CATEGORY_STATIC(LogPCGProfiler, Log, All);

// Constructor
UPCGPerformanceProfiler::UPCGPerformanceProfiler()
    : PerformanceHistory(1000) // Initialize with default capacity
{
    // Initialize configuration
    ProfilerConfig = FPCGProfilerConfig();
    bShowOverlay = true;
    bShowGraphs = true;
    bAutoOptimization = false;
    
    // Initialize state
    bIsActive = false;
    bIsPaused = false;
    bIsInitialized = false;
    LastUpdateTime = 0.0f;
    AccumulatedTime = 0.0f;
    SampleCount = 0;
    
    // Initialize performance data
    CurrentPerformance = FPCGSystemPerformance();
    AveragePerformance = FPCGSystemPerformance();
    PeakPerformance = FPCGSystemPerformance();
    
    // Initialize profiling states
    bMemoryProfilingActive = false;
    bGPUProfilingActive = false;
    bNetworkStreamingActive = false;
    
    // Initialize overlay settings
    OverlayPosition = FVector2D(50.0f, 50.0f);
    OverlayScale = 1.0f;
    bOverlayVisible = true;
    
    // TCircularBuffer no UE 5.6 é inicializado apenas no construtor
    
    // Initialize category states
    for (int32 i = 0; i < (int32)EPCGProfilerCategory::Custom + 1; ++i)
    {
        CategoryStates.Add((EPCGProfilerCategory)i, false);
    }
    
    // Enable default categories
    for (const EPCGProfilerCategory& Category : ProfilerConfig.EnabledCategories)
    {
        CategoryStates[Category] = true;
    }
    
    UE_LOG(LogPCGProfiler, Log, TEXT("PCG Performance Profiler initialized"));
}

// Initialization and Cleanup
void UPCGPerformanceProfiler::InitializeProfiler()
{
    if (bIsInitialized)
    {
        UE_LOG(LogPCGProfiler, Warning, TEXT("Profiler already initialized"));
        return;
    }
    
    // Recriar buffer com nova capacidade se necessário
    if (ProfilerConfig.MaxSampleHistory != PerformanceHistory.Capacity())
    {
        PerformanceHistory = TCircularBuffer<FPCGSystemPerformance>(ProfilerConfig.MaxSampleHistory);
    }
    
    // Register default metrics
    RegisterCustomMetric(TEXT("CPU_Usage"), EPCGProfilerCategory::CPU, TEXT("%"));
    RegisterCustomMetric(TEXT("Memory_Usage"), EPCGProfilerCategory::Memory, TEXT("MB"));
    RegisterCustomMetric(TEXT("GPU_Usage"), EPCGProfilerCategory::GPU, TEXT("%"));
    RegisterCustomMetric(TEXT("Frame_Time"), EPCGProfilerCategory::Rendering, TEXT("ms"));
    RegisterCustomMetric(TEXT("FPS"), EPCGProfilerCategory::Rendering, TEXT("fps"));
    RegisterCustomMetric(TEXT("PCG_Generation_Time"), EPCGProfilerCategory::PCG, TEXT("ms"));
    RegisterCustomMetric(TEXT("Streaming_Efficiency"), EPCGProfilerCategory::Streaming, TEXT("%"));
    
    // Set default thresholds
    SetMetricThreshold(TEXT("CPU_Usage"), ProfilerConfig.CPUWarningThreshold, 95.0f);
    SetMetricThreshold(TEXT("Memory_Usage"), ProfilerConfig.MemoryWarningThresholdMB, ProfilerConfig.MemoryWarningThresholdMB * 1.2f);
    SetMetricThreshold(TEXT("GPU_Usage"), ProfilerConfig.GPUWarningThreshold, 98.0f);
    SetMetricThreshold(TEXT("Frame_Time"), 16.67f, 33.33f); // 60fps warning, 30fps error
    
    // Initialize file logging if enabled
    if (ProfilerConfig.bEnableFileLogging)
    {
        FString LogDir = FPaths::ProjectLogDir();
        FString FullLogPath = FPaths::Combine(LogDir, ProfilerConfig.LogFilePath);
        
        // Create directory if it doesn't exist
        FString LogDirectory = FPaths::GetPath(FullLogPath);
        IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
        if (!PlatformFile.DirectoryExists(*LogDirectory))
        {
            PlatformFile.CreateDirectoryTree(*LogDirectory);
        }
        
        // Write header to log file
        FString LogHeader = FString::Printf(TEXT("PCG Performance Profiler Log - Started at %s\n"), *FDateTime::Now().ToString());
        FFileHelper::SaveStringToFile(LogHeader, *FullLogPath, FFileHelper::EEncodingOptions::AutoDetect, &IFileManager::Get(), FILEWRITE_Append);
    }
    
    bIsInitialized = true;
    UE_LOG(LogPCGProfiler, Log, TEXT("PCG Performance Profiler initialized successfully"));
}

void UPCGPerformanceProfiler::ShutdownProfiler()
{
    if (!bIsInitialized)
    {
        return;
    }
    
    StopProfiling();
    StopMemoryProfiling();
    StopGPUProfiling();
    StopNetworkProfiling();
    
    // Clear all data
    RegisteredMetrics.Empty();
    ActiveAlerts.Empty();
    AlertHistory.Empty();
    BenchmarkStartTimes.Empty();
    BenchmarkResults.Empty();
    MemorySnapshots.Empty();
    GPUFrameTimes.Empty();
    // TCircularBuffer no UE 5.6 não possui Reset(), recriamos o buffer
    PerformanceHistory = TCircularBuffer<FPCGSystemPerformance>(ProfilerConfig.MaxSampleHistory);
    
    // Clear integration references
    WorldPartitionManagerRef.Reset();
    NaniteOptimizerRef.Reset();
    LumenIntegratorRef.Reset();
    StreamingManagerRef.Reset();
    
    bIsInitialized = false;
    UE_LOG(LogPCGProfiler, Log, TEXT("PCG Performance Profiler shutdown complete"));
}

bool UPCGPerformanceProfiler::IsProfilerActive() const
{
    return bIsActive && bIsInitialized;
}

// Profiling Control
void UPCGPerformanceProfiler::StartProfiling()
{
    if (!bIsInitialized)
    {
        InitializeProfiler();
    }
    
    if (bIsActive)
    {
        UE_LOG(LogPCGProfiler, Warning, TEXT("Profiler already active"));
        return;
    }
    
    bIsActive = true;
    bIsPaused = false;
    LastUpdateTime = FPlatformTime::Seconds();
    AccumulatedTime = 0.0f;
    SampleCount = 0;
    
    // Start async profiling
    StartAsyncProfiling();
    
    UE_LOG(LogPCGProfiler, Log, TEXT("PCG Performance Profiler started"));
    LogToFile(TEXT("Profiling started"));
}

void UPCGPerformanceProfiler::StopProfiling()
{
    if (!bIsActive)
    {
        return;
    }
    
    bIsActive = false;
    bIsPaused = false;
    
    // Stop async profiling
    StopAsyncProfiling();
    
    // Generate final report
    FPCGProfilerReport FinalReport = GenerateReport();
    OnProfilerReportGenerated.Broadcast(FinalReport);
    
    UE_LOG(LogPCGProfiler, Log, TEXT("PCG Performance Profiler stopped"));
    LogToFile(TEXT("Profiling stopped"));
}

void UPCGPerformanceProfiler::PauseProfiling(bool bPause)
{
    if (!bIsActive)
    {
        return;
    }
    
    bIsPaused = bPause;
    UE_LOG(LogPCGProfiler, Log, TEXT("PCG Performance Profiler %s"), bPause ? TEXT("paused") : TEXT("resumed"));
}

void UPCGPerformanceProfiler::ResetProfiler()
{
    // Clear performance data
    CurrentPerformance = FPCGSystemPerformance();
    AveragePerformance = FPCGSystemPerformance();
    PeakPerformance = FPCGSystemPerformance();
    
    // Clear history and alerts
    // TCircularBuffer no UE 5.6 não possui Reset(), recriamos o buffer
    PerformanceHistory = TCircularBuffer<FPCGSystemPerformance>(ProfilerConfig.MaxSampleHistory);
    ActiveAlerts.Empty();
    AlertHistory.Empty();
    
    // Reset counters
    AccumulatedTime = 0.0f;
    SampleCount = 0;
    
    // Clear benchmark data
    BenchmarkStartTimes.Empty();
    BenchmarkResults.Empty();
    
    UE_LOG(LogPCGProfiler, Log, TEXT("PCG Performance Profiler reset"));
}

void UPCGPerformanceProfiler::SetSamplingRate(EPCGProfilerSamplingRate Rate)
{
    ProfilerConfig.SamplingRate = Rate;
    
    // Update sampling interval
    float NewInterval = GetSamplingInterval();
    ProfilerConfig.UpdateInterval = NewInterval;
    
    UE_LOG(LogPCGProfiler, Log, TEXT("Sampling rate changed to %s (%.3f seconds)"), 
           *UEnum::GetValueAsString(Rate), NewInterval);
}

void UPCGPerformanceProfiler::EnableCategory(EPCGProfilerCategory Category, bool bEnable)
{
    CategoryStates[Category] = bEnable;
    
    if (bEnable && !ProfilerConfig.EnabledCategories.Contains(Category))
    {
        ProfilerConfig.EnabledCategories.Add(Category);
    }
    else if (!bEnable)
    {
        ProfilerConfig.EnabledCategories.Remove(Category);
    }
    
    UE_LOG(LogPCGProfiler, Log, TEXT("Category %s %s"), 
           *UEnum::GetValueAsString(Category), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

// Metrics Management
void UPCGPerformanceProfiler::RegisterCustomMetric(const FString& MetricName, EPCGProfilerCategory Category, const FString& Unit)
{
    if (RegisteredMetrics.Contains(MetricName))
    {
        UE_LOG(LogPCGProfiler, Warning, TEXT("Metric '%s' already registered"), *MetricName);
        return;
    }
    
    FPCGPerformanceMetric NewMetric;
    NewMetric.MetricName = MetricName;
    NewMetric.Category = Category;
    NewMetric.Unit = Unit;
    NewMetric.LastUpdateTime = FDateTime::Now();
    
    RegisteredMetrics.Add(MetricName, NewMetric);
    
    UE_LOG(LogPCGProfiler, Log, TEXT("Registered custom metric: %s (%s)"), *MetricName, *Unit);
}

void UPCGPerformanceProfiler::UpdateCustomMetric(const FString& MetricName, float Value)
{
    if (!RegisteredMetrics.Contains(MetricName))
    {
        UE_LOG(LogPCGProfiler, Warning, TEXT("Metric '%s' not found"), *MetricName);
        return;
    }
    
    UpdateMetric(MetricName, Value);
}

void UPCGPerformanceProfiler::RemoveCustomMetric(const FString& MetricName)
{
    if (RegisteredMetrics.Remove(MetricName) > 0)
    {
        UE_LOG(LogPCGProfiler, Log, TEXT("Removed custom metric: %s"), *MetricName);
    }
}

FPCGPerformanceMetric UPCGPerformanceProfiler::GetMetric(const FString& MetricName) const
{
    if (const FPCGPerformanceMetric* Metric = RegisteredMetrics.Find(MetricName))
    {
        return *Metric;
    }
    
    return FPCGPerformanceMetric();
}

TArray<FPCGPerformanceMetric> UPCGPerformanceProfiler::GetAllMetrics() const
{
    TArray<FPCGPerformanceMetric> AllMetrics;
    for (const auto& MetricPair : RegisteredMetrics)
    {
        AllMetrics.Add(MetricPair.Value);
    }
    return AllMetrics;
}

TArray<FPCGPerformanceMetric> UPCGPerformanceProfiler::GetMetricsByCategory(EPCGProfilerCategory Category) const
{
    TArray<FPCGPerformanceMetric> CategoryMetrics;
    for (const auto& MetricPair : RegisteredMetrics)
    {
        if (MetricPair.Value.Category == Category)
        {
            CategoryMetrics.Add(MetricPair.Value);
        }
    }
    return CategoryMetrics;
}

// Performance Data
FPCGSystemPerformance UPCGPerformanceProfiler::GetCurrentPerformance() const
{
    return CurrentPerformance;
}

FPCGSystemPerformance UPCGPerformanceProfiler::GetAveragePerformance() const
{
    return AveragePerformance;
}

FPCGSystemPerformance UPCGPerformanceProfiler::GetPeakPerformance() const
{
    return PeakPerformance;
}

TArray<FPCGProfilerAlert> UPCGPerformanceProfiler::GetActiveAlerts() const
{
    return ActiveAlerts;
}

TArray<FPCGProfilerAlert> UPCGPerformanceProfiler::GetAlertHistory() const
{
    return AlertHistory;
}

// Reporting
FPCGProfilerReport UPCGPerformanceProfiler::GenerateReport() const
{
    FPCGProfilerReport Report;
    Report.GenerationTime = FDateTime::Now();
    Report.ProfileDuration = FTimespan::FromSeconds(AccumulatedTime);
    Report.AveragePerformance = AveragePerformance;
    Report.PeakPerformance = PeakPerformance;
    Report.DetailedMetrics = GetAllMetrics();
    Report.Alerts = AlertHistory;
    Report.PerformanceSummary = GetPerformanceSummary();
    Report.OptimizationRecommendations = GetOptimizationRecommendations();
    
    return Report;
}

void UPCGPerformanceProfiler::ExportReportToFile(const FString& FilePath) const
{
    FPCGProfilerReport Report = GenerateReport();
    
    FString ReportContent;
    ReportContent += FString::Printf(TEXT("PCG Performance Report\n"));
    ReportContent += FString::Printf(TEXT("Generated: %s\n"), *Report.GenerationTime.ToString());
    ReportContent += FString::Printf(TEXT("Duration: %s\n\n"), *Report.ProfileDuration.ToString());
    
    ReportContent += TEXT("Performance Summary:\n");
    ReportContent += Report.PerformanceSummary + TEXT("\n\n");
    
    ReportContent += TEXT("Optimization Recommendations:\n");
    for (const FString& Recommendation : Report.OptimizationRecommendations)
    {
        ReportContent += TEXT("- ") + Recommendation + TEXT("\n");
    }
    
    FFileHelper::SaveStringToFile(ReportContent, *FilePath);
    UE_LOG(LogPCGProfiler, Log, TEXT("Report exported to: %s"), *FilePath);
}

void UPCGPerformanceProfiler::ExportMetricsToCSV(const FString& FilePath) const
{
    FString CSVContent;
    CSVContent += TEXT("Metric Name,Category,Current Value,Min Value,Max Value,Average Value,Unit,Severity\n");
    
    for (const auto& MetricPair : RegisteredMetrics)
    {
        const FPCGPerformanceMetric& Metric = MetricPair.Value;
        CSVContent += FString::Printf(TEXT("%s,%s,%.3f,%.3f,%.3f,%.3f,%s,%s\n"),
            *Metric.MetricName,
            *UEnum::GetValueAsString(Metric.Category),
            Metric.CurrentValue,
            Metric.MinValue,
            Metric.MaxValue,
            Metric.AverageValue,
            *Metric.Unit,
            *UEnum::GetValueAsString(Metric.Severity)
        );
    }
    
    FFileHelper::SaveStringToFile(CSVContent, *FilePath);
    UE_LOG(LogPCGProfiler, Log, TEXT("Metrics exported to CSV: %s"), *FilePath);
}

FString UPCGPerformanceProfiler::GetPerformanceSummary() const
{
    FString Summary;
    Summary += FString::Printf(TEXT("CPU Usage: %.1f%% (Avg: %.1f%%)\n"), 
                              CurrentPerformance.CPUUsagePercent, AveragePerformance.CPUUsagePercent);
    Summary += FString::Printf(TEXT("Memory Usage: %d MB (Avg: %d MB)\n"), 
                              CurrentPerformance.UsedMemoryMB, AveragePerformance.UsedMemoryMB);
    Summary += FString::Printf(TEXT("GPU Usage: %.1f%% (Avg: %.1f%%)\n"), 
                              CurrentPerformance.GPUUsagePercent, AveragePerformance.GPUUsagePercent);
    Summary += FString::Printf(TEXT("Frame Rate: %.1f FPS (Avg: %.1f FPS)\n"), 
                              CurrentPerformance.FPS, AveragePerformance.FPS);
    Summary += FString::Printf(TEXT("Active PCG Components: %d\n"), CurrentPerformance.ActivePCGComponents);
    Summary += FString::Printf(TEXT("PCG Generation Time: %.2f ms\n"), CurrentPerformance.PCGGenerationTime);
    
    return Summary;
}

TArray<FString> UPCGPerformanceProfiler::GetOptimizationRecommendations() const
{
    TArray<FString> Recommendations;
    
    // CPU recommendations
    if (AveragePerformance.CPUUsagePercent > ProfilerConfig.CPUWarningThreshold)
    {
        Recommendations.Add(TEXT("Consider reducing PCG complexity or using LOD systems"));
        Recommendations.Add(TEXT("Enable multithreading for PCG generation"));
    }
    
    // Memory recommendations
    if (AveragePerformance.UsedMemoryMB > ProfilerConfig.MemoryWarningThresholdMB)
    {
        Recommendations.Add(TEXT("Implement streaming for large PCG datasets"));
        Recommendations.Add(TEXT("Use object pooling for frequently generated instances"));
    }
    
    // GPU recommendations
    if (AveragePerformance.GPUUsagePercent > ProfilerConfig.GPUWarningThreshold)
    {
        Recommendations.Add(TEXT("Enable Nanite for complex geometry"));
        Recommendations.Add(TEXT("Optimize material complexity"));
        Recommendations.Add(TEXT("Use GPU instancing for repeated objects"));
    }
    
    // Frame rate recommendations
    if (AveragePerformance.FPS < 30.0f)
    {
        Recommendations.Add(TEXT("Reduce draw calls through batching"));
        Recommendations.Add(TEXT("Implement aggressive LOD systems"));
        Recommendations.Add(TEXT("Consider temporal upsampling"));
    }
    
    return Recommendations;
}

// Thresholds and Alerts
void UPCGPerformanceProfiler::SetMetricThreshold(const FString& MetricName, float WarningThreshold, float ErrorThreshold)
{
    if (FPCGPerformanceMetric* Metric = RegisteredMetrics.Find(MetricName))
    {
        Metric->WarningThreshold = WarningThreshold;
        Metric->ErrorThreshold = ErrorThreshold;
        
        UE_LOG(LogPCGProfiler, Log, TEXT("Set thresholds for %s: Warning=%.2f, Error=%.2f"), 
               *MetricName, WarningThreshold, ErrorThreshold);
    }
}

void UPCGPerformanceProfiler::ClearAlert(const FString& MetricName)
{
    ActiveAlerts.RemoveAll([&MetricName](const FPCGProfilerAlert& Alert) {
        return Alert.AlertMessage.Contains(MetricName);
    });
}

void UPCGPerformanceProfiler::ClearAllAlerts()
{
    ActiveAlerts.Empty();
    UE_LOG(LogPCGProfiler, Log, TEXT("All alerts cleared"));
}

bool UPCGPerformanceProfiler::HasActiveAlerts() const
{
    return ActiveAlerts.Num() > 0;
}

// Integration with PCG Systems
void UPCGPerformanceProfiler::IntegrateWithWorldPartition(APCGWorldPartitionManager* WorldPartitionManager)
{
    WorldPartitionManagerRef = WorldPartitionManager;
    UE_LOG(LogPCGProfiler, Log, TEXT("Integrated with World Partition Manager"));
}

void UPCGPerformanceProfiler::IntegrateWithNanite(APCGNaniteOptimizer* NaniteOptimizer)
{
    NaniteOptimizerRef = NaniteOptimizer;
    UE_LOG(LogPCGProfiler, Log, TEXT("Integrated with Nanite Optimizer"));
}

void UPCGPerformanceProfiler::IntegrateWithLumen(APCGLumenIntegrator* LumenIntegrator)
{
    LumenIntegratorRef = LumenIntegrator;
    UE_LOG(LogPCGProfiler, Log, TEXT("Integrated with Lumen Integrator"));
}

void UPCGPerformanceProfiler::IntegrateWithStreaming(APCGStreamingManager* StreamingManager)
{
    StreamingManagerRef = StreamingManager;
    UE_LOG(LogPCGProfiler, Log, TEXT("Integrated with Streaming Manager"));
}

// Benchmarking
void UPCGPerformanceProfiler::StartBenchmark(const FString& BenchmarkName)
{
    BenchmarkStartTimes.Add(BenchmarkName, FDateTime::Now());
    UE_LOG(LogPCGProfiler, Log, TEXT("Started benchmark: %s"), *BenchmarkName);
}

void UPCGPerformanceProfiler::EndBenchmark(const FString& BenchmarkName)
{
    if (const FDateTime* StartTime = BenchmarkStartTimes.Find(BenchmarkName))
    {
        FTimespan Duration = FDateTime::Now() - *StartTime;
        float DurationMs = Duration.GetTotalMilliseconds();
        
        BenchmarkResults.Add(BenchmarkName, DurationMs);
        BenchmarkStartTimes.Remove(BenchmarkName);
        
        UE_LOG(LogPCGProfiler, Log, TEXT("Completed benchmark: %s (%.2f ms)"), *BenchmarkName, DurationMs);
    }
}

float UPCGPerformanceProfiler::GetBenchmarkResult(const FString& BenchmarkName) const
{
    if (const float* Result = BenchmarkResults.Find(BenchmarkName))
    {
        return *Result;
    }
    return 0.0f;
}

TMap<FString, float> UPCGPerformanceProfiler::GetAllBenchmarkResults() const
{
    return BenchmarkResults;
}

// Memory Profiling
void UPCGPerformanceProfiler::StartMemoryProfiling()
{
    bMemoryProfilingActive = true;
    UE_LOG(LogPCGProfiler, Log, TEXT("Memory profiling started"));
}

void UPCGPerformanceProfiler::StopMemoryProfiling()
{
    bMemoryProfilingActive = false;
    UE_LOG(LogPCGProfiler, Log, TEXT("Memory profiling stopped"));
}

void UPCGPerformanceProfiler::TakeMemorySnapshot(const FString& SnapshotName)
{
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    int32 UsedMemoryMB = MemStats.UsedPhysical / (1024 * 1024);
    
    MemorySnapshots.Add(SnapshotName, UsedMemoryMB);
    UE_LOG(LogPCGProfiler, Log, TEXT("Memory snapshot '%s': %d MB"), *SnapshotName, UsedMemoryMB);
}

int32 UPCGPerformanceProfiler::GetMemoryUsage(const FString& Category) const
{
    if (Category.IsEmpty())
    {
        FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
        return MemStats.UsedPhysical / (1024 * 1024);
    }
    
    if (const int32* Usage = MemorySnapshots.Find(Category))
    {
        return *Usage;
    }
    
    return 0;
}

// GPU Profiling
void UPCGPerformanceProfiler::StartGPUProfiling()
{
    bGPUProfilingActive = true;
    GPUFrameTimes.Empty();
    UE_LOG(LogPCGProfiler, Log, TEXT("GPU profiling started"));
}

void UPCGPerformanceProfiler::StopGPUProfiling()
{
    bGPUProfilingActive = false;
    UE_LOG(LogPCGProfiler, Log, TEXT("GPU profiling stopped"));
}

float UPCGPerformanceProfiler::GetGPUFrameTime() const
{
    return CurrentPerformance.FrameTime;
}

int32 UPCGPerformanceProfiler::GetDrawCallCount() const
{
    return CurrentPerformance.DrawCalls;
}

float UPCGPerformanceProfiler::GetAverageFrameTime() const
{
    return CurrentPerformance.FrameTime;
}

int32 UPCGPerformanceProfiler::GetTriangleCount() const
{
    return CurrentPerformance.Triangles;
}

// Network Profiling
void UPCGPerformanceProfiler::StartNetworkProfiling()
{
    bNetworkStreamingActive = true;
    CurrentNetworkEndpoint = ProfilerConfig.NetworkEndpoint;
    UE_LOG(LogPCGProfiler, Log, TEXT("Network profiling started, endpoint: %s"), *CurrentNetworkEndpoint);
}

void UPCGPerformanceProfiler::StopNetworkProfiling()
{
    bNetworkStreamingActive = false;
    UE_LOG(LogPCGProfiler, Log, TEXT("Network profiling stopped"));
}

void UPCGPerformanceProfiler::StreamDataToEndpoint(const FString& Endpoint)
{
    CurrentNetworkEndpoint = Endpoint;
    
    if (bNetworkStreamingActive)
    {
        FString PerformanceData = GetPerformanceSummary();
        StreamToNetwork(PerformanceData);
    }
}

// Visualization
void UPCGPerformanceProfiler::DrawProfilerOverlay(UCanvas* Canvas)
{
    if (!Canvas || !bOverlayVisible || !bIsActive)
    {
        return;
    }
    
    // Set up drawing parameters
    FVector2D DrawPosition = OverlayPosition;
    float LineHeight = 20.0f * OverlayScale;
    FLinearColor TextColor = FLinearColor::White;
    
    // Setup font render info once
    FFontRenderInfo RenderInfo;
    RenderInfo.bEnableShadow = true;
    
    // Draw background
    FVector2D BackgroundSize(400.0f * OverlayScale, 300.0f * OverlayScale);
    Canvas->DrawTile(Canvas->DefaultTexture, DrawPosition.X - 10, DrawPosition.Y - 10, 
                     BackgroundSize.X + 20, BackgroundSize.Y + 20, 0, 0, 1, 1);
    
    // Draw title
    Canvas->DrawText(GEngine->GetSmallFont(), TEXT("PCG Performance Profiler"), 
                     DrawPosition.X, DrawPosition.Y, OverlayScale, OverlayScale, 
                     RenderInfo);
    DrawPosition.Y += LineHeight * 1.5f;
    
    // Draw performance metrics
    FString CPUText = FString::Printf(TEXT("CPU: %.1f%%"), CurrentPerformance.CPUUsagePercent);
    Canvas->DrawText(GEngine->GetSmallFont(), CPUText, 
                     DrawPosition.X, DrawPosition.Y, OverlayScale, OverlayScale, 
                     RenderInfo);
    DrawPosition.Y += LineHeight;
    
    FString MemoryText = FString::Printf(TEXT("Memory: %d MB"), CurrentPerformance.UsedMemoryMB);
    Canvas->DrawText(GEngine->GetSmallFont(), MemoryText, 
                     DrawPosition.X, DrawPosition.Y, OverlayScale, OverlayScale, 
                     RenderInfo);
    DrawPosition.Y += LineHeight;
    
    FString GPUText = FString::Printf(TEXT("GPU: %.1f%%"), CurrentPerformance.GPUUsagePercent);
    Canvas->DrawText(GEngine->GetSmallFont(), GPUText, 
                     DrawPosition.X, DrawPosition.Y, OverlayScale, OverlayScale, 
                     RenderInfo);
    DrawPosition.Y += LineHeight;
    
    FString FPSText = FString::Printf(TEXT("FPS: %.1f"), CurrentPerformance.FPS);
    Canvas->DrawText(GEngine->GetSmallFont(), FPSText, 
                     DrawPosition.X, DrawPosition.Y, OverlayScale, OverlayScale, 
                     RenderInfo);
    DrawPosition.Y += LineHeight;
    
    // Draw PCG specific metrics
    FString PCGText = FString::Printf(TEXT("PCG Components: %d"), CurrentPerformance.ActivePCGComponents);
    Canvas->DrawText(GEngine->GetSmallFont(), PCGText, 
                     DrawPosition.X, DrawPosition.Y, OverlayScale, OverlayScale, RenderInfo);
    DrawPosition.Y += LineHeight;
    
    FString GenTimeText = FString::Printf(TEXT("Generation Time: %.2f ms"), CurrentPerformance.PCGGenerationTime);
    Canvas->DrawText(GEngine->GetSmallFont(), GenTimeText,
                     DrawPosition.X, DrawPosition.Y, OverlayScale, OverlayScale, 
                     RenderInfo);
    DrawPosition.Y += LineHeight;
    
    // Draw alerts
    if (ActiveAlerts.Num() > 0)
    {
        DrawPosition.Y += LineHeight * 0.5f;
        Canvas->DrawText(GEngine->GetSmallFont(), TEXT("Active Alerts:"), 
                         DrawPosition.X, DrawPosition.Y, OverlayScale, OverlayScale, 
                         RenderInfo);
        DrawPosition.Y += LineHeight;
        
        for (int32 i = 0; i < FMath::Min(ActiveAlerts.Num(), 3); ++i)
        {
            const FPCGProfilerAlert& Alert = ActiveAlerts[i];
            FLinearColor AlertColor = GetSeverityColor(Alert.Severity);
            
            Canvas->DrawText(GEngine->GetSmallFont(), Alert.AlertMessage, 
                             DrawPosition.X + 10, DrawPosition.Y, OverlayScale * 0.8f, OverlayScale * 0.8f, 
                             RenderInfo);
            DrawPosition.Y += LineHeight * 0.8f;
        }
    }
}

void UPCGPerformanceProfiler::SetOverlayPosition(const FVector2D& Position)
{
    OverlayPosition = Position;
}

void UPCGPerformanceProfiler::SetOverlayScale(float Scale)
{
    OverlayScale = FMath::Clamp(Scale, 0.5f, 2.0f);
}

void UPCGPerformanceProfiler::ToggleOverlayVisibility()
{
    bOverlayVisible = !bOverlayVisible;
}

// Internal Functions
void UPCGPerformanceProfiler::UpdateProfiler(float DeltaTime)
{
    if (!bIsActive || bIsPaused)
    {
        return;
    }
    
    AccumulatedTime += DeltaTime;
    
    // Check if it's time to update
    float CurrentTime = FPlatformTime::Seconds();
    if (CurrentTime - LastUpdateTime >= ProfilerConfig.UpdateInterval)
    {
        CollectSystemMetrics();
        CheckThresholds();
        ProcessAlerts();
        UpdateAverages();
        UpdatePeakValues();
        
        // Add to history - using circular buffer indexing
        static uint32 HistoryIndex = 0;
        PerformanceHistory[HistoryIndex % PerformanceHistory.Capacity()] = CurrentPerformance;
        HistoryIndex++;
        
        LastUpdateTime = CurrentTime;
        SampleCount++;
    }
}

void UPCGPerformanceProfiler::CollectSystemMetrics()
{
    CollectCPUMetrics();
    CollectMemoryMetrics();
    CollectGPUMetrics();
    CollectPCGMetrics();
    CollectNaniteMetrics();
    CollectLumenMetrics();
    CollectStreamingMetrics();
}

void UPCGPerformanceProfiler::CollectCPUMetrics()
{
    if (!CategoryStates[EPCGProfilerCategory::CPU])
    {
        return;
    }
    
    // Get CPU usage (simplified)
    CurrentPerformance.CPUUsagePercent = FPlatformTime::GetSecondsPerCycle() * 100.0f;
    CurrentPerformance.GameThreadTime = FApp::GetDeltaTime() * 1000.0f;
    CurrentPerformance.RenderThreadTime = FApp::GetDeltaTime() * 1000.0f;
    CurrentPerformance.RHIThreadTime = FApp::GetDeltaTime() * 1000.0f;
    
    UpdateMetric(TEXT("CPU_Usage"), CurrentPerformance.CPUUsagePercent);
}

void UPCGPerformanceProfiler::CollectMemoryMetrics()
{
    if (!CategoryStates[EPCGProfilerCategory::Memory])
    {
        return;
    }
    
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    CurrentPerformance.UsedMemoryMB = MemStats.UsedPhysical / (1024 * 1024);
    CurrentPerformance.AvailableMemoryMB = MemStats.AvailablePhysical / (1024 * 1024);
    
    UpdateMetric(TEXT("Memory_Usage"), CurrentPerformance.UsedMemoryMB);
}

void UPCGPerformanceProfiler::CollectGPUMetrics()
{
    if (!CategoryStates[EPCGProfilerCategory::GPU])
    {
        return;
    }
    
    CurrentPerformance.FrameTime = FApp::GetDeltaTime() * 1000.0f;
    CurrentPerformance.FPS = 1.0f / FApp::GetDeltaTime();
    
    UpdateMetric(TEXT("Frame_Time"), CurrentPerformance.FrameTime);
    UpdateMetric(TEXT("FPS"), CurrentPerformance.FPS);
}

void UPCGPerformanceProfiler::CollectPCGMetrics()
{
    if (!CategoryStates[EPCGProfilerCategory::PCG])
    {
        return;
    }
    
    // Collect PCG-specific metrics (simplified)
    CurrentPerformance.ActivePCGComponents = 0;
    CurrentPerformance.PCGGenerationTime = 0.0f;
    CurrentPerformance.GeneratedInstances = 0;
    
    UpdateMetric(TEXT("PCG_Generation_Time"), CurrentPerformance.PCGGenerationTime);
}

void UPCGPerformanceProfiler::CollectNaniteMetrics()
{
    if (!CategoryStates[EPCGProfilerCategory::Nanite] || !NaniteOptimizerRef.IsValid())
    {
        return;
    }
    
    // Collect Nanite metrics from optimizer
    CurrentPerformance.NaniteTriangles = 0;
    CurrentPerformance.NaniteClusters = 0;
    CurrentPerformance.NaniteMemoryMB = 0.0f;
}

void UPCGPerformanceProfiler::CollectLumenMetrics()
{
    if (!CategoryStates[EPCGProfilerCategory::Lumen] || !LumenIntegratorRef.IsValid())
    {
        return;
    }
    
    // Collect Lumen metrics from integrator
    CurrentPerformance.LumenUpdateTime = 0.0f;
    CurrentPerformance.LumenProbes = 0;
    CurrentPerformance.LumenMemoryMB = 0.0f;
}

void UPCGPerformanceProfiler::CollectStreamingMetrics()
{
    if (!CategoryStates[EPCGProfilerCategory::Streaming] || !StreamingManagerRef.IsValid())
    {
        return;
    }
    
    // Collect streaming metrics from manager
    CurrentPerformance.StreamingEfficiency = 100.0f;
    
    UpdateMetric(TEXT("Streaming_Efficiency"), CurrentPerformance.StreamingEfficiency);
}

void UPCGPerformanceProfiler::UpdateMetric(const FString& MetricName, float Value)
{
    if (FPCGPerformanceMetric* Metric = RegisteredMetrics.Find(MetricName))
    {
        Metric->CurrentValue = Value;
        Metric->LastUpdateTime = FDateTime::Now();
        
        // Update min/max
        if (Metric->ValueHistory.Num() == 0)
        {
            Metric->MinValue = Value;
            Metric->MaxValue = Value;
        }
        else
        {
            Metric->MinValue = FMath::Min(Metric->MinValue, Value);
            Metric->MaxValue = FMath::Max(Metric->MaxValue, Value);
        }
        
        // Add to history
        Metric->ValueHistory.Add(Value);
        if (Metric->ValueHistory.Num() > ProfilerConfig.MaxSampleHistory)
        {
            Metric->ValueHistory.RemoveAt(0);
        }
        
        // Calculate average
        float Sum = 0.0f;
        for (float HistoryValue : Metric->ValueHistory)
        {
            Sum += HistoryValue;
        }
        Metric->AverageValue = Sum / Metric->ValueHistory.Num();
        
        // Update severity based on thresholds
        if (Metric->ErrorThreshold > 0.0f && Value >= Metric->ErrorThreshold)
        {
            Metric->Severity = EPCGProfilerSeverity::Error;
        }
        else if (Metric->WarningThreshold > 0.0f && Value >= Metric->WarningThreshold)
        {
            Metric->Severity = EPCGProfilerSeverity::Warning;
        }
        else
        {
            Metric->Severity = EPCGProfilerSeverity::Info;
        }
        
        OnMetricUpdated.Broadcast(*Metric);
    }
}

void UPCGPerformanceProfiler::CheckThresholds()
{
    for (auto& MetricPair : RegisteredMetrics)
    {
        FPCGPerformanceMetric& Metric = MetricPair.Value;
        
        if (Metric.ErrorThreshold > 0.0f && Metric.CurrentValue >= Metric.ErrorThreshold)
        {
            OnMetricThresholdExceeded(Metric.MetricName, Metric.CurrentValue, Metric.ErrorThreshold);
        }
        else if (Metric.WarningThreshold > 0.0f && Metric.CurrentValue >= Metric.WarningThreshold)
        {
            OnMetricThresholdExceeded(Metric.MetricName, Metric.CurrentValue, Metric.WarningThreshold);
        }
    }
}

void UPCGPerformanceProfiler::ProcessAlerts()
{
    // Remove expired alerts
    FDateTime CurrentTime = FDateTime::Now();
    ActiveAlerts.RemoveAll([&CurrentTime](const FPCGProfilerAlert& Alert) {
        return (CurrentTime - Alert.Timestamp).GetTotalMinutes() > 5.0; // Remove alerts older than 5 minutes
    });
}

void UPCGPerformanceProfiler::UpdateAverages()
{
    if (SampleCount == 0)
    {
        return;
    }
    
    // Simple running average (could be improved with weighted average)
    float Weight = 1.0f / (SampleCount + 1);
    
    AveragePerformance.CPUUsagePercent = FMath::Lerp(AveragePerformance.CPUUsagePercent, CurrentPerformance.CPUUsagePercent, Weight);
    AveragePerformance.UsedMemoryMB = FMath::Lerp((float)AveragePerformance.UsedMemoryMB, (float)CurrentPerformance.UsedMemoryMB, Weight);
    AveragePerformance.GPUUsagePercent = FMath::Lerp(AveragePerformance.GPUUsagePercent, CurrentPerformance.GPUUsagePercent, Weight);
    AveragePerformance.FPS = FMath::Lerp(AveragePerformance.FPS, CurrentPerformance.FPS, Weight);
    AveragePerformance.PCGGenerationTime = FMath::Lerp(AveragePerformance.PCGGenerationTime, CurrentPerformance.PCGGenerationTime, Weight);
}

void UPCGPerformanceProfiler::UpdatePeakValues()
{
    PeakPerformance.CPUUsagePercent = FMath::Max(PeakPerformance.CPUUsagePercent, CurrentPerformance.CPUUsagePercent);
    PeakPerformance.UsedMemoryMB = FMath::Max(PeakPerformance.UsedMemoryMB, CurrentPerformance.UsedMemoryMB);
    PeakPerformance.GPUUsagePercent = FMath::Max(PeakPerformance.GPUUsagePercent, CurrentPerformance.GPUUsagePercent);
    PeakPerformance.FrameTime = FMath::Max(PeakPerformance.FrameTime, CurrentPerformance.FrameTime);
    PeakPerformance.PCGGenerationTime = FMath::Max(PeakPerformance.PCGGenerationTime, CurrentPerformance.PCGGenerationTime);
}

void UPCGPerformanceProfiler::LogToFile(const FString& Message)
{
    if (!ProfilerConfig.bEnableFileLogging)
    {
        return;
    }
    
    FString LogDir = FPaths::ProjectLogDir();
    FString FullLogPath = FPaths::Combine(LogDir, ProfilerConfig.LogFilePath);
    
    FString LogEntry = FString::Printf(TEXT("[%s] %s\n"), *FDateTime::Now().ToString(), *Message);
    FFileHelper::SaveStringToFile(LogEntry, *FullLogPath, FFileHelper::EEncodingOptions::AutoDetect, &IFileManager::Get(), FILEWRITE_Append);
}

void UPCGPerformanceProfiler::StreamToNetwork(const FString& Data)
{
    if (!bNetworkStreamingActive || CurrentNetworkEndpoint.IsEmpty())
    {
        return;
    }
    
    // Simplified network streaming (would need proper HTTP client implementation)
    UE_LOG(LogPCGProfiler, Log, TEXT("Streaming data to %s: %s"), *CurrentNetworkEndpoint, *Data);
}

// Utility Functions
float UPCGPerformanceProfiler::GetSamplingInterval() const
{
    switch (ProfilerConfig.SamplingRate)
    {
        case EPCGProfilerSamplingRate::VeryLow: return 1.0f;
        case EPCGProfilerSamplingRate::Low: return 0.2f;
        case EPCGProfilerSamplingRate::Medium: return 0.1f;
        case EPCGProfilerSamplingRate::High: return 0.033f;
        case EPCGProfilerSamplingRate::VeryHigh: return 0.016f;
        case EPCGProfilerSamplingRate::Realtime: return 0.008f;
        default: return 0.1f;
    }
}

FColor UPCGPerformanceProfiler::GetSeverityColor(EPCGProfilerSeverity Severity) const
{
    switch (Severity)
    {
        case EPCGProfilerSeverity::Info: return FColor::White;
        case EPCGProfilerSeverity::Warning: return FColor::Yellow;
        case EPCGProfilerSeverity::Error: return FColor::Orange;
        case EPCGProfilerSeverity::Critical: return FColor::Red;
        default: return FColor::White;
    }
}

FString UPCGPerformanceProfiler::FormatMetricValue(float Value, const FString& Unit) const
{
    if (Unit == TEXT("%"))
    {
        return FString::Printf(TEXT("%.1f%%"), Value);
    }
    else if (Unit == TEXT("MB"))
    {
        return FString::Printf(TEXT("%.0f MB"), Value);
    }
    else if (Unit == TEXT("ms"))
    {
        return FString::Printf(TEXT("%.2f ms"), Value);
    }
    else if (Unit == TEXT("fps"))
    {
        return FString::Printf(TEXT("%.1f fps"), Value);
    }
    else
    {
        return FString::Printf(TEXT("%.2f %s"), Value, *Unit);
    }
}

FString UPCGPerformanceProfiler::GenerateOptimizationRecommendation(const FPCGPerformanceMetric& Metric) const
{
    if (Metric.Category == EPCGProfilerCategory::CPU && Metric.CurrentValue > Metric.WarningThreshold)
    {
        return TEXT("Consider reducing CPU-intensive operations or enabling multithreading");
    }
    else if (Metric.Category == EPCGProfilerCategory::Memory && Metric.CurrentValue > Metric.WarningThreshold)
    {
        return TEXT("Implement memory optimization strategies or increase available memory");
    }
    else if (Metric.Category == EPCGProfilerCategory::GPU && Metric.CurrentValue > Metric.WarningThreshold)
    {
        return TEXT("Optimize rendering pipeline or reduce visual complexity");
    }
    
    return TEXT("Monitor metric for continued performance");
}

// Async Operations
void UPCGPerformanceProfiler::StartAsyncProfiling()
{
    // Start async task for continuous profiling
    AsyncTask(ENamedThreads::GameThread, [this]()
    {
        while (bIsActive)
        {
            if (!bIsPaused)
            {
                UpdateProfiler(FApp::GetDeltaTime());
            }
            
            FPlatformProcess::Sleep(GetSamplingInterval());
        }
    });
}

void UPCGPerformanceProfiler::StopAsyncProfiling()
{
    // Async task will stop when bIsActive becomes false
}

// Event Handlers
void UPCGPerformanceProfiler::OnMetricThresholdExceeded(const FString& MetricName, float Value, float Threshold)
{
    FPCGProfilerAlert NewAlert;
    NewAlert.AlertMessage = FString::Printf(TEXT("%s exceeded threshold: %.2f >= %.2f"), *MetricName, Value, Threshold);
    NewAlert.Severity = Value >= Threshold * 1.2f ? EPCGProfilerSeverity::Error : EPCGProfilerSeverity::Warning;
    NewAlert.Category = EPCGProfilerCategory::Custom;
    NewAlert.Timestamp = FDateTime::Now();
    NewAlert.MetricValue = Value;
    NewAlert.ThresholdValue = Threshold;
    NewAlert.bIsActive = true;
    
    // Find metric category
    if (const FPCGPerformanceMetric* Metric = RegisteredMetrics.Find(MetricName))
    {
        NewAlert.Category = Metric->Category;
    }
    
    ActiveAlerts.Add(NewAlert);
    AlertHistory.Add(NewAlert);
    
    OnPerformanceAlert.Broadcast(NewAlert);
    OnThresholdExceeded.Broadcast(MetricName, Value);
    
    UE_LOG(LogPCGProfiler, Warning, TEXT("Performance Alert: %s"), *NewAlert.AlertMessage);
    LogToFile(FString::Printf(TEXT("ALERT: %s"), *NewAlert.AlertMessage));
}

void UPCGPerformanceProfiler::OnSystemPerformanceChanged(const FPCGSystemPerformance& NewPerformance)
{
    CurrentPerformance = NewPerformance;
    UpdateAverages();
    UpdatePeakValues();
}