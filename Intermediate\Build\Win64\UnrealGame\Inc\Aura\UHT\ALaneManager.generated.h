// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "ALaneManager.h"

#ifdef AURA_ALaneManager_generated_h
#error "ALaneManager.generated.h already included, missing '#pragma once' in ALaneManager.h"
#endif
#define AURA_ALaneManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
enum class ELaneManagerType : uint8;
struct FTowerData;

// ********** Begin ScriptStruct FLaneData *********************************************************
#define FID_Aura_Source_Aura_Public_ALaneManager_h_34_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLaneData_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FLaneData;
// ********** End ScriptStruct FLaneData ***********************************************************

// ********** Begin ScriptStruct FTowerData ********************************************************
#define FID_Aura_Source_Aura_Public_ALaneManager_h_63_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTowerData_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FTowerData;
// ********** End ScriptStruct FTowerData **********************************************************

// ********** Begin ScriptStruct FPathNode *********************************************************
#define FID_Aura_Source_Aura_Public_ALaneManager_h_102_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPathNode_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FPathNode;
// ********** End ScriptStruct FPathNode ***********************************************************

// ********** Begin Class ALaneManager *************************************************************
#define FID_Aura_Source_Aura_Public_ALaneManager_h_132_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetMovementSpeedModifier); \
	DECLARE_FUNCTION(execGetBridgePosition); \
	DECLARE_FUNCTION(execIsPositionInRiver); \
	DECLARE_FUNCTION(execGetClosestLane); \
	DECLARE_FUNCTION(execIsPositionInLane); \
	DECLARE_FUNCTION(execValidateLaneGeometry); \
	DECLARE_FUNCTION(execGetBestTarget); \
	DECLARE_FUNCTION(execFindTargetsInRange); \
	DECLARE_FUNCTION(execSpawnTower); \
	DECLARE_FUNCTION(execGetNearbyWaypoints); \
	DECLARE_FUNCTION(execCalculateHeuristic); \
	DECLARE_FUNCTION(execFindPath); \
	DECLARE_FUNCTION(execGenerateWaypoints); \
	DECLARE_FUNCTION(execInitializeTowers); \
	DECLARE_FUNCTION(execInitializeLanes); \
	DECLARE_FUNCTION(execCalculateInferiorLanePosition); \
	DECLARE_FUNCTION(execCalculateCentralLanePosition); \
	DECLARE_FUNCTION(execCalculateSuperiorLanePosition);


AURA_API UClass* Z_Construct_UClass_ALaneManager_NoRegister();

#define FID_Aura_Source_Aura_Public_ALaneManager_h_132_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesALaneManager(); \
	friend struct Z_Construct_UClass_ALaneManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_ALaneManager_NoRegister(); \
public: \
	DECLARE_CLASS2(ALaneManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_ALaneManager_NoRegister) \
	DECLARE_SERIALIZER(ALaneManager)


#define FID_Aura_Source_Aura_Public_ALaneManager_h_132_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ALaneManager(ALaneManager&&) = delete; \
	ALaneManager(const ALaneManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ALaneManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ALaneManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ALaneManager) \
	NO_API virtual ~ALaneManager();


#define FID_Aura_Source_Aura_Public_ALaneManager_h_129_PROLOG
#define FID_Aura_Source_Aura_Public_ALaneManager_h_132_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_ALaneManager_h_132_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_ALaneManager_h_132_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_ALaneManager_h_132_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ALaneManager;

// ********** End Class ALaneManager ***************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_ALaneManager_h

// ********** Begin Enum ELaneManagerType **********************************************************
#define FOREACH_ENUM_ELANEMANAGERTYPE(op) \
	op(ELaneManagerType::Superior) \
	op(ELaneManagerType::Central) \
	op(ELaneManagerType::Inferior) 

enum class ELaneManagerType : uint8;
template<> struct TIsUEnumClass<ELaneManagerType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<ELaneManagerType>();
// ********** End Enum ELaneManagerType ************************************************************

// ********** Begin Enum ETowerType ****************************************************************
#define FOREACH_ENUM_ETOWERTYPE(op) \
	op(ETowerType::Externa) \
	op(ETowerType::Interna) \
	op(ETowerType::Inibidor) 

enum class ETowerType : uint8;
template<> struct TIsUEnumClass<ETowerType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<ETowerType>();
// ********** End Enum ETowerType ******************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
