// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "APCGStreamingManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAPCGStreamingManager() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_APCGNaniteOptimizer_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGStreamingManager();
AURA_API UClass* Z_Construct_UClass_APCGStreamingManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGWorldPartitionManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGStreamingAssetType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGStreamingManagerPriority();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGStreamingMode();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGStreamingState();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGStreamingAsset();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGStreamingConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGStreamingRegion();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGStreamingStats();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPCGStreamingManagerPriority **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGStreamingManagerPriority;
static UEnum* EPCGStreamingManagerPriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGStreamingManagerPriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGStreamingManagerPriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGStreamingManagerPriority, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGStreamingManagerPriority"));
	}
	return Z_Registration_Info_UEnum_EPCGStreamingManagerPriority.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGStreamingManagerPriority>()
{
	return EPCGStreamingManagerPriority_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGStreamingManagerPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums\n" },
#endif
		{ "Critical.DisplayName", "Critical Priority" },
		{ "Critical.Name", "EPCGStreamingManagerPriority::Critical" },
		{ "High.DisplayName", "High Priority" },
		{ "High.Name", "EPCGStreamingManagerPriority::High" },
		{ "Low.DisplayName", "Low Priority" },
		{ "Low.Name", "EPCGStreamingManagerPriority::Low" },
		{ "Medium.DisplayName", "Medium Priority" },
		{ "Medium.Name", "EPCGStreamingManagerPriority::Medium" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums" },
#endif
		{ "VeryLow.DisplayName", "Very Low Priority" },
		{ "VeryLow.Name", "EPCGStreamingManagerPriority::VeryLow" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGStreamingManagerPriority::VeryLow", (int64)EPCGStreamingManagerPriority::VeryLow },
		{ "EPCGStreamingManagerPriority::Low", (int64)EPCGStreamingManagerPriority::Low },
		{ "EPCGStreamingManagerPriority::Medium", (int64)EPCGStreamingManagerPriority::Medium },
		{ "EPCGStreamingManagerPriority::High", (int64)EPCGStreamingManagerPriority::High },
		{ "EPCGStreamingManagerPriority::Critical", (int64)EPCGStreamingManagerPriority::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGStreamingManagerPriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGStreamingManagerPriority",
	"EPCGStreamingManagerPriority",
	Z_Construct_UEnum_Aura_EPCGStreamingManagerPriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGStreamingManagerPriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGStreamingManagerPriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGStreamingManagerPriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGStreamingManagerPriority()
{
	if (!Z_Registration_Info_UEnum_EPCGStreamingManagerPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGStreamingManagerPriority.InnerSingleton, Z_Construct_UEnum_Aura_EPCGStreamingManagerPriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGStreamingManagerPriority.InnerSingleton;
}
// ********** End Enum EPCGStreamingManagerPriority ************************************************

// ********** Begin Enum EPCGStreamingState ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGStreamingState;
static UEnum* EPCGStreamingState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGStreamingState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGStreamingState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGStreamingState, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGStreamingState"));
	}
	return Z_Registration_Info_UEnum_EPCGStreamingState.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGStreamingState>()
{
	return EPCGStreamingState_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGStreamingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cached.DisplayName", "Cached" },
		{ "Cached.Name", "EPCGStreamingState::Cached" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EPCGStreamingState::Failed" },
		{ "Loaded.DisplayName", "Loaded" },
		{ "Loaded.Name", "EPCGStreamingState::Loaded" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EPCGStreamingState::Loading" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
		{ "Unloaded.DisplayName", "Unloaded" },
		{ "Unloaded.Name", "EPCGStreamingState::Unloaded" },
		{ "Unloading.DisplayName", "Unloading" },
		{ "Unloading.Name", "EPCGStreamingState::Unloading" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGStreamingState::Unloaded", (int64)EPCGStreamingState::Unloaded },
		{ "EPCGStreamingState::Loading", (int64)EPCGStreamingState::Loading },
		{ "EPCGStreamingState::Loaded", (int64)EPCGStreamingState::Loaded },
		{ "EPCGStreamingState::Unloading", (int64)EPCGStreamingState::Unloading },
		{ "EPCGStreamingState::Failed", (int64)EPCGStreamingState::Failed },
		{ "EPCGStreamingState::Cached", (int64)EPCGStreamingState::Cached },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGStreamingState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGStreamingState",
	"EPCGStreamingState",
	Z_Construct_UEnum_Aura_EPCGStreamingState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGStreamingState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGStreamingState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGStreamingState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGStreamingState()
{
	if (!Z_Registration_Info_UEnum_EPCGStreamingState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGStreamingState.InnerSingleton, Z_Construct_UEnum_Aura_EPCGStreamingState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGStreamingState.InnerSingleton;
}
// ********** End Enum EPCGStreamingState **********************************************************

// ********** Begin Enum EPCGStreamingMode *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGStreamingMode;
static UEnum* EPCGStreamingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGStreamingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGStreamingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGStreamingMode, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGStreamingMode"));
	}
	return Z_Registration_Info_UEnum_EPCGStreamingMode.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGStreamingMode>()
{
	return EPCGStreamingMode_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGStreamingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive Streaming" },
		{ "Adaptive.Name", "EPCGStreamingMode::Adaptive" },
		{ "BlueprintType", "true" },
		{ "Distance.DisplayName", "Distance Based" },
		{ "Distance.Name", "EPCGStreamingMode::Distance" },
		{ "Frustum.DisplayName", "Frustum Based" },
		{ "Frustum.Name", "EPCGStreamingMode::Frustum" },
		{ "Hybrid.DisplayName", "Hybrid Distance + Frustum" },
		{ "Hybrid.Name", "EPCGStreamingMode::Hybrid" },
		{ "Manual.DisplayName", "Manual Control" },
		{ "Manual.Name", "EPCGStreamingMode::Manual" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGStreamingMode::Distance", (int64)EPCGStreamingMode::Distance },
		{ "EPCGStreamingMode::Frustum", (int64)EPCGStreamingMode::Frustum },
		{ "EPCGStreamingMode::Hybrid", (int64)EPCGStreamingMode::Hybrid },
		{ "EPCGStreamingMode::Manual", (int64)EPCGStreamingMode::Manual },
		{ "EPCGStreamingMode::Adaptive", (int64)EPCGStreamingMode::Adaptive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGStreamingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGStreamingMode",
	"EPCGStreamingMode",
	Z_Construct_UEnum_Aura_EPCGStreamingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGStreamingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGStreamingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGStreamingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGStreamingMode()
{
	if (!Z_Registration_Info_UEnum_EPCGStreamingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGStreamingMode.InnerSingleton, Z_Construct_UEnum_Aura_EPCGStreamingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGStreamingMode.InnerSingleton;
}
// ********** End Enum EPCGStreamingMode ***********************************************************

// ********** Begin Enum EPCGStreamingAssetType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGStreamingAssetType;
static UEnum* EPCGStreamingAssetType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGStreamingAssetType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGStreamingAssetType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGStreamingAssetType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGStreamingAssetType"));
	}
	return Z_Registration_Info_UEnum_EPCGStreamingAssetType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGStreamingAssetType>()
{
	return EPCGStreamingAssetType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGStreamingAssetType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Animation.DisplayName", "Animation" },
		{ "Animation.Name", "EPCGStreamingAssetType::Animation" },
		{ "Blueprint.DisplayName", "Blueprint" },
		{ "Blueprint.Name", "EPCGStreamingAssetType::Blueprint" },
		{ "BlueprintType", "true" },
		{ "Level.DisplayName", "Level" },
		{ "Level.Name", "EPCGStreamingAssetType::Level" },
		{ "Material.DisplayName", "Material" },
		{ "Material.Name", "EPCGStreamingAssetType::Material" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
		{ "ParticleSystem.DisplayName", "Particle System" },
		{ "ParticleSystem.Name", "EPCGStreamingAssetType::ParticleSystem" },
		{ "Sound.DisplayName", "Sound" },
		{ "Sound.Name", "EPCGStreamingAssetType::Sound" },
		{ "StaticMesh.DisplayName", "Static Mesh" },
		{ "StaticMesh.Name", "EPCGStreamingAssetType::StaticMesh" },
		{ "Texture.DisplayName", "Texture" },
		{ "Texture.Name", "EPCGStreamingAssetType::Texture" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGStreamingAssetType::StaticMesh", (int64)EPCGStreamingAssetType::StaticMesh },
		{ "EPCGStreamingAssetType::Material", (int64)EPCGStreamingAssetType::Material },
		{ "EPCGStreamingAssetType::Texture", (int64)EPCGStreamingAssetType::Texture },
		{ "EPCGStreamingAssetType::Sound", (int64)EPCGStreamingAssetType::Sound },
		{ "EPCGStreamingAssetType::ParticleSystem", (int64)EPCGStreamingAssetType::ParticleSystem },
		{ "EPCGStreamingAssetType::Level", (int64)EPCGStreamingAssetType::Level },
		{ "EPCGStreamingAssetType::Blueprint", (int64)EPCGStreamingAssetType::Blueprint },
		{ "EPCGStreamingAssetType::Animation", (int64)EPCGStreamingAssetType::Animation },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGStreamingAssetType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGStreamingAssetType",
	"EPCGStreamingAssetType",
	Z_Construct_UEnum_Aura_EPCGStreamingAssetType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGStreamingAssetType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGStreamingAssetType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGStreamingAssetType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGStreamingAssetType()
{
	if (!Z_Registration_Info_UEnum_EPCGStreamingAssetType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGStreamingAssetType.InnerSingleton, Z_Construct_UEnum_Aura_EPCGStreamingAssetType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGStreamingAssetType.InnerSingleton;
}
// ********** End Enum EPCGStreamingAssetType ******************************************************

// ********** Begin ScriptStruct FPCGStreamingConfig ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGStreamingConfig;
class UScriptStruct* FPCGStreamingConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGStreamingConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGStreamingConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGStreamingConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGStreamingConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGStreamingConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structures\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structures" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingMode_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadDistance_MetaData[] = {
		{ "Category", "Distance" },
		{ "ClampMax", "50000.0" },
		{ "ClampMin", "100.0" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnloadDistance_MetaData[] = {
		{ "Category", "Distance" },
		{ "ClampMax", "25000.0" },
		{ "ClampMin", "50.0" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentLoads_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "100" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentUnloads_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "50" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ClampMax", "10000" },
		{ "ClampMin", "100" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCachedAssets_MetaData[] = {
		{ "Category", "Cache" },
		{ "ClampMax", "1000" },
		{ "ClampMin", "10" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncLoading_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePreloading_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLODStreaming_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDebugInfo_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_StreamingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StreamingMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UnloadDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentLoads;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentUnloads;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxMemoryUsageMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCachedAssets;
	static void NewProp_bEnableAsyncLoading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncLoading;
	static void NewProp_bEnablePreloading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePreloading;
	static void NewProp_bEnableLODStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLODStreaming;
	static void NewProp_bShowDebugInfo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDebugInfo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGStreamingConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_StreamingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_StreamingMode = { "StreamingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingConfig, StreamingMode), Z_Construct_UEnum_Aura_EPCGStreamingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingMode_MetaData), NewProp_StreamingMode_MetaData) }; // 894006293
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_LoadDistance = { "LoadDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingConfig, LoadDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadDistance_MetaData), NewProp_LoadDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_UnloadDistance = { "UnloadDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingConfig, UnloadDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnloadDistance_MetaData), NewProp_UnloadDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_MaxConcurrentLoads = { "MaxConcurrentLoads", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingConfig, MaxConcurrentLoads), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentLoads_MetaData), NewProp_MaxConcurrentLoads_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_MaxConcurrentUnloads = { "MaxConcurrentUnloads", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingConfig, MaxConcurrentUnloads), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentUnloads_MetaData), NewProp_MaxConcurrentUnloads_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_MaxMemoryUsageMB = { "MaxMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingConfig, MaxMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMemoryUsageMB_MetaData), NewProp_MaxMemoryUsageMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_MaxCachedAssets = { "MaxCachedAssets", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingConfig, MaxCachedAssets), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCachedAssets_MetaData), NewProp_MaxCachedAssets_MetaData) };
void Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bEnableAsyncLoading_SetBit(void* Obj)
{
	((FPCGStreamingConfig*)Obj)->bEnableAsyncLoading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bEnableAsyncLoading = { "bEnableAsyncLoading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGStreamingConfig), &Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bEnableAsyncLoading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncLoading_MetaData), NewProp_bEnableAsyncLoading_MetaData) };
void Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bEnablePreloading_SetBit(void* Obj)
{
	((FPCGStreamingConfig*)Obj)->bEnablePreloading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bEnablePreloading = { "bEnablePreloading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGStreamingConfig), &Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bEnablePreloading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePreloading_MetaData), NewProp_bEnablePreloading_MetaData) };
void Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bEnableLODStreaming_SetBit(void* Obj)
{
	((FPCGStreamingConfig*)Obj)->bEnableLODStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bEnableLODStreaming = { "bEnableLODStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGStreamingConfig), &Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bEnableLODStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLODStreaming_MetaData), NewProp_bEnableLODStreaming_MetaData) };
void Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bShowDebugInfo_SetBit(void* Obj)
{
	((FPCGStreamingConfig*)Obj)->bShowDebugInfo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bShowDebugInfo = { "bShowDebugInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGStreamingConfig), &Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bShowDebugInfo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDebugInfo_MetaData), NewProp_bShowDebugInfo_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_StreamingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_StreamingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_LoadDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_UnloadDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_MaxConcurrentLoads,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_MaxConcurrentUnloads,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_MaxMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_MaxCachedAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bEnableAsyncLoading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bEnablePreloading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bEnableLODStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewProp_bShowDebugInfo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGStreamingConfig",
	Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::PropPointers),
	sizeof(FPCGStreamingConfig),
	alignof(FPCGStreamingConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGStreamingConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGStreamingConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGStreamingConfig.InnerSingleton, Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGStreamingConfig.InnerSingleton;
}
// ********** End ScriptStruct FPCGStreamingConfig *************************************************

// ********** Begin ScriptStruct FPCGStreamingAsset ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGStreamingAsset;
class UScriptStruct* FPCGStreamingAsset::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGStreamingAsset.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGStreamingAsset.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGStreamingAsset, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGStreamingAsset"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGStreamingAsset.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetType_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetReference_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPosition_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoundingBox_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_State_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomLoadDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ClampMax", "100000.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEssential_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanBeUnloaded_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreloadOnStart_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAccessTime_MetaData[] = {
		{ "Category", "Runtime" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccessCount_MetaData[] = {
		{ "Category", "Runtime" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadedAsset_MetaData[] = {
		{ "Category", "Runtime" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_AssetType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AssetType;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetReference;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BoundingBox;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FBytePropertyParams NewProp_State_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_State;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CustomLoadDistance;
	static void NewProp_bIsEssential_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEssential;
	static void NewProp_bCanBeUnloaded_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanBeUnloaded;
	static void NewProp_bPreloadOnStart_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreloadOnStart;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastAccessTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AccessCount;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_LoadedAsset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGStreamingAsset>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_AssetType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_AssetType = { "AssetType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingAsset, AssetType), Z_Construct_UEnum_Aura_EPCGStreamingAssetType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetType_MetaData), NewProp_AssetType_MetaData) }; // 3126784552
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_AssetReference = { "AssetReference", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingAsset, AssetReference), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetReference_MetaData), NewProp_AssetReference_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_WorldPosition = { "WorldPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingAsset, WorldPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPosition_MetaData), NewProp_WorldPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_BoundingBox = { "BoundingBox", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingAsset, BoundingBox), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoundingBox_MetaData), NewProp_BoundingBox_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingAsset, Priority), Z_Construct_UEnum_Aura_EPCGStreamingManagerPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) }; // 3867279043
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_State_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingAsset, State), Z_Construct_UEnum_Aura_EPCGStreamingState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_State_MetaData), NewProp_State_MetaData) }; // 453959094
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_CustomLoadDistance = { "CustomLoadDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingAsset, CustomLoadDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomLoadDistance_MetaData), NewProp_CustomLoadDistance_MetaData) };
void Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_bIsEssential_SetBit(void* Obj)
{
	((FPCGStreamingAsset*)Obj)->bIsEssential = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_bIsEssential = { "bIsEssential", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGStreamingAsset), &Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_bIsEssential_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEssential_MetaData), NewProp_bIsEssential_MetaData) };
void Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_bCanBeUnloaded_SetBit(void* Obj)
{
	((FPCGStreamingAsset*)Obj)->bCanBeUnloaded = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_bCanBeUnloaded = { "bCanBeUnloaded", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGStreamingAsset), &Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_bCanBeUnloaded_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanBeUnloaded_MetaData), NewProp_bCanBeUnloaded_MetaData) };
void Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_bPreloadOnStart_SetBit(void* Obj)
{
	((FPCGStreamingAsset*)Obj)->bPreloadOnStart = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_bPreloadOnStart = { "bPreloadOnStart", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGStreamingAsset), &Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_bPreloadOnStart_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreloadOnStart_MetaData), NewProp_bPreloadOnStart_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_LastAccessTime = { "LastAccessTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingAsset, LastAccessTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAccessTime_MetaData), NewProp_LastAccessTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_AccessCount = { "AccessCount", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingAsset, AccessCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccessCount_MetaData), NewProp_AccessCount_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_LoadedAsset = { "LoadedAsset", nullptr, (EPropertyFlags)0x0014000000020015, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingAsset, LoadedAsset), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadedAsset_MetaData), NewProp_LoadedAsset_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_AssetType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_AssetType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_AssetReference,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_WorldPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_BoundingBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_State_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_CustomLoadDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_bIsEssential,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_bCanBeUnloaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_bPreloadOnStart,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_LastAccessTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_AccessCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewProp_LoadedAsset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGStreamingAsset",
	Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::PropPointers),
	sizeof(FPCGStreamingAsset),
	alignof(FPCGStreamingAsset),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGStreamingAsset()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGStreamingAsset.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGStreamingAsset.InnerSingleton, Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGStreamingAsset.InnerSingleton;
}
// ********** End ScriptStruct FPCGStreamingAsset **************************************************

// ********** Begin ScriptStruct FPCGStreamingRegion ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGStreamingRegion;
class UScriptStruct* FPCGStreamingRegion::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGStreamingRegion.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGStreamingRegion.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGStreamingRegion, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGStreamingRegion"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGStreamingRegion.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegionName_MetaData[] = {
		{ "Category", "Region" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegionBounds_MetaData[] = {
		{ "Category", "Region" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegionPriority_MetaData[] = {
		{ "Category", "Region" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Assets_MetaData[] = {
		{ "Category", "Region" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ClampMax", "50000.0" },
		{ "ClampMin", "100.0" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnloadDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ClampMax", "25000.0" },
		{ "ClampMin", "50.0" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoManaged_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsLoaded_MetaData[] = {
		{ "Category", "Runtime" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Runtime" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegionName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegionBounds;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RegionPriority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RegionPriority;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Assets_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Assets;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UnloadDistance;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static void NewProp_bAutoManaged_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoManaged;
	static void NewProp_bIsLoaded_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLoaded;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGStreamingRegion>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_RegionName = { "RegionName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingRegion, RegionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegionName_MetaData), NewProp_RegionName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_RegionBounds = { "RegionBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingRegion, RegionBounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegionBounds_MetaData), NewProp_RegionBounds_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_RegionPriority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_RegionPriority = { "RegionPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingRegion, RegionPriority), Z_Construct_UEnum_Aura_EPCGStreamingManagerPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegionPriority_MetaData), NewProp_RegionPriority_MetaData) }; // 3867279043
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_Assets_Inner = { "Assets", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGStreamingAsset, METADATA_PARAMS(0, nullptr) }; // 796445885
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_Assets = { "Assets", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingRegion, Assets), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Assets_MetaData), NewProp_Assets_MetaData) }; // 796445885
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_LoadDistance = { "LoadDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingRegion, LoadDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadDistance_MetaData), NewProp_LoadDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_UnloadDistance = { "UnloadDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingRegion, UnloadDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnloadDistance_MetaData), NewProp_UnloadDistance_MetaData) };
void Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FPCGStreamingRegion*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGStreamingRegion), &Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
void Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_bAutoManaged_SetBit(void* Obj)
{
	((FPCGStreamingRegion*)Obj)->bAutoManaged = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_bAutoManaged = { "bAutoManaged", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGStreamingRegion), &Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_bAutoManaged_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoManaged_MetaData), NewProp_bAutoManaged_MetaData) };
void Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_bIsLoaded_SetBit(void* Obj)
{
	((FPCGStreamingRegion*)Obj)->bIsLoaded = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_bIsLoaded = { "bIsLoaded", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGStreamingRegion), &Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_bIsLoaded_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsLoaded_MetaData), NewProp_bIsLoaded_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingRegion, LastUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_RegionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_RegionBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_RegionPriority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_RegionPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_Assets_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_Assets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_LoadDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_UnloadDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_bAutoManaged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_bIsLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGStreamingRegion",
	Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::PropPointers),
	sizeof(FPCGStreamingRegion),
	alignof(FPCGStreamingRegion),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGStreamingRegion()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGStreamingRegion.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGStreamingRegion.InnerSingleton, Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGStreamingRegion.InnerSingleton;
}
// ********** End ScriptStruct FPCGStreamingRegion *************************************************

// ********** Begin ScriptStruct FPCGStreamingStats ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGStreamingStats;
class UScriptStruct* FPCGStreamingStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGStreamingStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGStreamingStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGStreamingStats, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGStreamingStats"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGStreamingStats.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGStreamingStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalAssets_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadedAssets_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingAssets_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedAssets_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedAssets_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheMemoryMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLoadTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingEfficiency_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveRegions_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalAssets;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoadedAssets;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoadingAssets;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CachedAssets;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FailedAssets;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CacheMemoryMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLoadTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingEfficiency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveRegions;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGStreamingStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_TotalAssets = { "TotalAssets", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingStats, TotalAssets), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalAssets_MetaData), NewProp_TotalAssets_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_LoadedAssets = { "LoadedAssets", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingStats, LoadedAssets), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadedAssets_MetaData), NewProp_LoadedAssets_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_LoadingAssets = { "LoadingAssets", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingStats, LoadingAssets), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingAssets_MetaData), NewProp_LoadingAssets_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_CachedAssets = { "CachedAssets", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingStats, CachedAssets), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedAssets_MetaData), NewProp_CachedAssets_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_FailedAssets = { "FailedAssets", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingStats, FailedAssets), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedAssets_MetaData), NewProp_FailedAssets_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingStats, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_CacheMemoryMB = { "CacheMemoryMB", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingStats, CacheMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheMemoryMB_MetaData), NewProp_CacheMemoryMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_AverageLoadTime = { "AverageLoadTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingStats, AverageLoadTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLoadTime_MetaData), NewProp_AverageLoadTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_StreamingEfficiency = { "StreamingEfficiency", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingStats, StreamingEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingEfficiency_MetaData), NewProp_StreamingEfficiency_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_ActiveRegions = { "ActiveRegions", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGStreamingStats, ActiveRegions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveRegions_MetaData), NewProp_ActiveRegions_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_TotalAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_LoadedAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_LoadingAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_CachedAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_FailedAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_CacheMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_AverageLoadTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_StreamingEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewProp_ActiveRegions,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGStreamingStats",
	Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::PropPointers),
	sizeof(FPCGStreamingStats),
	alignof(FPCGStreamingStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGStreamingStats()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGStreamingStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGStreamingStats.InnerSingleton, Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGStreamingStats.InnerSingleton;
}
// ********** End ScriptStruct FPCGStreamingStats **************************************************

// ********** Begin Delegate FOnAssetLoaded ********************************************************
struct Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnAssetLoaded_Parms
	{
		FPCGStreamingAsset Asset;
		UObject* LoadedObject;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegates\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Asset_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Asset;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LoadedObject;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::NewProp_Asset = { "Asset", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnAssetLoaded_Parms, Asset), Z_Construct_UScriptStruct_FPCGStreamingAsset, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Asset_MetaData), NewProp_Asset_MetaData) }; // 796445885
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::NewProp_LoadedObject = { "LoadedObject", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnAssetLoaded_Parms, LoadedObject), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::NewProp_Asset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::NewProp_LoadedObject,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnAssetLoaded__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::_Script_Aura_eventOnAssetLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::_Script_Aura_eventOnAssetLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnAssetLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnAssetLoaded, FPCGStreamingAsset const& Asset, UObject* LoadedObject)
{
	struct _Script_Aura_eventOnAssetLoaded_Parms
	{
		FPCGStreamingAsset Asset;
		UObject* LoadedObject;
	};
	_Script_Aura_eventOnAssetLoaded_Parms Parms;
	Parms.Asset=Asset;
	Parms.LoadedObject=LoadedObject;
	OnAssetLoaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAssetLoaded **********************************************************

// ********** Begin Delegate FOnAssetUnloaded ******************************************************
struct Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnAssetUnloaded_Parms
	{
		FPCGStreamingAsset Asset;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Asset_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Asset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature_Statics::NewProp_Asset = { "Asset", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnAssetUnloaded_Parms, Asset), Z_Construct_UScriptStruct_FPCGStreamingAsset, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Asset_MetaData), NewProp_Asset_MetaData) }; // 796445885
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature_Statics::NewProp_Asset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnAssetUnloaded__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature_Statics::_Script_Aura_eventOnAssetUnloaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature_Statics::_Script_Aura_eventOnAssetUnloaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnAssetUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnAssetUnloaded, FPCGStreamingAsset const& Asset)
{
	struct _Script_Aura_eventOnAssetUnloaded_Parms
	{
		FPCGStreamingAsset Asset;
	};
	_Script_Aura_eventOnAssetUnloaded_Parms Parms;
	Parms.Asset=Asset;
	OnAssetUnloaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAssetUnloaded ********************************************************

// ********** Begin Delegate FOnAssetLoadFailed ****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnAssetLoadFailed_Parms
	{
		FPCGStreamingAsset Asset;
		FString ErrorMessage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Asset_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Asset;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::NewProp_Asset = { "Asset", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnAssetLoadFailed_Parms, Asset), Z_Construct_UScriptStruct_FPCGStreamingAsset, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Asset_MetaData), NewProp_Asset_MetaData) }; // 796445885
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnAssetLoadFailed_Parms, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::NewProp_Asset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::NewProp_ErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnAssetLoadFailed__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::_Script_Aura_eventOnAssetLoadFailed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::_Script_Aura_eventOnAssetLoadFailed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnAssetLoadFailed_DelegateWrapper(const FMulticastScriptDelegate& OnAssetLoadFailed, FPCGStreamingAsset const& Asset, const FString& ErrorMessage)
{
	struct _Script_Aura_eventOnAssetLoadFailed_Parms
	{
		FPCGStreamingAsset Asset;
		FString ErrorMessage;
	};
	_Script_Aura_eventOnAssetLoadFailed_Parms Parms;
	Parms.Asset=Asset;
	Parms.ErrorMessage=ErrorMessage;
	OnAssetLoadFailed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAssetLoadFailed ******************************************************

// ********** Begin Delegate FOnRegionLoaded *******************************************************
struct Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnRegionLoaded_Parms
	{
		FString RegionName;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegionName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegionName;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::NewProp_RegionName = { "RegionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnRegionLoaded_Parms, RegionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegionName_MetaData), NewProp_RegionName_MetaData) };
void Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((_Script_Aura_eventOnRegionLoaded_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_Aura_eventOnRegionLoaded_Parms), &Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::NewProp_RegionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnRegionLoaded__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::_Script_Aura_eventOnRegionLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::_Script_Aura_eventOnRegionLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRegionLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnRegionLoaded, const FString& RegionName, bool bSuccess)
{
	struct _Script_Aura_eventOnRegionLoaded_Parms
	{
		FString RegionName;
		bool bSuccess;
	};
	_Script_Aura_eventOnRegionLoaded_Parms Parms;
	Parms.RegionName=RegionName;
	Parms.bSuccess=bSuccess ? true : false;
	OnRegionLoaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRegionLoaded *********************************************************

// ********** Begin Delegate FOnRegionUnloaded *****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnRegionUnloaded_Parms
	{
		FString RegionName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegionName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegionName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature_Statics::NewProp_RegionName = { "RegionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnRegionUnloaded_Parms, RegionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegionName_MetaData), NewProp_RegionName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature_Statics::NewProp_RegionName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnRegionUnloaded__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature_Statics::_Script_Aura_eventOnRegionUnloaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature_Statics::_Script_Aura_eventOnRegionUnloaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRegionUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnRegionUnloaded, const FString& RegionName)
{
	struct _Script_Aura_eventOnRegionUnloaded_Parms
	{
		FString RegionName;
	};
	_Script_Aura_eventOnRegionUnloaded_Parms Parms;
	Parms.RegionName=RegionName;
	OnRegionUnloaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRegionUnloaded *******************************************************

// ********** Begin Class APCGStreamingManager Function AddStreamingViewer *************************
struct Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics
{
	struct PCGStreamingManager_eventAddStreamingViewer_Parms
	{
		AActor* Viewer;
		float ViewDistance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Viewers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Viewer Management\n" },
#endif
		{ "CPP_Default_ViewDistance", "5000.000000" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Viewer Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Viewer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ViewDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::NewProp_Viewer = { "Viewer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventAddStreamingViewer_Parms, Viewer), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::NewProp_ViewDistance = { "ViewDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventAddStreamingViewer_Parms, ViewDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::NewProp_Viewer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::NewProp_ViewDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "AddStreamingViewer", Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::PCGStreamingManager_eventAddStreamingViewer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::PCGStreamingManager_eventAddStreamingViewer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execAddStreamingViewer)
{
	P_GET_OBJECT(AActor,Z_Param_Viewer);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ViewDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddStreamingViewer(Z_Param_Viewer,Z_Param_ViewDistance);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function AddStreamingViewer ***************************

// ********** Begin Class APCGStreamingManager Function ClearCache *********************************
struct Z_Construct_UFunction_APCGStreamingManager_ClearCache_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_ClearCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "ClearCache", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_ClearCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_ClearCache_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGStreamingManager_ClearCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_ClearCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execClearCache)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearCache();
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function ClearCache ***********************************

// ********** Begin Class APCGStreamingManager Function CreateRegion *******************************
struct Z_Construct_UFunction_APCGStreamingManager_CreateRegion_Statics
{
	struct PCGStreamingManager_eventCreateRegion_Parms
	{
		FPCGStreamingRegion Region;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Regions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Region Management\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Region Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Region_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Region;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGStreamingManager_CreateRegion_Statics::NewProp_Region = { "Region", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventCreateRegion_Parms, Region), Z_Construct_UScriptStruct_FPCGStreamingRegion, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Region_MetaData), NewProp_Region_MetaData) }; // 4133696856
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_CreateRegion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_CreateRegion_Statics::NewProp_Region,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_CreateRegion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_CreateRegion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "CreateRegion", Z_Construct_UFunction_APCGStreamingManager_CreateRegion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_CreateRegion_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_CreateRegion_Statics::PCGStreamingManager_eventCreateRegion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_CreateRegion_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_CreateRegion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_CreateRegion_Statics::PCGStreamingManager_eventCreateRegion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_CreateRegion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_CreateRegion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execCreateRegion)
{
	P_GET_STRUCT_REF(FPCGStreamingRegion,Z_Param_Out_Region);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateRegion(Z_Param_Out_Region);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function CreateRegion *********************************

// ********** Begin Class APCGStreamingManager Function ForceLoadAsset *****************************
struct Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset_Statics
{
	struct PCGStreamingManager_eventForceLoadAsset_Parms
	{
		TSoftObjectPtr<UObject> AssetReference;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manual Control\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manual Control" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetReference_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetReference;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset_Statics::NewProp_AssetReference = { "AssetReference", nullptr, (EPropertyFlags)0x0014000008000182, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventForceLoadAsset_Parms, AssetReference), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetReference_MetaData), NewProp_AssetReference_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset_Statics::NewProp_AssetReference,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "ForceLoadAsset", Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset_Statics::PCGStreamingManager_eventForceLoadAsset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset_Statics::PCGStreamingManager_eventForceLoadAsset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execForceLoadAsset)
{
	P_GET_SOFTOBJECT_REF(TSoftObjectPtr<UObject>,Z_Param_Out_AssetReference);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceLoadAsset(Z_Param_Out_AssetReference);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function ForceLoadAsset *******************************

// ********** Begin Class APCGStreamingManager Function ForceUnloadAsset ***************************
struct Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset_Statics
{
	struct PCGStreamingManager_eventForceUnloadAsset_Parms
	{
		TSoftObjectPtr<UObject> AssetReference;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Control" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetReference_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetReference;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset_Statics::NewProp_AssetReference = { "AssetReference", nullptr, (EPropertyFlags)0x0014000008000182, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventForceUnloadAsset_Parms, AssetReference), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetReference_MetaData), NewProp_AssetReference_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset_Statics::NewProp_AssetReference,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "ForceUnloadAsset", Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset_Statics::PCGStreamingManager_eventForceUnloadAsset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset_Statics::PCGStreamingManager_eventForceUnloadAsset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execForceUnloadAsset)
{
	P_GET_SOFTOBJECT_REF(TSoftObjectPtr<UObject>,Z_Param_Out_AssetReference);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceUnloadAsset(Z_Param_Out_AssetReference);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function ForceUnloadAsset *****************************

// ********** Begin Class APCGStreamingManager Function GetActiveViewers ***************************
struct Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics
{
	struct PCGStreamingManager_eventGetActiveViewers_Parms
	{
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Viewers" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventGetActiveViewers_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "GetActiveViewers", Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::PCGStreamingManager_eventGetActiveViewers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::PCGStreamingManager_eventGetActiveViewers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execGetActiveViewers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->GetActiveViewers();
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function GetActiveViewers *****************************

// ********** Begin Class APCGStreamingManager Function GetAllAssets *******************************
struct Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics
{
	struct PCGStreamingManager_eventGetAllAssets_Parms
	{
		TArray<FPCGStreamingAsset> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Assets" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGStreamingAsset, METADATA_PARAMS(0, nullptr) }; // 796445885
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventGetAllAssets_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 796445885
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "GetAllAssets", Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::PCGStreamingManager_eventGetAllAssets_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::PCGStreamingManager_eventGetAllAssets_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_GetAllAssets()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_GetAllAssets_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execGetAllAssets)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGStreamingAsset>*)Z_Param__Result=P_THIS->GetAllAssets();
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function GetAllAssets *********************************

// ********** Begin Class APCGStreamingManager Function GetAllRegions ******************************
struct Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics
{
	struct PCGStreamingManager_eventGetAllRegions_Parms
	{
		TArray<FPCGStreamingRegion> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Regions" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGStreamingRegion, METADATA_PARAMS(0, nullptr) }; // 4133696856
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventGetAllRegions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4133696856
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "GetAllRegions", Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::PCGStreamingManager_eventGetAllRegions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::PCGStreamingManager_eventGetAllRegions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_GetAllRegions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_GetAllRegions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execGetAllRegions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGStreamingRegion>*)Z_Param__Result=P_THIS->GetAllRegions();
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function GetAllRegions ********************************

// ********** Begin Class APCGStreamingManager Function GetLoadedAsset *****************************
struct Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics
{
	struct PCGStreamingManager_eventGetLoadedAsset_Parms
	{
		TSoftObjectPtr<UObject> AssetReference;
		UObject* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Assets" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetReference_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetReference;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::NewProp_AssetReference = { "AssetReference", nullptr, (EPropertyFlags)0x0014000008000182, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventGetLoadedAsset_Parms, AssetReference), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetReference_MetaData), NewProp_AssetReference_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventGetLoadedAsset_Parms, ReturnValue), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::NewProp_AssetReference,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "GetLoadedAsset", Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::PCGStreamingManager_eventGetLoadedAsset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::PCGStreamingManager_eventGetLoadedAsset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execGetLoadedAsset)
{
	P_GET_SOFTOBJECT_REF(TSoftObjectPtr<UObject>,Z_Param_Out_AssetReference);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UObject**)Z_Param__Result=P_THIS->GetLoadedAsset(Z_Param_Out_AssetReference);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function GetLoadedAsset *******************************

// ********** Begin Class APCGStreamingManager Function GetStreamingEfficiency *********************
struct Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency_Statics
{
	struct PCGStreamingManager_eventGetStreamingEfficiency_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventGetStreamingEfficiency_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "GetStreamingEfficiency", Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency_Statics::PCGStreamingManager_eventGetStreamingEfficiency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency_Statics::PCGStreamingManager_eventGetStreamingEfficiency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execGetStreamingEfficiency)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetStreamingEfficiency();
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function GetStreamingEfficiency ***********************

// ********** Begin Class APCGStreamingManager Function GetStreamingStats **************************
struct Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats_Statics
{
	struct PCGStreamingManager_eventGetStreamingStats_Parms
	{
		FPCGStreamingStats ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance Management\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventGetStreamingStats_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGStreamingStats, METADATA_PARAMS(0, nullptr) }; // 1549786350
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "GetStreamingStats", Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats_Statics::PCGStreamingManager_eventGetStreamingStats_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats_Statics::PCGStreamingManager_eventGetStreamingStats_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execGetStreamingStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGStreamingStats*)Z_Param__Result=P_THIS->GetStreamingStats();
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function GetStreamingStats ****************************

// ********** Begin Class APCGStreamingManager Function HandleAssetLoadFailure *********************
struct Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure_Statics
{
	struct PCGStreamingManager_eventHandleAssetLoadFailure_Parms
	{
		TSoftObjectPtr<UObject> AssetReference;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetReference_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetReference;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure_Statics::NewProp_AssetReference = { "AssetReference", nullptr, (EPropertyFlags)0x0014000008000182, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventHandleAssetLoadFailure_Parms, AssetReference), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetReference_MetaData), NewProp_AssetReference_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure_Statics::NewProp_AssetReference,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "HandleAssetLoadFailure", Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure_Statics::PCGStreamingManager_eventHandleAssetLoadFailure_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure_Statics::PCGStreamingManager_eventHandleAssetLoadFailure_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execHandleAssetLoadFailure)
{
	P_GET_SOFTOBJECT_REF(TSoftObjectPtr<UObject>,Z_Param_Out_AssetReference);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HandleAssetLoadFailure(Z_Param_Out_AssetReference);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function HandleAssetLoadFailure ***********************

// ********** Begin Class APCGStreamingManager Function IntegrateWithNanite ************************
struct Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite_Statics
{
	struct PCGStreamingManager_eventIntegrateWithNanite_Parms
	{
		APCGNaniteOptimizer* NaniteOptimizer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Integration" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NaniteOptimizer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite_Statics::NewProp_NaniteOptimizer = { "NaniteOptimizer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventIntegrateWithNanite_Parms, NaniteOptimizer), Z_Construct_UClass_APCGNaniteOptimizer_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite_Statics::NewProp_NaniteOptimizer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "IntegrateWithNanite", Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite_Statics::PCGStreamingManager_eventIntegrateWithNanite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite_Statics::PCGStreamingManager_eventIntegrateWithNanite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execIntegrateWithNanite)
{
	P_GET_OBJECT(APCGNaniteOptimizer,Z_Param_NaniteOptimizer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithNanite(Z_Param_NaniteOptimizer);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function IntegrateWithNanite **************************

// ********** Begin Class APCGStreamingManager Function IntegrateWithPerformanceProfiler ***********
struct Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler_Statics
{
	struct PCGStreamingManager_eventIntegrateWithPerformanceProfiler_Parms
	{
		UPCGPerformanceProfiler* PerformanceProfiler;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Integration" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PerformanceProfiler;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler_Statics::NewProp_PerformanceProfiler = { "PerformanceProfiler", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventIntegrateWithPerformanceProfiler_Parms, PerformanceProfiler), Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler_Statics::NewProp_PerformanceProfiler,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "IntegrateWithPerformanceProfiler", Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler_Statics::PCGStreamingManager_eventIntegrateWithPerformanceProfiler_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler_Statics::PCGStreamingManager_eventIntegrateWithPerformanceProfiler_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execIntegrateWithPerformanceProfiler)
{
	P_GET_OBJECT(UPCGPerformanceProfiler,Z_Param_PerformanceProfiler);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithPerformanceProfiler(Z_Param_PerformanceProfiler);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function IntegrateWithPerformanceProfiler *************

// ********** Begin Class APCGStreamingManager Function IntegrateWithWorldPartition ****************
struct Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition_Statics
{
	struct PCGStreamingManager_eventIntegrateWithWorldPartition_Parms
	{
		APCGWorldPartitionManager* WorldPartitionManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with other systems\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with other systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartitionManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition_Statics::NewProp_WorldPartitionManager = { "WorldPartitionManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventIntegrateWithWorldPartition_Parms, WorldPartitionManager), Z_Construct_UClass_APCGWorldPartitionManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition_Statics::NewProp_WorldPartitionManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "IntegrateWithWorldPartition", Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition_Statics::PCGStreamingManager_eventIntegrateWithWorldPartition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition_Statics::PCGStreamingManager_eventIntegrateWithWorldPartition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execIntegrateWithWorldPartition)
{
	P_GET_OBJECT(APCGWorldPartitionManager,Z_Param_WorldPartitionManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithWorldPartition(Z_Param_WorldPartitionManager);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function IntegrateWithWorldPartition ******************

// ********** Begin Class APCGStreamingManager Function IsAssetLoaded ******************************
struct Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics
{
	struct PCGStreamingManager_eventIsAssetLoaded_Parms
	{
		TSoftObjectPtr<UObject> AssetReference;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Assets" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetReference_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetReference;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::NewProp_AssetReference = { "AssetReference", nullptr, (EPropertyFlags)0x0014000008000182, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventIsAssetLoaded_Parms, AssetReference), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetReference_MetaData), NewProp_AssetReference_MetaData) };
void Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGStreamingManager_eventIsAssetLoaded_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGStreamingManager_eventIsAssetLoaded_Parms), &Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::NewProp_AssetReference,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "IsAssetLoaded", Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::PCGStreamingManager_eventIsAssetLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::PCGStreamingManager_eventIsAssetLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execIsAssetLoaded)
{
	P_GET_SOFTOBJECT_REF(TSoftObjectPtr<UObject>,Z_Param_Out_AssetReference);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAssetLoaded(Z_Param_Out_AssetReference);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function IsAssetLoaded ********************************

// ********** Begin Class APCGStreamingManager Function IsRegionLoaded *****************************
struct Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics
{
	struct PCGStreamingManager_eventIsRegionLoaded_Parms
	{
		FString RegionName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Regions" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegionName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegionName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::NewProp_RegionName = { "RegionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventIsRegionLoaded_Parms, RegionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegionName_MetaData), NewProp_RegionName_MetaData) };
void Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGStreamingManager_eventIsRegionLoaded_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGStreamingManager_eventIsRegionLoaded_Parms), &Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::NewProp_RegionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "IsRegionLoaded", Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::PCGStreamingManager_eventIsRegionLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::PCGStreamingManager_eventIsRegionLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execIsRegionLoaded)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RegionName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsRegionLoaded(Z_Param_RegionName);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function IsRegionLoaded *******************************

// ********** Begin Class APCGStreamingManager Function LoadRegion *********************************
struct Z_Construct_UFunction_APCGStreamingManager_LoadRegion_Statics
{
	struct PCGStreamingManager_eventLoadRegion_Parms
	{
		FString RegionName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Regions" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegionName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegionName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGStreamingManager_LoadRegion_Statics::NewProp_RegionName = { "RegionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventLoadRegion_Parms, RegionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegionName_MetaData), NewProp_RegionName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_LoadRegion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_LoadRegion_Statics::NewProp_RegionName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_LoadRegion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_LoadRegion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "LoadRegion", Z_Construct_UFunction_APCGStreamingManager_LoadRegion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_LoadRegion_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_LoadRegion_Statics::PCGStreamingManager_eventLoadRegion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_LoadRegion_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_LoadRegion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_LoadRegion_Statics::PCGStreamingManager_eventLoadRegion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_LoadRegion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_LoadRegion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execLoadRegion)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RegionName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadRegion(Z_Param_RegionName);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function LoadRegion ***********************************

// ********** Begin Class APCGStreamingManager Function OnAssetLoadCompleted ***********************
struct Z_Construct_UFunction_APCGStreamingManager_OnAssetLoadCompleted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Event handlers\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event handlers" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_OnAssetLoadCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "OnAssetLoadCompleted", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_OnAssetLoadCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_OnAssetLoadCompleted_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGStreamingManager_OnAssetLoadCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_OnAssetLoadCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execOnAssetLoadCompleted)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnAssetLoadCompleted();
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function OnAssetLoadCompleted *************************

// ********** Begin Class APCGStreamingManager Function OptimizeMemoryUsage ************************
struct Z_Construct_UFunction_APCGStreamingManager_OptimizeMemoryUsage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_OptimizeMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "OptimizeMemoryUsage", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_OptimizeMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_OptimizeMemoryUsage_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGStreamingManager_OptimizeMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_OptimizeMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execOptimizeMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function OptimizeMemoryUsage **************************

// ********** Begin Class APCGStreamingManager Function PauseStreaming *****************************
struct Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics
{
	struct PCGStreamingManager_eventPauseStreaming_Parms
	{
		bool bPause;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Control" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bPause_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPause;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::NewProp_bPause_SetBit(void* Obj)
{
	((PCGStreamingManager_eventPauseStreaming_Parms*)Obj)->bPause = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::NewProp_bPause = { "bPause", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGStreamingManager_eventPauseStreaming_Parms), &Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::NewProp_bPause_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::NewProp_bPause,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "PauseStreaming", Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::PCGStreamingManager_eventPauseStreaming_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::PCGStreamingManager_eventPauseStreaming_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_PauseStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_PauseStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execPauseStreaming)
{
	P_GET_UBOOL(Z_Param_bPause);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PauseStreaming(Z_Param_bPause);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function PauseStreaming *******************************

// ********** Begin Class APCGStreamingManager Function PreloadAssetsInRadius **********************
struct Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics
{
	struct PCGStreamingManager_eventPreloadAssetsInRadius_Parms
	{
		FVector Center;
		float Radius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Control" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventPreloadAssetsInRadius_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventPreloadAssetsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::NewProp_Radius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "PreloadAssetsInRadius", Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::PCGStreamingManager_eventPreloadAssetsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::PCGStreamingManager_eventPreloadAssetsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execPreloadAssetsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PreloadAssetsInRadius(Z_Param_Out_Center,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function PreloadAssetsInRadius ************************

// ********** Begin Class APCGStreamingManager Function RegisterAsset ******************************
struct Z_Construct_UFunction_APCGStreamingManager_RegisterAsset_Statics
{
	struct PCGStreamingManager_eventRegisterAsset_Parms
	{
		FPCGStreamingAsset Asset;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Assets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Asset Management\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Asset Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Asset_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Asset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGStreamingManager_RegisterAsset_Statics::NewProp_Asset = { "Asset", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventRegisterAsset_Parms, Asset), Z_Construct_UScriptStruct_FPCGStreamingAsset, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Asset_MetaData), NewProp_Asset_MetaData) }; // 796445885
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_RegisterAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_RegisterAsset_Statics::NewProp_Asset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_RegisterAsset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_RegisterAsset_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "RegisterAsset", Z_Construct_UFunction_APCGStreamingManager_RegisterAsset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_RegisterAsset_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_RegisterAsset_Statics::PCGStreamingManager_eventRegisterAsset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_RegisterAsset_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_RegisterAsset_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_RegisterAsset_Statics::PCGStreamingManager_eventRegisterAsset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_RegisterAsset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_RegisterAsset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execRegisterAsset)
{
	P_GET_STRUCT_REF(FPCGStreamingAsset,Z_Param_Out_Asset);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterAsset(Z_Param_Out_Asset);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function RegisterAsset ********************************

// ********** Begin Class APCGStreamingManager Function RemoveRegion *******************************
struct Z_Construct_UFunction_APCGStreamingManager_RemoveRegion_Statics
{
	struct PCGStreamingManager_eventRemoveRegion_Parms
	{
		FString RegionName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Regions" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegionName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegionName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGStreamingManager_RemoveRegion_Statics::NewProp_RegionName = { "RegionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventRemoveRegion_Parms, RegionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegionName_MetaData), NewProp_RegionName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_RemoveRegion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_RemoveRegion_Statics::NewProp_RegionName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_RemoveRegion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_RemoveRegion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "RemoveRegion", Z_Construct_UFunction_APCGStreamingManager_RemoveRegion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_RemoveRegion_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_RemoveRegion_Statics::PCGStreamingManager_eventRemoveRegion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_RemoveRegion_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_RemoveRegion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_RemoveRegion_Statics::PCGStreamingManager_eventRemoveRegion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_RemoveRegion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_RemoveRegion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execRemoveRegion)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RegionName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveRegion(Z_Param_RegionName);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function RemoveRegion *********************************

// ********** Begin Class APCGStreamingManager Function RemoveStreamingViewer **********************
struct Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer_Statics
{
	struct PCGStreamingManager_eventRemoveStreamingViewer_Parms
	{
		AActor* Viewer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Viewers" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Viewer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer_Statics::NewProp_Viewer = { "Viewer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventRemoveStreamingViewer_Parms, Viewer), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer_Statics::NewProp_Viewer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "RemoveStreamingViewer", Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer_Statics::PCGStreamingManager_eventRemoveStreamingViewer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer_Statics::PCGStreamingManager_eventRemoveStreamingViewer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execRemoveStreamingViewer)
{
	P_GET_OBJECT(AActor,Z_Param_Viewer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveStreamingViewer(Z_Param_Viewer);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function RemoveStreamingViewer ************************

// ********** Begin Class APCGStreamingManager Function SetAssetPriority ***************************
struct Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics
{
	struct PCGStreamingManager_eventSetAssetPriority_Parms
	{
		TSoftObjectPtr<UObject> AssetReference;
		EPCGStreamingManagerPriority Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Assets" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetReference_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetReference;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::NewProp_AssetReference = { "AssetReference", nullptr, (EPropertyFlags)0x0014000008000182, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventSetAssetPriority_Parms, AssetReference), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetReference_MetaData), NewProp_AssetReference_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventSetAssetPriority_Parms, Priority), Z_Construct_UEnum_Aura_EPCGStreamingManagerPriority, METADATA_PARAMS(0, nullptr) }; // 3867279043
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::NewProp_AssetReference,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "SetAssetPriority", Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::PCGStreamingManager_eventSetAssetPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::PCGStreamingManager_eventSetAssetPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execSetAssetPriority)
{
	P_GET_SOFTOBJECT_REF(TSoftObjectPtr<UObject>,Z_Param_Out_AssetReference);
	P_GET_ENUM(EPCGStreamingManagerPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAssetPriority(Z_Param_Out_AssetReference,EPCGStreamingManagerPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function SetAssetPriority *****************************

// ********** Begin Class APCGStreamingManager Function SetMaxConcurrentLoads **********************
struct Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads_Statics
{
	struct PCGStreamingManager_eventSetMaxConcurrentLoads_Parms
	{
		int32 MaxLoads;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLoads;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads_Statics::NewProp_MaxLoads = { "MaxLoads", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventSetMaxConcurrentLoads_Parms, MaxLoads), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads_Statics::NewProp_MaxLoads,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "SetMaxConcurrentLoads", Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads_Statics::PCGStreamingManager_eventSetMaxConcurrentLoads_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads_Statics::PCGStreamingManager_eventSetMaxConcurrentLoads_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execSetMaxConcurrentLoads)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxLoads);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMaxConcurrentLoads(Z_Param_MaxLoads);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function SetMaxConcurrentLoads ************************

// ********** Begin Class APCGStreamingManager Function SetMaxMemoryUsage **************************
struct Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage_Statics
{
	struct PCGStreamingManager_eventSetMaxMemoryUsage_Parms
	{
		int32 MaxMemoryMB;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxMemoryMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage_Statics::NewProp_MaxMemoryMB = { "MaxMemoryMB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventSetMaxMemoryUsage_Parms, MaxMemoryMB), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage_Statics::NewProp_MaxMemoryMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "SetMaxMemoryUsage", Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage_Statics::PCGStreamingManager_eventSetMaxMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage_Statics::PCGStreamingManager_eventSetMaxMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execSetMaxMemoryUsage)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxMemoryMB);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMaxMemoryUsage(Z_Param_MaxMemoryMB);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function SetMaxMemoryUsage ****************************

// ********** Begin Class APCGStreamingManager Function SetStreamingMode ***************************
struct Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics
{
	struct PCGStreamingManager_eventSetStreamingMode_Parms
	{
		EPCGStreamingMode Mode;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Control" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Mode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Mode;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::NewProp_Mode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::NewProp_Mode = { "Mode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventSetStreamingMode_Parms, Mode), Z_Construct_UEnum_Aura_EPCGStreamingMode, METADATA_PARAMS(0, nullptr) }; // 894006293
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::NewProp_Mode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::NewProp_Mode,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "SetStreamingMode", Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::PCGStreamingManager_eventSetStreamingMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::PCGStreamingManager_eventSetStreamingMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execSetStreamingMode)
{
	P_GET_ENUM(EPCGStreamingMode,Z_Param_Mode);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetStreamingMode(EPCGStreamingMode(Z_Param_Mode));
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function SetStreamingMode *****************************

// ********** Begin Class APCGStreamingManager Function SynchronizeWithPCGSystem *******************
struct Z_Construct_UFunction_APCGStreamingManager_SynchronizeWithPCGSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Integration" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_SynchronizeWithPCGSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "SynchronizeWithPCGSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_SynchronizeWithPCGSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_SynchronizeWithPCGSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_APCGStreamingManager_SynchronizeWithPCGSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_SynchronizeWithPCGSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execSynchronizeWithPCGSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SynchronizeWithPCGSystem();
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function SynchronizeWithPCGSystem *********************

// ********** Begin Class APCGStreamingManager Function UnloadAssetsOutsideRadius ******************
struct Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics
{
	struct PCGStreamingManager_eventUnloadAssetsOutsideRadius_Parms
	{
		FVector Center;
		float Radius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Control" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventUnloadAssetsOutsideRadius_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventUnloadAssetsOutsideRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::NewProp_Radius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "UnloadAssetsOutsideRadius", Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::PCGStreamingManager_eventUnloadAssetsOutsideRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::PCGStreamingManager_eventUnloadAssetsOutsideRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execUnloadAssetsOutsideRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnloadAssetsOutsideRadius(Z_Param_Out_Center,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function UnloadAssetsOutsideRadius ********************

// ********** Begin Class APCGStreamingManager Function UnloadRegion *******************************
struct Z_Construct_UFunction_APCGStreamingManager_UnloadRegion_Statics
{
	struct PCGStreamingManager_eventUnloadRegion_Parms
	{
		FString RegionName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Regions" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegionName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegionName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGStreamingManager_UnloadRegion_Statics::NewProp_RegionName = { "RegionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventUnloadRegion_Parms, RegionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegionName_MetaData), NewProp_RegionName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_UnloadRegion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_UnloadRegion_Statics::NewProp_RegionName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UnloadRegion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_UnloadRegion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "UnloadRegion", Z_Construct_UFunction_APCGStreamingManager_UnloadRegion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UnloadRegion_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_UnloadRegion_Statics::PCGStreamingManager_eventUnloadRegion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UnloadRegion_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_UnloadRegion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_UnloadRegion_Statics::PCGStreamingManager_eventUnloadRegion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_UnloadRegion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_UnloadRegion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execUnloadRegion)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RegionName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnloadRegion(Z_Param_RegionName);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function UnloadRegion *********************************

// ********** Begin Class APCGStreamingManager Function UnregisterAsset ****************************
struct Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset_Statics
{
	struct PCGStreamingManager_eventUnregisterAsset_Parms
	{
		TSoftObjectPtr<UObject> AssetReference;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Assets" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetReference_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetReference;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset_Statics::NewProp_AssetReference = { "AssetReference", nullptr, (EPropertyFlags)0x0014000008000182, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventUnregisterAsset_Parms, AssetReference), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetReference_MetaData), NewProp_AssetReference_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset_Statics::NewProp_AssetReference,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "UnregisterAsset", Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset_Statics::PCGStreamingManager_eventUnregisterAsset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset_Statics::PCGStreamingManager_eventUnregisterAsset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execUnregisterAsset)
{
	P_GET_SOFTOBJECT_REF(TSoftObjectPtr<UObject>,Z_Param_Out_AssetReference);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterAsset(Z_Param_Out_AssetReference);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function UnregisterAsset ******************************

// ********** Begin Class APCGStreamingManager Function UpdateAssetPosition ************************
struct Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics
{
	struct PCGStreamingManager_eventUpdateAssetPosition_Parms
	{
		TSoftObjectPtr<UObject> AssetReference;
		FVector NewPosition;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Assets" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetReference_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetReference;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewPosition;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::NewProp_AssetReference = { "AssetReference", nullptr, (EPropertyFlags)0x0014000008000182, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventUpdateAssetPosition_Parms, AssetReference), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetReference_MetaData), NewProp_AssetReference_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::NewProp_NewPosition = { "NewPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventUpdateAssetPosition_Parms, NewPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewPosition_MetaData), NewProp_NewPosition_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::NewProp_AssetReference,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::NewProp_NewPosition,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "UpdateAssetPosition", Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::PCGStreamingManager_eventUpdateAssetPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::PCGStreamingManager_eventUpdateAssetPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execUpdateAssetPosition)
{
	P_GET_SOFTOBJECT_REF(TSoftObjectPtr<UObject>,Z_Param_Out_AssetReference);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_NewPosition);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAssetPosition(Z_Param_Out_AssetReference,Z_Param_Out_NewPosition);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function UpdateAssetPosition **************************

// ********** Begin Class APCGStreamingManager Function UpdateRegion *******************************
struct Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics
{
	struct PCGStreamingManager_eventUpdateRegion_Parms
	{
		FString RegionName;
		FPCGStreamingRegion UpdatedRegion;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Regions" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegionName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdatedRegion_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegionName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UpdatedRegion;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::NewProp_RegionName = { "RegionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventUpdateRegion_Parms, RegionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegionName_MetaData), NewProp_RegionName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::NewProp_UpdatedRegion = { "UpdatedRegion", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventUpdateRegion_Parms, UpdatedRegion), Z_Construct_UScriptStruct_FPCGStreamingRegion, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdatedRegion_MetaData), NewProp_UpdatedRegion_MetaData) }; // 4133696856
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::NewProp_RegionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::NewProp_UpdatedRegion,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "UpdateRegion", Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::PCGStreamingManager_eventUpdateRegion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::PCGStreamingManager_eventUpdateRegion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_UpdateRegion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_UpdateRegion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execUpdateRegion)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RegionName);
	P_GET_STRUCT_REF(FPCGStreamingRegion,Z_Param_Out_UpdatedRegion);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateRegion(Z_Param_RegionName,Z_Param_Out_UpdatedRegion);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function UpdateRegion *********************************

// ********** Begin Class APCGStreamingManager Function UpdateViewerPosition ***********************
struct Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics
{
	struct PCGStreamingManager_eventUpdateViewerPosition_Parms
	{
		AActor* Viewer;
		FVector Position;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming Viewers" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Viewer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::NewProp_Viewer = { "Viewer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventUpdateViewerPosition_Parms, Viewer), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGStreamingManager_eventUpdateViewerPosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::NewProp_Viewer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::NewProp_Position,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_APCGStreamingManager, nullptr, "UpdateViewerPosition", Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::PCGStreamingManager_eventUpdateViewerPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::PCGStreamingManager_eventUpdateViewerPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APCGStreamingManager::execUpdateViewerPosition)
{
	P_GET_OBJECT(AActor,Z_Param_Viewer);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateViewerPosition(Z_Param_Viewer,Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class APCGStreamingManager Function UpdateViewerPosition *************************

// ********** Begin Class APCGStreamingManager *****************************************************
void APCGStreamingManager::StaticRegisterNativesAPCGStreamingManager()
{
	UClass* Class = APCGStreamingManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddStreamingViewer", &APCGStreamingManager::execAddStreamingViewer },
		{ "ClearCache", &APCGStreamingManager::execClearCache },
		{ "CreateRegion", &APCGStreamingManager::execCreateRegion },
		{ "ForceLoadAsset", &APCGStreamingManager::execForceLoadAsset },
		{ "ForceUnloadAsset", &APCGStreamingManager::execForceUnloadAsset },
		{ "GetActiveViewers", &APCGStreamingManager::execGetActiveViewers },
		{ "GetAllAssets", &APCGStreamingManager::execGetAllAssets },
		{ "GetAllRegions", &APCGStreamingManager::execGetAllRegions },
		{ "GetLoadedAsset", &APCGStreamingManager::execGetLoadedAsset },
		{ "GetStreamingEfficiency", &APCGStreamingManager::execGetStreamingEfficiency },
		{ "GetStreamingStats", &APCGStreamingManager::execGetStreamingStats },
		{ "HandleAssetLoadFailure", &APCGStreamingManager::execHandleAssetLoadFailure },
		{ "IntegrateWithNanite", &APCGStreamingManager::execIntegrateWithNanite },
		{ "IntegrateWithPerformanceProfiler", &APCGStreamingManager::execIntegrateWithPerformanceProfiler },
		{ "IntegrateWithWorldPartition", &APCGStreamingManager::execIntegrateWithWorldPartition },
		{ "IsAssetLoaded", &APCGStreamingManager::execIsAssetLoaded },
		{ "IsRegionLoaded", &APCGStreamingManager::execIsRegionLoaded },
		{ "LoadRegion", &APCGStreamingManager::execLoadRegion },
		{ "OnAssetLoadCompleted", &APCGStreamingManager::execOnAssetLoadCompleted },
		{ "OptimizeMemoryUsage", &APCGStreamingManager::execOptimizeMemoryUsage },
		{ "PauseStreaming", &APCGStreamingManager::execPauseStreaming },
		{ "PreloadAssetsInRadius", &APCGStreamingManager::execPreloadAssetsInRadius },
		{ "RegisterAsset", &APCGStreamingManager::execRegisterAsset },
		{ "RemoveRegion", &APCGStreamingManager::execRemoveRegion },
		{ "RemoveStreamingViewer", &APCGStreamingManager::execRemoveStreamingViewer },
		{ "SetAssetPriority", &APCGStreamingManager::execSetAssetPriority },
		{ "SetMaxConcurrentLoads", &APCGStreamingManager::execSetMaxConcurrentLoads },
		{ "SetMaxMemoryUsage", &APCGStreamingManager::execSetMaxMemoryUsage },
		{ "SetStreamingMode", &APCGStreamingManager::execSetStreamingMode },
		{ "SynchronizeWithPCGSystem", &APCGStreamingManager::execSynchronizeWithPCGSystem },
		{ "UnloadAssetsOutsideRadius", &APCGStreamingManager::execUnloadAssetsOutsideRadius },
		{ "UnloadRegion", &APCGStreamingManager::execUnloadRegion },
		{ "UnregisterAsset", &APCGStreamingManager::execUnregisterAsset },
		{ "UpdateAssetPosition", &APCGStreamingManager::execUpdateAssetPosition },
		{ "UpdateRegion", &APCGStreamingManager::execUpdateRegion },
		{ "UpdateViewerPosition", &APCGStreamingManager::execUpdateViewerPosition },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_APCGStreamingManager;
UClass* APCGStreamingManager::GetPrivateStaticClass()
{
	using TClass = APCGStreamingManager;
	if (!Z_Registration_Info_UClass_APCGStreamingManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PCGStreamingManager"),
			Z_Registration_Info_UClass_APCGStreamingManager.InnerSingleton,
			StaticRegisterNativesAPCGStreamingManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_APCGStreamingManager.InnerSingleton;
}
UClass* Z_Construct_UClass_APCGStreamingManager_NoRegister()
{
	return APCGStreamingManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_APCGStreamingManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * APCGStreamingManager - Advanced streaming manager for procedural content generation\n * Provides intelligent asset streaming, memory management, and performance optimization\n * Optimized for UE5.6 with modern asset management and World Partition integration\n */" },
#endif
		{ "IncludePath", "APCGStreamingManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "APCGStreamingManager - Advanced streaming manager for procedural content generation\nProvides intelligent asset streaming, memory management, and performance optimization\nOptimized for UE5.6 with modern asset management and World Partition integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingConfig_MetaData[] = {
		{ "Category", "Streaming Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceMonitoring_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoOptimization_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDebugVisualization_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAssetLoaded_MetaData[] = {
		{ "Category", "Streaming Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAssetUnloaded_MetaData[] = {
		{ "Category", "Streaming Events" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAssetLoadFailed_MetaData[] = {
		{ "Category", "Streaming Events" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRegionLoaded_MetaData[] = {
		{ "Category", "Streaming Events" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRegionUnloaded_MetaData[] = {
		{ "Category", "Streaming Events" },
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredAssets_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal data\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingRegions_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingViewers_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentStats_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPartitionManagerRef_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration references\n" },
#endif
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration references" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteOptimizerRef_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceProfilerRef_MetaData[] = {
		{ "ModuleRelativePath", "Public/APCGStreamingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingConfig;
	static void NewProp_bEnablePerformanceMonitoring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceMonitoring;
	static void NewProp_bAutoOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoOptimization;
	static void NewProp_bShowDebugVisualization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDebugVisualization;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAssetLoaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAssetUnloaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAssetLoadFailed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRegionLoaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRegionUnloaded;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredAssets_ValueProp;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_RegisteredAssets_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RegisteredAssets;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingRegions_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StreamingRegions_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_StreamingRegions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingViewers_ValueProp;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_StreamingViewers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_StreamingViewers;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentStats;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_WorldPartitionManagerRef;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_NaniteOptimizerRef;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_PerformanceProfilerRef;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_APCGStreamingManager_AddStreamingViewer, "AddStreamingViewer" }, // 3439054988
		{ &Z_Construct_UFunction_APCGStreamingManager_ClearCache, "ClearCache" }, // 3552015628
		{ &Z_Construct_UFunction_APCGStreamingManager_CreateRegion, "CreateRegion" }, // 3022165626
		{ &Z_Construct_UFunction_APCGStreamingManager_ForceLoadAsset, "ForceLoadAsset" }, // 190236636
		{ &Z_Construct_UFunction_APCGStreamingManager_ForceUnloadAsset, "ForceUnloadAsset" }, // 4074799755
		{ &Z_Construct_UFunction_APCGStreamingManager_GetActiveViewers, "GetActiveViewers" }, // 3277628292
		{ &Z_Construct_UFunction_APCGStreamingManager_GetAllAssets, "GetAllAssets" }, // 2236077280
		{ &Z_Construct_UFunction_APCGStreamingManager_GetAllRegions, "GetAllRegions" }, // 1508829407
		{ &Z_Construct_UFunction_APCGStreamingManager_GetLoadedAsset, "GetLoadedAsset" }, // 529190317
		{ &Z_Construct_UFunction_APCGStreamingManager_GetStreamingEfficiency, "GetStreamingEfficiency" }, // 3018592967
		{ &Z_Construct_UFunction_APCGStreamingManager_GetStreamingStats, "GetStreamingStats" }, // 1584752755
		{ &Z_Construct_UFunction_APCGStreamingManager_HandleAssetLoadFailure, "HandleAssetLoadFailure" }, // 3594257
		{ &Z_Construct_UFunction_APCGStreamingManager_IntegrateWithNanite, "IntegrateWithNanite" }, // 4269628639
		{ &Z_Construct_UFunction_APCGStreamingManager_IntegrateWithPerformanceProfiler, "IntegrateWithPerformanceProfiler" }, // 3328283181
		{ &Z_Construct_UFunction_APCGStreamingManager_IntegrateWithWorldPartition, "IntegrateWithWorldPartition" }, // 1048867877
		{ &Z_Construct_UFunction_APCGStreamingManager_IsAssetLoaded, "IsAssetLoaded" }, // 3977518020
		{ &Z_Construct_UFunction_APCGStreamingManager_IsRegionLoaded, "IsRegionLoaded" }, // 3817186562
		{ &Z_Construct_UFunction_APCGStreamingManager_LoadRegion, "LoadRegion" }, // 798455606
		{ &Z_Construct_UFunction_APCGStreamingManager_OnAssetLoadCompleted, "OnAssetLoadCompleted" }, // 2558996132
		{ &Z_Construct_UFunction_APCGStreamingManager_OptimizeMemoryUsage, "OptimizeMemoryUsage" }, // 1369782166
		{ &Z_Construct_UFunction_APCGStreamingManager_PauseStreaming, "PauseStreaming" }, // 3932366240
		{ &Z_Construct_UFunction_APCGStreamingManager_PreloadAssetsInRadius, "PreloadAssetsInRadius" }, // 1881731070
		{ &Z_Construct_UFunction_APCGStreamingManager_RegisterAsset, "RegisterAsset" }, // 1379416160
		{ &Z_Construct_UFunction_APCGStreamingManager_RemoveRegion, "RemoveRegion" }, // 940783211
		{ &Z_Construct_UFunction_APCGStreamingManager_RemoveStreamingViewer, "RemoveStreamingViewer" }, // 1702387420
		{ &Z_Construct_UFunction_APCGStreamingManager_SetAssetPriority, "SetAssetPriority" }, // 192411060
		{ &Z_Construct_UFunction_APCGStreamingManager_SetMaxConcurrentLoads, "SetMaxConcurrentLoads" }, // 3911866051
		{ &Z_Construct_UFunction_APCGStreamingManager_SetMaxMemoryUsage, "SetMaxMemoryUsage" }, // 2749848419
		{ &Z_Construct_UFunction_APCGStreamingManager_SetStreamingMode, "SetStreamingMode" }, // 3675272274
		{ &Z_Construct_UFunction_APCGStreamingManager_SynchronizeWithPCGSystem, "SynchronizeWithPCGSystem" }, // 2260412010
		{ &Z_Construct_UFunction_APCGStreamingManager_UnloadAssetsOutsideRadius, "UnloadAssetsOutsideRadius" }, // 3212521024
		{ &Z_Construct_UFunction_APCGStreamingManager_UnloadRegion, "UnloadRegion" }, // 137725551
		{ &Z_Construct_UFunction_APCGStreamingManager_UnregisterAsset, "UnregisterAsset" }, // 648986065
		{ &Z_Construct_UFunction_APCGStreamingManager_UpdateAssetPosition, "UpdateAssetPosition" }, // 766053571
		{ &Z_Construct_UFunction_APCGStreamingManager_UpdateRegion, "UpdateRegion" }, // 1781254536
		{ &Z_Construct_UFunction_APCGStreamingManager_UpdateViewerPosition, "UpdateViewerPosition" }, // 3197587140
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<APCGStreamingManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingConfig = { "StreamingConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGStreamingManager, StreamingConfig), Z_Construct_UScriptStruct_FPCGStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingConfig_MetaData), NewProp_StreamingConfig_MetaData) }; // 947800899
void Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_bEnablePerformanceMonitoring_SetBit(void* Obj)
{
	((APCGStreamingManager*)Obj)->bEnablePerformanceMonitoring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_bEnablePerformanceMonitoring = { "bEnablePerformanceMonitoring", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGStreamingManager), &Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_bEnablePerformanceMonitoring_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceMonitoring_MetaData), NewProp_bEnablePerformanceMonitoring_MetaData) };
void Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_bAutoOptimization_SetBit(void* Obj)
{
	((APCGStreamingManager*)Obj)->bAutoOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_bAutoOptimization = { "bAutoOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGStreamingManager), &Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_bAutoOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoOptimization_MetaData), NewProp_bAutoOptimization_MetaData) };
void Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_bShowDebugVisualization_SetBit(void* Obj)
{
	((APCGStreamingManager*)Obj)->bShowDebugVisualization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_bShowDebugVisualization = { "bShowDebugVisualization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APCGStreamingManager), &Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_bShowDebugVisualization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDebugVisualization_MetaData), NewProp_bShowDebugVisualization_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_OnAssetLoaded = { "OnAssetLoaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGStreamingManager, OnAssetLoaded), Z_Construct_UDelegateFunction_Aura_OnAssetLoaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAssetLoaded_MetaData), NewProp_OnAssetLoaded_MetaData) }; // 777413579
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_OnAssetUnloaded = { "OnAssetUnloaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGStreamingManager, OnAssetUnloaded), Z_Construct_UDelegateFunction_Aura_OnAssetUnloaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAssetUnloaded_MetaData), NewProp_OnAssetUnloaded_MetaData) }; // 1449760010
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_OnAssetLoadFailed = { "OnAssetLoadFailed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGStreamingManager, OnAssetLoadFailed), Z_Construct_UDelegateFunction_Aura_OnAssetLoadFailed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAssetLoadFailed_MetaData), NewProp_OnAssetLoadFailed_MetaData) }; // 1368817031
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_OnRegionLoaded = { "OnRegionLoaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGStreamingManager, OnRegionLoaded), Z_Construct_UDelegateFunction_Aura_OnRegionLoaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRegionLoaded_MetaData), NewProp_OnRegionLoaded_MetaData) }; // 1119780632
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_OnRegionUnloaded = { "OnRegionUnloaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGStreamingManager, OnRegionUnloaded), Z_Construct_UDelegateFunction_Aura_OnRegionUnloaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRegionUnloaded_MetaData), NewProp_OnRegionUnloaded_MetaData) }; // 319181001
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_RegisteredAssets_ValueProp = { "RegisteredAssets", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPCGStreamingAsset, METADATA_PARAMS(0, nullptr) }; // 796445885
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_RegisteredAssets_Key_KeyProp = { "RegisteredAssets_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_RegisteredAssets = { "RegisteredAssets", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGStreamingManager, RegisteredAssets), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredAssets_MetaData), NewProp_RegisteredAssets_MetaData) }; // 796445885
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingRegions_ValueProp = { "StreamingRegions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPCGStreamingRegion, METADATA_PARAMS(0, nullptr) }; // 4133696856
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingRegions_Key_KeyProp = { "StreamingRegions_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingRegions = { "StreamingRegions", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGStreamingManager, StreamingRegions), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingRegions_MetaData), NewProp_StreamingRegions_MetaData) }; // 4133696856
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingViewers_ValueProp = { "StreamingViewers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingViewers_Key_KeyProp = { "StreamingViewers_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingViewers = { "StreamingViewers", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGStreamingManager, StreamingViewers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingViewers_MetaData), NewProp_StreamingViewers_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_CurrentStats = { "CurrentStats", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGStreamingManager, CurrentStats), Z_Construct_UScriptStruct_FPCGStreamingStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentStats_MetaData), NewProp_CurrentStats_MetaData) }; // 1549786350
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_WorldPartitionManagerRef = { "WorldPartitionManagerRef", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGStreamingManager, WorldPartitionManagerRef), Z_Construct_UClass_APCGWorldPartitionManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPartitionManagerRef_MetaData), NewProp_WorldPartitionManagerRef_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_NaniteOptimizerRef = { "NaniteOptimizerRef", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGStreamingManager, NaniteOptimizerRef), Z_Construct_UClass_APCGNaniteOptimizer_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteOptimizerRef_MetaData), NewProp_NaniteOptimizerRef_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_PerformanceProfilerRef = { "PerformanceProfilerRef", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APCGStreamingManager, PerformanceProfilerRef), Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceProfilerRef_MetaData), NewProp_PerformanceProfilerRef_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_APCGStreamingManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_bEnablePerformanceMonitoring,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_bAutoOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_bShowDebugVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_OnAssetLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_OnAssetUnloaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_OnAssetLoadFailed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_OnRegionLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_OnRegionUnloaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_RegisteredAssets_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_RegisteredAssets_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_RegisteredAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingRegions_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingRegions_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingRegions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingViewers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingViewers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_StreamingViewers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_CurrentStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_WorldPartitionManagerRef,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_NaniteOptimizerRef,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APCGStreamingManager_Statics::NewProp_PerformanceProfilerRef,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APCGStreamingManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_APCGStreamingManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APCGStreamingManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_APCGStreamingManager_Statics::ClassParams = {
	&APCGStreamingManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_APCGStreamingManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_APCGStreamingManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_APCGStreamingManager_Statics::Class_MetaDataParams), Z_Construct_UClass_APCGStreamingManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_APCGStreamingManager()
{
	if (!Z_Registration_Info_UClass_APCGStreamingManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_APCGStreamingManager.OuterSingleton, Z_Construct_UClass_APCGStreamingManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_APCGStreamingManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(APCGStreamingManager);
APCGStreamingManager::~APCGStreamingManager() {}
// ********** End Class APCGStreamingManager *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGStreamingManager_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPCGStreamingManagerPriority_StaticEnum, TEXT("EPCGStreamingManagerPriority"), &Z_Registration_Info_UEnum_EPCGStreamingManagerPriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3867279043U) },
		{ EPCGStreamingState_StaticEnum, TEXT("EPCGStreamingState"), &Z_Registration_Info_UEnum_EPCGStreamingState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 453959094U) },
		{ EPCGStreamingMode_StaticEnum, TEXT("EPCGStreamingMode"), &Z_Registration_Info_UEnum_EPCGStreamingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 894006293U) },
		{ EPCGStreamingAssetType_StaticEnum, TEXT("EPCGStreamingAssetType"), &Z_Registration_Info_UEnum_EPCGStreamingAssetType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3126784552U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPCGStreamingConfig::StaticStruct, Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics::NewStructOps, TEXT("PCGStreamingConfig"), &Z_Registration_Info_UScriptStruct_FPCGStreamingConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGStreamingConfig), 947800899U) },
		{ FPCGStreamingAsset::StaticStruct, Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics::NewStructOps, TEXT("PCGStreamingAsset"), &Z_Registration_Info_UScriptStruct_FPCGStreamingAsset, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGStreamingAsset), 796445885U) },
		{ FPCGStreamingRegion::StaticStruct, Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics::NewStructOps, TEXT("PCGStreamingRegion"), &Z_Registration_Info_UScriptStruct_FPCGStreamingRegion, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGStreamingRegion), 4133696856U) },
		{ FPCGStreamingStats::StaticStruct, Z_Construct_UScriptStruct_FPCGStreamingStats_Statics::NewStructOps, TEXT("PCGStreamingStats"), &Z_Registration_Info_UScriptStruct_FPCGStreamingStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGStreamingStats), 1549786350U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_APCGStreamingManager, APCGStreamingManager::StaticClass, TEXT("APCGStreamingManager"), &Z_Registration_Info_UClass_APCGStreamingManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(APCGStreamingManager), 1636473330U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGStreamingManager_h__Script_Aura_1052571829(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGStreamingManager_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGStreamingManager_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGStreamingManager_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGStreamingManager_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGStreamingManager_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_APCGStreamingManager_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
