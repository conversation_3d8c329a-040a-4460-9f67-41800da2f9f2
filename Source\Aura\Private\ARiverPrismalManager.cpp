#include "ARiverPrismalManager.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SplineComponent.h"
#include "UObject/ConstructorHelpers.h"
#include "Materials/MaterialInterface.h"
#include "DrawDebugHelpers.h"
#include "TimerManager.h"
#include "Kismet/KismetMathLibrary.h"
#include "ProceduralMeshComponent.h"
#include "KismetProceduralMeshLibrary.h"

ARiverPrismalManager::ARiverPrismalManager()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.TickInterval = 0.016f; // 60 FPS

    // Configurar componente raiz
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    RootComponent = RootSceneComponent;

    // Configurar spline do rio
    RiverSpline = CreateDefaultSubobject<USplineComponent>(TEXT("RiverSpline"));
    RiverSpline->SetupAttachment(RootComponent);
    RiverSpline->SetClosedLoop(false);
    RiverSpline->SetSplinePointType(0, ESplinePointType::CurveClamped);

    // Configurar mesh da ilha
    IslandMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("IslandMesh"));
    IslandMesh->SetupAttachment(RootComponent);
    IslandMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    IslandMesh->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);

    // Configurar colisão da água
    WaterCollisionBox = CreateDefaultSubobject<UBoxComponent>(TEXT("WaterCollisionBox"));
    WaterCollisionBox->SetupAttachment(RootComponent);
    WaterCollisionBox->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    WaterCollisionBox->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
    WaterCollisionBox->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Overlap);
    WaterCollisionBox->SetBoxExtent(FVector(RIVER_LENGTH * 0.6f, RIVER_WIDTH * 0.6f, RIVER_DEPTH));

    // Inicializar configurações padrão
    RiverLength = RIVER_LENGTH;
    RiverWidth = RIVER_WIDTH;
    RiverDepth = RIVER_DEPTH;
    SinusoidalAmplitude = RIVER_AMPLITUDE;
    SinusoidalFrequency = RIVER_FREQUENCY;
    RiverSegments.Reserve(RIVER_SEGMENTS); // Reservar espaço para os segmentos
    FlowDirection = ERiverFlowDirection::Eastward;
    bGenerateIsland = true;
    bIsInitialized = false;
    LastUpdateTime = 0.0f;

    // Configurar ilha central
    CentralIsland.CenterPosition = FVector::ZeroVector;
    CentralIsland.Radius = ISLAND_RADIUS;
    CentralIsland.Height = ISLAND_HEIGHT;
    CentralIsland.IslandType = EIslandType::Hexagonal;
    CentralIsland.bHasVegetation = true;
    CentralIsland.bIsAccessible = false;

    // Configurar propriedades da água
    WaterConfig.Quality = EWaterQuality::Clear;
    WaterConfig.Temperature = 20.0f;
    WaterConfig.Viscosity = 1.0f;
    WaterConfig.WaterColor = FLinearColor(0.0f, 0.4f, 0.8f, 0.8f);
    WaterConfig.Transparency = 0.8f;
    WaterConfig.RefractionIndex = 1.33f;
    WaterConfig.bHasCurrent = true;
    WaterConfig.bIsSwimmable = true;
    WaterConfig.bCausesDamage = false;

    // Configurações de debug
    bShowDebugLines = false;
    bShowFlowVectors = false;
    bShowIslandGeometry = false;
    bEnableCollisionLogging = false;

    // Tentar carregar materiais padrão
    static ConstructorHelpers::FObjectFinder<UMaterialInterface> WaterMat(TEXT("/Engine/EngineMaterials/Water"));
    if (WaterMat.Succeeded())
    {
        WaterMaterial = WaterMat.Object;
    }

    static ConstructorHelpers::FObjectFinder<UMaterialInterface> LandscapeMat(TEXT("/Engine/EngineMaterials/DefaultMaterial"));
    if (LandscapeMat.Succeeded())
    {
        IslandMaterial = LandscapeMat.Object;
        BridgeMaterial = LandscapeMat.Object;
    }
}

void ARiverPrismalManager::BeginPlay()
{
    Super::BeginPlay();
    
    // Configurar callbacks de colisão
    SetupCollisionCallbacks();
    
    // Inicializar sistema do rio
    InitializeRiverSystem();
    
    // Configurar timers
    GetWorldTimerManager().SetTimer(WaterUpdateTimer, [this]()
    {
        UpdateActorWaterDepthCache();
    }, 0.5f, true);
    
    GetWorldTimerManager().SetTimer(CollisionCleanupTimer, [this]()
    {
        CleanupOldWaterCollisions();
    }, 5.0f, true);
    
    bIsInitialized = true;
    
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Sistema inicializado com sucesso"));
}

void ARiverPrismalManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (!bIsInitialized)
        return;
    
    LastUpdateTime += DeltaTime;
    
    // Atualizar fluxo da água
    UpdateWaterFlow(DeltaTime);
    
    // Desenhar debug se habilitado
    if (bShowDebugLines)
    {
        DrawDebugRiver();
    }
    
    if (bShowIslandGeometry)
    {
        DrawDebugIsland();
    }
    
    if (bShowFlowVectors)
    {
        DrawDebugWaterFlow();
    }
}

void ARiverPrismalManager::InitializeRiverSystem()
{
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Inicializando sistema do rio"));
    
    // Limpar dados existentes
    RiverSegments.Empty();
    RiverMeshes.Empty();
    WaterCollisionBoxes.Empty();
    Bridges.Empty();
    
    // Gerar geometria do rio
    GenerateRiverGeometry();
    
    // Gerar ilha hexagonal
    if (bGenerateIsland)
    {
        GenerateHexagonalIsland();
    }
    
    // Configurar física da água
    SetupWaterPhysics();
    
    // Criar pontes
    CreateBridges();
    
    // Validar geometria
    if (!ValidateRiverGeometry())
    {
        UE_LOG(LogTemp, Error, TEXT("ARiverPrismalManager: Falha na validação da geometria do rio"));
    }
    
    if (bGenerateIsland && !ValidateIslandGeometry())
    {
        UE_LOG(LogTemp, Error, TEXT("ARiverPrismalManager: Falha na validação da geometria da ilha"));
    }
}

void ARiverPrismalManager::GenerateRiverGeometry()
{
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Gerando geometria senoidal do rio"));
    
    // Calcular posições inicial e final
    FVector StartPos = FVector(-RiverLength * 0.5f, 0.0f, 0.0f);
    FVector EndPos = FVector(RiverLength * 0.5f, 0.0f, 0.0f);
    
    // Gerar caminho senoidal
    TArray<FVector> RiverPath = GenerateSinusoidalPath(StartPos, EndPos);
    
    // Configurar spline com os pontos do rio
    RiverSpline->ClearSplinePoints();
    for (int32 i = 0; i < RiverPath.Num(); i++)
    {
        RiverSpline->AddSplinePoint(RiverPath[i], ESplineCoordinateSpace::Local);
        RiverSpline->SetSplinePointType(i, ESplinePointType::CurveClamped);
    }
    RiverSpline->UpdateSpline();
    
    // Criar segmentos do rio
    InitializeDefaultRiverSegments();
    
    // Criar meshes dos segmentos
    CreateRiverMeshes();
    
    // Configurar caixas de colisão
    SetupWaterCollisionBoxes();
}

TArray<FVector> ARiverPrismalManager::GenerateSinusoidalPath(const FVector& StartPos, const FVector& EndPos) const
{
    TArray<FVector> PathPoints;
    
    FVector Direction = (EndPos - StartPos).GetSafeNormal();
    float TotalDistance = FVector::Dist(StartPos, EndPos);
    float SegmentLength = TotalDistance / RiverSegments.Num();
    
    for (int32 i = 0; i <= RiverSegments.Num(); i++)
    {
        float Distance = i * SegmentLength;
        FVector BasePoint = StartPos + Direction * Distance;
        
        // Aplicar deslocamento senoidal
        FVector SinusoidalPoint = CalculateSinusoidalPoint(Distance, Direction);
        FVector FinalPoint = BasePoint + SinusoidalPoint;
        
        PathPoints.Add(FinalPoint);
    }
    
    return PathPoints;
}

FVector ARiverPrismalManager::CalculateSinusoidalPoint(float Distance, const FVector& BaseDirection) const
{
    // Calcular deslocamento senoidal perpendicular à direção base
    float SinValue = FMath::Sin(Distance * SinusoidalFrequency * 2.0f * PI);
    float Offset = SinValue * SinusoidalAmplitude;
    
    // Vetor perpendicular (assumindo direção no plano XY)
    FVector PerpendicularDirection = FVector(-BaseDirection.Y, BaseDirection.X, 0.0f).GetSafeNormal();
    
    return PerpendicularDirection * Offset;
}

TArray<FVector> ARiverPrismalManager::GenerateRiverBankPoints(const TArray<FVector>& CenterLine, float BankOffset) const
{
    TArray<FVector> BankPoints;
    
    for (int32 i = 0; i < CenterLine.Num(); i++)
    {
        FVector CurrentPoint = CenterLine[i];
        FVector Direction;
        
        if (i < CenterLine.Num() - 1)
        {
            Direction = (CenterLine[i + 1] - CurrentPoint).GetSafeNormal();
        }
        else if (i > 0)
        {
            Direction = (CurrentPoint - CenterLine[i - 1]).GetSafeNormal();
        }
        else
        {
            Direction = FVector::ForwardVector;
        }
        
        FVector PerpendicularDirection = FVector(-Direction.Y, Direction.X, 0.0f).GetSafeNormal();
        
        // Adicionar pontos das duas margens
        BankPoints.Add(CurrentPoint + PerpendicularDirection * BankOffset);
        BankPoints.Add(CurrentPoint - PerpendicularDirection * BankOffset);
    }
    
    return BankPoints;
}

void ARiverPrismalManager::GenerateHexagonalIsland()
{
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Gerando ilha hexagonal central"));
    
    // Calcular vértices do hexágono
    CentralIsland.HexagonVertices = CalculateHexagonVertices(CentralIsland.CenterPosition, CentralIsland.Radius);
    
    // Calcular pontos médios das arestas
    CentralIsland.EdgeMidpoints = CalculateHexagonEdgeMidpoints(CentralIsland.HexagonVertices);
    
    // Calcular propriedades geométricas
    CentralIsland.EdgeLength = CalculateHexagonEdgeLength(CentralIsland.Radius);
    CentralIsland.Apothem = CalculateHexagonApothem(CentralIsland.Radius);
    
    // Criar mesh da ilha
    UStaticMeshComponent* NewIslandMesh = CreateIslandMesh();
    if (NewIslandMesh && IslandMaterial)
    {
        NewIslandMesh->SetMaterial(0, IslandMaterial);
    }
}

TArray<FVector> ARiverPrismalManager::CalculateHexagonVertices(const FVector& Center, float Radius) const
{
    TArray<FVector> Vertices;
    
    for (int32 i = 0; i < HEXAGON_SIDES; i++)
    {
        float Angle = (i * 60.0f) * PI / 180.0f; // 60 graus em radianos
        float X = Center.X + Radius * FMath::Cos(Angle);
        float Y = Center.Y + Radius * FMath::Sin(Angle);
        float Z = Center.Z + CentralIsland.Height;
        
        Vertices.Add(FVector(X, Y, Z));
    }
    
    return Vertices;
}

TArray<FVector> ARiverPrismalManager::CalculateHexagonEdgeMidpoints(const TArray<FVector>& Vertices) const
{
    TArray<FVector> Midpoints;
    
    for (int32 i = 0; i < Vertices.Num(); i++)
    {
        int32 NextIndex = (i + 1) % Vertices.Num();
        FVector Midpoint = (Vertices[i] + Vertices[NextIndex]) * 0.5f;
        Midpoints.Add(Midpoint);
    }
    
    return Midpoints;
}

float ARiverPrismalManager::CalculateHexagonApothem(float Radius) const
{
    // Apótema = Raio * cos(30°) = Raio * sqrt(3)/2
    return Radius * FMath::Sqrt(3.0f) * 0.5f;
}

float ARiverPrismalManager::CalculateHexagonEdgeLength(float Radius) const
{
    // Para hexágono regular, comprimento da aresta = raio
    return Radius;
}

bool ARiverPrismalManager::IsPointInsideHexagon(const FVector& Point, const FHexagonalIsland& Island) const
{
    // Usar algoritmo de ray casting para polígono
    FVector2D TestPoint(Point.X, Point.Y);
    TArray<FVector2D> HexVertices2D;
    
    for (const FVector& Vertex : Island.HexagonVertices)
    {
        HexVertices2D.Add(FVector2D(Vertex.X, Vertex.Y));
    }
    
    int32 Intersections = 0;
    for (int32 i = 0; i < HexVertices2D.Num(); i++)
    {
        int32 NextIndex = (i + 1) % HexVertices2D.Num();
        FVector2D V1 = HexVertices2D[i];
        FVector2D V2 = HexVertices2D[NextIndex];
        
        if (((V1.Y > TestPoint.Y) != (V2.Y > TestPoint.Y)) &&
            (TestPoint.X < (V2.X - V1.X) * (TestPoint.Y - V1.Y) / (V2.Y - V1.Y) + V1.X))
        {
            Intersections++;
        }
    }
    
    return (Intersections % 2) == 1;
}

void ARiverPrismalManager::SetupWaterPhysics()
{
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Configurando física da água"));
    
    // Configurar propriedades físicas da água
    if (WaterCollisionBox)
    {
        WaterCollisionBox->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        WaterCollisionBox->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
        WaterCollisionBox->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Overlap);
        
        // Configurar densidade e viscosidade
        if (UPrimitiveComponent* PrimComp = Cast<UPrimitiveComponent>(WaterCollisionBox))
        {
            PrimComp->SetMassOverrideInKg(NAME_None, 1000.0f); // Densidade da água
        }
    }
}

bool ARiverPrismalManager::CheckWaterCollision(const FVector& Position, FRiverCollisionData& OutCollisionData) const
{
    if (!IsPositionInWater(Position))
    {
        return false;
    }
    
    OutCollisionData.CollisionPoint = Position;
    OutCollisionData.WaterSurfaceNormal = FVector::UpVector;
    OutCollisionData.WaterDepthAtPoint = CalculateWaterDepth(Position);
    OutCollisionData.CollisionTime = FDateTime::Now();
    OutCollisionData.FlowVelocityAtPoint = CalculateFlowDirection(Position) * CalculateFlowSpeed(Position);
    OutCollisionData.bIsUnderwater = Position.Z < GetWaterSurfacePosition(Position).Z;
    
    return true;
}

bool ARiverPrismalManager::IsPositionInWater(const FVector& Position) const
{
    // Verificar se está dentro dos limites do rio
    if (!WaterCollisionBox)
        return false;
    
    FVector BoxCenter = WaterCollisionBox->GetComponentLocation();
    FVector BoxExtent = WaterCollisionBox->GetScaledBoxExtent();
    
    return (FMath::Abs(Position.X - BoxCenter.X) <= BoxExtent.X &&
            FMath::Abs(Position.Y - BoxCenter.Y) <= BoxExtent.Y &&
            FMath::Abs(Position.Z - BoxCenter.Z) <= BoxExtent.Z);
}

bool ARiverPrismalManager::IsPositionOnIsland(const FVector& Position) const
{
    if (!bGenerateIsland)
        return false;
    
    return IsPointInsideHexagon(Position, CentralIsland) && 
           Position.Z >= CentralIsland.CenterPosition.Z;
}

FVector ARiverPrismalManager::GetWaterSurfacePosition(const FVector& Position) const
{
    FVector SurfacePos = Position;
    SurfacePos.Z = 0.0f; // Assumindo nível do mar em Z=0
    return SurfacePos;
}

FVector ARiverPrismalManager::CalculateFlowDirection(const FVector& Position) const
{
    // Encontrar o ponto mais próximo no spline do rio
    if (!RiverSpline)
        return FVector::ForwardVector;
    
    float ClosestInputKey = RiverSpline->FindInputKeyClosestToWorldLocation(Position);
    FVector TangentAtPoint = RiverSpline->GetTangentAtSplineInputKey(ClosestInputKey, ESplineCoordinateSpace::World);
    
    return TangentAtPoint.GetSafeNormal();
}

float ARiverPrismalManager::CalculateFlowSpeed(const FVector& Position) const
{
    // Velocidade base do fluxo
    float BaseSpeed = WATER_FLOW_SPEED;
    
    // Modificar velocidade baseada na profundidade
    float Depth = CalculateWaterDepth(Position);
    float DepthFactor = FMath::Clamp(Depth / RiverDepth, 0.1f, 1.0f);
    
    // Modificar velocidade baseada na distância do centro
    float DistanceFromCenter = CalculateDistanceToRiver(Position);
    float CenterFactor = FMath::Clamp(1.0f - (DistanceFromCenter / (RiverWidth * 0.5f)), 0.1f, 1.0f);
    
    return BaseSpeed * DepthFactor * CenterFactor;
}

float ARiverPrismalManager::CalculateWaterDepth(const FVector& Position) const
{
    if (!IsPositionInWater(Position))
        return 0.0f;
    
    // Calcular profundidade baseada na distância do centro do rio
    float DistanceFromCenter = CalculateDistanceToRiver(Position);
    float NormalizedDistance = FMath::Clamp(DistanceFromCenter / (RiverWidth * 0.5f), 0.0f, 1.0f);
    
    // Perfil parabólico da profundidade
    float DepthFactor = 1.0f - (NormalizedDistance * NormalizedDistance);
    
    return RiverDepth * DepthFactor;
}

FVector ARiverPrismalManager::ApplyWaterCurrent(const FVector& ActorVelocity, const FVector& Position, float DeltaTime) const
{
    if (!IsPositionInWater(Position) || !WaterConfig.bHasCurrent)
        return ActorVelocity;
    
    FVector LocalFlowDirection = CalculateFlowDirection(Position);
    float FlowSpeed = CalculateFlowSpeed(Position);
    FVector CurrentForce = LocalFlowDirection * FlowSpeed;
    
    // Aplicar força da correnteza
    float CurrentStrength = 0.5f; // Fator de influência da correnteza
    FVector ModifiedVelocity = ActorVelocity + (CurrentForce * CurrentStrength * DeltaTime);
    
    return ModifiedVelocity;
}

void ARiverPrismalManager::CreateBridges()
{
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Criando pontes"));
    
    // Criar ponte principal no centro do mapa
    FVector BridgePosition = FVector(0.0f, 0.0f, 200.0f); // 2 metros acima da água
    FRotator BridgeRotation = FRotator(0.0f, 90.0f, 0.0f); // Perpendicular ao rio
    
    AddBridge(BridgePosition, BridgeRotation, 800.0f, 300.0f);
    
    // Criar pontes secundárias se necessário
    if (RiverLength > 10000.0f) // Se o rio for muito longo
    {
        FVector Bridge2Pos = FVector(-5000.0f, 0.0f, 200.0f);
        FVector Bridge3Pos = FVector(5000.0f, 0.0f, 200.0f);
        
        AddBridge(Bridge2Pos, BridgeRotation, 600.0f, 250.0f);
        AddBridge(Bridge3Pos, BridgeRotation, 600.0f, 250.0f);
    }
}

int32 ARiverPrismalManager::AddBridge(const FVector& Position, const FRotator& Rotation, float Length, float Width)
{
    FBridgeData NewBridge;
    NewBridge.Position = Position;
    NewBridge.Rotation = Rotation;
    NewBridge.Length = Length;
    NewBridge.Width = Width;
    NewBridge.Height = 150.0f; // 1.5 metros de altura
    NewBridge.bIsDestructible = false;
    
    int32 BridgeIndex = Bridges.Add(NewBridge);
    
    // Criar mesh da ponte
    UStaticMeshComponent* BridgeMesh = CreateBridgeMesh(NewBridge, BridgeIndex);
    if (BridgeMesh && BridgeMaterial)
    {
        BridgeMesh->SetMaterial(0, BridgeMaterial);
    }
    
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Ponte criada no índice %d"), BridgeIndex);
    
    return BridgeIndex;
}

bool ARiverPrismalManager::ValidateRiverGeometry() const
{
    // Validar configurações básicas
    if (RiverLength <= 0.0f || RiverWidth <= 0.0f || RiverDepth <= 0.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("ARiverPrismalManager: Dimensões do rio inválidas"));
        return false;
    }
    
    if (SinusoidalAmplitude < 0.0f || SinusoidalFrequency <= 0.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("ARiverPrismalManager: Parâmetros senoidais inválidos"));
        return false;
    }
    
    if (RiverSegments.Num() < 10)
    {
        UE_LOG(LogTemp, Warning, TEXT("ARiverPrismalManager: Número de segmentos muito baixo"));
    }
    
    // Validar spline
    if (!RiverSpline || RiverSpline->GetNumberOfSplinePoints() < 2)
    {
        UE_LOG(LogTemp, Error, TEXT("ARiverPrismalManager: Spline do rio inválido"));
        return false;
    }
    
    return true;
}

bool ARiverPrismalManager::ValidateIslandGeometry() const
{
    if (!bGenerateIsland)
        return true;
    
    if (CentralIsland.Radius <= 0.0f || CentralIsland.Height < 0.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("ARiverPrismalManager: Dimensões da ilha inválidas"));
        return false;
    }
    
    if (CentralIsland.HexagonVertices.Num() != HEXAGON_SIDES)
    {
        UE_LOG(LogTemp, Error, TEXT("ARiverPrismalManager: Número incorreto de vértices do hexágono"));
        return false;
    }
    
    return true;
}

void ARiverPrismalManager::DrawDebugRiver() const
{
    if (!GetWorld() || !RiverSpline)
        return;
    
    // Desenhar linha central do rio
    for (int32 i = 0; i < RiverSpline->GetNumberOfSplinePoints() - 1; i++)
    {
        FVector Start = RiverSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
        FVector End = RiverSpline->GetLocationAtSplinePoint(i + 1, ESplineCoordinateSpace::World);
        
        DrawDebugLine(GetWorld(), Start, End, FColor::Blue, false, -1.0f, 0, 5.0f);
    }
    
    // Desenhar margens do rio
    TArray<FVector> CenterPoints;
    for (int32 i = 0; i < RiverSpline->GetNumberOfSplinePoints(); i++)
    {
        CenterPoints.Add(RiverSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World));
    }
    
    TArray<FVector> BankPoints = GenerateRiverBankPoints(CenterPoints, RiverWidth * 0.5f);
    
    for (int32 i = 0; i < BankPoints.Num() - 2; i += 2)
    {
        DrawDebugLine(GetWorld(), BankPoints[i], BankPoints[i + 2], FColor::Green, false, -1.0f, 0, 3.0f);
        DrawDebugLine(GetWorld(), BankPoints[i + 1], BankPoints[i + 3], FColor::Green, false, -1.0f, 0, 3.0f);
    }
}

void ARiverPrismalManager::DrawDebugIsland() const
{
    if (!GetWorld() || !bGenerateIsland)
        return;
    
    // Desenhar vértices do hexágono
    for (int32 i = 0; i < CentralIsland.HexagonVertices.Num(); i++)
    {
        int32 NextIndex = (i + 1) % CentralIsland.HexagonVertices.Num();
        
        DrawDebugLine(GetWorld(), 
                     CentralIsland.HexagonVertices[i], 
                     CentralIsland.HexagonVertices[NextIndex], 
                     FColor::Yellow, false, -1.0f, 0, 4.0f);
    }
    
    // Desenhar centro da ilha
    DrawDebugSphere(GetWorld(), CentralIsland.CenterPosition, 50.0f, 12, FColor::Orange, false, -1.0f, 0, 2.0f);
}

void ARiverPrismalManager::DrawDebugWaterFlow() const
{
    if (!GetWorld() || !RiverSpline)
        return;
    
    // Desenhar vetores de fluxo em pontos regulares
    for (int32 i = 0; i < RiverSpline->GetNumberOfSplinePoints(); i += 5)
    {
        FVector Position = RiverSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
        FVector FlowDir = CalculateFlowDirection(Position);
        float FlowSpeed = CalculateFlowSpeed(Position);
        
        FVector EndPoint = Position + FlowDir * (FlowSpeed * 0.01f); // Escalar para visualização
        
        DrawDebugDirectionalArrow(GetWorld(), Position, EndPoint, 20.0f, FColor::Cyan, false, -1.0f, 0, 2.0f);
    }
}

// Implementações das funções auxiliares internas
void ARiverPrismalManager::InitializeDefaultRiverSegments()
{
    RiverSegments.Empty();
    
    if (!RiverSpline || RiverSpline->GetNumberOfSplinePoints() < 2)
        return;
    
    for (int32 i = 0; i < RiverSpline->GetNumberOfSplinePoints() - 1; i++)
    {
        FRiverSegment NewSegment;
        NewSegment.StartPosition = RiverSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
        NewSegment.EndPosition = RiverSpline->GetLocationAtSplinePoint(i + 1, ESplineCoordinateSpace::World);
        NewSegment.CenterPosition = (NewSegment.StartPosition + NewSegment.EndPosition) * 0.5f;
        NewSegment.SegmentType = ERiverSegmentType::Curved;
        NewSegment.Width = RiverWidth;
        NewSegment.Depth = RiverDepth;
        NewSegment.FlowSpeed = WATER_FLOW_SPEED;
        NewSegment.FlowDirection = (NewSegment.EndPosition - NewSegment.StartPosition).GetSafeNormal();
        
        RiverSegments.Add(NewSegment);
    }
}

void ARiverPrismalManager::CreateRiverMeshes()
{
    // Limpar meshes existentes
    for (UStaticMeshComponent* Mesh : RiverMeshes)
    {
        if (Mesh)
        {
            Mesh->DestroyComponent();
        }
    }
    RiverMeshes.Empty();
    
    // Criar novos meshes para cada segmento
    for (int32 i = 0; i < RiverSegments.Num(); i++)
    {
        UStaticMeshComponent* SegmentMesh = CreateRiverSegmentMesh(RiverSegments[i], i);
        if (SegmentMesh)
        {
            RiverMeshes.Add(SegmentMesh);
            
            if (WaterMaterial)
            {
                SegmentMesh->SetMaterial(0, WaterMaterial);
            }
        }
    }
}

UStaticMeshComponent* ARiverPrismalManager::CreateRiverSegmentMesh(const FRiverSegment& Segment, int32 SegmentIndex)
{
    FString ComponentName = FString::Printf(TEXT("RiverSegment_%d"), SegmentIndex);
    UStaticMeshComponent* SegmentMesh = CreateDefaultSubobject<UStaticMeshComponent>(*ComponentName);
    
    if (SegmentMesh)
    {
        SegmentMesh->SetupAttachment(RootComponent);
        SegmentMesh->SetWorldLocation(Segment.CenterPosition);
        SegmentMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        SegmentMesh->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
    }
    
    return SegmentMesh;
}

UStaticMeshComponent* ARiverPrismalManager::CreateIslandMesh()
{
    if (!IslandMesh)
        return nullptr;
    
    IslandMesh->SetWorldLocation(CentralIsland.CenterPosition);
    IslandMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    IslandMesh->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);
    
    return IslandMesh;
}

UStaticMeshComponent* ARiverPrismalManager::CreateBridgeMesh(const FBridgeData& BridgeData, int32 BridgeIndex)
{
    FString ComponentName = FString::Printf(TEXT("Bridge_%d"), BridgeIndex);
    UStaticMeshComponent* BridgeMesh = CreateDefaultSubobject<UStaticMeshComponent>(*ComponentName);
    
    if (BridgeMesh)
    {
        BridgeMesh->SetupAttachment(RootComponent);
        BridgeMesh->SetWorldLocation(BridgeData.Position);
        BridgeMesh->SetWorldRotation(BridgeData.Rotation);
        BridgeMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        BridgeMesh->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);
    }
    
    return BridgeMesh;
}

void ARiverPrismalManager::SetupWaterCollisionBoxes()
{
    // Configurar caixa de colisão principal
    if (WaterCollisionBox)
    {
        FVector RiverCenter = GetRiverCenter();
        WaterCollisionBox->SetWorldLocation(RiverCenter);
        WaterCollisionBox->SetBoxExtent(FVector(RiverLength * 0.6f, RiverWidth * 0.6f, RiverDepth));
    }
}

void ARiverPrismalManager::SetupCollisionCallbacks()
{
    if (WaterCollisionBox)
    {
        WaterCollisionBox->OnComponentHit.AddDynamic(this, &ARiverPrismalManager::OnWaterHit);
        WaterCollisionBox->OnComponentBeginOverlap.AddDynamic(this, &ARiverPrismalManager::OnActorEnterWater);
        WaterCollisionBox->OnComponentEndOverlap.AddDynamic(this, &ARiverPrismalManager::OnActorExitWater);
    }
}

void ARiverPrismalManager::UpdateWaterFlow(float DeltaTime)
{
    // Atualizar propriedades dinâmicas da água
    if (WaterConfig.bHasCurrent)
    {
        // Simular variações no fluxo da água
        float TimeVariation = FMath::Sin(LastUpdateTime * 0.5f) * 0.1f;
        // Aplicar variações sutis na velocidade do fluxo
    }
}

void ARiverPrismalManager::CleanupOldWaterCollisions()
{
    FDateTime CurrentTime = FDateTime::Now();
    FTimespan MaxAge = FTimespan::FromSeconds(30.0); // Manter colisões por 30 segundos
    
    RecentWaterCollisions.RemoveAll([CurrentTime, MaxAge](const FRiverCollisionData& Collision)
    {
        return (CurrentTime - Collision.CollisionTime) > MaxAge;
    });
}

void ARiverPrismalManager::UpdateActorWaterDepthCache()
{
    // Atualizar cache de profundidade para atores próximos
    if (!GetWorld())
        return;
    
    ActorWaterDepthCache.Empty();
    
    // Encontrar atores próximos ao rio
    TArray<AActor*> NearbyActors;
    // Implementar lógica de busca de atores próximos
}

float ARiverPrismalManager::CalculateDistanceToRiver(const FVector& Position) const
{
    if (!RiverSpline)
        return FLT_MAX;
    
    FVector ClosestPoint = RiverSpline->FindLocationClosestToWorldLocation(Position, ESplineCoordinateSpace::World);
    return FVector::Dist2D(Position, ClosestPoint);
}

FVector ARiverPrismalManager::GetRiverCenter() const
{
    if (!RiverSpline || RiverSpline->GetNumberOfSplinePoints() == 0)
        return FVector::ZeroVector;
    
    float SplineLength = RiverSpline->GetSplineLength();
    return RiverSpline->GetLocationAtDistanceAlongSpline(SplineLength * 0.5f, ESplineCoordinateSpace::World);
}

// Implementações dos eventos de colisão
void ARiverPrismalManager::OnWaterHit(UPrimitiveComponent* HitComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, FVector NormalImpulse, const FHitResult& Hit)
{
    if (!OtherActor || !bEnableCollisionLogging)
        return;
    
    FRiverCollisionData CollisionData;
    CollisionData.CollisionPoint = Hit.Location;
    CollisionData.WaterSurfaceNormal = Hit.Normal;
    CollisionData.CollidingActor = OtherActor;
    CollisionData.WaterDepthAtPoint = CalculateWaterDepth(Hit.Location);
    CollisionData.FlowVelocityAtPoint = CalculateFlowDirection(Hit.Location) * CalculateFlowSpeed(Hit.Location);
    
    UpdateCollisionHistory(CollisionData);
    
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Colisão com água detectada - Ator: %s"), *OtherActor->GetName());
}

void ARiverPrismalManager::OnActorEnterWater(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    if (!OtherActor)
        return;
    
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Ator entrou na água - %s"), *OtherActor->GetName());
    
    // Aplicar efeitos de entrada na água
    if (WaterConfig.bCausesDamage)
    {
        // Implementar dano por água se necessário
    }
}

void ARiverPrismalManager::OnActorExitWater(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, int32 OtherBodyIndex)
{
    if (!OtherActor)
        return;
    
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Ator saiu da água - %s"), *OtherActor->GetName());
    
    // Remover efeitos de água
    ActorWaterDepthCache.Remove(OtherActor);
}

void ARiverPrismalManager::UpdateCollisionHistory(const FRiverCollisionData& CollisionData)
{
    RecentWaterCollisions.Add(CollisionData);
    
    // Limitar histórico a 100 colisões
    if (RecentWaterCollisions.Num() > 100)
    {
        RecentWaterCollisions.RemoveAt(0);
    }
}

// Implementações dos getters
float ARiverPrismalManager::GetTotalRiverLength() const
{
    return RiverSpline ? RiverSpline->GetSplineLength() : RiverLength;
}

float ARiverPrismalManager::GetRiverVolume() const
{
    return GetTotalRiverLength() * RiverWidth * RiverDepth;
}

float ARiverPrismalManager::GetIslandArea() const
{
    if (!bGenerateIsland)
        return 0.0f;
    
    // Área do hexágono regular = (3 * sqrt(3) / 2) * r²
    return (3.0f * FMath::Sqrt(3.0f) / 2.0f) * FMath::Square(CentralIsland.Radius);
}

FVector ARiverPrismalManager::GetIslandCenter() const
{
    return CentralIsland.CenterPosition;
}

// Implementações dos setters
void ARiverPrismalManager::SetWaterProperties(const FWaterProperties& NewWaterConfig)
{
    WaterConfig = NewWaterConfig;
    
    // Atualizar materiais se necessário
    if (WaterMaterial)
    {
        // Implementar atualização de propriedades do material
    }
}

void ARiverPrismalManager::SetRiverDimensions(float NewLength, float NewWidth, float NewDepth)
{
    RiverLength = FMath::Max(NewLength, 100.0f);
    RiverWidth = FMath::Max(NewWidth, 50.0f);
    RiverDepth = FMath::Max(NewDepth, 10.0f);
    
    // Regenerar geometria se já inicializado
    if (bIsInitialized)
    {
        GenerateRiverGeometry();
    }
}

void ARiverPrismalManager::SetSinusoidalParameters(float NewAmplitude, float NewFrequency)
{
    SinusoidalAmplitude = FMath::Max(NewAmplitude, 0.0f);
    SinusoidalFrequency = FMath::Max(NewFrequency, 0.0001f);
    
    // Regenerar geometria se já inicializado
    if (bIsInitialized)
    {
        GenerateRiverGeometry();
    }
}

void ARiverPrismalManager::SetIslandRadius(float NewRadius)
{
    CentralIsland.Radius = FMath::Max(NewRadius, 100.0f);

    // Regenerar ilha se já inicializada
    if (bIsInitialized && bGenerateIsland)
    {
        GenerateHexagonalIsland();
    }
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES DE GEOMETRIA DA ILHA =====

FVector ARiverPrismalManager::GetClosestPointOnIsland(const FVector& Position) const
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Calculando ponto mais próximo na ilha para posição %s"), *Position.ToString());

    if (!bGenerateIsland)
    {
        return CentralIsland.CenterPosition;
    }

    // Calcular vértices do hexágono usando APIs modernas do UE 5.6
    TArray<FVector> HexVertices = CalculateHexagonVertices(CentralIsland.CenterPosition, CentralIsland.Radius);

    FVector ClosestPoint = CentralIsland.CenterPosition;
    float MinDistance = FLT_MAX;

    // Verificar se o ponto está dentro do hexágono
    if (IsPointInsideHexagon(Position, CentralIsland))
    {
        // Se está dentro, o ponto mais próximo é a projeção na borda
        FVector ToPoint = Position - CentralIsland.CenterPosition;
        ToPoint.Z = 0.0f; // Projetar no plano XY

        // Encontrar a aresta mais próxima
        for (int32 i = 0; i < HexVertices.Num(); i++)
        {
            int32 NextIndex = (i + 1) % HexVertices.Num();
            FVector EdgeStart = HexVertices[i];
            FVector EdgeEnd = HexVertices[NextIndex];

            // Usar API moderna do UE 5.6 para projeção em linha
            FVector PointOnEdge = FMath::ClosestPointOnLine(EdgeStart, EdgeEnd, Position);
            float Distance = FVector::Dist(Position, PointOnEdge);

            if (Distance < MinDistance)
            {
                MinDistance = Distance;
                ClosestPoint = PointOnEdge;
            }
        }
    }
    else
    {
        // Se está fora, encontrar o ponto mais próximo nas arestas
        for (int32 i = 0; i < HexVertices.Num(); i++)
        {
            int32 NextIndex = (i + 1) % HexVertices.Num();
            FVector EdgeStart = HexVertices[i];
            FVector EdgeEnd = HexVertices[NextIndex];

            FVector PointOnEdge = FMath::ClosestPointOnLine(EdgeStart, EdgeEnd, Position);
            float Distance = FVector::Dist(Position, PointOnEdge);

            if (Distance < MinDistance)
            {
                MinDistance = Distance;
                ClosestPoint = PointOnEdge;
            }
        }
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Ponto mais próximo calculado: %s (distância: %.2f)"),
        *ClosestPoint.ToString(), MinDistance);

    return ClosestPoint;
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES DO SISTEMA DE PONTES =====

void ARiverPrismalManager::RemoveBridge(int32 BridgeIndex)
{
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Removendo ponte índice %d"), BridgeIndex);

    if (!Bridges.IsValidIndex(BridgeIndex))
    {
        UE_LOG(LogTemp, Error, TEXT("ARiverPrismalManager: Índice de ponte inválido: %d"), BridgeIndex);
        return;
    }

    FBridgeData& Bridge = Bridges[BridgeIndex];

    // Remover atores autorizados da ponte
    Bridge.AuthorizedUnits.Empty();

    // Destruir mesh da ponte se existir
    if (BridgeMeshes.IsValidIndex(BridgeIndex) && IsValid(BridgeMeshes[BridgeIndex]))
    {
        BridgeMeshes[BridgeIndex]->DestroyComponent();
        BridgeMeshes[BridgeIndex] = nullptr;
    }

    // Remover da lista de pontes
    Bridges.RemoveAt(BridgeIndex);
    if (BridgeMeshes.IsValidIndex(BridgeIndex))
    {
        BridgeMeshes.RemoveAt(BridgeIndex);
    }

    // Atualizar índices das pontes restantes
    for (int32 i = BridgeIndex; i < Bridges.Num(); i++)
    {
        // Reindexar se necessário
    }

    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Ponte removida com sucesso. Pontes restantes: %d"), Bridges.Num());
}

bool ARiverPrismalManager::CanUnitCrossBridge(AActor* Unit, int32 BridgeIndex) const
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Verificando se unidade pode cruzar ponte %d"), BridgeIndex);

    if (!IsValid(Unit))
    {
        UE_LOG(LogTemp, Warning, TEXT("ARiverPrismalManager: Unidade inválida"));
        return false;
    }

    if (!Bridges.IsValidIndex(BridgeIndex))
    {
        UE_LOG(LogTemp, Warning, TEXT("ARiverPrismalManager: Índice de ponte inválido: %d"), BridgeIndex);
        return false;
    }

    const FBridgeData& Bridge = Bridges[BridgeIndex];

    // Verificar se a unidade está na lista de autorizados
    if (Bridge.AuthorizedUnits.Contains(Unit))
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Unidade autorizada na ponte"));
        return true;
    }

    // Verificar distância da ponte
    FVector UnitLocation = Unit->GetActorLocation();
    float DistanceToBridge = FVector::Dist2D(UnitLocation, Bridge.Position);

    // Permitir se estiver próximo o suficiente (dentro do raio da ponte)
    float BridgeRadius = FMath::Max(Bridge.Length, Bridge.Width) * 0.5f;
    bool bWithinRange = DistanceToBridge <= BridgeRadius + 200.0f; // 2 metros de tolerância

    // Verificar se a ponte não está destruída
    bool bBridgeIntact = !Bridge.bIsDestructible || IsValid(BridgeMeshes.IsValidIndex(BridgeIndex) ? BridgeMeshes[BridgeIndex] : nullptr);

    bool bCanCross = bWithinRange && bBridgeIntact;

    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Unidade %s cruzar ponte (distância: %.2f, raio: %.2f, intacta: %s)"),
        bCanCross ? TEXT("PODE") : TEXT("NÃO PODE"), DistanceToBridge, BridgeRadius, bBridgeIntact ? TEXT("SIM") : TEXT("NÃO"));

    return bCanCross;
}

FVector ARiverPrismalManager::GetNearestBridgePosition(const FVector& Position) const
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Buscando ponte mais próxima de %s"), *Position.ToString());

    if (Bridges.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("ARiverPrismalManager: Nenhuma ponte disponível"));
        return Position;
    }

    FVector NearestBridgePos = Bridges[0].Position;
    float MinDistance = FVector::Dist(Position, NearestBridgePos);

    for (int32 i = 1; i < Bridges.Num(); i++)
    {
        const FBridgeData& Bridge = Bridges[i];
        float Distance = FVector::Dist(Position, Bridge.Position);

        if (Distance < MinDistance)
        {
            MinDistance = Distance;
            NearestBridgePos = Bridge.Position;
        }
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Ponte mais próxima em %s (distância: %.2f)"),
        *NearestBridgePos.ToString(), MinDistance);

    return NearestBridgePos;
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES DE NAVEGAÇÃO AQUÁTICA =====

TArray<FVector> ARiverPrismalManager::FindWaterPath(const FVector& StartPos, const FVector& EndPos) const
{
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Calculando caminho aquático de %s para %s"),
        *StartPos.ToString(), *EndPos.ToString());

    TArray<FVector> WaterPath;

    // Verificar se ambos os pontos estão na água
    bool bStartInWater = IsPositionInWater(StartPos);
    bool bEndInWater = IsPositionInWater(EndPos);

    if (!bStartInWater && !bEndInWater)
    {
        UE_LOG(LogTemp, Warning, TEXT("ARiverPrismalManager: Nem início nem fim estão na água"));
        return WaterPath;
    }

    // Ajustar pontos para superfície da água se necessário
    FVector AdjustedStart = bStartInWater ? StartPos : GetWaterSurfacePosition(StartPos);
    FVector AdjustedEnd = bEndInWater ? EndPos : GetWaterSurfacePosition(EndPos);

    // Algoritmo A* simplificado para navegação aquática
    const int32 MaxWaypoints = 20;
    const float StepSize = FVector::Dist(AdjustedStart, AdjustedEnd) / MaxWaypoints;

    WaterPath.Add(AdjustedStart);

    FVector CurrentPos = AdjustedStart;
    FVector Direction = (AdjustedEnd - AdjustedStart).GetSafeNormal();

    for (int32 i = 1; i < MaxWaypoints; i++)
    {
        FVector NextPos = AdjustedStart + (Direction * StepSize * i);

        // Ajustar para seguir o fluxo do rio usando função senoidal
        if (RiverSpline && IsValid(RiverSpline))
        {
            FVector ClosestSplinePoint = RiverSpline->FindLocationClosestToWorldLocation(NextPos, ESplineCoordinateSpace::World);

            // Interpolar entre caminho direto e seguir o rio
            float BlendFactor = 0.3f; // 30% influência do rio
            NextPos = FMath::Lerp(NextPos, ClosestSplinePoint, BlendFactor);
        }

        // Garantir que está na superfície da água
        NextPos = GetWaterSurfacePosition(NextPos);

        // Verificar se o caminho está livre
        if (IsWaterPathClear(CurrentPos, NextPos))
        {
            WaterPath.Add(NextPos);
            CurrentPos = NextPos;
        }
        else
        {
            // Tentar contornar obstáculo
            FVector AvoidanceOffset = FVector(0.0f, 200.0f, 0.0f); // Desviar 2 metros para o lado
            FVector AlternativePos = NextPos + AvoidanceOffset;

            if (IsWaterPathClear(CurrentPos, AlternativePos))
            {
                WaterPath.Add(AlternativePos);
                CurrentPos = AlternativePos;
            }
            else
            {
                // Tentar do outro lado
                AlternativePos = NextPos - AvoidanceOffset;
                if (IsWaterPathClear(CurrentPos, AlternativePos))
                {
                    WaterPath.Add(AlternativePos);
                    CurrentPos = AlternativePos;
                }
            }
        }
    }

    // Adicionar ponto final
    if (WaterPath.Num() > 0 && FVector::Dist(WaterPath.Last(), AdjustedEnd) > 100.0f)
    {
        WaterPath.Add(AdjustedEnd);
    }

    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Caminho aquático calculado com %d waypoints"), WaterPath.Num());

    return WaterPath;
}

bool ARiverPrismalManager::IsWaterPathClear(const FVector& StartPos, const FVector& EndPos) const
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Verificando se caminho aquático está livre"));

    if (!GetWorld())
    {
        return false;
    }

    // Fazer line trace para verificar obstáculos
    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.bReturnPhysicalMaterial = false;
    QueryParams.AddIgnoredActor(this);

    // Trace ligeiramente acima da água para evitar colisão com a superfície
    FVector TraceStart = StartPos + FVector(0.0f, 0.0f, 50.0f);
    FVector TraceEnd = EndPos + FVector(0.0f, 0.0f, 50.0f);

    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        TraceStart,
        TraceEnd,
        ECC_WorldStatic,
        QueryParams
    );

    if (bHit)
    {
        // Verificar se o obstáculo é uma ponte (permitido)
        if (AActor* HitActor = HitResult.GetActor())
        {
            if (HitActor->GetName().Contains(TEXT("Bridge")))
            {
                UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Caminho passa por ponte - permitido"));
                return true;
            }
        }

        UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Caminho bloqueado por obstáculo: %s"),
            HitResult.GetActor() ? *HitResult.GetActor()->GetName() : TEXT("Unknown"));
        return false;
    }

    // Verificar se ambos os pontos estão em água navegável
    bool bStartNavigable = IsPositionInWater(StartPos) && CalculateWaterDepth(StartPos) > 50.0f; // Mínimo 50cm
    bool bEndNavigable = IsPositionInWater(EndPos) && CalculateWaterDepth(EndPos) > 50.0f;

    bool bPathClear = bStartNavigable && bEndNavigable;

    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Caminho %s (início navegável: %s, fim navegável: %s)"),
        bPathClear ? TEXT("LIVRE") : TEXT("BLOQUEADO"),
        bStartNavigable ? TEXT("SIM") : TEXT("NÃO"),
        bEndNavigable ? TEXT("SIM") : TEXT("NÃO"));

    return bPathClear;
}

FVector ARiverPrismalManager::GetSafeWaterPosition(const FVector& DesiredPosition, float SafeDistance) const
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Buscando posição aquática segura próxima a %s"),
        *DesiredPosition.ToString());

    // Se a posição desejada já está na água e é segura, retornar ela
    if (IsPositionInWater(DesiredPosition))
    {
        float WaterDepth = CalculateWaterDepth(DesiredPosition);
        if (WaterDepth > 50.0f) // Profundidade mínima segura
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Posição desejada já é segura"));
            return DesiredPosition;
        }
    }

    // Buscar posição segura em espiral ao redor da posição desejada
    const int32 MaxAttempts = 16;
    const float AngleStep = 360.0f / MaxAttempts;

    for (int32 Ring = 1; Ring <= 3; Ring++) // Até 3 anéis de busca
    {
        float SearchRadius = SafeDistance * Ring;

        for (int32 i = 0; i < MaxAttempts; i++)
        {
            float Angle = i * AngleStep;
            FVector Offset = FVector(
                FMath::Cos(FMath::DegreesToRadians(Angle)) * SearchRadius,
                FMath::Sin(FMath::DegreesToRadians(Angle)) * SearchRadius,
                0.0f
            );

            FVector TestPosition = DesiredPosition + Offset;

            // Ajustar para superfície da água
            TestPosition = GetWaterSurfacePosition(TestPosition);

            // Verificar se é uma posição segura
            if (IsPositionInWater(TestPosition))
            {
                float WaterDepth = CalculateWaterDepth(TestPosition);
                if (WaterDepth > 50.0f)
                {
                    // Verificar se não há obstáculos próximos
                    bool bSafeFromObstacles = true;

                    if (GetWorld())
                    {
                        FHitResult HitResult;
                        FCollisionQueryParams QueryParams;
                        QueryParams.AddIgnoredActor(this);

                        // Verificar em várias direções ao redor
                        for (int32 j = 0; j < 8; j++)
                        {
                            float CheckAngle = j * 45.0f;
                            FVector CheckDirection = FVector(
                                FMath::Cos(FMath::DegreesToRadians(CheckAngle)),
                                FMath::Sin(FMath::DegreesToRadians(CheckAngle)),
                                0.0f
                            );

                            FVector CheckEnd = TestPosition + (CheckDirection * SafeDistance);

                            if (GetWorld()->LineTraceSingleByChannel(HitResult, TestPosition, CheckEnd, ECC_WorldStatic, QueryParams))
                            {
                                if (FVector::Dist(TestPosition, HitResult.Location) < SafeDistance * 0.5f)
                                {
                                    bSafeFromObstacles = false;
                                    break;
                                }
                            }
                        }
                    }

                    if (bSafeFromObstacles)
                    {
                        UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Posição segura encontrada: %s (profundidade: %.2f)"),
                            *TestPosition.ToString(), WaterDepth);
                        return TestPosition;
                    }
                }
            }
        }
    }

    // Se não encontrou posição segura, retornar a mais próxima do centro do rio
    FVector SafePosition = DesiredPosition;
    if (RiverSpline && IsValid(RiverSpline))
    {
        SafePosition = RiverSpline->FindLocationClosestToWorldLocation(DesiredPosition, ESplineCoordinateSpace::World);
        SafePosition = GetWaterSurfacePosition(SafePosition);
    }

    UE_LOG(LogTemp, Warning, TEXT("ARiverPrismalManager: Não foi possível encontrar posição completamente segura, usando %s"),
        *SafePosition.ToString());

    return SafePosition;
}

TArray<FVector> ARiverPrismalManager::GetSwimmingLanes() const
{
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Calculando lanes de natação"));

    TArray<FVector> SwimmingLanes;

    if (!RiverSpline || !IsValid(RiverSpline))
    {
        UE_LOG(LogTemp, Warning, TEXT("ARiverPrismalManager: RiverSpline não disponível"));
        return SwimmingLanes;
    }

    // Calcular lanes paralelas ao rio usando spline
    float SplineLength = RiverSpline->GetSplineLength();
    const int32 NumSegments = 20; // Dividir em 20 segmentos
    const float SegmentLength = SplineLength / NumSegments;

    // Criar 3 lanes: centro, margem esquerda e margem direita
    const int32 NumLanes = 3;
    const float LaneOffsets[NumLanes] = { 0.0f, -RiverWidth * 0.3f, RiverWidth * 0.3f };

    for (int32 Lane = 0; Lane < NumLanes; Lane++)
    {
        for (int32 Segment = 0; Segment <= NumSegments; Segment++)
        {
            float Distance = Segment * SegmentLength;

            // Obter posição e direção no spline
            FVector SplineLocation = RiverSpline->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            FVector SplineDirection = RiverSpline->GetDirectionAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);

            // Calcular direção perpendicular para offset lateral
            FVector RightVector = FVector::CrossProduct(SplineDirection, FVector::UpVector).GetSafeNormal();

            // Aplicar offset da lane
            FVector LanePosition = SplineLocation + (RightVector * LaneOffsets[Lane]);

            // Ajustar para superfície da água
            LanePosition = GetWaterSurfacePosition(LanePosition);

            // Verificar se a posição é navegável
            if (IsPositionInWater(LanePosition) && CalculateWaterDepth(LanePosition) > 50.0f)
            {
                SwimmingLanes.Add(LanePosition);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: %d pontos de lanes de natação calculados"), SwimmingLanes.Num());

    return SwimmingLanes;
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES DE VALIDAÇÃO =====

bool ARiverPrismalManager::ValidateWaterPhysics() const
{
    UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Validando física da água"));

    bool bAllValid = true;
    TArray<FString> ValidationErrors;

    // Validar configuração da água
    if (WaterConfig.FlowSpeed <= 0.0f)
    {
        ValidationErrors.Add(TEXT("FlowSpeed deve ser maior que zero"));
        bAllValid = false;
    }

    if (RiverWidth <= 0.0f)
    {
        ValidationErrors.Add(TEXT("RiverWidth deve ser maior que zero"));
        bAllValid = false;
    }

    if (RiverDepth <= 0.0f)
    {
        ValidationErrors.Add(TEXT("RiverDepth deve ser maior que zero"));
        bAllValid = false;
    }

    // Validar spline do rio
    if (!RiverSpline || !IsValid(RiverSpline))
    {
        ValidationErrors.Add(TEXT("RiverSpline não está configurado"));
        bAllValid = false;
    }
    else
    {
        if (RiverSpline->GetNumberOfSplinePoints() < 2)
        {
            ValidationErrors.Add(TEXT("RiverSpline deve ter pelo menos 2 pontos"));
            bAllValid = false;
        }

        if (RiverSpline->GetSplineLength() < 1000.0f)
        {
            ValidationErrors.Add(TEXT("RiverSpline muito curto (mínimo 10 metros)"));
            bAllValid = false;
        }
    }

    // Validar segmentos do rio
    for (int32 i = 0; i < RiverSegments.Num(); i++)
    {
        const FRiverSegment& Segment = RiverSegments[i];

        if (Segment.Width <= 0.0f)
        {
            ValidationErrors.Add(FString::Printf(TEXT("Segmento %d tem largura inválida: %.2f"), i, Segment.Width));
            bAllValid = false;
        }

        if (Segment.Depth <= 0.0f)
        {
            ValidationErrors.Add(FString::Printf(TEXT("Segmento %d tem profundidade inválida: %.2f"), i, Segment.Depth));
            bAllValid = false;
        }

        if (Segment.FlowSpeed < 0.0f)
        {
            ValidationErrors.Add(FString::Printf(TEXT("Segmento %d tem velocidade de fluxo negativa: %.2f"), i, Segment.FlowSpeed));
            bAllValid = false;
        }
    }

    // Validar ilha central se habilitada
    if (bGenerateIsland)
    {
        if (CentralIsland.Radius <= 0.0f)
        {
            ValidationErrors.Add(TEXT("Raio da ilha central deve ser maior que zero"));
            bAllValid = false;
        }

        if (CentralIsland.Radius > RiverWidth * 0.8f)
        {
            ValidationErrors.Add(TEXT("Raio da ilha muito grande em relação à largura do rio"));
            bAllValid = false;
        }
    }

    // Validar pontes
    for (int32 i = 0; i < Bridges.Num(); i++)
    {
        const FBridgeData& Bridge = Bridges[i];

        if (Bridge.Length <= 0.0f || Bridge.Width <= 0.0f)
        {
            ValidationErrors.Add(FString::Printf(TEXT("Ponte %d tem dimensões inválidas: %.2fx%.2f"),
                i, Bridge.Length, Bridge.Width));
            bAllValid = false;
        }

        if (Bridge.Height < 100.0f)
        {
            ValidationErrors.Add(FString::Printf(TEXT("Ponte %d muito baixa: %.2f (mínimo 1 metro)"),
                i, Bridge.Height));
            bAllValid = false;
        }
    }

    // Log dos erros encontrados
    if (!bAllValid)
    {
        UE_LOG(LogTemp, Error, TEXT("ARiverPrismalManager: %d erros de validação encontrados:"), ValidationErrors.Num());
        for (const FString& Error : ValidationErrors)
        {
            UE_LOG(LogTemp, Error, TEXT("  - %s"), *Error);
        }
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("ARiverPrismalManager: Validação da física da água bem-sucedida"));
    }

    return bAllValid;
}

void ARiverPrismalManager::DrawDebugBridges() const
{
    if (!GetWorld() || !bShowDebugLines)
    {
        return;
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Desenhando debug das pontes"));

    for (int32 i = 0; i < Bridges.Num(); i++)
    {
        const FBridgeData& Bridge = Bridges[i];

        // Desenhar caixa da ponte
        FVector BridgeExtent = FVector(Bridge.Length * 0.5f, Bridge.Width * 0.5f, Bridge.Height * 0.5f);

        DrawDebugBox(
            GetWorld(),
            Bridge.Position,
            BridgeExtent,
            Bridge.Rotation.Quaternion(),
            FColor(139, 69, 19), // Cor marrom
            false,
            -1.0f,
            0,
            5.0f
        );

        // Desenhar linha central da ponte
        FVector ForwardVector = Bridge.Rotation.Vector();
        FVector BridgeStart = Bridge.Position - (ForwardVector * Bridge.Length * 0.5f);
        FVector BridgeEnd = Bridge.Position + (ForwardVector * Bridge.Length * 0.5f);

        DrawDebugLine(
            GetWorld(),
            BridgeStart,
            BridgeEnd,
            FColor::Yellow,
            false,
            -1.0f,
            0,
            8.0f
        );

        // Desenhar texto com informações da ponte
        FString BridgeInfo = FString::Printf(TEXT("Ponte %d\n%.1fx%.1fx%.1f\n%s"),
            i, Bridge.Length, Bridge.Width, Bridge.Height,
            Bridge.bIsDestructible ? TEXT("Destrutível") : TEXT("Permanente"));

        DrawDebugString(
            GetWorld(),
            Bridge.Position + FVector(0.0f, 0.0f, Bridge.Height + 100.0f),
            BridgeInfo,
            nullptr,
            FColor::White,
            -1.0f
        );

        // Desenhar conexões com unidades autorizadas
        for (AActor* AuthorizedUnit : Bridge.AuthorizedUnits)
        {
            if (IsValid(AuthorizedUnit))
            {
                DrawDebugLine(
                    GetWorld(),
                    Bridge.Position,
                    AuthorizedUnit->GetActorLocation(),
                    FColor::Green,
                    false,
                    -1.0f,
                    0,
                    2.0f
                );
            }
        }
    }
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES MATEMÁTICAS E GEOMÉTRICAS =====

FVector ARiverPrismalManager::RotateVectorAroundAxis(const FVector& Vector, const FVector& Axis, float AngleDegrees) const
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Rotacionando vetor %s ao redor do eixo %s por %.2f graus"),
        *Vector.ToString(), *Axis.ToString(), AngleDegrees);

    // Usar API moderna do UE 5.6 para rotação de vetor
    FQuat RotationQuat = FQuat(Axis.GetSafeNormal(), FMath::DegreesToRadians(AngleDegrees));
    FVector RotatedVector = RotationQuat.RotateVector(Vector);

    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Vetor rotacionado: %s"), *RotatedVector.ToString());

    return RotatedVector;
}

FVector ARiverPrismalManager::ProjectPointOntoRiver(const FVector& Point) const
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Projetando ponto %s no rio"), *Point.ToString());

    if (!RiverSpline || !IsValid(RiverSpline))
    {
        UE_LOG(LogTemp, Warning, TEXT("ARiverPrismalManager: RiverSpline não disponível"));
        return Point;
    }

    // Usar API moderna do UE 5.6 para encontrar ponto mais próximo no spline
    FVector ClosestPoint = RiverSpline->FindLocationClosestToWorldLocation(Point, ESplineCoordinateSpace::World);

    // Ajustar para superfície da água
    ClosestPoint = GetWaterSurfacePosition(ClosestPoint);

    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Ponto projetado: %s"), *ClosestPoint.ToString());

    return ClosestPoint;
}

float ARiverPrismalManager::InterpolateWaterDepth(const FVector& Position) const
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Interpolando profundidade da água em %s"), *Position.ToString());

    if (!RiverSpline || !IsValid(RiverSpline))
    {
        UE_LOG(LogTemp, Warning, TEXT("ARiverPrismalManager: RiverSpline não disponível"));
        return RiverDepth;
    }

    // Encontrar posição mais próxima no spline
    FVector ClosestSplinePoint = RiverSpline->FindLocationClosestToWorldLocation(Position, ESplineCoordinateSpace::World);
    float DistanceAlongSpline = RiverSpline->FindInputKeyClosestToWorldLocation(Position);

    // Calcular distância lateral do centro do rio
    float LateralDistance = FVector::Dist2D(Position, ClosestSplinePoint);
    float NormalizedLateralDistance = FMath::Clamp(LateralDistance / (RiverWidth * 0.5f), 0.0f, 1.0f);

    // Interpolar profundidade baseada na distância do centro
    // Centro do rio é mais profundo, margens são mais rasas
    float CenterDepth = RiverDepth;
    float EdgeDepth = RiverDepth * 0.2f; // 20% da profundidade nas margens

    float InterpolatedDepth = FMath::Lerp(CenterDepth, EdgeDepth, NormalizedLateralDistance);

    // Aplicar variação baseada nos segmentos do rio
    if (RiverSegments.Num() > 0)
    {
        // Encontrar segmento mais próximo
        int32 ClosestSegmentIndex = 0;
        float MinSegmentDistance = FLT_MAX;

        for (int32 i = 0; i < RiverSegments.Num(); i++)
        {
            float SegmentDistance = FVector::Dist(Position, RiverSegments[i].StartPosition);
            if (SegmentDistance < MinSegmentDistance)
            {
                MinSegmentDistance = SegmentDistance;
                ClosestSegmentIndex = i;
            }
        }

        if (RiverSegments.IsValidIndex(ClosestSegmentIndex))
        {
            const FRiverSegment& Segment = RiverSegments[ClosestSegmentIndex];

            // Interpolar com a profundidade do segmento
            float BlendFactor = FMath::Clamp(MinSegmentDistance / 1000.0f, 0.0f, 1.0f); // Influência até 10 metros
            InterpolatedDepth = FMath::Lerp(Segment.Depth, InterpolatedDepth, BlendFactor);
        }
    }

    // Adicionar variação procedural usando ruído
    float NoiseValue = FMath::PerlinNoise2D(FVector2D(Position.X * 0.001f, Position.Y * 0.001f));
    InterpolatedDepth += NoiseValue * (RiverDepth * 0.1f); // Variação de até 10%

    // Garantir profundidade mínima
    InterpolatedDepth = FMath::Max(InterpolatedDepth, 10.0f); // Mínimo 10cm

    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Profundidade interpolada: %.2f (lateral: %.2f, ruído: %.2f)"),
        InterpolatedDepth, NormalizedLateralDistance, NoiseValue);

    return InterpolatedDepth;
}

FVector ARiverPrismalManager::CalculateWaterNormal(const FVector& Position) const
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Calculando normal da água em %s"), *Position.ToString());

    if (!RiverSpline || !IsValid(RiverSpline))
    {
        UE_LOG(LogTemp, Warning, TEXT("ARiverPrismalManager: RiverSpline não disponível"));
        return FVector::UpVector;
    }

    // Obter direção do fluxo no ponto mais próximo do spline
    FVector ClosestSplinePoint = RiverSpline->FindLocationClosestToWorldLocation(Position, ESplineCoordinateSpace::World);
    float InputKey = RiverSpline->FindInputKeyClosestToWorldLocation(Position);

    FVector LocalFlowDirection = RiverSpline->GetDirectionAtSplineInputKey(InputKey, ESplineCoordinateSpace::World);
    LocalFlowDirection.Normalize();

    // Calcular inclinação baseada na configuração da água
    float SurfaceIncline = WaterConfig.SurfaceIncline;

    // Aplicar inclinação na direção do fluxo
    FVector InclinedNormal = FVector::UpVector;

    if (FMath::Abs(SurfaceIncline) > KINDA_SMALL_NUMBER)
    {
        // Rotacionar normal baseado na inclinação
        FVector RightVector = FVector::CrossProduct(LocalFlowDirection, FVector::UpVector).GetSafeNormal();
        FQuat InclineRotation = FQuat(RightVector, FMath::DegreesToRadians(SurfaceIncline));
        InclinedNormal = InclineRotation.RotateVector(FVector::UpVector);
    }

    // Adicionar perturbação baseada em ondas se configurado
    if (WaterConfig.WaveAmplitude > 0.0f)
    {
        float Time = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
        float WaveFrequency = WaterConfig.WaveFrequency;

        // Calcular ondas usando múltiplas frequências para realismo
        float Wave1 = FMath::Sin((Position.X * 0.01f + Time * WaveFrequency) * 2.0f * PI);
        float Wave2 = FMath::Sin((Position.Y * 0.015f + Time * WaveFrequency * 1.3f) * 2.0f * PI) * 0.5f;
        float Wave3 = FMath::Sin(((Position.X + Position.Y) * 0.008f + Time * WaveFrequency * 0.7f) * 2.0f * PI) * 0.3f;

        float CombinedWave = (Wave1 + Wave2 + Wave3) * WaterConfig.WaveAmplitude;

        // Calcular gradiente das ondas para obter normal
        float Gradient = FMath::Cos((Position.X * 0.01f + Time * WaveFrequency) * 2.0f * PI) * 0.01f * WaveFrequency;

        FVector WaveNormal = FVector(-Gradient, 0.0f, 1.0f).GetSafeNormal();

        // Combinar normal inclinada com normal das ondas
        InclinedNormal = (InclinedNormal + WaveNormal * 0.3f).GetSafeNormal();
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("ARiverPrismalManager: Normal calculada: %s (inclinação: %.2f)"),
        *InclinedNormal.ToString(), SurfaceIncline);

    return InclinedNormal;
}

// ===== IMPLEMENTAÇÃO DAS FUNÇÕES AUXILIARES =====

