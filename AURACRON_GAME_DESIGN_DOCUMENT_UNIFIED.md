# 🌟 AURACRON - DOCUMENTO DE DESIGN DE JOGO UNIFICADO
**Versão**: 3.0 - Documento Auditado e Corrigido  
**Data**: Janeiro de 2025  
**Plataforma**: Mobile (Android/iOS) + PC  
**Engine**: Unreal Engine 5.6  
**Tagline**: _"Domine os ambientes. Desperte o Auracron."_

---

## 📋 **ÍNDICE**
1. [<PERSON>isão Geral](#1-visão-geral)
2. [<PERSON><PERSON><PERSON>e Competitiva](#2-análise-competitiva)
3. [Mecânicas Inovadoras](#3-mecânicas-inovadoras)
   - 3.1 [Sistema de Alternância de Ambientes](#31-sistema-de-alternância-de-ambientes)
   - 3.2 [Sistema de Sígilos Auracron](#32-sistema-de-sígilos-auracron)
   - 3.3 [Sistema de Combate Tático](#33-sistema-de-combate-tático)
   - 3.4 [Selva Adaptativa com IA](#34-selva-adaptativa-com-ia)
   - 3.5 [Objetivos Procedurais](#35-objetivos-procedurais)
4. [Direção Visual e Arte](#4-direção-visual-e-arte)
5. [Arquitetura Técnica](#5-arquitetura-técnica)
6. [Progressão e Monetização](#6-progressão-e-monetização)
7. [Terminologia Padronizada](#7-terminologia-padronizada)

---

## 1. 🎯 **VISÃO GERAL**

### **Conceito Central**
**AURACRON** é um MOBA 5v5 revolucionário que combina elementos tradicionais com **ambientes dinâmicos alternantes** e **IA adaptativa**. O diferencial está na capacidade do jogo alternar entre três ambientes distintos durante a partida, criando experiências de combate únicas e objetivos procedurais.

### **Público-Alvo**
- **Primário**: Jogadores de MOBA mobile (18-35 anos)
- **Secundário**: Jogadores de PC buscando inovação no gênero
- **Terciário**: Criadores de conteúdo e streamers

### **Pilares de Design**
1. **📐 EVOLUÇÃO CONSTANTE**: Ambientes que mudam, estratégias que se adaptam
2. **🎮 ACESSIBILIDADE INTELIGENTE**: Complexo para mestres, simples para iniciantes
3. **🤝 COOPERAÇÃO AMPLIADA**: Mecânicas que recompensam trabalho em equipe criativo
4. **⚡ INOVAÇÃO TECNOLÓGICA**: IA, geração procedural, física avançada

### **Identidade da Marca**
- **Nome oficial**: AURACRON
- **Universo**: Reino dos Auracrons
- **Domínios reservados**: auracron.com / auracron.gg / auracron.game

### **Proposta de Valor Única**
- **Ambientes Dinâmicos**: Três ambientes distintos que alteram completamente a estratégia
- **IA Adaptativa**: Sistema que aprende e se adapta ao estilo de jogo dos jogadores
- **Sígilos Auracron**: Sistema de personalização que multiplica as possibilidades estratégicas
- **Objetivos Procedurais**: Conteúdo sempre fresco e imprevisível

---

## 2. ⚔️ **ANÁLISE COMPETITIVA**

### **Posicionamento no Mercado**

| **ASPECTO** | **WILD RIFT** | **MOBILE LEGENDS** | **AURACRON** | **NOSSA VANTAGEM** |
|-------------|---------------|-------------------|---------------|-------------------|
| **Ambiente** | Estático, 3 trilhos fixos | Estático tradicional | 3 ambientes dinâmicos | 🟢 **INOVAÇÃO TOTAL** |
| **Combate** | 2D horizontal | 2D horizontal | Alternância de ambientes | 🟢 **DIFERENCIAL ÚNICO** |
| **Objetivos** | Fixos e previsíveis | Fixos tradicionais | Procedurais + IA adaptativa | 🟢 **TECNOLOGIA SUPERIOR** |
| **Campeões** | 164 fixos | 120+ fixos | 30 no lançamento + Sistema de Sígilos (90 combinações) | 🔴 **MENOR QUANTIDADE** |
| **Selva** | Padrões fixos de spawn | Padrões tradicionais | IA que aprende e adapta | 🟢 **IA REVOLUCIONÁRIA** |
| **Reconhecimento** | ✅ Marca estabelecida | ✅ Marca estabelecida | ❌ Marca nova | 🔴 **DESAFIO** |
| **Base de Jogadores** | ✅ 50M+ jogadores | ✅ 100M+ jogadores | ❌ Zero jogadores | 🔴 **DESAFIO** |
| **Recursos** | ✅ Orçamento Riot Games | ✅ Orçamento Moonton | ❌ Orçamento limitado | 🔴 **DESAFIO** |

### **Estratégia de Diferenciação**
1. **Inovação Tecnológica**: Focar em recursos que grandes estúdios não podem implementar rapidamente
2. **Comunidade Engajada**: Construir base leal através de inovação genuína
3. **Desenvolvimento Ágil**: Iterar rapidamente baseado no feedback da comunidade
4. **Nicho Premium**: Focar em jogadores que buscam experiências inovadoras

### **Análise SWOT**

**Forças:**
- Mecânicas inovadoras únicas no mercado
- Tecnologia de IA adaptativa
- Flexibilidade de desenvolvimento independente
- Potencial para criar nova categoria de MOBA

**Fraquezas:**
- Marca desconhecida
- Recursos limitados
- Equipe pequena
- Sem base de jogadores estabelecida

**Oportunidades:**
- Mercado saturado busca inovação
- Crescimento do mobile gaming
- Interesse em IA e procedural generation
- Potencial para parcerias estratégicas

**Ameaças:**
- Competidores estabelecidos podem copiar inovações
- Alto custo de aquisição de usuários
- Mudanças nas plataformas de distribuição
- Dependência de tecnologia complexa

---

## 3. 🚀 **MECÂNICAS INOVADORAS**

### **3.1 SISTEMA DE ALTERNÂNCIA DE AMBIENTES** 🌍

#### **Conceito Fundamental**
Três ambientes distintos que se alternam durante a partida, cada um com mecânicas únicas e vantagens estratégicas específicas.

#### **Estrutura dos Ambientes**

##### **I. PLANÍCIE RADIANTE**
**Função**: Ambiente base terrestre com foco em combate tradicional

**Características Geológicas:**
- **Campos Cristalinos**: Áreas abertas com recursos que mudam de posição horizontal
- **Cânions Vivos**: Ravinas que se expandem/contraem baseadas em ações dos jogadores
- **Florestas Respirantes**: Vegetação que migra pelo ambiente, fornecendo cobertura dinâmica
- **Pontes Tectônicas**: Estruturas que se formam e desmoronam baseadas no tempo de partida
- **Portais Geotermais**: Pontos de teletransporte horizontal entre áreas distantes da Planície

**Objetivos Exclusivos:**
- **Guardião Prismal**: Criatura épica que empurra trilhos e concede controle territorial
- **Torres Prismais**: Estruturas defensivas com aura de dano em área

**Vantagens Estratégicas:**
- Familiaridade com mecânicas tradicionais de MOBA
- Controle territorial através de estruturas fixas
- Farming eficiente e rotações clássicas

##### **II. FIRMAMENTO ZEPHYR**
**Função**: Ambiente celestial com foco em mobilidade e velocidade

**Características Celestiais:**
- **Campos Orbitais**: Áreas circulares que orbitam pontos centrais
- **Trilhas Aurora**: Caminhos de luz que aparecem/desaparecem com ciclos dia/noite
- **Fortalezas Nuvem**: Posições defensivas móveis que derivam pelo ambiente
- **Jardins Estelares**: Áreas com velocidade aumentada e recursos únicos
- **Portais do Vento**: Pontos de teletransporte horizontal entre áreas distantes do Firmamento

**Objetivos Exclusivos:**
- **Núcleo de Tempestade**: Buff ofensivo que aumenta dano em área
- **Santuários dos Ventos**: Reduz recarga de habilidades de mobilidade

**Vantagens Estratégicas:**
- Controle de rotas através de velocidade aumentada
- Mobilidade aprimorada para rotações rápidas
- Vantagem para campeões com habilidades de mobilidade

##### **III. REINO PURGATÓRIO**
**Função**: Dimensão espelhada com mecânicas invertidas e foco em estratégia reversa

**Características Dimensionais:**
- **Planícies Espectrais**: Versão invertida das Planícies Radiantes
- **Rios de Almas**: Correntes que fluem na direção oposta ao Fluxo Prismal
- **Estruturas Fragmentadas**: Versões corrompidas dos marcos terrestres
- **Zonas de Distorção Temporal**: Áreas onde o tempo flui diferentemente
- **Nexos Sombrios**: Pontos de controle que espelham objetivos terrestres

**Objetivos Exclusivos:**
- **Guardião Espectral**: Versão sombria do Guardião Prismal com habilidades invertidas
- **Torres de Lamentação**: Estruturas que drenam energia dos inimigos

**Vantagens Estratégicas:**
- Estratégias de emboscada e controle territorial
- Mecânicas invertidas que favorecem adaptabilidade
- Vantagem para campeões com habilidades de furtividade

#### **Fluxo Prismal - Elemento Central**

**Conceito de Design:**
Um rio de energia serpentino que atravessa os três ambientes, servindo como objetivo principal e espinha dorsal estratégica.

**Características Físicas:**
- **Largura**: Varia de 20-50 unidades, criando pontos de estrangulamento
- **Padrão de Fluxo**: Caminho serpentino que muda a cada 10 minutos
- **Design Visual**: Energia prismática que muda de cor baseada no controle de equipe
- **Força da Corrente**: Velocidade variável que afeta movimento e habilidades

**Ilha Central Auracron:**
- **Localização**: Curva central do Fluxo Prismal
- **Setores**: Nexus (recursos), Santuário (cura), Arsenal (upgrades), Caos (alto risco/recompensa)
- **Controle**: Parcial ou total baseado na dominação de setores

#### **Sistema de Trilhos Dinâmicos**

##### **Trilhos Solares**
- **Aparência**: Correntes de energia dourada
- **Função**: Boost de velocidade e regeneração de vida
- **Comportamento**: Mais forte durante o "dia" do ciclo de jogo
- **Estratégia**: Permite rotações agressivas e sustain em combate

##### **Trilhos Axis**
- **Aparência**: Canais prateados neutros
- **Função**: Teletransporte horizontal de alta velocidade
- **Comportamento**: Ativa baseado no controle de pontos nexus
- **Estratégia**: Crítico para rotações rápidas e reposicionamento tático

##### **Trilhos Lunares**
- **Aparência**: Caminhos etéreos azul-branco
- **Função**: Furtividade e visão aprimorada
- **Comportamento**: Visível apenas durante a "noite" do ciclo
- **Estratégia**: Permite manobras de flanqueamento

#### **Mecânica de Teletransporte Horizontal**

**Conceito Técnico:**
Cada ambiente possui pontos de teletransporte para reposicionamento tático horizontal.

**Processo de Teletransporte:**
1. **Ativação**: Interação com Portais de Posicionamento Tático
2. **Efeito Visual**: Transição de 1-2 segundos com efeitos específicos
3. **Teletransporte**: Jogador é movido para posição estratégica distante no mesmo ambiente
4. **Reposicionamento**: Aparição em pontos táticos predefinidos horizontalmente



**Tipos de Portais por Ambiente:**
- **Portais Radiantes**: Energia dourada, teletransporte na Planície Radiante
- **Portais Zephyr**: Energia prateada, teletransporte no Firmamento Zephyr
- **Portais Umbrais**: Energia violeta, teletransporte no Reino Purgatório

#### **Fases da Partida**

**FASE 1: DESPERTAR (0-15 minutos)**
- Todos os ambientes acessíveis
- Trilhos a 50% de poder
- Foco em farming e posicionamento inicial

**FASE 2: CONVERGÊNCIA (15-25 minutos)**
- Fronteiras entre ambientes começam a se confundir
- Trilhos atingem poder total
- Objetivos principais se tornam ativos

**FASE 3: INTENSIFICAÇÃO (25-35 minutos)**
- Trilhos se intersectam
- Mudanças dramáticas de terreno
- Novos caminhos aparecem

**FASE 4: RESOLUÇÃO (35+ minutos)**
- Convergência final dos ambientes
- Trilhos convergem
- Efeitos finais intensificados

### **3.2 SISTEMA DE SÍGILOS AURACRON** 👥

#### **Mecânica Central**
Sistema de personalização que permite aos jogadores escolher especializações que se fundem aos campeões, criando arquétipos únicos.

**Processo:**
1. **Seleção**: Durante a tela de seleção, cada jogador escolhe 1 de 3 Sígilos
2. **Fusão**: O Sígilo se funde ao campeão aos 6 minutos de jogo
3. **Evolução**: Desbloqueia árvore de habilidades alternativa
4. **Re-forja**: Pode ser alterado uma vez por partida no Núcleo Auracron

**Combinatória:**
30 campeões (lançamento) × 3 Sígilos = 90 arquétipos únicos
*Meta: 50 campeões até final do Ano 1 = 150 arquétipos únicos*

#### **Tipos de Sígilos**

| **Sígilo** | **Bônus Passivo** | **Habilidade Exclusiva** | **Arquétipo** |
|------------|-------------------|--------------------------|---------------|
| **Aegis** (Tanque) | +15% HP, Armadura adaptativa | "Muralha Prismal" - barreira circular 3s | Linha de Frente |
| **Ruin** (Dano) | +12% ATK/AP adaptativo | "Fragmento Prismal" - reset parcial de recarga | Explosão/DPS |
| **Vesper** (Utilidade) | +10% Vel. Move + 8% Recarga | "Sopro do Fluxo" - dash aliado + escudo | Suporte/Roamer |

#### **Impacto Estratégico**
- **Flexibilidade**: Permite adaptação durante a seleção de campeões
- **Expressão Individual**: Cada jogador pode personalizar seu estilo
- **Balanceamento**: Evita meta rígida através da variedade de combinações
- **Profundidade**: Cria camadas adicionais de maestria e aprendizado

### **3.3 SISTEMA DE COMBATE TÁTICO** ⚔️

#### **Combate Adaptativo por Ambiente**

**Planície Radiante:**
- **Estilo**: Combate tradicional de MOBA
- **Alcance**: Padrão (800 unidades)
- **Área de Efeito**: Média (300 unidades)
- **Foco**: Posicionamento horizontal e controle de trilhos

**Firmamento Zephyr:**
- **Estilo**: Combate com alta mobilidade
- **Alcance**: Estendido (1000 unidades)
- **Área de Efeito**: Ampla (400 unidades)
- **Foco**: Velocidade aumentada e rotações rápidas
- **Navegação**: Personagens usam Trilhas Aurora e Portais do Vento para teletransporte

**Reino Purgatório:**
- **Estilo**: Combate com mecânicas invertidas
- **Alcance**: Variável (600-1200 unidades)
- **Área de Efeito**: Dinâmica (200-500 unidades)
- **Foco**: Adaptabilidade e estratégias não convencionais

#### **Mecânicas de Combate Únicas**

**Ressonância Prismal:**
- Habilidades ganham efeitos adicionais baseados no ambiente atual
- Combos específicos por ambiente
- Sinergia entre Sígilos e ambiente

**Fluxo de Energia:**
- Sistema de recursos que varia por ambiente
- Regeneração diferente em cada ambiente
- Custos de habilidades adaptados ao contexto

### **3.4 SELVA ADAPTATIVA COM IA** 🤖

#### **Sistema de Aprendizado de Máquina**

**Análise Comportamental:**
- **Padrões de Movimento**: Monitora rotas preferidas dos jogadores
- **Timing de Limpeza**: Aprende horários de farm da selva
- **Estratégias de Equipe**: Identifica composições e táticas
- **Adaptação Individual**: Personaliza desafios por jogador

**Elementos Adaptativos:**
- **Spawn Dinâmico**: Camps aparecem baseados em padrões de limpeza
- **Dificuldade Escalável**: Criaturas se tornam mais desafiadoras
- **Contra-estratégias**: IA cria situações que contrariam padrões repetitivos
- **Recompensas Balanceadas**: Ajusta drops baseado na performance

#### **Criaturas Inteligentes**

**Comportamento Evolutivo:**
- **Memória de Encontros**: Criaturas "lembram" de jogadores específicos
- **Adaptação Tática**: Mudam comportamento baseado em derrotas anteriores
- **Cooperação**: Criaturas próximas coordenam ataques
- **Migração**: Camps se movem para evitar farming excessivo

**Tipos de Adaptação:**
- **Defensiva**: Criaturas se tornam mais cautelosas
- **Agressiva**: Ataques mais coordenados e frequentes
- **Evasiva**: Tentativas de fuga quando em desvantagem
- **Cooperativa**: Chamam reforços de camps próximos

### **3.5 OBJETIVOS PROCEDURAIS** 🎲

#### **Sistema de Geração Dinâmica**

**Análise de Estado da Partida:**
- **Diferença de Ouro**: Monitora vantagem econômica entre equipes
- **Contagem de Abates**: Rastreia diferença de kills
- **Controle Territorial**: Avalia dominação de objetivos
- **Ritmo de Jogo**: Detecta se a partida está muito passiva ou agressiva

**Tipos de Objetivos Gerados:**

**Objetivos de Recuperação:**
- **Fragmentos de Esperança**: Spawnam quando uma equipe está >15% atrás
- **Portais de Reversão**: Permitem comeback através de plays arriscadas
- **Cristais de Equalização**: Fornecem bônus temporários para equipe perdedora

**Objetivos de Agressão:**
- **Catalisadores de Conflito**: Forçam team fights em localizações estratégicas
- **Zonas de Dominação**: Áreas que devem ser controladas por tempo determinado
- **Relíquias de Poder**: Buffs poderosos que exigem coordenação de equipe

**Objetivos de Ritmo:**
- **Aceleradores Temporais**: Reduzem timers quando jogo está muito lento
- **Estabilizadores**: Criam pausas estratégicas quando ritmo está muito rápido
- **Diversificadores**: Introduzem elementos novos para quebrar monotonia

#### **Algoritmo de Spawn**

**Fatores de Decisão:**
1. **Estado da Partida** (40%): Diferenças entre equipes
2. **Tempo de Jogo** (25%): Fase atual da partida
3. **Atividade Recente** (20%): Nível de engajamento
4. **Composição de Equipes** (15%): Arquétipos e Sígilos escolhidos

**Validação de Spawn:**
- **Localização Estratégica**: Objetivos aparecem em pontos tacticamente relevantes
- **Timing Balanceado**: Evita sobrecarga de objetivos simultâneos
- **Impacto Mensurável**: Cada objetivo tem potencial de influenciar o resultado
- **Contraplay Disponível**: Sempre existe forma de contestar ou contornar

---

## 4. 🎨 **DIREÇÃO VISUAL E ARTE**

### **Identidade Visual Geral**

**Estilo Artístico:**
- **Realismo Estilizado**: Equilibrio entre realismo e fantasia
- **Paleta Dinâmica**: Cores que mudam baseadas no ambiente ativo
- **Iluminação Dramática**: Uso de luz para criar atmosfera e guiar jogadores
- **Detalhamento Escalável**: Níveis de detalhe adaptados ao hardware

### **Design Visual por Ambiente**

#### **Planície Radiante - Linguagem Terrestre**

**Paleta de Cores:**
- **Primárias**: Verde esmeralda, marrons terrosos
- **Secundárias**: Azuis cristalinos, laranjas vulcânicos
- **Acentos**: Dourados metálicos para recursos

**Filosofia de Texturas:**
- Superfícies orgânicas com padrões de erosão
- Vegetação viva que reage ao ambiente
- Cristais que pulsam com energia

**Iluminação:**
- Ciclo natural dia/noite
- Sombras dinâmicas
- Bioluminescência sutil

#### **Firmamento Zephyr - Linguagem Celestial**

**Paleta de Cores:**
- **Primárias**: Roxos suaves, brancos etéreos
- **Secundárias**: Verdes aurora, azuis cósmicos
- **Acentos**: Pratas estelares

**Filosofia de Texturas:**
- Superfícies translúcidas
- Padrões de constelações
- Efeitos de refração

**Iluminação:**
- Brilho estelar omnidirecional
- Refrações prismáticas
- Auroras dinâmicas

#### **Reino Purgatório - Linguagem Espectral**

**Paleta de Cores:**
- **Primárias**: Violetas espectrais, cinzas etéreos
- **Secundárias**: Azuis fantasmagóricos, vermelhos sangue
- **Acentos**: Pratas espectrais

**Filosofia de Texturas:**
- Superfícies semi-transparentes
- Reflexos distorcidos
- Materiais interdimensionais

**Iluminação:**
- Iluminação invertida
- Auras espectrais
- Distorções luminosas

### **Sistema de Efeitos Visuais**

#### **Trilhos Dinâmicos**
- **Solares**: Partículas douradas com efeitos de calor
- **Axis**: Padrões geométricos com distorção gravitacional
- **Lunares**: Névoa etérea com partículas estelares

#### **Fluxo Prismal**
- **Estado Base**: Cristal líquido com reflexões prismáticas
- **Controle de Equipe**: Mudança de cor baseada na dominação
- **Intensidade**: Efeitos que escalam com a fase da partida

### **Otimização Visual Escalável**

#### **Níveis de Qualidade**

**Baixa (Entry Level):**
- Geometria simplificada
- Partículas mínimas
- Texturas comprimidas
- Efeitos básicos

**Média (Mid-Range):**
- Geometria moderada
- Partículas seletivas
- Texturas balanceadas
- Efeitos importantes

**Alta (High-End):**
- Geometria completa
- Todas as partículas
- Texturas em alta resolução
- Todos os efeitos visuais

#### **Sistema LOD Adaptativo**
- **Distância**: Redução de detalhes baseada na distância
- **Performance**: Ajuste automático baseado no framerate
- **Hardware**: Detecção automática de capacidades
- **Priorização**: Foco em elementos importantes para gameplay

---

## 5. 🔧 **ARQUITETURA TÉCNICA**

### **Engine e Tecnologias Core**

#### **Unreal Engine 5.6 - Configuração Escalável**

**Recursos por Nível de Hardware:**

**Entry Level (2GB RAM):**
- **Lumen**: Desabilitado, iluminação estática
- **Nanite**: Desabilitado, geometria tradicional
- **Chaos Physics**: Física simplificada
- **Rendering**: Forward rendering
- **World Partition**: Streaming básico
- **Resolução**: 720p máximo

**Mid-Range (3GB RAM):**
- **Lumen**: Simplificado para áreas principais
- **Nanite**: Seletivo para objetos importantes
- **Chaos Physics**: Moderada com destruição limitada
- **Rendering**: Deferred rendering básico
- **World Partition**: Streaming otimizado
- **Resolução**: 1080p máximo

**High-End (4GB+ RAM):**
- **Lumen**: Sistema completo de iluminação global
- **Nanite**: Geometria virtualizada completa
- **Chaos Physics**: Sistema completo
- **Rendering**: Completo com ray tracing opcional
- **World Partition**: Streaming avançado com predição
- **Resolução**: 1080p+ com opções de upscaling

### **Arquitetura de Rede**

#### **Sistema Multiplayer**

**Servidor Autoritativo:**
- **Validação**: Todas as ações críticas validadas server-side
- **Anti-Cheat**: Detecção de speed hacks e anomalias
- **Sincronização**: Estados de jogo replicados em tempo real
- **Latência**: Compensação de lag e predição de movimento

**Otimizações de Rede:**
- **Compressão Delta**: Redução de largura de banda
- **Interest Management**: Replicação baseada em relevância
- **Rollback**: Correção de dessincronia
- **Predição**: Execução local com validação posterior

#### **Backend Services**

**Epic Online Services (EOS):**
- **Matchmaking**: Sistema baseado em skill rating
- **Amigos**: Sistema cross-platform
- **Conquistas**: Sistema unificado
- **Voice Chat**: Integração com Vivox
- **Anti-Cheat**: EOS Anti-Cheat integrado

**Firebase Integration:**
- **Dados Persistentes**: Progresso e estatísticas
- **Analytics**: Telemetria de gameplay
- **Configuração Remota**: Balanceamento dinâmico
- **Crash Reporting**: Monitoramento de estabilidade

### **Sistema de IA Adaptativa**

#### **Machine Learning Pipeline**

**Coleta de Dados:**
- **Comportamento**: Padrões de movimento e decisão
- **Performance**: Métricas de eficiência
- **Estratégias**: Análise de composições e táticas
- **Timing**: Padrões temporais de ações

**Processamento:**
- **Análise de Padrões**: Identificação de comportamentos recorrentes
- **Predição**: Antecipação de ações futuras
- **Adaptação**: Ajuste de parâmetros de jogo
- **Validação**: Teste de eficácia das adaptações

**Implementação:**
- **Spawn Dinâmico**: Ajuste de camps da selva
- **Dificuldade**: Escalonamento baseado em skill
- **Objetivos**: Geração procedural contextual
- **Balanceamento**: Ajustes em tempo real

### **Sistema de Geração Procedural**

#### **Objetivos Dinâmicos**

**Algoritmo de Geração:**
```
Função GerarObjetivo():
    estado_partida = AnalisarEstadoPartida()
    peso_recuperacao = CalcularDesequilibrio(estado_partida)
    peso_agressao = CalcularPassividade(estado_partida)
    peso_ritmo = CalcularRitmo(estado_partida)
    
    tipo_objetivo = SelecionarTipo(peso_recuperacao, peso_agressao, peso_ritmo)
    localizacao = EncontrarLocalizacaoEstrategica(tipo_objetivo)
    parametros = CalcularParametros(estado_partida, tipo_objetivo)
    
    RetornarObjetivo(tipo_objetivo, localizacao, parametros)
```

**Validação de Spawn:**
- **Relevância Estratégica**: Objetivos em posições tacticamente importantes
- **Timing Balanceado**: Evita sobrecarga de objetivos
- **Contraplay**: Sempre existe forma de contestar
- **Impacto Mensurável**: Potencial de influenciar resultado

### **Performance e Otimização**

#### **Targets de Performance**

| **Plataforma** | **FPS Mínimo** | **Resolução** | **RAM Máxima** |
|----------------|----------------|---------------|----------------|
| **Mobile Entry** | 30 FPS | 720p | 2GB |
| **Mobile Mid** | 45 FPS | 1080p | 3GB |
| **Mobile High** | 60 FPS | 1080p+ | 4GB |
| **PC** | 60-120 FPS | 1080p-4K | 8-16GB |

#### **Sistema de Adaptação Automática**

**Detecção de Hardware:**
- **CPU**: Identificação de núcleos e frequência
- **GPU**: Detecção de modelo e VRAM
- **RAM**: Quantidade disponível
- **Storage**: Tipo e velocidade

**Ajuste Dinâmico:**
- **Qualidade Gráfica**: Redução automática se FPS < target
- **Efeitos**: Desabilitação seletiva de efeitos custosos
- **LOD**: Ajuste de distâncias de renderização
- **Partículas**: Redução de densidade

### **Segurança e Anti-Cheat**

#### **Validação Server-Side**
- **Movimento**: Verificação de velocidade e física
- **Habilidades**: Validação de cooldowns e recursos
- **Dano**: Cálculo autoritativo no servidor
- **Recursos**: Controle de ouro e experiência

#### **Detecção de Anomalias**
- **Estatísticas**: Identificação de performance impossível
- **Comportamento**: Detecção de padrões não humanos
- **Timing**: Análise de precisão sobre-humana
- **Rede**: Monitoramento de pacotes suspeitos

#### **Sistema Anti-Cheat Detalhado**

**EOS Anti-Cheat Integration:**
- **Detecção de Injeção**: Monitora modificações de memória
- **Análise de Comportamento**: Machine learning para detectar padrões suspeitos
- **Validação de Input**: Verificação de comandos impossíveis
- **Proteção de Integridade**: Verificação de arquivos do jogo

**Penalidades Progressivas:**
- **1ª Infração**: Aviso + monitoramento intensificado
- **2ª Infração**: Suspensão de 24 horas
- **3ª Infração**: Suspensão de 7 dias
- **4ª Infração**: Suspensão de 30 dias
- **5ª Infração**: Banimento permanente

**Sistema de Apelação:**
- **Revisão Manual**: Para casos complexos
- **Evidência de Vídeo**: Análise de replays suspeitos
- **Tempo de Resposta**: Máximo 72 horas
- **Transparência**: Relatórios detalhados de banimentos

### **Sistema de Comunicação e Social**

#### **Comunicação In-Game**

**Chat de Texto:**
- **Chat de Equipe**: Comunicação apenas com aliados
- **Chat Global**: Comunicação com todos os jogadores (pré e pós partida)
- **Filtro de Toxicidade**: IA para detectar e filtrar linguagem tóxica
- **Sistema de Ping**: 8 tipos de ping contextuais

**Chat de Voz:**
- **Integração Vivox**: Sistema de voz de alta qualidade
- **Push-to-Talk**: Padrão para evitar ruído
- **Controle Individual**: Mutar jogadores específicos
- **Qualidade Adaptativa**: Ajuste automático baseado na conexão

#### **Sistema Social**

**Lista de Amigos:**
- **Cross-Platform**: Amigos entre PC e Mobile
- **Status Online**: Visibilidade de atividade
- **Convites para Partida**: Sistema integrado
- **Histórico de Jogos**: Últimas 10 partidas juntos

**Sistema de Clãs:**
- **Criação**: Máximo 50 membros por clã
- **Hierarquia**: Líder, Oficiais, Membros
- **Chat de Clã**: Comunicação persistente
- **Eventos de Clã**: Torneios internos e desafios

**Comportamento e Moderação:**
- **Sistema de Honra**: Recompensas por comportamento positivo
- **Relatórios**: Sistema simplificado de denúncias
- **Revisão Automática**: IA para análise inicial
- **Moderação Humana**: Para casos complexos

---

## 6. 💰 **PROGRESSÃO E MONETIZAÇÃO**

### **Modelo de Monetização Ética**

#### **Princípios Fundamentais**
1. **Sem Pay-to-Win**: Nenhuma vantagem competitiva através de pagamento
2. **Transparência**: Preços claros e sem pegadinhas
3. **Valor Real**: Conteúdo premium oferece valor genuíno
4. **Acessibilidade**: Experiência completa disponível gratuitamente

### **Sistema de Progressão**

#### **Nível de Conta (1-500)**

**Marcos de Progressão:**
- **Nível 10**: Desbloqueio do modo ranqueado
- **Nível 15**: Acesso aos Sígilos Auracron (correção: anteriormente mencionado nível 25)
- **Nível 30**: Sistema de maestria de ambiente
- **Nível 50**: Criação de lobbies personalizados
- **Nível 100**: Acesso a beta de novos recursos
- **Nível 200**: Sistema de mentoria para novos jogadores
- **Nível 500**: Status lendário com recompensas exclusivas

#### **Sistema de Ranqueado**

**Divisões e Ligas:**
- **Bronze** (I-III): Jogadores iniciantes (0-1.200 pontos)
- **Prata** (I-III): Jogadores intermediários (1.200-1.800 pontos)
- **Ouro** (I-III): Jogadores avançados (1.800-2.400 pontos)
- **Platina** (I-III): Jogadores experientes (2.400-3.000 pontos)
- **Diamante** (I-III): Jogadores elite (3.000-3.600 pontos)
- **Mestre**: Top 500 jogadores por região (3.600+ pontos)
- **Grão-Mestre**: Top 200 jogadores por região
- **Desafiante**: Top 50 jogadores por região

**Sistema de Pontos:**
- **Vitória**: +15 a +25 pontos (baseado em MMR)
- **Derrota**: -10 a -20 pontos (baseado em MMR)
- **Proteção contra Queda**: 3 derrotas consecutivas necessárias
- **Reset Sazonal**: Soft reset a cada 3 meses

**Experiência por Atividade:**
- **Partida Completa**: 100-300 XP (baseado em duração)
- **Vitória**: +50% bônus
- **Primeira Vitória do Dia**: +100% bônus
- **Comportamento Positivo**: +25% bônus
- **Jogar com Amigos**: +15% bônus

#### **Maestria de Campeão (1-10)**

**Sistema de Progressão:**
- **Maestria 1-3**: Recompensas cosméticas básicas
- **Maestria 4-6**: Variações de skin e efeitos
- **Maestria 7-8**: Emotes e animações exclusivos
- **Maestria 9-10**: Títulos e bordas especiais

**Requisitos por Nível:**
- **Partidas Jogadas**: Número mínimo de jogos
- **Performance**: KDA e participação em objetivos
- **Vitórias**: Percentual mínimo de vitórias
- **Versatilidade**: Uso de diferentes Sígilos

#### **Maestria de Ambiente (Sistema Único)**

**Planície Radiante - Especialista Terrestre:**
- **Bônus**: +10% XP em objetivos terrestres
- **Habilidade**: Detecção aprimorada de emboscadas
- **Cosmético**: Efeitos dourados em habilidades

**Firmamento Zephyr - Mestre Celestial:**
- **Bônus**: +15% velocidade de movimento em plataformas
- **Habilidade**: Visão estendida de pontos elevados
- **Cosmético**: Efeitos prateados e partículas estelares

**Reino Purgatório - Especialista Espectral:**
- **Bônus**: +20% dano em emboscadas
- **Habilidade**: Detecção de inimigos em furtividade
- **Cosmético**: Aura espectral e efeitos sombrios

### **Monetização Premium**

#### **Battle Pass Adaptativo**

**Trilha Gratuita:**
- 50 níveis de recompensas gratuitas
- Campeões, skins básicas, moeda do jogo
- Acessível a todos os jogadores

**Trilha Premium (R$ 30):**
- 100 níveis de recompensas premium
- Skins exclusivas, efeitos visuais, emotes
- Valor equivalente a R$ 150 em conteúdo

**Trilhas Especializadas (R$ 15 cada):**
- **Trilha de Suporte**: Focada em campeões e itens de suporte
- **Trilha de Dano**: Skins e efeitos para campeões de dano
- **Trilha de Tanque**: Conteúdo para especialistas em tanque
- **Trilha de Selva**: Recompensas para junglers

#### **Aquisição de Campeões**

**Sistema de Moedas:**
- **Essência Azul** (Gratuita): Obtida jogando
- **Cristais Auracron** (Premium): Comprados com dinheiro real
- **Taxa de Conversão**: 100 Cristais = R$ 5,00

**Preços de Campeões:**
- **Campeões Básicos**: 1.500 Essência Azul ou 300 Cristais
- **Campeões Avançados**: 3.000 Essência Azul ou 500 Cristais
- **Campeões Épicos**: 4.500 Essência Azul ou 700 Cristais
- **Taxa de Ganho**: 1 campeão básico por semana jogando regularmente
- **Bônus**: Primeira vitória do dia, missões semanais

**Rotação Gratuita:**
- **20 Campeões/Semana**: Maior que a concorrência
- **Favoritos da Comunidade**: Rotação especial baseada em votação
- **Novos Lançamentos**: 2 semanas gratuitas para novos campeões

#### **Cosméticos Premium**

**Skins de Campeão:**
- **Básicas**: 300 Cristais (R$ 15) - Mudança de modelo e cores
- **Épicas**: 600 Cristais (R$ 30) - Novos efeitos visuais e sonoros
- **Lendárias**: 1.000 Cristais (R$ 50) - Transformação completa com novas animações

**Temas de Ambiente:**
- **Sazonais**: 400 Cristais (R$ 20) - Versões temáticas dos ambientes
- **Comunitários**: 400 Cristais (R$ 20) - Criados baseados em feedback da comunidade
- **Especiais**: 500 Cristais (R$ 25) - Eventos limitados e colaborações

**Personalizações:**
- **Emotes**: 100 Cristais (R$ 5) - Expressões e celebrações
- **Bordas**: 200 Cristais (R$ 10) - Molduras para perfil
- **Efeitos de Recall**: 300 Cristais (R$ 15) - Animações de retorno à base

### **Sistema de Recompensas**

#### **Missões Diárias**
- **Jogar 1 Partida**: 50 Essência Azul
- **Conseguir 5 Abates**: 75 Essência Azul
- **Vencer 1 Partida**: 100 Essência Azul
- **Usar Sígilo Específico**: 25 Essência Azul

#### **Missões Semanais**
- **Vencer 5 Partidas**: 500 Essência Azul
- **Jogar em Todos os Ambientes**: 300 Essência Azul
- **Conseguir 25 Assistências**: 400 Essência Azul
- **Completar Objetivos**: 350 Essência Azul

#### **Eventos Especiais**
- **Eventos Mensais**: Recompensas temáticas exclusivas
- **Torneios Comunitários**: Skins e títulos especiais
- **Colaborações**: Conteúdo crossover limitado
- **Aniversário**: Recompensas comemorativas anuais

### **Análise de Receita**

#### **Projeções Conservadoras (Ano 1)**

**Base de Usuários:**
- **Mês 1**: 10.000 usuários ativos
- **Mês 6**: 100.000 usuários ativos
- **Mês 12**: 500.000 usuários ativos

**Conversão para Pagante:**
- **Taxa de Conversão**: 5% (conservadora)
- **Gasto Médio por Usuário Pagante**: R$ 30/mês
- **Receita Mensal (Mês 12)**: R$ 750.000

**Distribuição de Receita:**
- **Battle Pass (35%)**: R$ 262.500
- **Skins (40%)**: R$ 300.000
- **Campeões Premium (15%)**: R$ 112.500
- **Outros Cosméticos (10%)**: R$ 75.000

**Justificativa da Revisão:**
- Aumento do gasto médio devido ao sistema de Cristais Auracron
- Maior foco em skins devido à variedade de arquétipos
- Battle Pass mantém atratividade mas com menor participação percentual

---


## 7. 🔤 **TERMINOLOGIA PADRONIZADA**

### **Glossário AURACRON**

| **Termo Tradicional** | **Termo AURACRON** | **Definição** |
|----------------------|-------------------|---------------|
| **Lane** | **Trilho** | Caminhos principais do mapa onde os jogadores avançam |
| **Brush** | **Canopy** | Áreas de vegetação que fornecem ocultação |
| **Ward** | **Baliza** | Dispositivos que fornecem visão de área |
| **River** | **Fluxo Prismal** | Elemento central serpentino que atravessa os ambientes |
| **Baron/Herald** | **Guardião Prismal** | Criatura épica que concede buffs poderosos |
| **Dragon** | **Leviatã Umbrático** | Criatura que fornece buffs permanentes |
| **Jungle** | **Selva Adaptativa** | Área neutra com criaturas controladas por IA |
| **Minions** | **Servos Auracron** | Unidades que avançam pelos trilhos |
| **Nexus** | **Núcleo Auracron** | Estrutura principal que deve ser destruída para vencer |
| **Champion** | **Campeão** | Personagem controlado pelo jogador |
| **Ability** | **Habilidade** | Poder especial do campeão |
| **Ultimate** | **Suprema** | Habilidade mais poderosa do campeão |
| **Recall** | **Retorno** | Ação de voltar à base |
| **Gank** | **Emboscada** | Ataque coordenado contra inimigo isolado |
| **Farm** | **Cultivo** | Ação de eliminar servos para ganhar ouro |
| **Push** | **Avanço** | Ação de avançar pelo trilho |
| **Roam** | **Perambular** | Movimento entre trilhos para ajudar aliados |
| **Split Push** | **Avanço Dividido** | Estratégia de pressionar múltiplos trilhos |
| **Team Fight** | **Combate de Equipe** | Confronto envolvendo múltiplos jogadores |
| **Carry** | **Portador** | Jogador responsável por causar dano principal |
| **Support** | **Apoio** | Jogador responsável por auxiliar a equipe |
| **Tank** | **Tanque** | Jogador responsável por absorver dano |
| **Assassin** | **Assassino** | Jogador especializado em eliminar alvos isolados |
| **Mage** | **Mago** | Jogador especializado em dano mágico |
| **Marksman** | **Atirador** | Jogador especializado em dano à distância |
| **Jungler** | **Selvícola** | Jogador que atua na Selva Adaptativa |

### **Termos Únicos do AURACRON**

| **Termo** | **Definição** |
|-----------|---------------|
| **Sígilos Auracron** | Sistema de especialização que se funde aos campeões |
| **Planície Radiante** | Ambiente terrestre com mecânicas tradicionais |
| **Firmamento Zephyr** | Ambiente celestial com foco em mobilidade |
| **Reino Purgatório** | Ambiente espectral com mecânicas invertidas |
| **Trilhos Solares** | Caminhos de energia dourada que fornecem buffs |
| **Trilhos Axis** | Caminhos neutros para teletransporte horizontal de alta velocidade |
| **Trilhos Lunares** | Caminhos noturnos que concedem furtividade |
| **Portais de Posicionamento** | Pontos de teletransporte horizontal dentro do mesmo ambiente |
| **Ilha Central Auracron** | Estrutura central do Fluxo Prismal com múltiplos setores |
| **Ressonância Prismal** | Efeitos de habilidades que variam por ambiente |
| **Convergência** | Fase final onde ambientes se fundem |
| **Selvícola** | Especialista na Selva Adaptativa |
| **Emboscada Espectral** | Ataque surpresa no Reino Purgatório |
| **Rotação Celestial** | Movimento estratégico no Firmamento Zephyr |
| **Cultivo Prismal** | Coleta de recursos do Fluxo Prismal |
| **Maestria de Ambiente** | Sistema de progressão específico por ambiente |
| **Trilhas Aurora** | Caminhos de luz que aparecem/desaparecem no Firmamento |
| **Portais Geotermais** | Teletransporte horizontal entre pontos da Planície Radiante |
| **Portais do Vento** | Teletransporte horizontal entre áreas distantes do Firmamento |
| **Campos Orbitais** | Áreas circulares que orbitam pontos centrais |
| **Jardins Estelares** | Áreas com velocidade aumentada e recursos únicos |

### **Abreviações e Siglas**

| **Sigla** | **Significado** | **Contexto** |
|-----------|-----------------|---------------|
| **GDD** | Game Design Document | Documentação |
| **UE5** | Unreal Engine 5 | Engine |
| **IA** | Inteligência Artificial | Sistemas adaptativos |
| **ML** | Machine Learning | IA da selva |
| **KPI** | Key Performance Indicator | Métricas |
| **LOD** | Level of Detail | Otimização gráfica |
| **FPS** | Frames Per Second | Performance |
| **MOBA** | Multiplayer Online Battle Arena | Gênero |
| **UI/UX** | User Interface/User Experience | Interface |
| **API** | Application Programming Interface | Integração |
| **SDK** | Software Development Kit | Desenvolvimento |
| **EOS** | Epic Online Services | Backend |
| **VFX** | Visual Effects | Efeitos visuais |
| **SFX** | Sound Effects | Efeitos sonoros |
| **QA** | Quality Assurance | Testes |

---
*"O futuro dos MOBAs não está em fazer mais do mesmo, mas em reimaginar o que é possível quando tecnologia e criatividade se encontram."*

**- Equipe AURACRON**