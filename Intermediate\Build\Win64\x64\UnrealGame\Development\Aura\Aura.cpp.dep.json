{"Version": "1.2", "Data": {"Source": "c:\\aura\\source\\aura\\aura.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\intermediate\\build\\win64\\x64\\aura\\development\\engine\\sharedpch.engine.project.rtti.exceptions.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\intermediate\\build\\win64\\x64\\unrealgame\\development\\aura\\definitions.aura.h", "c:\\aura\\source\\aura\\aura.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}