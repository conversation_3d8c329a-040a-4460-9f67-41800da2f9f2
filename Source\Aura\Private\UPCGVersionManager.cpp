#include "UPCGVersionManager.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Serialization/MemoryWriter.h"
#include "Serialization/MemoryReader.h"
#include "Compression/OodleDataCompression.h"
#include "Misc/SecureHash.h"
#include "TimerManager.h"
#include "Json.h"

// Includes dos componentes MOBA
#include "ALaneManager.h"
#include "ABaronAuracronManager.h"
#include "ADragonPrismalManager.h"
#include "ARiverPrismalManager.h"
#include "AWallCollisionManager.h"
#include "AMinionWaveManager.h"
#include "AMapManager.h"

UPCGVersionManager::UPCGVersionManager()
{
	bIsInitialized = false;
	bAutoBackupActive = false;
	LastBackupTime = 0.0f;
	
	// Configuração padrão
	Config = FPCGVersionConfig();
	
	// Versão inicial
	CurrentVersion = FPCGVersionInfo();
	CurrentVersion.Description = TEXT("Initial MOBA Map Version");
	CurrentVersion.Author = TEXT("PCG System");
}

void UPCGVersionManager::Initialize(const FPCGVersionConfig& InConfig)
{
	if (bIsInitialized)
	{
		UE_LOG(LogTemp, Warning, TEXT("UPCGVersionManager already initialized"));
		return;
	}

	Config = InConfig;
	
	// Criar diretório de backup
	if (!CreateBackupDirectory())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to create backup directory: %s"), *Config.BackupDirectory);
		return;
	}

	// Carregar banco de dados de versões
	LoadVersionDatabase();

	// Iniciar backup automático se habilitado
	if (Config.bAutoBackupEnabled)
	{
		StartAutoBackup();
	}

	bIsInitialized = true;
	UE_LOG(LogTemp, Log, TEXT("UPCGVersionManager initialized successfully"));
}

void UPCGVersionManager::Shutdown()
{
	if (!bIsInitialized)
	{
		return;
	}

	// Parar backup automático
	StopAutoBackup();

	// Salvar banco de dados
	SaveVersionDatabase();

	// Limpar componentes registrados
	RegisteredComponents.Empty();

	bIsInitialized = false;
	UE_LOG(LogTemp, Log, TEXT("UPCGVersionManager shutdown completed"));
}

void UPCGVersionManager::SetConfiguration(const FPCGVersionConfig& InConfig)
{
	Config = InConfig;
	
	if (bIsInitialized)
	{
		// Reiniciar backup automático com nova configuração
		if (Config.bAutoBackupEnabled && !bAutoBackupActive)
		{
			StartAutoBackup();
		}
		else if (!Config.bAutoBackupEnabled && bAutoBackupActive)
		{
			StopAutoBackup();
		}
	}
}

FPCGVersionInfo UPCGVersionManager::CreateVersion(EPCGVersionType VersionType, const FString& Description, const FString& Author)
{
	FPCGVersionInfo NewVersion;
	NewVersion.Description = Description;
	NewVersion.Author = Author.IsEmpty() ? TEXT("PCG System") : Author;
	NewVersion.CreationTime = FDateTime::Now();

	// Incrementar versão baseado no tipo
	switch (VersionType)
	{
	case EPCGVersionType::Major:
		NewVersion.MajorVersion = CurrentVersion.MajorVersion + 1;
		NewVersion.MinorVersion = 0;
		NewVersion.PatchVersion = 0;
		break;
	case EPCGVersionType::Minor:
		NewVersion.MajorVersion = CurrentVersion.MajorVersion;
		NewVersion.MinorVersion = CurrentVersion.MinorVersion + 1;
		NewVersion.PatchVersion = 0;
		break;
	case EPCGVersionType::Hotfix:
		NewVersion.MajorVersion = CurrentVersion.MajorVersion;
		NewVersion.MinorVersion = CurrentVersion.MinorVersion;
		NewVersion.PatchVersion = CurrentVersion.PatchVersion + 1;
		break;
	case EPCGVersionType::Snapshot:
	default:
		NewVersion = CurrentVersion;
		NewVersion.Description = Description;
		NewVersion.CreationTime = FDateTime::Now();
		break;
	}

	// Atualizar build number
	NewVersion.BuildNumber = FString::Printf(TEXT("%d%02d%02d_%02d%02d"), 
		NewVersion.CreationTime.GetYear(), NewVersion.CreationTime.GetMonth(), NewVersion.CreationTime.GetDay(),
		NewVersion.CreationTime.GetHour(), NewVersion.CreationTime.GetMinute());

	// Adicionar ao histórico
	VersionHistory.Add(NewVersion);
	CurrentVersion = NewVersion;

	// Disparar evento
	OnVersionCreated.Broadcast(NewVersion);

	UE_LOG(LogTemp, Log, TEXT("Created new version: %s"), *NewVersion.GetVersionString());
	return NewVersion;
}

bool UPCGVersionManager::CreateBackup(const FPCGVersionInfo& VersionInfo)
{
	if (!bIsInitialized)
	{
		UE_LOG(LogTemp, Error, TEXT("UPCGVersionManager not initialized"));
		return false;
	}

	FPCGBackupData BackupData;
	BackupData.VersionInfo = VersionInfo;
	BackupData.Status = EPCGBackupStatus::InProgress;
	BackupData.BackupPath = GetBackupFilePath(VersionInfo);

	UpdateBackupProgress(0.0f, TEXT("Starting backup..."));

	// Backup dos componentes MOBA
	bool bSuccess = true;
	bSuccess &= BackupLaneGeometry();
	UpdateBackupProgress(20.0f, TEXT("Lane geometry backed up"));

	bSuccess &= BackupObjectiveData();
	UpdateBackupProgress(40.0f, TEXT("Objective data backed up"));

	bSuccess &= BackupRiverConfiguration();
	UpdateBackupProgress(60.0f, TEXT("River configuration backed up"));

	bSuccess &= BackupWallCollisions();
	UpdateBackupProgress(80.0f, TEXT("Wall collisions backed up"));

	bSuccess &= BackupMinionWaveSettings();
	UpdateBackupProgress(90.0f, TEXT("Minion wave settings backed up"));

	// Serializar dados dos componentes registrados
	for (const auto& ComponentPair : RegisteredComponents)
	{
		if (ComponentPair.Value.IsValid())
		{
			TArray<uint8> ComponentData;
			if (SerializeComponentData(ComponentPair.Value.Get(), ComponentData))
			{
				FString ComponentDataString = FBase64::Encode(ComponentData);
				BackupData.ComponentData.Add(ComponentPair.Key, ComponentDataString);
			}
		}
	}

	// Comprimir dados se habilitado
	TArray<uint8> FinalData;
	FMemoryWriter Writer(FinalData);
	Writer << BackupData.ComponentData;

	if (Config.bCompressBackups)
	{
		if (!CompressData(FinalData, BackupData.CompressedData))
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to compress backup data"));
			bSuccess = false;
		}
	}
	else
	{
		BackupData.CompressedData = FinalData;
	}

	// Calcular checksum
	if (Config.bValidateChecksums)
	{
		BackupData.Checksum = CalculateChecksum(BackupData.CompressedData);
	}

	// Salvar arquivo de backup
	if (bSuccess)
	{
		bSuccess = FFileHelper::SaveArrayToFile(BackupData.CompressedData, *BackupData.BackupPath);
		if (bSuccess)
		{
			BackupData.BackupSize = BackupData.CompressedData.Num();
			BackupData.Status = EPCGBackupStatus::Completed;
			UE_LOG(LogTemp, Log, TEXT("Backup completed: %s (%d bytes)"), *BackupData.BackupPath, BackupData.BackupSize);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to save backup file: %s"), *BackupData.BackupPath);
		}
	}

	if (!bSuccess)
	{
		BackupData.Status = EPCGBackupStatus::Failed;
	}

	// Armazenar no banco de dados
	BackupDatabase.Add(VersionInfo.GetVersionString(), BackupData);

	UpdateBackupProgress(100.0f, TEXT("Backup completed"));
	OnBackupCompleted.Broadcast(bSuccess, VersionInfo);

	return bSuccess;
}

bool UPCGVersionManager::RestoreVersion(const FPCGVersionInfo& VersionInfo, EPCGRestoreMode RestoreMode)
{
	if (!bIsInitialized)
	{
		UE_LOG(LogTemp, Error, TEXT("UPCGVersionManager not initialized"));
		return false;
	}

	FString VersionString = VersionInfo.GetVersionString();
	if (!BackupDatabase.Contains(VersionString))
	{
		UE_LOG(LogTemp, Error, TEXT("Backup not found for version: %s"), *VersionString);
		return false;
	}

	FPCGBackupData BackupData = BackupDatabase[VersionString];

	// Validar backup se habilitado
	if (Config.bValidateChecksums && !ValidateBackup(VersionInfo))
	{
		UE_LOG(LogTemp, Error, TEXT("Backup validation failed for version: %s"), *VersionString);
		return false;
	}

	bool bSuccess = true;

	// Restaurar baseado no modo
	switch (RestoreMode)
	{
	case EPCGRestoreMode::Full:
		bSuccess &= RestoreLaneGeometry(VersionInfo);
		bSuccess &= RestoreObjectiveData(VersionInfo);
		bSuccess &= RestoreRiverConfiguration(VersionInfo);
		bSuccess &= RestoreWallCollisions(VersionInfo);
		bSuccess &= RestoreMinionWaveSettings(VersionInfo);
		break;
	case EPCGRestoreMode::GeometryOnly:
		bSuccess &= RestoreLaneGeometry(VersionInfo);
		bSuccess &= RestoreRiverConfiguration(VersionInfo);
		bSuccess &= RestoreWallCollisions(VersionInfo);
		break;
	case EPCGRestoreMode::ConfigOnly:
		bSuccess &= RestoreObjectiveData(VersionInfo);
		bSuccess &= RestoreMinionWaveSettings(VersionInfo);
		break;
	case EPCGRestoreMode::Partial:
	default:
		// Implementar lógica de restauração parcial
		break;
	}

	if (bSuccess)
	{
		CurrentVersion = VersionInfo;
		UE_LOG(LogTemp, Log, TEXT("Version restored successfully: %s"), *VersionString);
	}

	OnRestoreCompleted.Broadcast(bSuccess, VersionInfo);
	return bSuccess;
}

bool UPCGVersionManager::DeleteVersion(const FPCGVersionInfo& VersionInfo)
{
	FString VersionString = VersionInfo.GetVersionString();
	
	if (!BackupDatabase.Contains(VersionString))
	{
		return false;
	}

	FPCGBackupData BackupData = BackupDatabase[VersionString];
	
	// Deletar arquivo de backup
	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
	if (PlatformFile.FileExists(*BackupData.BackupPath))
	{
		PlatformFile.DeleteFile(*BackupData.BackupPath);
	}

	// Remover do banco de dados
	BackupDatabase.Remove(VersionString);

	// Remover do histórico
	VersionHistory.RemoveAll([&VersionInfo](const FPCGVersionInfo& Version)
	{
		return Version.GetVersionString() == VersionInfo.GetVersionString();
	});

	UE_LOG(LogTemp, Log, TEXT("Version deleted: %s"), *VersionString);
	return true;
}

TArray<FPCGVersionInfo> UPCGVersionManager::GetAllVersions() const
{
	return VersionHistory;
}

FPCGVersionInfo UPCGVersionManager::GetLatestVersion() const
{
	if (VersionHistory.Num() == 0)
	{
		return FPCGVersionInfo();
	}

	FPCGVersionInfo LatestVersion = VersionHistory[0];
	for (const FPCGVersionInfo& Version : VersionHistory)
	{
		if (Version.CreationTime > LatestVersion.CreationTime)
		{
			LatestVersion = Version;
		}
	}

	return LatestVersion;
}

TArray<FPCGVersionInfo> UPCGVersionManager::FindVersionsByAuthor(const FString& Author) const
{
	TArray<FPCGVersionInfo> Result;
	for (const FPCGVersionInfo& Version : VersionHistory)
	{
		if (Version.Author.Contains(Author))
		{
			Result.Add(Version);
		}
	}
	return Result;
}

TArray<FPCGVersionInfo> UPCGVersionManager::FindVersionsByDateRange(const FDateTime& StartDate, const FDateTime& EndDate) const
{
	TArray<FPCGVersionInfo> Result;
	for (const FPCGVersionInfo& Version : VersionHistory)
	{
		if (Version.CreationTime >= StartDate && Version.CreationTime <= EndDate)
		{
			Result.Add(Version);
		}
	}
	return Result;
}

void UPCGVersionManager::StartAutoBackup()
{
	if (!bIsInitialized || bAutoBackupActive)
	{
		return;
	}

	UWorld* World = GetWorld();
	if (!World)
	{
		return;
	}

	World->GetTimerManager().SetTimer(AutoBackupTimerHandle, this, &UPCGVersionManager::PerformAutoBackup, Config.AutoBackupInterval, true);
	bAutoBackupActive = true;

	UE_LOG(LogTemp, Log, TEXT("Auto backup started with interval: %.2f seconds"), Config.AutoBackupInterval);
}

void UPCGVersionManager::StopAutoBackup()
{
	if (!bAutoBackupActive)
	{
		return;
	}

	UWorld* World = GetWorld();
	if (World)
	{
		World->GetTimerManager().ClearTimer(AutoBackupTimerHandle);
	}

	bAutoBackupActive = false;
	UE_LOG(LogTemp, Log, TEXT("Auto backup stopped"));
}

void UPCGVersionManager::ForceBackup(const FString& Description)
{
	FPCGVersionInfo BackupVersion = CreateVersion(EPCGVersionType::Snapshot, Description, TEXT("Manual"));
	CreateBackup(BackupVersion);
}

bool UPCGVersionManager::ValidateBackup(const FPCGVersionInfo& VersionInfo)
{
	FString VersionString = VersionInfo.GetVersionString();
	if (!BackupDatabase.Contains(VersionString))
	{
		return false;
	}

	FPCGBackupData BackupData = BackupDatabase[VersionString];

	// Verificar se arquivo existe
	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
	if (!PlatformFile.FileExists(*BackupData.BackupPath))
	{
		return false;
	}

	// Validar checksum se habilitado
	if (Config.bValidateChecksums && !BackupData.Checksum.IsEmpty())
	{
		TArray<uint8> FileData;
		if (!FFileHelper::LoadFileToArray(FileData, *BackupData.BackupPath))
		{
			return false;
		}

		FString FileChecksum = CalculateChecksum(FileData);
		if (FileChecksum != BackupData.Checksum)
		{
			UE_LOG(LogTemp, Error, TEXT("Checksum mismatch for backup: %s"), *VersionString);
			return false;
		}
	}

	return true;
}

bool UPCGVersionManager::RepairCorruptedBackup(const FPCGVersionInfo& VersionInfo)
{
	// Implementar lógica de reparo de backup corrompido
	// Por enquanto, apenas marcar como corrompido
	FString VersionString = VersionInfo.GetVersionString();
	if (BackupDatabase.Contains(VersionString))
	{
		BackupDatabase[VersionString].Status = EPCGBackupStatus::Corrupted;
		return false;
	}
	return false;
}

void UPCGVersionManager::CleanupOldBackups()
{
	if (VersionHistory.Num() <= Config.MaxBackupCount)
	{
		return;
	}

	// Ordenar por data de criação
	TArray<FPCGVersionInfo> SortedVersions = VersionHistory;
	SortedVersions.Sort([](const FPCGVersionInfo& A, const FPCGVersionInfo& B)
	{
		return A.CreationTime < B.CreationTime;
	});

	// Deletar versões mais antigas
	int32 VersionsToDelete = SortedVersions.Num() - Config.MaxBackupCount;
	for (int32 i = 0; i < VersionsToDelete; i++)
	{
		DeleteVersion(SortedVersions[i]);
	}

	UE_LOG(LogTemp, Log, TEXT("Cleaned up %d old backups"), VersionsToDelete);
}

TArray<FString> UPCGVersionManager::CompareVersions(const FPCGVersionInfo& VersionA, const FPCGVersionInfo& VersionB)
{
	TArray<FString> Differences;

	// Comparar informações básicas
	if (VersionA.MajorVersion != VersionB.MajorVersion)
	{
		Differences.Add(FString::Printf(TEXT("Major version: %d -> %d"), VersionA.MajorVersion, VersionB.MajorVersion));
	}

	if (VersionA.MinorVersion != VersionB.MinorVersion)
	{
		Differences.Add(FString::Printf(TEXT("Minor version: %d -> %d"), VersionA.MinorVersion, VersionB.MinorVersion));
	}

	if (VersionA.PatchVersion != VersionB.PatchVersion)
	{
		Differences.Add(FString::Printf(TEXT("Patch version: %d -> %d"), VersionA.PatchVersion, VersionB.PatchVersion));
	}

	if (VersionA.Author != VersionB.Author)
	{
		Differences.Add(FString::Printf(TEXT("Author: %s -> %s"), *VersionA.Author, *VersionB.Author));
	}

	if (VersionA.Description != VersionB.Description)
	{
		Differences.Add(FString::Printf(TEXT("Description: %s -> %s"), *VersionA.Description, *VersionB.Description));
	}

	// Comparar arquivos alterados
	for (const FString& FileA : VersionA.ChangedFiles)
	{
		if (!VersionB.ChangedFiles.Contains(FileA))
		{
			Differences.Add(FString::Printf(TEXT("File removed: %s"), *FileA));
		}
	}

	for (const FString& FileB : VersionB.ChangedFiles)
	{
		if (!VersionA.ChangedFiles.Contains(FileB))
		{
			Differences.Add(FString::Printf(TEXT("File added: %s"), *FileB));
		}
	}

	return Differences;
}

FString UPCGVersionManager::GenerateVersionDiff(const FPCGVersionInfo& VersionA, const FPCGVersionInfo& VersionB)
{
	TArray<FString> Differences = CompareVersions(VersionA, VersionB);
	
	FString DiffReport = FString::Printf(TEXT("Version Comparison: %s vs %s\n"), 
		*VersionA.GetVersionString(), *VersionB.GetVersionString());
	DiffReport += FString::Printf(TEXT("Date Range: %s to %s\n\n"), 
		*VersionA.CreationTime.ToString(), *VersionB.CreationTime.ToString());

	if (Differences.Num() == 0)
	{
		DiffReport += TEXT("No differences found.\n");
	}
	else
	{
		DiffReport += FString::Printf(TEXT("Found %d differences:\n"), Differences.Num());
		for (int32 i = 0; i < Differences.Num(); i++)
		{
			DiffReport += FString::Printf(TEXT("%d. %s\n"), i + 1, *Differences[i]);
		}
	}

	return DiffReport;
}

bool UPCGVersionManager::ExportVersion(const FPCGVersionInfo& VersionInfo, const FString& ExportPath)
{
	FString VersionString = VersionInfo.GetVersionString();
	if (!BackupDatabase.Contains(VersionString))
	{
		return false;
	}

	FPCGBackupData BackupData = BackupDatabase[VersionString];

	// Criar estrutura JSON para exportação
	TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
	JsonObject->SetStringField(TEXT("Version"), VersionString);
	JsonObject->SetStringField(TEXT("Author"), VersionInfo.Author);
	JsonObject->SetStringField(TEXT("Description"), VersionInfo.Description);
	JsonObject->SetStringField(TEXT("CreationTime"), VersionInfo.CreationTime.ToString());
	JsonObject->SetStringField(TEXT("Checksum"), BackupData.Checksum);
	JsonObject->SetNumberField(TEXT("BackupSize"), BackupData.BackupSize);

	// Adicionar dados dos componentes
	TSharedPtr<FJsonObject> ComponentsObject = MakeShareable(new FJsonObject);
	for (const auto& ComponentPair : BackupData.ComponentData)
	{
		ComponentsObject->SetStringField(ComponentPair.Key, ComponentPair.Value);
	}
	JsonObject->SetObjectField(TEXT("Components"), ComponentsObject);

	// Salvar JSON
	FString OutputString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
	FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

	FString ExportFilePath = FPaths::Combine(ExportPath, FString::Printf(TEXT("%s.json"), *VersionString));
	return FFileHelper::SaveStringToFile(OutputString, *ExportFilePath);
}

bool UPCGVersionManager::ImportVersion(const FString& ImportPath)
{
	FString JsonString;
	if (!FFileHelper::LoadFileToString(JsonString, *ImportPath))
	{
		return false;
	}

	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);
	if (!FJsonSerializer::Deserialize(Reader, JsonObject))
	{
		return false;
	}

	// Extrair informações da versão
	FPCGVersionInfo ImportedVersion;
	FString VersionString = JsonObject->GetStringField(TEXT("Version"));
	ImportedVersion.Author = JsonObject->GetStringField(TEXT("Author"));
	ImportedVersion.Description = JsonObject->GetStringField(TEXT("Description"));
	FDateTime::Parse(JsonObject->GetStringField(TEXT("CreationTime")), ImportedVersion.CreationTime);

	// Extrair dados do backup
	FPCGBackupData ImportedBackup;
	ImportedBackup.VersionInfo = ImportedVersion;
	ImportedBackup.Checksum = JsonObject->GetStringField(TEXT("Checksum"));
	ImportedBackup.BackupSize = JsonObject->GetNumberField(TEXT("BackupSize"));
	ImportedBackup.Status = EPCGBackupStatus::Completed;

	// Extrair dados dos componentes
	TSharedPtr<FJsonObject> ComponentsObject = JsonObject->GetObjectField(TEXT("Components"));
	for (const auto& ComponentPair : ComponentsObject->Values)
	{
		ImportedBackup.ComponentData.Add(ComponentPair.Key, ComponentPair.Value->AsString());
	}

	// Adicionar ao banco de dados
	BackupDatabase.Add(VersionString, ImportedBackup);
	VersionHistory.Add(ImportedVersion);

	UE_LOG(LogTemp, Log, TEXT("Version imported successfully: %s"), *VersionString);
	return true;
}

void UPCGVersionManager::RegisterMOBAComponent(const FString& ComponentName, UObject* Component)
{
	if (!Component)
	{
		return;
	}

	RegisteredComponents.Add(ComponentName, Component);
	UE_LOG(LogTemp, Log, TEXT("MOBA component registered: %s"), *ComponentName);
}

void UPCGVersionManager::UnregisterMOBAComponent(const FString& ComponentName)
{
	RegisteredComponents.Remove(ComponentName);
	UE_LOG(LogTemp, Log, TEXT("MOBA component unregistered: %s"), *ComponentName);
}

bool UPCGVersionManager::BackupMOBAComponent(const FString& ComponentName)
{
	if (!RegisteredComponents.Contains(ComponentName))
	{
		return false;
	}

	TWeakObjectPtr<UObject> ComponentPtr = RegisteredComponents[ComponentName];
	if (!ComponentPtr.IsValid())
	{
		return false;
	}

	TArray<uint8> ComponentData;
	if (!SerializeComponentData(ComponentPtr.Get(), ComponentData))
	{
		return false;
	}

	// Salvar dados do componente
	FString ComponentBackupPath = FPaths::Combine(Config.BackupDirectory, FString::Printf(TEXT("%s_backup.dat"), *ComponentName));
	return FFileHelper::SaveArrayToFile(ComponentData, *ComponentBackupPath);
}

bool UPCGVersionManager::RestoreMOBAComponent(const FString& ComponentName, const FPCGVersionInfo& VersionInfo)
{
	if (!RegisteredComponents.Contains(ComponentName))
	{
		return false;
	}

	TWeakObjectPtr<UObject> ComponentPtr = RegisteredComponents[ComponentName];
	if (!ComponentPtr.IsValid())
	{
		return false;
	}

	// Carregar dados do componente do backup
	FString VersionString = VersionInfo.GetVersionString();
	if (!BackupDatabase.Contains(VersionString))
	{
		return false;
	}

	FPCGBackupData BackupData = BackupDatabase[VersionString];
	if (!BackupData.ComponentData.Contains(ComponentName))
	{
		return false;
	}

	FString ComponentDataString = BackupData.ComponentData[ComponentName];
	TArray<uint8> ComponentData;
	FBase64::Decode(ComponentDataString, ComponentData);

	return DeserializeComponentData(ComponentPtr.Get(), ComponentData);
}

// === Funções Internas ===

void UPCGVersionManager::PerformAutoBackup()
{
	if (!bIsInitialized)
	{
		return;
	}

	float CurrentTime = GetWorld()->GetTimeSeconds();
	if (CurrentTime - LastBackupTime < Config.AutoBackupInterval)
	{
		return;
	}

	FPCGVersionInfo AutoBackupVersion = CreateVersion(EPCGVersionType::Snapshot, TEXT("Auto Backup"), TEXT("System"));
	CreateBackup(AutoBackupVersion);

	LastBackupTime = CurrentTime;

	// Limpar backups antigos se necessário
	CleanupOldBackups();
}

bool UPCGVersionManager::SerializeComponentData(UObject* Component, TArray<uint8>& OutData)
{
	if (!Component)
	{
		return false;
	}

	FMemoryWriter Writer(OutData);
	Component->Serialize(Writer);
	return true;
}

bool UPCGVersionManager::DeserializeComponentData(UObject* Component, const TArray<uint8>& InData)
{
	if (!Component || InData.Num() == 0)
	{
		return false;
	}

	FMemoryReader Reader(InData);
	Component->Serialize(Reader);
	return true;
}

FString UPCGVersionManager::CalculateChecksum(const TArray<uint8>& Data)
{
	return FMD5::HashBytes(Data.GetData(), Data.Num());
}

bool UPCGVersionManager::CompressData(const TArray<uint8>& InData, TArray<uint8>& OutCompressedData)
{
	// Usar compressão Oodle se disponível, senão usar compressão padrão
	int32 CompressedSize = FCompression::CompressMemoryBound(NAME_Oodle, InData.Num());
	OutCompressedData.SetNumUninitialized(CompressedSize);

	if (FCompression::CompressMemory(NAME_Oodle, OutCompressedData.GetData(), CompressedSize, InData.GetData(), InData.Num()))
	{
		OutCompressedData.SetNum(CompressedSize);
		return true;
	}

	return false;
}

bool UPCGVersionManager::DecompressData(const TArray<uint8>& InCompressedData, TArray<uint8>& OutData)
{
	// Implementar descompressão
	int32 UncompressedSize = InCompressedData.Num() * 4; // Estimativa inicial
	OutData.SetNumUninitialized(UncompressedSize);

	if (FCompression::UncompressMemory(NAME_Oodle, OutData.GetData(), UncompressedSize, InCompressedData.GetData(), InCompressedData.Num()))
	{
		OutData.SetNum(UncompressedSize);
		return true;
	}

	return false;
}

void UPCGVersionManager::SaveVersionDatabase()
{
	FString DatabasePath = FPaths::Combine(Config.BackupDirectory, TEXT("version_database.json"));

	// Criar JSON com todas as versões e backups
	TSharedPtr<FJsonObject> DatabaseObject = MakeShareable(new FJsonObject);

	// Salvar histórico de versões
	TArray<TSharedPtr<FJsonValue>> VersionArray;
	for (const FPCGVersionInfo& Version : VersionHistory)
	{
		TSharedPtr<FJsonObject> VersionObject = MakeShareable(new FJsonObject);
		VersionObject->SetStringField(TEXT("VersionString"), Version.GetVersionString());
		VersionObject->SetStringField(TEXT("Author"), Version.Author);
		VersionObject->SetStringField(TEXT("Description"), Version.Description);
		VersionObject->SetStringField(TEXT("CreationTime"), Version.CreationTime.ToString());
		VersionArray.Add(MakeShareable(new FJsonValueObject(VersionObject)));
	}
	DatabaseObject->SetArrayField(TEXT("Versions"), VersionArray);

	// Salvar informações de backup
	TSharedPtr<FJsonObject> BackupsObject = MakeShareable(new FJsonObject);
	for (const auto& BackupPair : BackupDatabase)
	{
		TSharedPtr<FJsonObject> BackupObject = MakeShareable(new FJsonObject);
		BackupObject->SetStringField(TEXT("BackupPath"), BackupPair.Value.BackupPath);
		BackupObject->SetNumberField(TEXT("BackupSize"), BackupPair.Value.BackupSize);
		BackupObject->SetStringField(TEXT("Checksum"), BackupPair.Value.Checksum);
		BackupObject->SetNumberField(TEXT("Status"), (int32)BackupPair.Value.Status);
		BackupsObject->SetObjectField(BackupPair.Key, BackupObject);
	}
	DatabaseObject->SetObjectField(TEXT("Backups"), BackupsObject);

	// Salvar arquivo
	FString OutputString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
	FJsonSerializer::Serialize(DatabaseObject.ToSharedRef(), Writer);
	FFileHelper::SaveStringToFile(OutputString, *DatabasePath);
}

void UPCGVersionManager::LoadVersionDatabase()
{
	FString DatabasePath = FPaths::Combine(Config.BackupDirectory, TEXT("version_database.json"));

	FString JsonString;
	if (!FFileHelper::LoadFileToString(JsonString, *DatabasePath))
	{
		return; // Arquivo não existe, começar com banco vazio
	}

	TSharedPtr<FJsonObject> DatabaseObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);
	if (!FJsonSerializer::Deserialize(Reader, DatabaseObject))
	{
		return;
	}

	// Carregar histórico de versões
	const TArray<TSharedPtr<FJsonValue>>* VersionArray;
	if (DatabaseObject->TryGetArrayField(TEXT("Versions"), VersionArray))
	{
		for (const TSharedPtr<FJsonValue>& VersionValue : *VersionArray)
		{
			TSharedPtr<FJsonObject> VersionObject = VersionValue->AsObject();
			FPCGVersionInfo Version;
			Version.Author = VersionObject->GetStringField(TEXT("Author"));
			Version.Description = VersionObject->GetStringField(TEXT("Description"));
			FDateTime::Parse(VersionObject->GetStringField(TEXT("CreationTime")), Version.CreationTime);
			VersionHistory.Add(Version);
		}
	}

	// Carregar informações de backup
	TSharedPtr<FJsonObject> BackupsObject = DatabaseObject->GetObjectField(TEXT("Backups"));
	if (BackupsObject.IsValid())
	{
		for (const auto& BackupPair : BackupsObject->Values)
		{
			TSharedPtr<FJsonObject> BackupObject = BackupPair.Value->AsObject();
			FPCGBackupData BackupData;
			BackupData.BackupPath = BackupObject->GetStringField(TEXT("BackupPath"));
			BackupData.BackupSize = BackupObject->GetNumberField(TEXT("BackupSize"));
			BackupData.Checksum = BackupObject->GetStringField(TEXT("Checksum"));
			BackupData.Status = (EPCGBackupStatus)BackupObject->GetNumberField(TEXT("Status"));
			BackupDatabase.Add(BackupPair.Key, BackupData);
		}
	}
}

bool UPCGVersionManager::CreateBackupDirectory()
{
	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
	FString FullPath = FPaths::ConvertRelativePathToFull(Config.BackupDirectory);
	return PlatformFile.CreateDirectoryTree(*FullPath);
}

FString UPCGVersionManager::GetBackupFilePath(const FPCGVersionInfo& VersionInfo)
{
	FString FileName = FString::Printf(TEXT("%s.backup"), *VersionInfo.GetVersionString());
	return FPaths::Combine(Config.BackupDirectory, FileName);
}

void UPCGVersionManager::UpdateBackupProgress(float Progress, const FString& CurrentFile)
{
	OnBackupProgress.Broadcast(Progress, CurrentFile);
}

// === Funções Específicas MOBA ===

bool UPCGVersionManager::BackupLaneGeometry()
{
	// Implementar backup específico da geometria das lanes
	UE_LOG(LogTemp, Log, TEXT("Backing up lane geometry..."));
	return true;
}

bool UPCGVersionManager::BackupObjectiveData()
{
	// Implementar backup dos dados dos objetivos (Baron, Dragon, etc.)
	UE_LOG(LogTemp, Log, TEXT("Backing up objective data..."));
	return true;
}

bool UPCGVersionManager::BackupRiverConfiguration()
{
	// Implementar backup da configuração do rio
	UE_LOG(LogTemp, Log, TEXT("Backing up river configuration..."));
	return true;
}

bool UPCGVersionManager::BackupWallCollisions()
{
	// Implementar backup das colisões das paredes
	UE_LOG(LogTemp, Log, TEXT("Backing up wall collisions..."));
	return true;
}

bool UPCGVersionManager::BackupMinionWaveSettings()
{
	// Implementar backup das configurações das ondas de minions
	UE_LOG(LogTemp, Log, TEXT("Backing up minion wave settings..."));
	return true;
}

bool UPCGVersionManager::RestoreLaneGeometry(const FPCGVersionInfo& VersionInfo)
{
	// Implementar restauração da geometria das lanes
	UE_LOG(LogTemp, Log, TEXT("Restoring lane geometry for version: %s"), *VersionInfo.GetVersionString());
	return true;
}

bool UPCGVersionManager::RestoreObjectiveData(const FPCGVersionInfo& VersionInfo)
{
	// Implementar restauração dos dados dos objetivos
	UE_LOG(LogTemp, Log, TEXT("Restoring objective data for version: %s"), *VersionInfo.GetVersionString());
	return true;
}

bool UPCGVersionManager::RestoreRiverConfiguration(const FPCGVersionInfo& VersionInfo)
{
	// Implementar restauração da configuração do rio
	UE_LOG(LogTemp, Log, TEXT("Restoring river configuration for version: %s"), *VersionInfo.GetVersionString());
	return true;
}

bool UPCGVersionManager::RestoreWallCollisions(const FPCGVersionInfo& VersionInfo)
{
	// Implementar restauração das colisões das paredes
	UE_LOG(LogTemp, Log, TEXT("Restoring wall collisions for version: %s"), *VersionInfo.GetVersionString());
	return true;
}

bool UPCGVersionManager::RestoreMinionWaveSettings(const FPCGVersionInfo& VersionInfo)
{
	// Implementar restauração das configurações das ondas de minions
	UE_LOG(LogTemp, Log, TEXT("Restoring minion wave settings for version: %s"), *VersionInfo.GetVersionString());
	return true;
}

// === Validação Específica MOBA ===

bool UPCGVersionManager::ValidateMOBAGeometry(const FPCGVersionInfo& VersionInfo)
{
	// Implementar validação da geometria do mapa MOBA
	bool bValid = true;
	bValid &= ValidateLaneIntegrity(VersionInfo);
	bValid &= ValidateObjectivePositions(VersionInfo);
	bValid &= ValidateRiverFlow(VersionInfo);
	return bValid;
}

bool UPCGVersionManager::ValidateLaneIntegrity(const FPCGVersionInfo& VersionInfo)
{
	// Validar integridade das 3 lanes (Top, Mid, Bot)
	UE_LOG(LogTemp, Log, TEXT("Validating lane integrity for version: %s"), *VersionInfo.GetVersionString());
	return true;
}

bool UPCGVersionManager::ValidateObjectivePositions(const FPCGVersionInfo& VersionInfo)
{
	// Validar posições dos objetivos (Baron, Dragon, Torres)
	UE_LOG(LogTemp, Log, TEXT("Validating objective positions for version: %s"), *VersionInfo.GetVersionString());
	return true;
}

bool UPCGVersionManager::ValidateRiverFlow(const FPCGVersionInfo& VersionInfo)
{
	// Validar fluxo do rio e geometria senoidal
	UE_LOG(LogTemp, Log, TEXT("Validating river flow for version: %s"), *VersionInfo.GetVersionString());
	return true;
}