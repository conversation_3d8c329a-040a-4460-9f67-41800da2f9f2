#include "ADragonPrismalManager.h"
#include "Interfaces/RewardSystemInterface.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "Components/SceneComponent.h"
#include "Components/AudioComponent.h"
#include "DrawDebugHelpers.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/GameModeBase.h"
#include "GameFramework/PlayerState.h"
#include "Engine/GameInstance.h"
#include "Math/UnrealMathUtility.h"
#include "TimerManager.h"
#include "Engine/World.h"
#include "Engine/DamageEvents.h"
#include "Logging/LogMacros.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Sound/SoundBase.h"

// Constantes para o Dragão Prismal - UE5.6 Modern APIs
DEFINE_LOG_CATEGORY_STATIC(LogDragonPrismal, Log, All);

static constexpr float DRAGON_SPAWN_TIME = 300.0f; // 5 minutos
static constexpr float DRAGON_RESPAWN_TIME = 360.0f; // 6 minutos
static constexpr float DRAGON_BASE_HP = 3000.0f;
static constexpr float DRAGON_HP_PER_MINUTE = 200.0f;
static constexpr float MAGIC_RESISTANCE = 0.4f; // 40%
static constexpr float PHYSICAL_RESISTANCE = 0.2f; // 20%
static constexpr float BUFF_DURATION = 180.0f; // 3 minutos
static constexpr float BUFF_DAMAGE_INCREASE = 0.2f; // 20%
static constexpr float BUFF_SPEED_INCREASE = 0.15f; // 15%

// Geometria elíptica - conforme documentação
static constexpr float ELLIPSE_CENTER_X = 0.0f;
static constexpr float ELLIPSE_CENTER_Y = 4800.0f;
static constexpr float ELLIPSE_SEMI_MAJOR = 800.0f; // a = 800 UU
static constexpr float ELLIPSE_SEMI_MINOR = 600.0f; // b = 600 UU
static constexpr float ENTRANCE_WIDTH = 300.0f;
static constexpr float SOPRO_PRISMAL_RANGE = 1000.0f;
static constexpr float SOPRO_PRISMAL_ANGLE = 60.0f;

// Definições das constantes
const float ADragonPrismalManager::ELLIPSE_SEMI_MAJOR = 2000.0f;
const float ADragonPrismalManager::ELLIPSE_SEMI_MINOR = 1500.0f;

ADragonPrismalManager::ADragonPrismalManager()
{
    PrimaryActorTick.bCanEverTick = true;

    // Configurar componente raiz
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    RootComponent = RootSceneComponent;

    // Configurar mesh do Dragão - UE5.6 Modern API
    DragonMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("DragonMesh"));
    DragonMesh->SetupAttachment(RootComponent);
    DragonMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    DragonMesh->SetCollisionResponseToAllChannels(ECR_Block);

    // Configurar área de combate - geometria elíptica conforme documentação
    CombatArea = CreateDefaultSubobject<USphereComponent>(TEXT("CombatArea"));
    CombatArea->SetupAttachment(RootComponent);
    CombatArea->SetSphereRadius(ELLIPSE_SEMI_MAJOR); // 800 UU conforme doc
    CombatArea->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    CombatArea->SetCollisionResponseToAllChannels(ECR_Ignore);
    CombatArea->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);

    // Configurar área de detecção
    DetectionArea = CreateDefaultSubobject<USphereComponent>(TEXT("DetectionArea"));
    DetectionArea->SetupAttachment(RootComponent);
    DetectionArea->SetSphereRadius(COMBAT_DETECTION_RADIUS);
    DetectionArea->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    DetectionArea->SetCollisionResponseToAllChannels(ECR_Ignore);
    DetectionArea->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);

    // Configurar área de recompensas
    RewardArea = CreateDefaultSubobject<USphereComponent>(TEXT("RewardArea"));
    RewardArea->SetupAttachment(RootComponent);
    RewardArea->SetSphereRadius(REWARD_DISTRIBUTION_RADIUS);
    RewardArea->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    RewardArea->SetCollisionResponseToAllChannels(ECR_Ignore);
    RewardArea->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);

    // Inicializar dados padrão - conforme especificações da documentação
    DragonData = FDragonData();
    DragonData.CurrentHealth = DRAGON_BASE_HP; // 3000 HP inicial
    DragonData.BaseHealth = DRAGON_BASE_HP;
    DragonData.HealthScaling = DRAGON_HP_PER_MINUTE; // 200 HP por minuto
    DragonData.CurrentState = EDragonState::Dormant;
    DragonData.Element = EDragonElement::Prismal;
    DragonData.bIsInCombat = false;
    DragonData.bFuriaCrescenteActive = false;
    DragonData.DamageMultiplier = 1.0f;
    DragonData.SpeedMultiplier = 1.0f;
    
    EllipticalCovil = FEllipticalArea();
    EllipticalCovil.Center = FVector(ELLIPSE_CENTER_X, ELLIPSE_CENTER_Y, 0.0f); // (0, +4800)
    EllipticalCovil.SemiMajorAxis = ELLIPSE_SEMI_MAJOR; // a = 800 UU
    EllipticalCovil.SemiMinorAxis = ELLIPSE_SEMI_MINOR; // b = 600 UU
    EllipticalCovil.RotationAngle = 0.0f;
    
    RewardSystem = FDragonReward();
    FlightPath = FFlightPath();
    GameStartTime = 0.0f;
    CurrentGameTime = 0.0f;
    bShowDebugInfo = false;

    // Configurar posição inicial no centro da elipse
    SetActorLocation(EllipticalCovil.Center);

    // Inicializar ataques padrão
    DragonAttacks.Empty();
    
    // Ataque de fogo
    FDragonAttack FireBreath;
    FireBreath.AttackName = TEXT("Fire Breath");
    FireBreath.Damage = 800.0f;
    FireBreath.Range = 1200.0f;
    FireBreath.Cooldown = 5.0f;
    FireBreath.Element = EDragonElement::Fire;
    FireBreath.bIsAreaAttack = true;
    FireBreath.AreaRadius = 400.0f;
    DragonAttacks.Add(FireBreath);
    
    // Ataque de garra
    FDragonAttack ClawAttack;
    ClawAttack.AttackName = TEXT("Claw Strike");
    ClawAttack.Damage = 1200.0f;
    ClawAttack.Range = 300.0f;
    ClawAttack.Cooldown = 3.0f;
    ClawAttack.Element = EDragonElement::Earth;
    ClawAttack.bIsAreaAttack = false;
    ClawAttack.AreaRadius = 0.0f;
    DragonAttacks.Add(ClawAttack);
    
    // Ataque de cauda
    FDragonAttack TailSwipe;
    TailSwipe.AttackName = TEXT("Tail Swipe");
    TailSwipe.Damage = 600.0f;
    TailSwipe.Range = 800.0f;
    TailSwipe.Cooldown = 4.0f;
    TailSwipe.Element = EDragonElement::Air;
    TailSwipe.bIsAreaAttack = true;
    TailSwipe.AreaRadius = 500.0f;
    DragonAttacks.Add(TailSwipe);
}

void ADragonPrismalManager::BeginPlay()
{
    Super::BeginPlay();

    // Validações de segurança - UE5.6 Modern APIs
    if (!ensure(IsValid(GetWorld())))
    {
        UE_LOG(LogDragonPrismal, Error, TEXT("World is invalid in BeginPlay"));
        return;
    }
    
    if (!ensure(CombatArea && DetectionArea && RewardArea && DragonMesh))
    {
        UE_LOG(LogDragonPrismal, Error, TEXT("Required components are invalid"));
        return;
    }

    // Inicializar geometria elíptica
    CalculateEllipticalGeometry();
    
    // Gerar caminho de voo
    GenerateFlightPath();
    
    // Configurar timers
    GameStartTime = GetWorld()->GetTimeSeconds();
    
    // Configurar timer de spawn do Dragão
    GetWorld()->GetTimerManager().SetTimer(
        DragonSpawnTimer,
        this,
        &ADragonPrismalManager::CheckSpawnConditions,
        1.0f, // Verificar a cada segundo
        true
    );
    
    // Configurar timer de rastreamento de combate
    GetWorld()->GetTimerManager().SetTimer(
        CombatTrackingTimer,
        this,
        &ADragonPrismalManager::UpdateCombatTrackingTimer,
        1.0f,
        true
    );

    // Configurar eventos de colisão com validações
    CombatArea->OnComponentBeginOverlap.AddDynamic(this, &ADragonPrismalManager::OnCombatAreaEnter);
    CombatArea->OnComponentEndOverlap.AddDynamic(this, &ADragonPrismalManager::OnCombatAreaExit);
    
    DetectionArea->OnComponentBeginOverlap.AddDynamic(this, &ADragonPrismalManager::OnDetectionAreaEnter);
    DetectionArea->OnComponentEndOverlap.AddDynamic(this, &ADragonPrismalManager::OnDetectionAreaExit);
    
    RewardArea->OnComponentBeginOverlap.AddDynamic(this, &ADragonPrismalManager::OnRewardAreaEnter);
    RewardArea->OnComponentEndOverlap.AddDynamic(this, &ADragonPrismalManager::OnRewardAreaExit);

    UE_LOG(LogDragonPrismal, Log, TEXT("Dragon Prismal Manager initialized successfully"));
}

void ADragonPrismalManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Validação de segurança
    if (!ensure(DeltaTime > 0.0f && DeltaTime < 1.0f))
    {
        return;
    }

    UpdateGameTime(DeltaTime);
    UpdateDragonBehavior(DeltaTime);
    
    if (DragonData.CurrentState == EDragonState::Flying)
    {
        UpdateFlightMovement(DeltaTime);
    }
    
    // Atualizar HP escalável conforme documentação: 3000 + (200 × minutos)
    UpdateDragonHealth();
    
    // Debug drawing
    if (bShowDebugInfo)
    {
        DrawDebugEllipse();
        DrawDebugFlightPath();
    }
}

void ADragonPrismalManager::UpdateGameTime(float DeltaTime)
{
    CurrentGameTime = GetWorld()->GetTimeSeconds() - GameStartTime;
}

void ADragonPrismalManager::CheckSpawnConditions()
{
    if (DragonData.CurrentState == EDragonState::Dormant && ShouldSpawnDragon())
    {
        SpawnDragon();
    }
}

void ADragonPrismalManager::UpdateDragonBehavior(float DeltaTime)
{
    switch (DragonData.CurrentState)
    {
        case EDragonState::Spawning:
        {
            // Implementação robusta do spawn do dragão conforme documentação
            if (!ensure(IsValid(DragonMesh)))
            {
                UE_LOG(LogDragonPrismal, Error, TEXT("DragonMesh is invalid during spawn"));
                return;
            }
            
            // Calcular HP escalável baseado no tempo de jogo: 3000 + (200 × minutos)
            const float GameTimeMinutes = CurrentGameTime / 60.0f;
            DragonData.BaseHealth = BASE_HEALTH + (HEALTH_PER_MINUTE * GameTimeMinutes);
            DragonData.CurrentHealth = DragonData.BaseHealth;
            
            // Posicionar dragão no centro da elipse conforme documentação: (0, +4800)
            const FVector SpawnLocation = EllipticalCovil.Center;
            SetActorLocation(SpawnLocation);
            
            // Configurar mesh e componentes
            DragonMesh->SetVisibility(true);
            DragonMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            
            // Ativar áreas de detecção
            if (ensure(IsValid(CombatArea)))
            {
                CombatArea->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            }
            if (ensure(IsValid(DetectionArea)))
            {
                DetectionArea->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            }
            if (ensure(IsValid(RewardArea)))
            {
                RewardArea->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            }
            
            // Resetar estado de combate
            DragonData.bIsInCombat = false;
            DragonData.bFuriaCrescenteActive = false;
            DragonData.DamageMultiplier = 1.0f;
            DragonData.SpeedMultiplier = 1.0f;
            CombatParticipants.Empty();
            
            // Gerar waypoints de voo em círculo ao redor da elipse
            GenerateFlightPath();
            
            // Transição para estado ativo
            DragonData.CurrentState = EDragonState::Active;
            
            UE_LOG(LogDragonPrismal, Log, TEXT("Dragon spawned successfully at %s with HP: %f (Game time: %f min)"), 
                   *SpawnLocation.ToString(), DragonData.BaseHealth, GameTimeMinutes);
            break;
        }
            
        case EDragonState::Active:
            // Verificar se deve iniciar voo
            if (!GetWorld()->GetTimerManager().IsTimerActive(FlightTimer))
            {
                StartDragonFlight();
            }
            break;
            
        case EDragonState::Combat:
        {
            // Sistema de combate robusto conforme documentação
            if (!DragonData.bIsInCombat)
            {
                UE_LOG(LogDragonPrismal, Warning, TEXT("Dragon not in combat but in Combat state - transitioning to Active"));
                DragonData.CurrentState = EDragonState::Active;
                break;
            }
            
            // Verificar se deve ativar Fúria Crescente (HP <= 30%)
            const float HealthPercentage = (DragonData.CurrentHealth / DragonData.BaseHealth) * 100.0f;
            if (HealthPercentage <= 30.0f && !DragonData.bFuriaCrescenteActive)
            {
                ActivateFuriaCrescente();
            }
            
            // Sistema de ataques baseado em cooldown e prioridade
            if (!GetWorld()->GetTimerManager().IsTimerActive(AttackTimer))
            {
                // Determinar próximo ataque baseado no estado e HP
                EDragonAttackType NextAttack = DetermineNextAttack();
                
                switch (NextAttack)
                {
                    case EDragonAttackType::SoproPrismal:
                        ExecuteSoproPrismalAttack();
                        break;
                        
                    case EDragonAttackType::Cristalizacao:
                        ExecuteCristalizacaoAttack();
                        break;
                        
                    case EDragonAttackType::AreaDamage:
                        ExecuteAreaDamageAttack();
                        break;
                        
                    default:
                        ExecuteSoproPrismalAttack(); // Ataque padrão
                        break;
                }
                
                // Configurar cooldown baseado no tipo de ataque e Fúria Crescente
                float AttackCooldown = DragonData.bFuriaCrescenteActive ? 
                    DragonAttacks[static_cast<int32>(NextAttack)].Cooldown * 0.7f : 
                    DragonAttacks[static_cast<int32>(NextAttack)].Cooldown;
                    
                GetWorld()->GetTimerManager().SetTimer(AttackTimer, AttackCooldown, false);
                
                UE_LOG(LogDragonPrismal, Log, TEXT("Dragon executed %s attack, next in %f seconds"), 
                       *UEnum::GetValueAsString(NextAttack), AttackCooldown);
            }
            
            // Atualizar rastreamento de combate
            UpdateCombatTracking(DeltaTime);
            
            // Verificar se deve sair do combate (sem inimigos por 10 segundos)
            if (CombatParticipants.Num() == 0)
            {
                if (!GetWorld()->GetTimerManager().IsTimerActive(CombatExitTimer))
                {
                    GetWorld()->GetTimerManager().SetTimer(CombatExitTimer, [this]()
                    {
                        ExitCombatState();
                    }, 10.0f, false);
                }
            }
            else
            {
                // Cancelar timer de saída se houver inimigos
                GetWorld()->GetTimerManager().ClearTimer(CombatExitTimer);
            }
            
            break;
        }
            
        case EDragonState::Flying:
            // Movimento de voo é tratado em UpdateFlightMovement
            break;
            
        case EDragonState::Landing:
            // Lógica de pouso
            break;
            
        default:
            break;
    }
}

void ADragonPrismalManager::UpdateCombatTracking(float DeltaTime)
{
    // Remover participantes inativos
    RemoveInactiveCombatParticipants();
}

void ADragonPrismalManager::UpdateCombatTrackingTimer()
{
    // Wrapper sem parâmetros para o timer - usa DeltaTime do Tick
    UpdateCombatTracking(GetWorld()->GetDeltaSeconds());
}

// Funções de geometria elíptica
void ADragonPrismalManager::CalculateEllipticalGeometry()
{
    // Calcular excentricidade
    EllipticalCovil.Eccentricity = CalculateEccentricity();
    
    // Calcular área
    EllipticalCovil.Area = CalculateEllipticalArea();
    
    // Calcular perímetro
    EllipticalCovil.Perimeter = CalculateEllipticalPerimeter();
    
    // Gerar pontos da elipse
    EllipticalCovil.EllipsePoints = GenerateEllipsePoints(64);
    
    // Calcular zonas de pouso
    EllipticalCovil.LandingZones = CalculateLandingZones();
    
    UE_LOG(LogTemp, Warning, TEXT("Elipse calculada - Área: %f, Perímetro: %f, Excentricidade: %f"), 
           EllipticalCovil.Area, EllipticalCovil.Perimeter, EllipticalCovil.Eccentricity);
}

float ADragonPrismalManager::CalculateEllipticalArea() const
{
    // Área da elipse: π × a × b = π × 800 × 600 = 1.507.964 UU² (conforme documentação)
    return UE_PI * EllipticalCovil.SemiMajorAxis * EllipticalCovil.SemiMinorAxis;
}

float ADragonPrismalManager::CalculateEllipticalPerimeter() const
{
    // Aproximação de Ramanujan para perímetro da elipse - UE5.6 Modern API
    const float a = EllipticalCovil.SemiMajorAxis;
    const float b = EllipticalCovil.SemiMinorAxis;
    const float h = FMath::Pow((a - b) / (a + b), 2);
    return UE_PI * (a + b) * (1 + (3 * h) / (10 + FMath::Sqrt(4 - 3 * h)));
}

float ADragonPrismalManager::CalculateEccentricity() const
{
    // Excentricidade da elipse: e = √(1 - b²/a²)
    const float a = EllipticalCovil.SemiMajorAxis;
    const float b = EllipticalCovil.SemiMinorAxis;
    
    if (!ensure(a > 0.0f && b > 0.0f))
    {
        UE_LOG(LogDragonPrismal, Warning, TEXT("Invalid ellipse parameters: a=%f, b=%f"), a, b);
        return 0.0f;
    }
    
    return FMath::Sqrt(1.0f - (b * b) / (a * a));
}

TArray<FVector> ADragonPrismalManager::GenerateEllipsePoints(int32 NumPoints) const
{
    TArray<FVector> Points;
    Points.Reserve(NumPoints);
    
    for (int32 i = 0; i < NumPoints; i++)
    {
        float Angle = (UE_TWO_PI * i) / NumPoints;
        FVector Point = CalculateEllipsePoint(Angle);
        Points.Add(Point);
    }
    
    return Points;
}

FVector ADragonPrismalManager::CalculateEllipsePoint(float AngleRadians) const
{
    return CalculateEllipsePoint(
        AngleRadians,
        EllipticalCovil.Center,
        EllipticalCovil.SemiMajorAxis,
        EllipticalCovil.SemiMinorAxis,
        EllipticalCovil.RotationAngle
    );
}

FVector ADragonPrismalManager::CalculateEllipsePoint(float AngleRadians, const FVector& Center, float SemiMajor, float SemiMinor, float RotationAngle) const
{
    // Calcular ponto na elipse não rotacionada
    float X = SemiMajor * FMath::Cos(AngleRadians);
    float Y = SemiMinor * FMath::Sin(AngleRadians);
    
    // Aplicar rotação
    FVector LocalPoint(X, Y, 0.0f);
    FVector RotatedPoint = RotatePoint2D(LocalPoint, RotationAngle);
    
    // Transladar para o centro
    return Center + RotatedPoint;
}

bool ADragonPrismalManager::IsPositionInEllipse(const FVector& Position) const
{
    return IsPointInsideEllipse(Position);
}

bool ADragonPrismalManager::IsPointInsideEllipse(const FVector& Point) const
{
    // Equação da elipse: (x-h)²/a² + (y-k)²/b² ≤ 1
    // Transladar ponto para origem da elipse
    FVector LocalPoint = Point - EllipticalCovil.Center;
    
    // Aplicar rotação inversa
    FVector RotatedPoint = RotatePoint2D(LocalPoint, -EllipticalCovil.RotationAngle);
    
    // Verificar se está dentro da elipse usando a equação padrão
    const float a = EllipticalCovil.SemiMajorAxis;
    const float b = EllipticalCovil.SemiMinorAxis;
    
    const float EllipseValue = (RotatedPoint.X * RotatedPoint.X) / (a * a) + (RotatedPoint.Y * RotatedPoint.Y) / (b * b);
    
    return EllipseValue <= 1.0f;
}

FVector ADragonPrismalManager::GetClosestPointOnEllipse(const FVector& FromPosition) const
{
    // Encontrar o ponto mais próximo na elipse usando busca binária angular
    float BestAngle = 0.0f;
    float MinDistance = FLT_MAX;
    
    // Busca grosseira
    for (int32 i = 0; i < 36; i++) // 10 graus de incremento
    {
        float Angle = (UE_TWO_PI * i) / 36.0f;
        FVector EllipsePoint = CalculateEllipsePoint(Angle);
        float Distance = FVector::Dist(FromPosition, EllipsePoint);
        
        if (Distance < MinDistance)
        {
            MinDistance = Distance;
            BestAngle = Angle;
        }
    }
    
    // Refinamento
    float AngleStep = UE_TWO_PI / 36.0f;
    for (int32 Refinement = 0; Refinement < 5; Refinement++)
    {
        AngleStep *= 0.1f;
        
        for (int32 i = -5; i <= 5; i++)
        {
            float TestAngle = BestAngle + (i * AngleStep);
            FVector EllipsePoint = CalculateEllipsePoint(TestAngle);
            float Distance = FVector::Dist(FromPosition, EllipsePoint);
            
            if (Distance < MinDistance)
            {
                MinDistance = Distance;
                BestAngle = TestAngle;
            }
        }
    }
    
    return CalculateEllipsePoint(BestAngle);
}

TArray<FVector> ADragonPrismalManager::CalculateLandingZones() const
{
    TArray<FVector> LandingZones;
    
    // Criar 4 zonas de pouso nos pontos cardeais da elipse
    for (int32 i = 0; i < 4; i++)
    {
        float Angle = (PI * i) / 2.0f; // 0°, 90°, 180°, 270°
        FVector LandingPoint = CalculateEllipsePoint(Angle);
        LandingZones.Add(LandingPoint);
    }
    
    return LandingZones;
}

FVector ADragonPrismalManager::RotatePoint2D(const FVector& Point, float AngleDegrees) const
{
    float AngleRadians = FMath::DegreesToRadians(AngleDegrees);
    float CosAngle = FMath::Cos(AngleRadians);
    float SinAngle = FMath::Sin(AngleRadians);
    
    float NewX = Point.X * CosAngle - Point.Y * SinAngle;
    float NewY = Point.X * SinAngle + Point.Y * CosAngle;
    
    return FVector(NewX, NewY, Point.Z);
}

float ADragonPrismalManager::CalculateDistanceToEllipseEdge(const FVector& Point) const
{
    FVector ClosestPoint = GetClosestPointOnEllipse(Point);
    return FVector::Dist(Point, ClosestPoint);
}

// Funções de spawn e controle do Dragão
void ADragonPrismalManager::SpawnDragon()
{
    // Validações de segurança
    if (!ensure(IsValid(GetWorld())))
    {
        UE_LOG(LogDragonPrismal, Error, TEXT("Cannot spawn dragon: World is invalid"));
        return;
    }
    
    if (DragonData.CurrentState != EDragonState::Dormant)
    {
        UE_LOG(LogDragonPrismal, Warning, TEXT("Dragon spawn ignored: Current state is %d"), (int32)DragonData.CurrentState);
        return;
    }
    
    SetDragonState(EDragonState::Spawning);
    
    // Calcular health baseado no tempo de jogo - HP escalável conforme documentação
    float GameTimeMinutes = CurrentGameTime / 60.0f;
    DragonData.CurrentHealth = DragonData.BaseHealth + (DragonData.HealthScaling * GameTimeMinutes);
    
    // Posicionar o Dragão no centro da elipse (0, +4800)
    SetActorLocation(EllipticalCovil.Center + FVector(0.0f, 0.0f, FLIGHT_HEIGHT));
    
    // Tornar visível com validação
    if (ensure(DragonMesh))
    {
        DragonMesh->SetVisibility(true);
        DragonMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    }
    
    // Limpar participantes de combate
    CombatParticipants.Empty();
    
    SetDragonState(EDragonState::Flying);
    
    UE_LOG(LogDragonPrismal, Log, TEXT("Dragon Prismal spawned with %f HP at location %s"), 
           DragonData.CurrentHealth, *EllipticalCovil.Center.ToString());
}

void ADragonPrismalManager::DespawnDragon()
{
    SetDragonState(EDragonState::Dormant);
    
    // Tornar invisível
    DragonMesh->SetVisibility(false);
    DragonMesh->SetCollisionEnabled(ECollisionEnabled::NoCollision);
    
    // Limpar timers
    GetWorld()->GetTimerManager().ClearTimer(FlightTimer);
    GetWorld()->GetTimerManager().ClearTimer(AttackTimer);
    
    // Limpar participantes
    CombatParticipants.Empty();
    
    UE_LOG(LogTemp, Warning, TEXT("Dragão Prismal despawnou"));
}

void ADragonPrismalManager::UpdateDragonHealth()
{
    // Não atualizar HP se o dragão estiver morto ou dormente
    if (DragonData.CurrentState == EDragonState::Dormant || DragonData.CurrentState == EDragonState::Dead)
    {
        return;
    }
    
    // Calcular novo HP máximo baseado no tempo de jogo: 3000 + (200 × minutos) conforme documentação
    float GameTimeMinutes = CurrentGameTime / 60.0f;
    float NewMaxHealth = DragonData.BaseHealth + (DragonData.HealthScaling * GameTimeMinutes);
    
    // Manter proporção de dano se o dragão já recebeu dano
    if (DragonData.CurrentHealth < DragonData.BaseHealth)
    {
        float HealthRatio = DragonData.CurrentHealth / DragonData.BaseHealth;
        DragonData.CurrentHealth = NewMaxHealth * HealthRatio;
    }
    else
    {
        DragonData.CurrentHealth = NewMaxHealth;
    }
    
    // Atualizar base health para próximos cálculos
    DragonData.BaseHealth = NewMaxHealth;
    
    // Log apenas quando há mudança significativa
    static float LastLoggedHealth = 0.0f;
    if (FMath::Abs(NewMaxHealth - LastLoggedHealth) > 100.0f)
    {
        UE_LOG(LogDragonPrismal, VeryVerbose, TEXT("Dragon HP updated: %f (Game time: %f minutes)"), 
               DragonData.CurrentHealth, GameTimeMinutes);
        LastLoggedHealth = NewMaxHealth;
    }
}

void ADragonPrismalManager::StartDragonFlight()
{
    if (DragonData.CurrentState == EDragonState::Active || DragonData.CurrentState == EDragonState::Flying)
    {
        SetDragonState(EDragonState::Flying);
        
        // Resetar caminho de voo
        FlightPath.CurrentWaypointIndex = 0;
        
        GetWorld()->GetTimerManager().SetTimer(
            FlightTimer,
            this,
            &ADragonPrismalManager::LandDragon,
            FLIGHT_DURATION,
            false
        );
        
        UE_LOG(LogTemp, Warning, TEXT("Dragão iniciou voo por %f segundos"), FLIGHT_DURATION);
    }
}

void ADragonPrismalManager::LandDragon()
{
    if (DragonData.CurrentState == EDragonState::Flying)
    {
        SetDragonState(EDragonState::Landing);
        
        // Escolher zona de pouso aleatória
        if (EllipticalCovil.LandingZones.Num() > 0)
        {
            int32 LandingIndex = FMath::RandRange(0, EllipticalCovil.LandingZones.Num() - 1);
            FVector LandingPosition = EllipticalCovil.LandingZones[LandingIndex];
            
            // Mover para posição de pouso
            SetActorLocation(LandingPosition);
        }
        
        SetDragonState(EDragonState::Active);
        
        // Programar próximo voo
        FTimerHandle NextFlightTimer;
        GetWorld()->GetTimerManager().SetTimer(
            NextFlightTimer,
            this,
            &ADragonPrismalManager::StartDragonFlight,
            LANDING_DURATION,
            false
        );
        
        UE_LOG(LogTemp, Warning, TEXT("Dragão pousou e ficará em terra por %f segundos"), LANDING_DURATION);
    }
}

// Funções de combate
void ADragonPrismalManager::StartCombat(AActor* Attacker)
{
    if (DragonData.CurrentState == EDragonState::Active || DragonData.CurrentState == EDragonState::Flying)
    {
        SetDragonState(EDragonState::Combat);
        
        // Forçar pouso se estiver voando
        if (DragonData.CurrentState == EDragonState::Flying)
        {
            LandDragon();
        }
        
        UE_LOG(LogTemp, Warning, TEXT("Combate iniciado com o Dragão Prismal"));
    }
}

void ADragonPrismalManager::PerformSoproPrismal()
{
    // Validações de segurança
    if (DragonData.CurrentState != EDragonState::Combat)
    {
        UE_LOG(LogDragonPrismal, Warning, TEXT("Sopro Prismal cancelled: Not in combat state"));
        return;
    }
    
    if (!ensure(IsValid(GetWorld())))
    {
        return;
    }
    
    // Sopro Prismal: Cone 60°, alcance 1000 UU conforme documentação
    const FVector StartLocation = GetActorLocation();
    const FVector ForwardDirection = GetActorForwardVector();
    
    TArray<AActor*> TargetsInCone = FindTargetsInCone(StartLocation, ForwardDirection, SOPRO_PRISMAL_RANGE, SOPRO_PRISMAL_ANGLE);
    
    int32 TargetsHit = 0;
    for (AActor* Target : TargetsInCone)
    {
        if (ACharacter* Character = Cast<ACharacter>(Target))
        {
            // Aplicar dano mágico (sujeito a 40% de resistência)
            const float BaseDamage = 300.0f;
            const float FinalDamage = BaseDamage * (1.0f - MAGIC_RESISTANCE);
            ApplyDamageToTarget(Character, FinalDamage, EDamageType::Magical);
            
            // Aplicar efeito de cristalização: -50% velocidade por 3s
            ApplyCrystallizationEffect(Character);
            
            TargetsHit++;
        }
    }
    
    // Efeitos visuais e sonoros
    PlaySoproPrismalEffects();
    
    UE_LOG(LogDragonPrismal, Log, TEXT("Sopro Prismal executed: %d targets hit in cone (Range: %f, Angle: %f)"), 
           TargetsHit, SOPRO_PRISMAL_RANGE, SOPRO_PRISMAL_ANGLE);
}

void ADragonPrismalManager::ApplyCrystallizationEffect(ACharacter* Target)
{
    if (!ensure(IsValid(Target)))
    {
        UE_LOG(LogDragonPrismal, Warning, TEXT("Cannot apply crystallization: Invalid target"));
        return;
    }
    
    // Cristalização: -50% velocidade por 3s conforme documentação
    UCharacterMovementComponent* MovementComp = Target->GetCharacterMovement();
    if (!ensure(IsValid(MovementComp)))
    {
        UE_LOG(LogDragonPrismal, Warning, TEXT("Cannot apply crystallization: No movement component on %s"), *Target->GetName());
        return;
    }
    
    const float OriginalSpeed = MovementComp->MaxWalkSpeed;
    const float ReducedSpeed = OriginalSpeed * 0.5f; // 50% de redução conforme doc
    
    MovementComp->MaxWalkSpeed = ReducedSpeed;
    
    // Configurar timer para remover efeito após 3 segundos
    FTimerHandle CrystallizationTimer;
    FTimerDelegate TimerDelegate;
    TimerDelegate.BindLambda([MovementComp, OriginalSpeed, TargetName = Target->GetName()]()
    {
        if (IsValid(MovementComp))
        {
            MovementComp->MaxWalkSpeed = OriginalSpeed;
            UE_LOG(LogDragonPrismal, VeryVerbose, TEXT("Crystallization effect removed from %s"), *TargetName);
        }
    });
    
    GetWorld()->GetTimerManager().SetTimer(CrystallizationTimer, TimerDelegate, 3.0f, false);
    
    UE_LOG(LogDragonPrismal, Log, TEXT("Crystallization effect applied to %s (Speed: %f -> %f)"), 
           *Target->GetName(), OriginalSpeed, ReducedSpeed);
}

void ADragonPrismalManager::EndCombat()
{
    if (DragonData.CurrentState == EDragonState::Combat)
    {
        SetDragonState(EDragonState::Active);
        UE_LOG(LogTemp, Warning, TEXT("Combate com o Dragão Prismal terminou"));
    }
}

float ADragonPrismalManager::TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser)
{
    if (DragonData.CurrentState == EDragonState::Dead || DragonData.CurrentState == EDragonState::Dormant)
    {
        return 0.0f;
    }
    
    // Chamar a implementação da classe base primeiro
    float ActualDamage = Super::TakeDamage(DamageAmount, DamageEvent, EventInstigator, DamageCauser);
    
    DragonData.CurrentHealth -= ActualDamage;
    
    // Adicionar participante ao combate
    if (DamageCauser)
    {
        AddCombatParticipant(DamageCauser, ActualDamage);
        
        // Iniciar combate se não estiver em combate
        if (DragonData.CurrentState != EDragonState::Combat)
        {
            StartCombat(DamageCauser);
        }
    }
    
    UE_LOG(LogTemp, Warning, TEXT("Dragão Prismal recebeu %f de dano. Vida restante: %f"), 
           ActualDamage, DragonData.CurrentHealth);
    
    if (DragonData.CurrentHealth <= 0.0f)
    {
        OnDragonDeath(DamageCauser);
    }
    
    return ActualDamage;
}

void ADragonPrismalManager::OnDragonDeath(AActor* Killer)
{
    SetDragonState(EDragonState::Dead);
    
    // Calcular e distribuir recompensas
    CalculateAndDistributeRewards();
    
    // Programar respawn
    FTimerHandle RespawnTimer;
    GetWorld()->GetTimerManager().SetTimer(
        RespawnTimer,
        [this]()
        {
            DespawnDragon();
            // Resetar para permitir novo spawn após o tempo de respawn
            FTimerHandle DelayedSpawnTimer;
            GetWorld()->GetTimerManager().SetTimer(
                DelayedSpawnTimer,
                [this]()
                {
                    SetDragonState(EDragonState::Dormant);
                },
                DRAGON_RESPAWN_TIME,
                false
            );
        },
        5.0f, // 5 segundos para mostrar morte
        false
    );
    
    UE_LOG(LogTemp, Warning, TEXT("Dragão Prismal foi derrotado! Respawn em %f segundos"), DRAGON_RESPAWN_TIME);
}

void ADragonPrismalManager::PerformAttack(int32 AttackIndex)
{
    if (AttackIndex < 0 || AttackIndex >= DragonAttacks.Num())
    {
        return;
    }
    
    const FDragonAttack& Attack = DragonAttacks[AttackIndex];
    
    if (Attack.bIsAreaAttack)
    {
        ExecuteAreaAttack(Attack);
    }
    else
    {
        ExecuteBreathAttack(Attack);
    }
    
    // Configurar cooldown
    GetWorld()->GetTimerManager().SetTimer(
        AttackTimer,
        [this]()
        {
            // Timer expira, permitindo próximo ataque
        },
        Attack.Cooldown,
        false
    );
    
    UE_LOG(LogTemp, Warning, TEXT("Dragão executou ataque: %s"), *Attack.AttackName);
}

void ADragonPrismalManager::ExecuteBreathAttack(const FDragonAttack& Attack)
{
    // Encontrar alvos na área de combate
    TArray<AActor*> OverlappingActors;
    CombatArea->GetOverlappingActors(OverlappingActors, APawn::StaticClass());
    
    for (AActor* Actor : OverlappingActors)
    {
        float Distance = FVector::Dist(GetActorLocation(), Actor->GetActorLocation());
        if (Distance <= Attack.Range)
        {
            // Aplicar dano (implementação específica dependeria do sistema de dano)
            UE_LOG(LogTemp, Warning, TEXT("Ataque de sopro atingiu: %s por %f de dano"), 
                   *Actor->GetName(), Attack.Damage);
        }
    }
}

void ADragonPrismalManager::ExecuteAreaAttack(const FDragonAttack& Attack)
{
    // Encontrar alvos na área de efeito
    TArray<AActor*> OverlappingActors;
    CombatArea->GetOverlappingActors(OverlappingActors, APawn::StaticClass());
    
    FVector AttackCenter = GetActorLocation();
    
    for (AActor* Actor : OverlappingActors)
    {
        float Distance = FVector::Dist(AttackCenter, Actor->GetActorLocation());
        if (Distance <= Attack.AreaRadius)
        {
            // Calcular dano baseado na distância
            float DamageMultiplier = 1.0f - (Distance / Attack.AreaRadius);
            float FinalDamage = Attack.Damage * DamageMultiplier;
            
            UE_LOG(LogTemp, Warning, TEXT("Ataque em área atingiu: %s por %f de dano"), 
                   *Actor->GetName(), FinalDamage);
        }
    }
}

// Sistema de voo
void ADragonPrismalManager::GenerateFlightPath()
{
    FlightPath.Waypoints.Empty();
    FlightPath.CurrentWaypointIndex = 0;
    
    // Gerar waypoints em círculo ao redor da elipse
    for (int32 i = 0; i < FLIGHT_WAYPOINTS; i++)
    {
        float Angle = (UE_TWO_PI * i) / FLIGHT_WAYPOINTS;
        
        // Expandir o raio para voo externo à elipse
        float FlightRadius = FMath::Max(EllipticalCovil.SemiMajorAxis, EllipticalCovil.SemiMinorAxis) * 1.5f;
        
        FVector Waypoint = EllipticalCovil.Center + FVector(
            FlightRadius * FMath::Cos(Angle),
            FlightRadius * FMath::Sin(Angle),
            FLIGHT_HEIGHT
        );
        
        FlightPath.Waypoints.Add(Waypoint);
    }
    
    FlightPath.bIsCircular = true;
    FlightPath.PathLength = CalculateFlightPathLength();
    FlightPath.FlightDuration = FlightPath.PathLength / FLIGHT_SPEED;
    
    UE_LOG(LogTemp, Warning, TEXT("Caminho de voo gerado com %d waypoints, comprimento: %f"), 
           FlightPath.Waypoints.Num(), FlightPath.PathLength);
}

void ADragonPrismalManager::UpdateFlightMovement(float DeltaTime)
{
    if (FlightPath.Waypoints.Num() == 0 || DragonData.CurrentState != EDragonState::Flying)
    {
        return;
    }
    
    FVector CurrentTarget = GetNextWaypoint();
    FVector CurrentPosition = GetActorLocation();
    
    // Calcular direção e distância
    FVector Direction = (CurrentTarget - CurrentPosition).GetSafeNormal();
    float Distance = FVector::Dist(CurrentPosition, CurrentTarget);
    
    // Mover em direção ao waypoint
    FVector NewPosition = CurrentPosition + (Direction * FLIGHT_SPEED * DeltaTime);
    SetActorLocation(NewPosition);
    
    // Atualizar rotação
    UpdateDragonRotation(Direction);
    
    // Verificar se chegou ao waypoint
    if (Distance < 100.0f) // 1 metro de tolerância
    {
        AdvanceToNextWaypoint();
    }
}

FVector ADragonPrismalManager::GetNextWaypoint() const
{
    if (FlightPath.Waypoints.Num() == 0)
    {
        return GetActorLocation();
    }
    
    return FlightPath.Waypoints[FlightPath.CurrentWaypointIndex];
}

void ADragonPrismalManager::AdvanceToNextWaypoint()
{
    if (FlightPath.Waypoints.Num() == 0)
    {
        return;
    }
    
    FlightPath.CurrentWaypointIndex++;
    
    if (FlightPath.bIsCircular && FlightPath.CurrentWaypointIndex >= FlightPath.Waypoints.Num())
    {
        FlightPath.CurrentWaypointIndex = 0;
    }
}

float ADragonPrismalManager::CalculateFlightPathLength() const
{
    if (FlightPath.Waypoints.Num() < 2)
    {
        return 0.0f;
    }
    
    float TotalLength = 0.0f;
    
    for (int32 i = 0; i < FlightPath.Waypoints.Num() - 1; i++)
    {
        TotalLength += FVector::Dist(FlightPath.Waypoints[i], FlightPath.Waypoints[i + 1]);
    }
    
    // Se for circular, adicionar distância do último ao primeiro
    if (FlightPath.bIsCircular)
    {
        TotalLength += FVector::Dist(FlightPath.Waypoints.Last(), FlightPath.Waypoints[0]);
    }
    
    return TotalLength;
}

void ADragonPrismalManager::UpdateDragonRotation(const FVector& TargetDirection)
{
    if (TargetDirection.IsNearlyZero())
    {
        return;
    }
    
    FRotator TargetRotation = TargetDirection.Rotation();
    FRotator CurrentRotation = GetActorRotation();
    
    // Interpolar suavemente para a rotação alvo
    FRotator NewRotation = FMath::RInterpTo(CurrentRotation, TargetRotation, GetWorld()->GetDeltaSeconds(), 2.0f);
    SetActorRotation(NewRotation);
}

// Sistema de recompensas
void ADragonPrismalManager::CalculateAndDistributeRewards()
{
    TArray<AActor*> EligibleParticipants = GetEligibleParticipants();
    
    if (EligibleParticipants.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("Nenhum participante elegível para recompensas"));
        return;
    }
    
    float GameTimeMinutes = CurrentGameTime / 60.0f;
    float BaseGold = RewardSystem.BaseGold + (RewardSystem.GoldScaling * GameTimeMinutes);
    float BaseExperience = RewardSystem.BaseExperience + (RewardSystem.ExperienceScaling * GameTimeMinutes);
    
    // Calcular bônus de equipe ou solo
    float TeamBonus = 1.0f;
    if (EligibleParticipants.Num() == 1)
    {
        TeamBonus = CalculateSoloBonus(EligibleParticipants[0]);
    }
    else
    {
        TeamBonus = CalculateTeamBonus(EligibleParticipants);
    }
    
    // Distribuir recompensas
    for (AActor* Participant : EligibleParticipants)
    {
        float ParticipationPercent = CalculateParticipationPercentage(Participant);
        
        float GoldReward = BaseGold * ParticipationPercent * TeamBonus;
        float ExperienceReward = BaseExperience * ParticipationPercent * TeamBonus;
        
        GiveRewardToParticipant(Participant, GoldReward, ExperienceReward);
    }
    
    UE_LOG(LogTemp, Warning, TEXT("Recompensas distribuídas para %d participantes"), EligibleParticipants.Num());
}

TArray<AActor*> ADragonPrismalManager::GetEligibleParticipants() const
{
    TArray<AActor*> EligibleParticipants;
    float TotalDamage = GetTotalDamageDealt();
    
    for (const auto& Participant : CombatParticipants)
    {
        float ParticipationPercent = Participant.Value / TotalDamage;
        
        if (ParticipationPercent >= RewardSystem.ParticipationThreshold)
        {
            EligibleParticipants.Add(Participant.Key);
        }
    }
    
    return EligibleParticipants;
}

float ADragonPrismalManager::CalculateParticipationPercentage(AActor* Participant) const
{
    if (!CombatParticipants.Contains(Participant))
    {
        return 0.0f;
    }
    
    float ParticipantDamage = CombatParticipants[Participant];
    float TotalDamage = GetTotalDamageDealt();
    
    return (TotalDamage > 0.0f) ? (ParticipantDamage / TotalDamage) : 0.0f;
}

void ADragonPrismalManager::GiveRewardToParticipant(AActor* Participant, float GoldAmount, float ExperienceAmount)
{
    if (!ensure(IsValid(Participant)))
    {
        UE_LOG(LogDragonPrismal, Error, TEXT("Invalid participant for reward distribution"));
        return;
    }
    
    // Integração robusta com sistema de jogadores UE5.6
    if (APawn* PlayerPawn = Cast<APawn>(Participant))
    {
        if (APlayerController* PC = PlayerPawn->GetController<APlayerController>())
        {
            // Integrar com GameMode para distribuição de recompensas
            if (AGameModeBase* GameMode = GetWorld()->GetAuthGameMode())
            {
                // Usar interface de recompensas se disponível
                if (GameMode->GetClass()->ImplementsInterface(URewardSystemInterface::StaticClass()))
                {
                    IRewardSystemInterface::Execute_GivePlayerReward(
                        GameMode,
                        PC,
                        static_cast<int32>(GoldAmount),
                        static_cast<int32>(ExperienceAmount)
                    );
                }
                else
                {
                    // Fallback: usar PlayerState se disponível
                    if (APlayerState* PS = PC->GetPlayerState<APlayerState>())
                    {
                        // Tentar cast para PlayerState customizado com sistema de recompensas
                        if (PS->GetClass()->GetName().Contains(TEXT("Aura")))
                        {
                            // Usar reflection para chamar métodos de recompensa
                            UFunction* AddGoldFunc = PS->GetClass()->FindFunctionByName(TEXT("AddGold"));
                            UFunction* AddXPFunc = PS->GetClass()->FindFunctionByName(TEXT("AddExperience"));
                            
                            if (AddGoldFunc && AddXPFunc)
                            {
                                PS->ProcessEvent(AddGoldFunc, &GoldAmount);
                                PS->ProcessEvent(AddXPFunc, &ExperienceAmount);
                                
                                UE_LOG(LogDragonPrismal, Log, TEXT("Rewards given to %s via PlayerState: %f gold, %f XP"), 
                                       *PC->GetName(), GoldAmount, ExperienceAmount);
                            }
                            else
                            {
                                UE_LOG(LogDragonPrismal, Warning, TEXT("PlayerState methods not found for %s"), *PC->GetName());
                            }
                        }
                    }
                }
                
                // Broadcast evento de recompensa para UI/HUD
                if (UGameplayStatics::GetPlayerController(GetWorld(), 0) == PC)
                {
                    // Criar e broadcast evento de recompensa
                    FString RewardMessage = FString::Printf(TEXT("Dragon Prismal defeated! +%d Gold, +%d XP"), 
                                                          static_cast<int32>(GoldAmount), 
                                                          static_cast<int32>(ExperienceAmount));
                    
                    // Usar GameInstance para broadcast se disponível
                    if (UGameInstance* GI = GetWorld()->GetGameInstance())
                    {
                        // Tentar encontrar delegate de notificação
                        UFunction* NotifyFunc = GI->GetClass()->FindFunctionByName(TEXT("ShowRewardNotification"));
                        if (NotifyFunc)
                        {
                            GI->ProcessEvent(NotifyFunc, &RewardMessage);
                        }
                    }
                }
            }
        }
    }
    
    // Log detalhado para debugging
    UE_LOG(LogDragonPrismal, Log, TEXT("Reward distribution completed for %s: %f gold, %f XP (Participation: %f%%)"), 
           *Participant->GetName(), GoldAmount, ExperienceAmount, 
           CalculateParticipationPercentage(Participant) * 100.0f);
}

float ADragonPrismalManager::CalculateTeamBonus(const TArray<AActor*>& TeamMembers) const
{
    return RewardSystem.TeamBonusMultiplier;
}

float ADragonPrismalManager::CalculateSoloBonus(AActor* SoloPlayer) const
{
    return RewardSystem.SoloBonusMultiplier;
}

void ADragonPrismalManager::AddCombatParticipant(AActor* Participant, float Damage)
{
    if (!Participant)
    {
        return;
    }
    
    if (CombatParticipants.Contains(Participant))
    {
        CombatParticipants[Participant] += Damage;
    }
    else
    {
        CombatParticipants.Add(Participant, Damage);
    }
}

void ADragonPrismalManager::RemoveInactiveCombatParticipants()
{
    // Remover participantes que não estão mais na área de recompensas
    TArray<AActor*> OverlappingActors;
    RewardArea->GetOverlappingActors(OverlappingActors, APawn::StaticClass());
    
    TArray<AActor*> ParticipantsToRemove;
    
    for (const auto& Participant : CombatParticipants)
    {
        if (!OverlappingActors.Contains(Participant.Key))
        {
            ParticipantsToRemove.Add(Participant.Key);
        }
    }
    
    for (AActor* ParticipantToRemove : ParticipantsToRemove)
    {
        CombatParticipants.Remove(ParticipantToRemove);
    }
}

float ADragonPrismalManager::GetTotalDamageDealt() const
{
    float TotalDamage = 0.0f;
    
    for (const auto& Participant : CombatParticipants)
    {
        TotalDamage += Participant.Value;
    }
    
    return TotalDamage;
}

// Funções de validação e debug
bool ADragonPrismalManager::ValidateEllipticalGeometry() const
{
    if (EllipticalCovil.SemiMajorAxis <= 0.0f || EllipticalCovil.SemiMinorAxis <= 0.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("Eixos da elipse devem ser positivos"));
        return false;
    }
    
    if (EllipticalCovil.SemiMajorAxis < EllipticalCovil.SemiMinorAxis)
    {
        UE_LOG(LogTemp, Error, TEXT("Eixo maior deve ser maior que o eixo menor"));
        return false;
    }
    
    if (EllipticalCovil.Eccentricity < 0.0f || EllipticalCovil.Eccentricity >= 1.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("Excentricidade deve estar entre 0 e 1"));
        return false;
    }
    
    return true;
}

void ADragonPrismalManager::DrawDebugEllipse() const
{
    if (!GetWorld()) return;
    
    // Desenhar pontos da elipse
    for (int32 i = 0; i < EllipticalCovil.EllipsePoints.Num(); i++)
    {
        int32 NextIndex = (i + 1) % EllipticalCovil.EllipsePoints.Num();
        
        DrawDebugLine(
            GetWorld(),
            EllipticalCovil.EllipsePoints[i],
            EllipticalCovil.EllipsePoints[NextIndex],
            FColor::Blue,
            false,
            1.0f,
            0,
            3.0f
        );
    }
    
    // Desenhar centro
    DrawDebugSphere(
        GetWorld(),
        EllipticalCovil.Center,
        50.0f,
        12,
        FColor::Yellow,
        false,
        1.0f
    );
    
    // Desenhar eixos
    FVector MajorAxisEnd = EllipticalCovil.Center + FVector(EllipticalCovil.SemiMajorAxis, 0.0f, 0.0f);
    FVector MinorAxisEnd = EllipticalCovil.Center + FVector(0.0f, EllipticalCovil.SemiMinorAxis, 0.0f);
    
    DrawDebugLine(GetWorld(), EllipticalCovil.Center, MajorAxisEnd, FColor::Red, false, 1.0f, 0, 5.0f);
    DrawDebugLine(GetWorld(), EllipticalCovil.Center, MinorAxisEnd, FColor::Green, false, 1.0f, 0, 5.0f);
}

void ADragonPrismalManager::DrawDebugFlightPath() const
{
    if (!GetWorld() || FlightPath.Waypoints.Num() < 2) return;
    
    for (int32 i = 0; i < FlightPath.Waypoints.Num() - 1; i++)
    {
        DrawDebugLine(
            GetWorld(),
            FlightPath.Waypoints[i],
            FlightPath.Waypoints[i + 1],
            FColor::Purple,
            false,
            1.0f,
            0,
            4.0f
        );
        
        DrawDebugSphere(
            GetWorld(),
            FlightPath.Waypoints[i],
            30.0f,
            8,
            FColor::Purple,
            false,
            1.0f
        );
    }
    
    // Conectar último ao primeiro se for circular
    if (FlightPath.bIsCircular && FlightPath.Waypoints.Num() > 2)
    {
        DrawDebugLine(
            GetWorld(),
            FlightPath.Waypoints.Last(),
            FlightPath.Waypoints[0],
            FColor::Purple,
            false,
            1.0f,
            0,
            4.0f
        );
    }
}

void ADragonPrismalManager::DrawDebugLandingZones() const
{
    if (!GetWorld()) return;
    
    for (const FVector& LandingZone : EllipticalCovil.LandingZones)
    {
        DrawDebugSphere(
            GetWorld(),
            LandingZone,
            100.0f,
            12,
            FColor::Orange,
            false,
            1.0f
        );
    }
}

// Funções de timing e estado
bool ADragonPrismalManager::ShouldSpawnDragon() const
{
    return CurrentGameTime >= DRAGON_SPAWN_TIME;
}

float ADragonPrismalManager::GetTimeUntilSpawn() const
{
    return FMath::Max(0.0f, DRAGON_SPAWN_TIME - CurrentGameTime);
}

float ADragonPrismalManager::GetTimeUntilRespawn() const
{
    if (DragonData.CurrentState == EDragonState::Dead)
    {
        return DRAGON_RESPAWN_TIME; // Simplificado
    }
    return 0.0f;
}

void ADragonPrismalManager::SetDragonState(EDragonState NewState)
{
    EDragonState OldState = DragonData.CurrentState;
    DragonData.CurrentState = NewState;
    
    UE_LOG(LogTemp, Warning, TEXT("Estado do Dragão mudou de %d para %d"), 
           (int32)OldState, (int32)NewState);
}

void ADragonPrismalManager::SetDragonType(EDragonType NewType)
{
    DragonData.Type = NewType;
    UpdateAttacksForElement();
}

void ADragonPrismalManager::SetDragonElement(EDragonElement NewElement)
{
    DragonData.Element = NewElement;
    UpdateAttacksForElement();
}

void ADragonPrismalManager::UpdateAttacksForElement()
{
    // Atualizar ataques baseado no elemento
    for (FDragonAttack& Attack : DragonAttacks)
    {
        Attack.Element = DragonData.Element;
        
        // Ajustar dano baseado no elemento
        switch (DragonData.Element)
        {
            case EDragonElement::Fire:
                Attack.Damage *= 1.2f; // +20% dano de fogo
                break;
            case EDragonElement::Water:
                Attack.Damage *= 1.0f; // Dano normal
                Attack.AreaRadius *= 1.3f; // +30% área
                break;
            case EDragonElement::Earth:
                Attack.Damage *= 1.5f; // +50% dano físico
                break;
            case EDragonElement::Air:
                Attack.Range *= 1.4f; // +40% alcance
                break;
        }
    }
}

// Mecânicas de Fúria Crescente
void ADragonPrismalManager::ActivateFuriaCrescente()
{
    // Fúria Crescente: Ativa quando HP <= 30% conforme documentação
    const float HealthPercentage = DragonData.CurrentHealth / DragonData.BaseHealth;
    
    if (HealthPercentage <= 0.3f && !DragonData.bFuriaCrescenteActive)
    {
        DragonData.bFuriaCrescenteActive = true;
        
        // Aumentar dano e velocidade progressivamente baseado no HP restante
        const float FuryIntensity = FMath::Clamp((0.3f - HealthPercentage) / 0.3f, 0.0f, 1.0f);
        DragonData.DamageMultiplier = 1.0f + (0.5f * FuryIntensity); // Até +50% dano
        DragonData.SpeedMultiplier = 1.0f + (0.3f * FuryIntensity);  // Até +30% velocidade
        
        // Efeitos visuais da fúria
        PlayFuriaCrescenteEffects();
        
        UE_LOG(LogDragonPrismal, Warning, TEXT("Fúria Crescente activated! HP: %f%%, Damage: +%f%%, Speed: +%f%%"), 
               HealthPercentage * 100.0f, (DragonData.DamageMultiplier - 1.0f) * 100.0f, 
               (DragonData.SpeedMultiplier - 1.0f) * 100.0f);
    }
    else if (HealthPercentage > 0.3f && DragonData.bFuriaCrescenteActive)
    {
        // Desativar se HP subir acima de 30% (cura, etc.)
        DeactivateFuriaCrescente();
    }
}

void ADragonPrismalManager::UpdateFuriaCrescente()
{
    if (!DragonData.bIsInCombat)
    {
        return;
    }
    
    const float HealthPercentage = DragonData.CurrentHealth / DragonData.BaseHealth;
    
    if (DragonData.bFuriaCrescenteActive)
    {
        if (HealthPercentage > 0.3f)
        {
            // Desativar fúria se HP subir acima de 30%
            DeactivateFuriaCrescente();
        }
        else
        {
            // Atualizar intensidade da fúria baseada no HP atual
            const float FuryIntensity = FMath::Clamp((0.3f - HealthPercentage) / 0.3f, 0.0f, 1.0f);
            const float NewDamageMultiplier = 1.0f + (0.5f * FuryIntensity);
            const float NewSpeedMultiplier = 1.0f + (0.3f * FuryIntensity);
            
            // Só logar se houve mudança significativa
            if (FMath::Abs(DragonData.DamageMultiplier - NewDamageMultiplier) > 0.05f)
            {
                DragonData.DamageMultiplier = NewDamageMultiplier;
                DragonData.SpeedMultiplier = NewSpeedMultiplier;
                
                UE_LOG(LogDragonPrismal, VeryVerbose, TEXT("Fúria Crescente intensity updated: HP %f%%, Damage +%f%%, Speed +%f%%"), 
                       HealthPercentage * 100.0f, (DragonData.DamageMultiplier - 1.0f) * 100.0f, 
                       (DragonData.SpeedMultiplier - 1.0f) * 100.0f);
            }
        }
    }
    else if (HealthPercentage <= 0.3f)
    {
        // Ativar fúria se HP cair para 30% ou menos
        ActivateFuriaCrescente();
    }
}

void ADragonPrismalManager::DeactivateFuriaCrescente()
{
    if (!DragonData.bFuriaCrescenteActive)
    {
        return;
    }
    
    DragonData.bFuriaCrescenteActive = false;
    DragonData.DamageMultiplier = 1.0f;
    DragonData.SpeedMultiplier = 1.0f;
    
    // Efeitos visuais de desativação
    StopFuriaCrescenteEffects();
    
    UE_LOG(LogDragonPrismal, Log, TEXT("Fúria Crescente deactivated - multipliers reset to normal"));
}

// Funções auxiliares para mecânicas
TArray<AActor*> ADragonPrismalManager::FindTargetsInCone(const FVector& StartLocation, const FVector& Direction, float Range, float AngleDegrees) const
{
    TArray<AActor*> TargetsInCone;
    
    if (!ensure(IsValid(GetWorld())))
    {
        return TargetsInCone;
    }
    
    // Encontrar todos os atores na área de combate
    TArray<AActor*> OverlappingActors;
    CombatArea->GetOverlappingActors(OverlappingActors, APawn::StaticClass());
    
    const float HalfAngleRadians = FMath::DegreesToRadians(AngleDegrees * 0.5f);
    const FVector NormalizedDirection = Direction.GetSafeNormal();
    
    for (AActor* Actor : OverlappingActors)
    {
        if (!IsValid(Actor) || Actor == this)
        {
            continue;
        }
        
        const FVector ToTarget = (Actor->GetActorLocation() - StartLocation).GetSafeNormal();
        const float Distance = FVector::Dist(StartLocation, Actor->GetActorLocation());
        
        // Verificar se está dentro do alcance
        if (Distance > Range)
        {
            continue;
        }
        
        // Verificar se está dentro do cone
        const float DotProduct = FVector::DotProduct(NormalizedDirection, ToTarget);
        const float AngleToTarget = FMath::Acos(DotProduct);
        
        if (AngleToTarget <= HalfAngleRadians)
        {
            TargetsInCone.Add(Actor);
        }
    }
    
    return TargetsInCone;
}

void ADragonPrismalManager::ApplyDamageToTarget(ACharacter* Target, float Damage, EDamageType DamageType)
{
    if (!ensure(IsValid(Target)))
    {
        UE_LOG(LogDragonPrismal, Error, TEXT("ApplyDamageToTarget: Invalid target"));
        return;
    }
    
    if (!ensure(IsValid(GetWorld())))
    {
        UE_LOG(LogDragonPrismal, Error, TEXT("ApplyDamageToTarget: Invalid world context"));
        return;
    }
    
    // Aplicar resistências conforme documentação
    float FinalDamage = Damage;
    switch (DamageType)
    {
        case EDamageType::Magical:
            FinalDamage *= (1.0f - MAGIC_RESISTANCE); // 40% resistência mágica
            break;
        case EDamageType::Physical:
            FinalDamage *= (1.0f - PHYSICAL_RESISTANCE); // 20% resistência física
            break;
        default:
            break;
    }
    
    // Aplicar multiplicador de Fúria Crescente
    if (DragonData.bFuriaCrescenteActive)
    {
        FinalDamage *= DragonData.DamageMultiplier;
        UE_LOG(LogDragonPrismal, VeryVerbose, TEXT("Fúria Crescente damage multiplier applied: %f"), DragonData.DamageMultiplier);
    }
    
    // Validar dano mínimo
    if (FinalDamage <= 0.0f)
    {
        UE_LOG(LogDragonPrismal, Warning, TEXT("Final damage is zero or negative: %f"), FinalDamage);
        return;
    }
    
    // Registrar participação no combate ANTES de aplicar o dano
    AddCombatParticipant(Target, FinalDamage);
    
    // Aplicar dano real usando o sistema UE5.6
    const float ActualDamageDealt = UGameplayStatics::ApplyDamage(
        Target,                    // Target actor
        FinalDamage,              // Base damage
        nullptr,                  // Event instigator (pode ser o controller do dragão)
        this,                     // Damage causer (o próprio dragão)
        UDamageType::StaticClass() // Damage type class
    );
    
    // Verificar se o dano foi realmente aplicado
    if (ActualDamageDealt > 0.0f)
    {
        UE_LOG(LogDragonPrismal, Log, TEXT("Damage successfully applied to %s: %f (%s, Original: %f, Calculated: %f)"), 
               *Target->GetName(), ActualDamageDealt,
               DamageType == EDamageType::Magical ? TEXT("Magical") : TEXT("Physical"), 
               Damage, FinalDamage);
        
        // Efeitos visuais de impacto no alvo
        if (IsValid(CrystalShatterEffect) && DamageType == EDamageType::Magical)
        {
            // Spawnar efeito de cristalização no local do alvo
            const FVector ImpactLocation = Target->GetActorLocation();
            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                CrystalShatterEffect,
                ImpactLocation,
                FRotator::ZeroRotator
            );
            
            UE_LOG(LogDragonPrismal, VeryVerbose, TEXT("Crystal shatter effect spawned at target location"));
        }
        
        // Som de impacto se disponível
        if (IsValid(CrystalBreakSound) && DamageType == EDamageType::Magical)
        {
            UGameplayStatics::PlaySoundAtLocation(
                GetWorld(),
                CrystalBreakSound,
                Target->GetActorLocation(),
                0.8f // Volume
            );
            
            UE_LOG(LogDragonPrismal, VeryVerbose, TEXT("Crystal break sound played at target location"));
        }
    }
    else
    {
        UE_LOG(LogDragonPrismal, Warning, TEXT("No damage was dealt to %s (Target may be invulnerable or dead)"), 
               *Target->GetName());
    }
    
    // Atualizar estatísticas de combate
    DragonData.TotalDamageDealt += ActualDamageDealt;
    
    UE_LOG(LogDragonPrismal, VeryVerbose, TEXT("Combat stats updated - Total damage dealt: %f"), DragonData.TotalDamageDealt);
}

void ADragonPrismalManager::PlaySoproPrismalEffects()
{
    if (!ensure(IsValid(this)))
    {
        UE_LOG(LogDragonPrismal, Error, TEXT("PlaySoproPrismalEffects: Invalid DragonPrismalManager"));
        return;
    }

    // Ativar sistema de partículas Niagara para Sopro Prismal
    if (IsValid(SoproPrismalParticles) && IsValid(SoproPrismalEffect))
    {
        SoproPrismalParticles->SetAsset(SoproPrismalEffect);
        SoproPrismalParticles->Activate(true);
        
        // Configurar parâmetros do efeito baseados na documentação
        SoproPrismalParticles->SetFloatParameter(TEXT("ConeAngle"), 60.0f);
        SoproPrismalParticles->SetFloatParameter(TEXT("Range"), SOPRO_PRISMAL_RANGE);
        SoproPrismalParticles->SetVectorParameter(TEXT("StartLocation"), GetActorLocation());
        SoproPrismalParticles->SetVectorParameter(TEXT("Direction"), GetActorForwardVector());
        
        // Intensidade baseada na Fúria Crescente
        const float Intensity = DragonData.bFuriaCrescenteActive ? DragonData.DamageMultiplier : 1.0f;
        SoproPrismalParticles->SetFloatParameter(TEXT("Intensity"), Intensity);
        
        UE_LOG(LogDragonPrismal, Log, TEXT("Sopro Prismal particles activated with intensity: %f"), Intensity);
    }
    else
    {
        UE_LOG(LogDragonPrismal, Warning, TEXT("SoproPrismalParticles or SoproPrismalEffect not configured"));
    }

    // Reproduzir som do Sopro Prismal
    if (IsValid(DragonAudioComponent) && IsValid(SoproPrismalSound))
    {
        DragonAudioComponent->SetSound(SoproPrismalSound);
        DragonAudioComponent->Play();
        
        // Ajustar volume baseado na Fúria Crescente
        const float VolumeMultiplier = DragonData.bFuriaCrescenteActive ? 1.5f : 1.0f;
        DragonAudioComponent->SetVolumeMultiplier(VolumeMultiplier);
        
        UE_LOG(LogDragonPrismal, VeryVerbose, TEXT("Sopro Prismal sound playing with volume multiplier: %f"), VolumeMultiplier);
    }
    else
    {
        UE_LOG(LogDragonPrismal, Warning, TEXT("DragonAudioComponent or SoproPrismalSound not configured"));
    }

    // Efeito adicional de cristalização se disponível
    if (IsValid(CrystalShatterEffect))
    {
        // Spawnar efeito de cristalização no local do impacto (será implementado quando houver detecção de alvos)
        UE_LOG(LogDragonPrismal, VeryVerbose, TEXT("Crystal shatter effect ready for target impact"));
    }

    UE_LOG(LogDragonPrismal, Log, TEXT("Sopro Prismal effects activated successfully"));
}

void ADragonPrismalManager::PlayFuriaCrescenteEffects()
{
    if (!ensure(IsValid(this)))
    {
        UE_LOG(LogDragonPrismal, Error, TEXT("PlayFuriaCrescenteEffects: Invalid DragonPrismalManager"));
        return;
    }

    // Ativar sistema de partículas Niagara para Fúria Crescente
    if (IsValid(FuriaCrescenteParticles) && IsValid(FuriaCrescenteEffect))
    {
        FuriaCrescenteParticles->SetAsset(FuriaCrescenteEffect);
        FuriaCrescenteParticles->Activate(true);
        
        // Configurar parâmetros da aura baseados no HP atual
        const float HealthPercentage = DragonData.CurrentHealth / DragonData.BaseHealth;
        const float FuryIntensity = FMath::Clamp((0.3f - HealthPercentage) / 0.3f, 0.0f, 1.0f);
        
        // Parâmetros da aura vermelha
        FuriaCrescenteParticles->SetFloatParameter(TEXT("AuraIntensity"), FuryIntensity);
        FuriaCrescenteParticles->SetFloatParameter(TEXT("AuraRadius"), 500.0f + (FuryIntensity * 300.0f));
        FuriaCrescenteParticles->SetVectorParameter(TEXT("AuraColor"), FVector(1.0f, 0.2f * (1.0f - FuryIntensity), 0.0f));
        
        // Partículas de energia crescente
        FuriaCrescenteParticles->SetFloatParameter(TEXT("EnergyParticleCount"), 50.0f + (FuryIntensity * 100.0f));
        FuriaCrescenteParticles->SetFloatParameter(TEXT("EnergySpeed"), 200.0f + (FuryIntensity * 400.0f));
        
        // Efeito nos olhos do dragão
        FuriaCrescenteParticles->SetVectorParameter(TEXT("EyeGlowColor"), FVector(1.0f, 0.1f, 0.0f));
        FuriaCrescenteParticles->SetFloatParameter(TEXT("EyeGlowIntensity"), 2.0f + (FuryIntensity * 3.0f));
        
        UE_LOG(LogDragonPrismal, Log, TEXT("Fúria Crescente particles activated - Intensity: %f, HP: %f%%"), 
               FuryIntensity, HealthPercentage * 100.0f);
    }
    else
    {
        UE_LOG(LogDragonPrismal, Warning, TEXT("FuriaCrescenteParticles or FuriaCrescenteEffect not configured"));
    }

    // Reproduzir som da Fúria Crescente
    if (IsValid(CombatAudioComponent) && IsValid(FuriaCrescenteSound))
    {
        CombatAudioComponent->SetSound(FuriaCrescenteSound);
        CombatAudioComponent->Play();
        
        // Volume baseado na intensidade da fúria
        const float HealthPercentage = DragonData.CurrentHealth / DragonData.BaseHealth;
        const float FuryIntensity = FMath::Clamp((0.3f - HealthPercentage) / 0.3f, 0.0f, 1.0f);
        const float VolumeMultiplier = 0.8f + (FuryIntensity * 0.7f); // 0.8 a 1.5
        
        CombatAudioComponent->SetVolumeMultiplier(VolumeMultiplier);
        CombatAudioComponent->SetPitchMultiplier(0.9f + (FuryIntensity * 0.3f)); // Pitch mais grave quando mais furioso
        
        UE_LOG(LogDragonPrismal, VeryVerbose, TEXT("Fúria Crescente sound playing - Volume: %f, Pitch: %f"), 
               VolumeMultiplier, 0.9f + (FuryIntensity * 0.3f));
    }
    else
    {
        UE_LOG(LogDragonPrismal, Warning, TEXT("CombatAudioComponent or FuriaCrescenteSound not configured"));
    }

    // Rugido do dragão para anunciar a fúria
    if (IsValid(DragonAudioComponent) && IsValid(DragonRoarSound))
    {
        // Delay de 0.5s para o rugido após o início da fúria
        FTimerHandle RoarTimer;
        GetWorld()->GetTimerManager().SetTimer(RoarTimer, [this]()
        {
            if (IsValid(DragonAudioComponent) && IsValid(DragonRoarSound))
            {
                DragonAudioComponent->SetSound(DragonRoarSound);
                DragonAudioComponent->Play();
                UE_LOG(LogDragonPrismal, Log, TEXT("Dragon roar played for Fúria Crescente activation"));
            }
        }, 0.5f, false);
    }

    UE_LOG(LogDragonPrismal, Log, TEXT("Fúria Crescente effects activated - Aura, particles, and audio systems engaged"));
}

void ADragonPrismalManager::StopFuriaCrescenteEffects()
{
    if (!ensure(IsValid(this)))
    {
        UE_LOG(LogDragonPrismal, Error, TEXT("StopFuriaCrescenteEffects: Invalid DragonPrismalManager"));
        return;
    }

    // Desativar sistema de partículas da Fúria Crescente
    if (IsValid(FuriaCrescenteParticles))
    {
        // Fade out gradual das partículas em vez de parada abrupta
        FuriaCrescenteParticles->SetFloatParameter(TEXT("FadeOutDuration"), 2.0f);
        FuriaCrescenteParticles->SetFloatParameter(TEXT("AuraIntensity"), 0.0f);
        FuriaCrescenteParticles->SetFloatParameter(TEXT("EnergyParticleCount"), 0.0f);
        FuriaCrescenteParticles->SetFloatParameter(TEXT("EyeGlowIntensity"), 0.5f); // Retorna ao brilho normal
        
        // Desativar após o fade out
        FTimerHandle FadeTimer;
        GetWorld()->GetTimerManager().SetTimer(FadeTimer, [this]()
        {
            if (IsValid(FuriaCrescenteParticles))
            {
                FuriaCrescenteParticles->Deactivate();
                UE_LOG(LogDragonPrismal, VeryVerbose, TEXT("Fúria Crescente particles deactivated after fade out"));
            }
        }, 2.5f, false);
        
        UE_LOG(LogDragonPrismal, Log, TEXT("Fúria Crescente particles fade out initiated"));
    }
    else
    {
        UE_LOG(LogDragonPrismal, Warning, TEXT("FuriaCrescenteParticles component not found"));
    }

    // Parar som da Fúria Crescente com fade out
    if (IsValid(CombatAudioComponent))
    {
        // Fade out do áudio em 1.5 segundos
        CombatAudioComponent->FadeOut(1.5f, 0.0f);
        
        UE_LOG(LogDragonPrismal, VeryVerbose, TEXT("Fúria Crescente audio fade out initiated"));
    }
    else
    {
        UE_LOG(LogDragonPrismal, Warning, TEXT("CombatAudioComponent not found"));
    }

    // Resetar parâmetros visuais do dragão
    if (IsValid(DragonMesh))
    {
        // Aqui seria resetada a cor dos olhos e outros materiais do dragão
        // Por enquanto apenas log para auditoria
        UE_LOG(LogDragonPrismal, VeryVerbose, TEXT("Dragon visual parameters reset to normal state"));
    }

    // Som de desativação da fúria (rugido mais calmo)
    if (IsValid(DragonAudioComponent) && IsValid(DragonRoarSound))
    {
        // Delay de 1s para o rugido de desativação
        FTimerHandle DeactivationRoarTimer;
        GetWorld()->GetTimerManager().SetTimer(DeactivationRoarTimer, [this]()
        {
            if (IsValid(DragonAudioComponent) && IsValid(DragonRoarSound))
            {
                DragonAudioComponent->SetSound(DragonRoarSound);
                DragonAudioComponent->SetVolumeMultiplier(0.7f); // Volume mais baixo
                DragonAudioComponent->SetPitchMultiplier(0.8f);  // Pitch mais grave e calmo
                DragonAudioComponent->Play();
                
                // Resetar parâmetros após o som
                FTimerHandle ResetTimer;
                GetWorld()->GetTimerManager().SetTimer(ResetTimer, [this]()
                {
                    if (IsValid(DragonAudioComponent))
                    {
                        DragonAudioComponent->SetVolumeMultiplier(1.0f);
                        DragonAudioComponent->SetPitchMultiplier(1.0f);
                    }
                }, 3.0f, false);
                
                UE_LOG(LogDragonPrismal, Log, TEXT("Dragon deactivation roar played"));
            }
        }, 1.0f, false);
    }

    UE_LOG(LogDragonPrismal, Log, TEXT("Fúria Crescente effects deactivation sequence initiated - All systems fading out"));
}

// Implementações das funções faltantes
void ADragonPrismalManager::ExitCombatState()
{
    DragonData.bIsInCombat = false;
    DragonData.CurrentState = EDragonState::Patrolling;

    UE_LOG(LogDragonPrismal, Log, TEXT("Dragon exited combat state"));
}

EDragonAttackType ADragonPrismalManager::DetermineNextAttack()
{
    // Lógica simples para determinar próximo ataque
    int32 RandomAttack = FMath::RandRange(0, 2);

    switch (RandomAttack)
    {
        case 0: return EDragonAttackType::SoproPrismal;
        case 1: return EDragonAttackType::Cristalizacao;
        case 2: return EDragonAttackType::AreaDamage;
        default: return EDragonAttackType::SoproPrismal;
    }
}

void ADragonPrismalManager::ExecuteSoproPrismalAttack()
{
    PerformSoproPrismal();
}

void ADragonPrismalManager::ExecuteCristalizacaoAttack()
{
    // Encontrar alvos próximos e aplicar cristalização
    TArray<AActor*> NearbyTargets;
    UKismetSystemLibrary::SphereOverlapActors(
        GetWorld(),
        GetActorLocation(),
        800.0f,
        TArray<TEnumAsByte<EObjectTypeQuery>>(),
        ACharacter::StaticClass(),
        TArray<AActor*>(),
        NearbyTargets
    );

    for (AActor* Target : NearbyTargets)
    {
        if (ACharacter* Character = Cast<ACharacter>(Target))
        {
            ApplyCrystallizationEffect(Character);
        }
    }
}

void ADragonPrismalManager::ExecuteAreaDamageAttack()
{
    // Ataque de área ao redor do dragão
    TArray<AActor*> NearbyTargets;
    UKismetSystemLibrary::SphereOverlapActors(
        GetWorld(),
        GetActorLocation(),
        1200.0f,
        TArray<TEnumAsByte<EObjectTypeQuery>>(),
        ACharacter::StaticClass(),
        TArray<AActor*>(),
        NearbyTargets
    );

    for (AActor* Target : NearbyTargets)
    {
        if (ACharacter* Character = Cast<ACharacter>(Target))
        {
            ApplyDamageToTarget(Character, 600.0f, EDamageType::Magical);
        }
    }
}

// Função duplicada removida - usar a implementação anterior

// Implementações das funções de área de combate
void ADragonPrismalManager::OnCombatAreaEnter(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    if (ACharacter* Character = Cast<ACharacter>(OtherActor))
    {
        DragonData.bIsInCombat = true;
        DragonData.CurrentState = EDragonState::Combat;

        UE_LOG(LogDragonPrismal, Log, TEXT("Character entered combat area: %s"), *Character->GetName());
    }
}

void ADragonPrismalManager::OnCombatAreaExit(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
    if (ACharacter* Character = Cast<ACharacter>(OtherActor))
    {
        UE_LOG(LogDragonPrismal, Log, TEXT("Character exited combat area: %s"), *Character->GetName());
    }
}

void ADragonPrismalManager::OnDetectionAreaEnter(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    if (ACharacter* Character = Cast<ACharacter>(OtherActor))
    {
        UE_LOG(LogDragonPrismal, Log, TEXT("Character entered detection area: %s"), *Character->GetName());
    }
}

void ADragonPrismalManager::OnDetectionAreaExit(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
    if (ACharacter* Character = Cast<ACharacter>(OtherActor))
    {
        UE_LOG(LogDragonPrismal, Log, TEXT("Character exited detection area: %s"), *Character->GetName());
    }
}

void ADragonPrismalManager::OnRewardAreaEnter(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    if (ACharacter* Character = Cast<ACharacter>(OtherActor))
    {
        UE_LOG(LogDragonPrismal, Log, TEXT("Character entered reward area: %s"), *Character->GetName());
    }
}

void ADragonPrismalManager::OnRewardAreaExit(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
    if (ACharacter* Character = Cast<ACharacter>(OtherActor))
    {
        UE_LOG(LogDragonPrismal, Log, TEXT("Character exited reward area: %s"), *Character->GetName());
    }
}