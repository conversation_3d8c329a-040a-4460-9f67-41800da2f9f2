// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AMapManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAMapManager() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_ABaronAuracronManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_ADragonPrismalManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_ALaneManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AMapManager();
AURA_API UClass* Z_Construct_UClass_AMapManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AMinionWaveManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_ARiverPrismalManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AWallCollisionManager_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EMapGenerationPhase();
AURA_API UEnum* Z_Construct_UEnum_Aura_EMapSize();
AURA_API UEnum* Z_Construct_UEnum_Aura_EMapValidationLevel();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FMapConfiguration();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FMapGenerationProgress();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FMapValidationResult();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EMapGenerationPhase *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMapGenerationPhase;
static UEnum* EMapGenerationPhase_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMapGenerationPhase.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMapGenerationPhase.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EMapGenerationPhase, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EMapGenerationPhase"));
	}
	return Z_Registration_Info_UEnum_EMapGenerationPhase.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EMapGenerationPhase>()
{
	return EMapGenerationPhase_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EMapGenerationPhase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "BuildingWalls.DisplayName", "Building Walls" },
		{ "BuildingWalls.Name", "EMapGenerationPhase::BuildingWalls" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums para configura\xc3\xa7\xc3\xa3o do mapa\n" },
#endif
		{ "Complete.DisplayName", "Complete" },
		{ "Complete.Name", "EMapGenerationPhase::Complete" },
		{ "CreatingLanes.DisplayName", "Creating Lanes" },
		{ "CreatingLanes.Name", "EMapGenerationPhase::CreatingLanes" },
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EMapGenerationPhase::Error" },
		{ "GeneratingRiver.DisplayName", "Generating River" },
		{ "GeneratingRiver.Name", "EMapGenerationPhase::GeneratingRiver" },
		{ "GeneratingTerrain.DisplayName", "Generating Terrain" },
		{ "GeneratingTerrain.Name", "EMapGenerationPhase::GeneratingTerrain" },
		{ "Initializing.DisplayName", "Initializing" },
		{ "Initializing.Name", "EMapGenerationPhase::Initializing" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EMapGenerationPhase::None" },
		{ "PlacingObjectives.DisplayName", "Placing Objectives" },
		{ "PlacingObjectives.Name", "EMapGenerationPhase::PlacingObjectives" },
		{ "SpawningMinions.DisplayName", "Spawning Minions" },
		{ "SpawningMinions.Name", "EMapGenerationPhase::SpawningMinions" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums para configura\xc3\xa7\xc3\xa3o do mapa" },
#endif
		{ "Validating.DisplayName", "Validating" },
		{ "Validating.Name", "EMapGenerationPhase::Validating" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMapGenerationPhase::None", (int64)EMapGenerationPhase::None },
		{ "EMapGenerationPhase::Initializing", (int64)EMapGenerationPhase::Initializing },
		{ "EMapGenerationPhase::GeneratingTerrain", (int64)EMapGenerationPhase::GeneratingTerrain },
		{ "EMapGenerationPhase::CreatingLanes", (int64)EMapGenerationPhase::CreatingLanes },
		{ "EMapGenerationPhase::PlacingObjectives", (int64)EMapGenerationPhase::PlacingObjectives },
		{ "EMapGenerationPhase::BuildingWalls", (int64)EMapGenerationPhase::BuildingWalls },
		{ "EMapGenerationPhase::GeneratingRiver", (int64)EMapGenerationPhase::GeneratingRiver },
		{ "EMapGenerationPhase::SpawningMinions", (int64)EMapGenerationPhase::SpawningMinions },
		{ "EMapGenerationPhase::Validating", (int64)EMapGenerationPhase::Validating },
		{ "EMapGenerationPhase::Complete", (int64)EMapGenerationPhase::Complete },
		{ "EMapGenerationPhase::Error", (int64)EMapGenerationPhase::Error },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EMapGenerationPhase_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EMapGenerationPhase",
	"EMapGenerationPhase",
	Z_Construct_UEnum_Aura_EMapGenerationPhase_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EMapGenerationPhase_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EMapGenerationPhase_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EMapGenerationPhase_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EMapGenerationPhase()
{
	if (!Z_Registration_Info_UEnum_EMapGenerationPhase.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMapGenerationPhase.InnerSingleton, Z_Construct_UEnum_Aura_EMapGenerationPhase_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMapGenerationPhase.InnerSingleton;
}
// ********** End Enum EMapGenerationPhase *********************************************************

// ********** Begin Enum EMapSize ******************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMapSize;
static UEnum* EMapSize_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMapSize.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMapSize.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EMapSize, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EMapSize"));
	}
	return Z_Registration_Info_UEnum_EMapSize.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EMapSize>()
{
	return EMapSize_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EMapSize_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EMapSize::Custom" },
		{ "Large.DisplayName", "Large (20000x20000)" },
		{ "Large.Name", "EMapSize::Large" },
		{ "Medium.DisplayName", "Medium (15000x15000)" },
		{ "Medium.Name", "EMapSize::Medium" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
		{ "Small.DisplayName", "Small (10000x10000)" },
		{ "Small.Name", "EMapSize::Small" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMapSize::Small", (int64)EMapSize::Small },
		{ "EMapSize::Medium", (int64)EMapSize::Medium },
		{ "EMapSize::Large", (int64)EMapSize::Large },
		{ "EMapSize::Custom", (int64)EMapSize::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EMapSize_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EMapSize",
	"EMapSize",
	Z_Construct_UEnum_Aura_EMapSize_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EMapSize_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EMapSize_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EMapSize_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EMapSize()
{
	if (!Z_Registration_Info_UEnum_EMapSize.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMapSize.InnerSingleton, Z_Construct_UEnum_Aura_EMapSize_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMapSize.InnerSingleton;
}
// ********** End Enum EMapSize ********************************************************************

// ********** Begin Enum EMapValidationLevel *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMapValidationLevel;
static UEnum* EMapValidationLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMapValidationLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMapValidationLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EMapValidationLevel, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EMapValidationLevel"));
	}
	return Z_Registration_Info_UEnum_EMapValidationLevel.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EMapValidationLevel>()
{
	return EMapValidationLevel_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EMapValidationLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Basic.DisplayName", "Basic" },
		{ "Basic.Name", "EMapValidationLevel::Basic" },
		{ "BlueprintType", "true" },
		{ "Comprehensive.DisplayName", "Comprehensive" },
		{ "Comprehensive.Name", "EMapValidationLevel::Comprehensive" },
		{ "Debug.DisplayName", "Debug" },
		{ "Debug.Name", "EMapValidationLevel::Debug" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
		{ "Standard.DisplayName", "Standard" },
		{ "Standard.Name", "EMapValidationLevel::Standard" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMapValidationLevel::Basic", (int64)EMapValidationLevel::Basic },
		{ "EMapValidationLevel::Standard", (int64)EMapValidationLevel::Standard },
		{ "EMapValidationLevel::Comprehensive", (int64)EMapValidationLevel::Comprehensive },
		{ "EMapValidationLevel::Debug", (int64)EMapValidationLevel::Debug },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EMapValidationLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EMapValidationLevel",
	"EMapValidationLevel",
	Z_Construct_UEnum_Aura_EMapValidationLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EMapValidationLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EMapValidationLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EMapValidationLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EMapValidationLevel()
{
	if (!Z_Registration_Info_UEnum_EMapValidationLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMapValidationLevel.InnerSingleton, Z_Construct_UEnum_Aura_EMapValidationLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMapValidationLevel.InnerSingleton;
}
// ********** End Enum EMapValidationLevel *********************************************************

// ********** Begin ScriptStruct FMapConfiguration *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMapConfiguration;
class UScriptStruct* FMapConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMapConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMapConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMapConfiguration, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("MapConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FMapConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMapConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estruturas de dados para configura\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AMapManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estruturas de dados para configura\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapSize_MetaData[] = {
		{ "Category", "Map Configuration" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomMapSize_MetaData[] = {
		{ "Category", "Map Configuration" },
		{ "EditCondition", "MapSize == EMapSize::Custom" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RandomSeed_MetaData[] = {
		{ "Category", "Map Configuration" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAsyncGeneration_MetaData[] = {
		{ "Category", "Map Configuration" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationLevel_MetaData[] = {
		{ "Category", "Map Configuration" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRealTimeValidation_MetaData[] = {
		{ "Category", "Map Configuration" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationTimeLimit_MetaData[] = {
		{ "Category", "Map Configuration" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapSize_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CustomMapSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RandomSeed;
	static void NewProp_bUseAsyncGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAsyncGeneration;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ValidationLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ValidationLevel;
	static void NewProp_bEnableRealTimeValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRealTimeValidation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationTimeLimit;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMapConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_MapSize_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_MapSize = { "MapSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapConfiguration, MapSize), Z_Construct_UEnum_Aura_EMapSize, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapSize_MetaData), NewProp_MapSize_MetaData) }; // 1167419830
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_CustomMapSize = { "CustomMapSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapConfiguration, CustomMapSize), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomMapSize_MetaData), NewProp_CustomMapSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_RandomSeed = { "RandomSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapConfiguration, RandomSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RandomSeed_MetaData), NewProp_RandomSeed_MetaData) };
void Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_bUseAsyncGeneration_SetBit(void* Obj)
{
	((FMapConfiguration*)Obj)->bUseAsyncGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_bUseAsyncGeneration = { "bUseAsyncGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMapConfiguration), &Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_bUseAsyncGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAsyncGeneration_MetaData), NewProp_bUseAsyncGeneration_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_ValidationLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_ValidationLevel = { "ValidationLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapConfiguration, ValidationLevel), Z_Construct_UEnum_Aura_EMapValidationLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationLevel_MetaData), NewProp_ValidationLevel_MetaData) }; // 714687143
void Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_bEnableRealTimeValidation_SetBit(void* Obj)
{
	((FMapConfiguration*)Obj)->bEnableRealTimeValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_bEnableRealTimeValidation = { "bEnableRealTimeValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMapConfiguration), &Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_bEnableRealTimeValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRealTimeValidation_MetaData), NewProp_bEnableRealTimeValidation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_GenerationTimeLimit = { "GenerationTimeLimit", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapConfiguration, GenerationTimeLimit), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationTimeLimit_MetaData), NewProp_GenerationTimeLimit_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FMapConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_MapSize_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_MapSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_CustomMapSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_RandomSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_bUseAsyncGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_ValidationLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_ValidationLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_bEnableRealTimeValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewProp_GenerationTimeLimit,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMapConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMapConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"MapConfiguration",
	Z_Construct_UScriptStruct_FMapConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMapConfiguration_Statics::PropPointers),
	sizeof(FMapConfiguration),
	alignof(FMapConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMapConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMapConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMapConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FMapConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMapConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FMapConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMapConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FMapConfiguration ***************************************************

// ********** Begin ScriptStruct FMapGenerationProgress ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMapGenerationProgress;
class UScriptStruct* FMapGenerationProgress::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMapGenerationProgress.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMapGenerationProgress.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMapGenerationProgress, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("MapGenerationProgress"));
	}
	return Z_Registration_Info_UScriptStruct_FMapGenerationProgress.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMapGenerationProgress_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPhase_MetaData[] = {
		{ "Category", "Generation Progress" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverallProgress_MetaData[] = {
		{ "Category", "Generation Progress" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseProgress_MetaData[] = {
		{ "Category", "Generation Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 0.0 a 1.0\n" },
#endif
		{ "ModuleRelativePath", "Public/AMapManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "0.0 a 1.0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTask_MetaData[] = {
		{ "Category", "Generation Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 0.0 a 1.0\n" },
#endif
		{ "ModuleRelativePath", "Public/AMapManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "0.0 a 1.0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElapsedTime_MetaData[] = {
		{ "Category", "Generation Progress" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasErrors_MetaData[] = {
		{ "Category", "Generation Progress" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessages_MetaData[] = {
		{ "Category", "Generation Progress" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OverallProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhaseProgress;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentTask;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ElapsedTime;
	static void NewProp_bHasErrors_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasErrors;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ErrorMessages;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMapGenerationProgress>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_CurrentPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_CurrentPhase = { "CurrentPhase", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapGenerationProgress, CurrentPhase), Z_Construct_UEnum_Aura_EMapGenerationPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPhase_MetaData), NewProp_CurrentPhase_MetaData) }; // 1653464265
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_OverallProgress = { "OverallProgress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapGenerationProgress, OverallProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverallProgress_MetaData), NewProp_OverallProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_PhaseProgress = { "PhaseProgress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapGenerationProgress, PhaseProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseProgress_MetaData), NewProp_PhaseProgress_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_CurrentTask = { "CurrentTask", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapGenerationProgress, CurrentTask), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTask_MetaData), NewProp_CurrentTask_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_ElapsedTime = { "ElapsedTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapGenerationProgress, ElapsedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElapsedTime_MetaData), NewProp_ElapsedTime_MetaData) };
void Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_bHasErrors_SetBit(void* Obj)
{
	((FMapGenerationProgress*)Obj)->bHasErrors = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_bHasErrors = { "bHasErrors", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMapGenerationProgress), &Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_bHasErrors_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasErrors_MetaData), NewProp_bHasErrors_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_ErrorMessages_Inner = { "ErrorMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_ErrorMessages = { "ErrorMessages", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapGenerationProgress, ErrorMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessages_MetaData), NewProp_ErrorMessages_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_CurrentPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_CurrentPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_OverallProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_PhaseProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_CurrentTask,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_ElapsedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_bHasErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_ErrorMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewProp_ErrorMessages,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"MapGenerationProgress",
	Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::PropPointers),
	sizeof(FMapGenerationProgress),
	alignof(FMapGenerationProgress),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMapGenerationProgress()
{
	if (!Z_Registration_Info_UScriptStruct_FMapGenerationProgress.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMapGenerationProgress.InnerSingleton, Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMapGenerationProgress.InnerSingleton;
}
// ********** End ScriptStruct FMapGenerationProgress **********************************************

// ********** Begin ScriptStruct FMapValidationResult **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMapValidationResult;
class UScriptStruct* FMapValidationResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMapValidationResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMapValidationResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMapValidationResult, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("MapValidationResult"));
	}
	return Z_Registration_Info_UScriptStruct_FMapValidationResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMapValidationResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValid_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationScore_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationErrors_MetaData[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 0.0 a 1.0\n" },
#endif
		{ "ModuleRelativePath", "Public/AMapManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "0.0 a 1.0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationWarnings_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentScores_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationTime_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValid;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValidationScore;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationErrors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValidationErrors;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationWarnings_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValidationWarnings;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ComponentScores_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ComponentScores_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ComponentScores;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValidationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMapValidationResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_bIsValid_SetBit(void* Obj)
{
	((FMapValidationResult*)Obj)->bIsValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_bIsValid = { "bIsValid", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMapValidationResult), &Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_bIsValid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValid_MetaData), NewProp_bIsValid_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ValidationScore = { "ValidationScore", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapValidationResult, ValidationScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationScore_MetaData), NewProp_ValidationScore_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ValidationErrors_Inner = { "ValidationErrors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ValidationErrors = { "ValidationErrors", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapValidationResult, ValidationErrors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationErrors_MetaData), NewProp_ValidationErrors_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ValidationWarnings_Inner = { "ValidationWarnings", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ValidationWarnings = { "ValidationWarnings", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapValidationResult, ValidationWarnings), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationWarnings_MetaData), NewProp_ValidationWarnings_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ComponentScores_ValueProp = { "ComponentScores", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ComponentScores_Key_KeyProp = { "ComponentScores_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ComponentScores = { "ComponentScores", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapValidationResult, ComponentScores), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentScores_MetaData), NewProp_ComponentScores_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ValidationTime = { "ValidationTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMapValidationResult, ValidationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationTime_MetaData), NewProp_ValidationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FMapValidationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_bIsValid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ValidationScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ValidationErrors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ValidationErrors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ValidationWarnings_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ValidationWarnings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ComponentScores_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ComponentScores_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ComponentScores,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewProp_ValidationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMapValidationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMapValidationResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"MapValidationResult",
	Z_Construct_UScriptStruct_FMapValidationResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMapValidationResult_Statics::PropPointers),
	sizeof(FMapValidationResult),
	alignof(FMapValidationResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMapValidationResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMapValidationResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMapValidationResult()
{
	if (!Z_Registration_Info_UScriptStruct_FMapValidationResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMapValidationResult.InnerSingleton, Z_Construct_UScriptStruct_FMapValidationResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMapValidationResult.InnerSingleton;
}
// ********** End ScriptStruct FMapValidationResult ************************************************

// ********** Begin Delegate FOnMapGenerationPhaseChanged ******************************************
struct Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnMapGenerationPhaseChanged_Parms
	{
		EMapGenerationPhase NewPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegates para eventos de gera\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AMapManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates para eventos de gera\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase = { "NewPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnMapGenerationPhaseChanged_Parms, NewPhase), Z_Construct_UEnum_Aura_EMapGenerationPhase, METADATA_PARAMS(0, nullptr) }; // 1653464265
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnMapGenerationPhaseChanged__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::_Script_Aura_eventOnMapGenerationPhaseChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::_Script_Aura_eventOnMapGenerationPhaseChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnMapGenerationPhaseChanged_DelegateWrapper(const FMulticastScriptDelegate& OnMapGenerationPhaseChanged, EMapGenerationPhase NewPhase)
{
	struct _Script_Aura_eventOnMapGenerationPhaseChanged_Parms
	{
		EMapGenerationPhase NewPhase;
	};
	_Script_Aura_eventOnMapGenerationPhaseChanged_Parms Parms;
	Parms.NewPhase=NewPhase;
	OnMapGenerationPhaseChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMapGenerationPhaseChanged ********************************************

// ********** Begin Delegate FOnMapGenerationProgress **********************************************
struct Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnMapGenerationProgress_Parms
	{
		FMapGenerationProgress Progress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Progress_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnMapGenerationProgress_Parms, Progress), Z_Construct_UScriptStruct_FMapGenerationProgress, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Progress_MetaData), NewProp_Progress_MetaData) }; // 1510933566
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature_Statics::NewProp_Progress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnMapGenerationProgress__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature_Statics::_Script_Aura_eventOnMapGenerationProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature_Statics::_Script_Aura_eventOnMapGenerationProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnMapGenerationProgress_DelegateWrapper(const FMulticastScriptDelegate& OnMapGenerationProgress, FMapGenerationProgress const& Progress)
{
	struct _Script_Aura_eventOnMapGenerationProgress_Parms
	{
		FMapGenerationProgress Progress;
	};
	_Script_Aura_eventOnMapGenerationProgress_Parms Parms;
	Parms.Progress=Progress;
	OnMapGenerationProgress.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMapGenerationProgress ************************************************

// ********** Begin Delegate FOnMapGenerationComplete **********************************************
struct Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnMapGenerationComplete_Parms
	{
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((_Script_Aura_eventOnMapGenerationComplete_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_Aura_eventOnMapGenerationComplete_Parms), &Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnMapGenerationComplete__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::_Script_Aura_eventOnMapGenerationComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::_Script_Aura_eventOnMapGenerationComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnMapGenerationComplete_DelegateWrapper(const FMulticastScriptDelegate& OnMapGenerationComplete, bool bSuccess)
{
	struct _Script_Aura_eventOnMapGenerationComplete_Parms
	{
		bool bSuccess;
	};
	_Script_Aura_eventOnMapGenerationComplete_Parms Parms;
	Parms.bSuccess=bSuccess ? true : false;
	OnMapGenerationComplete.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMapGenerationComplete ************************************************

// ********** Begin Delegate FOnMapValidationComplete **********************************************
struct Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnMapValidationComplete_Parms
	{
		FMapValidationResult ValidationResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ValidationResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature_Statics::NewProp_ValidationResult = { "ValidationResult", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnMapValidationComplete_Parms, ValidationResult), Z_Construct_UScriptStruct_FMapValidationResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationResult_MetaData), NewProp_ValidationResult_MetaData) }; // 3451245038
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature_Statics::NewProp_ValidationResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnMapValidationComplete__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature_Statics::_Script_Aura_eventOnMapValidationComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature_Statics::_Script_Aura_eventOnMapValidationComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnMapValidationComplete_DelegateWrapper(const FMulticastScriptDelegate& OnMapValidationComplete, FMapValidationResult const& ValidationResult)
{
	struct _Script_Aura_eventOnMapValidationComplete_Parms
	{
		FMapValidationResult ValidationResult;
	};
	_Script_Aura_eventOnMapValidationComplete_Parms Parms;
	Parms.ValidationResult=ValidationResult;
	OnMapValidationComplete.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMapValidationComplete ************************************************

// ********** Begin Delegate FOnMapGenerationError *************************************************
struct Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnMapGenerationError_Parms
	{
		FString ErrorMessage;
		EMapGenerationPhase Phase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Phase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Phase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnMapGenerationError_Parms, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::NewProp_Phase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::NewProp_Phase = { "Phase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnMapGenerationError_Parms, Phase), Z_Construct_UEnum_Aura_EMapGenerationPhase, METADATA_PARAMS(0, nullptr) }; // 1653464265
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::NewProp_Phase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::NewProp_Phase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnMapGenerationError__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::_Script_Aura_eventOnMapGenerationError_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::_Script_Aura_eventOnMapGenerationError_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnMapGenerationError_DelegateWrapper(const FMulticastScriptDelegate& OnMapGenerationError, const FString& ErrorMessage, EMapGenerationPhase Phase)
{
	struct _Script_Aura_eventOnMapGenerationError_Parms
	{
		FString ErrorMessage;
		EMapGenerationPhase Phase;
	};
	_Script_Aura_eventOnMapGenerationError_Parms Parms;
	Parms.ErrorMessage=ErrorMessage;
	Parms.Phase=Phase;
	OnMapGenerationError.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMapGenerationError ***************************************************

// ********** Begin Class AMapManager Function ClearMap ********************************************
struct Z_Construct_UFunction_AMapManager_ClearMap_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Limpa todos os elementos do mapa\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AMapManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpa todos os elementos do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_ClearMap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "ClearMap", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_ClearMap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_ClearMap_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMapManager_ClearMap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_ClearMap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execClearMap)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearMap();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function ClearMap **********************************************

// ********** Begin Class AMapManager Function GetBaronManager *************************************
struct Z_Construct_UFunction_AMapManager_GetBaronManager_Statics
{
	struct MapManager_eventGetBaronManager_Parms
	{
		ABaronAuracronManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Managers" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMapManager_GetBaronManager_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MapManager_eventGetBaronManager_Parms, ReturnValue), Z_Construct_UClass_ABaronAuracronManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_GetBaronManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_GetBaronManager_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetBaronManager_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_GetBaronManager_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "GetBaronManager", Z_Construct_UFunction_AMapManager_GetBaronManager_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetBaronManager_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_GetBaronManager_Statics::MapManager_eventGetBaronManager_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetBaronManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_GetBaronManager_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_GetBaronManager_Statics::MapManager_eventGetBaronManager_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_GetBaronManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_GetBaronManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execGetBaronManager)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ABaronAuracronManager**)Z_Param__Result=P_THIS->GetBaronManager();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function GetBaronManager ***************************************

// ********** Begin Class AMapManager Function GetCurrentPhase *************************************
struct Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics
{
	struct MapManager_eventGetCurrentPhase_Parms
	{
		EMapGenerationPhase ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Status" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MapManager_eventGetCurrentPhase_Parms, ReturnValue), Z_Construct_UEnum_Aura_EMapGenerationPhase, METADATA_PARAMS(0, nullptr) }; // 1653464265
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "GetCurrentPhase", Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::MapManager_eventGetCurrentPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::MapManager_eventGetCurrentPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_GetCurrentPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_GetCurrentPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execGetCurrentPhase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EMapGenerationPhase*)Z_Param__Result=P_THIS->GetCurrentPhase();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function GetCurrentPhase ***************************************

// ********** Begin Class AMapManager Function GetDragonManager ************************************
struct Z_Construct_UFunction_AMapManager_GetDragonManager_Statics
{
	struct MapManager_eventGetDragonManager_Parms
	{
		ADragonPrismalManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Managers" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMapManager_GetDragonManager_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MapManager_eventGetDragonManager_Parms, ReturnValue), Z_Construct_UClass_ADragonPrismalManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_GetDragonManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_GetDragonManager_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetDragonManager_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_GetDragonManager_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "GetDragonManager", Z_Construct_UFunction_AMapManager_GetDragonManager_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetDragonManager_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_GetDragonManager_Statics::MapManager_eventGetDragonManager_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetDragonManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_GetDragonManager_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_GetDragonManager_Statics::MapManager_eventGetDragonManager_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_GetDragonManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_GetDragonManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execGetDragonManager)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ADragonPrismalManager**)Z_Param__Result=P_THIS->GetDragonManager();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function GetDragonManager **************************************

// ********** Begin Class AMapManager Function GetGenerationProgress *******************************
struct Z_Construct_UFunction_AMapManager_GetGenerationProgress_Statics
{
	struct MapManager_eventGetGenerationProgress_Parms
	{
		FMapGenerationProgress ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Status" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMapManager_GetGenerationProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MapManager_eventGetGenerationProgress_Parms, ReturnValue), Z_Construct_UScriptStruct_FMapGenerationProgress, METADATA_PARAMS(0, nullptr) }; // 1510933566
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_GetGenerationProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_GetGenerationProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetGenerationProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_GetGenerationProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "GetGenerationProgress", Z_Construct_UFunction_AMapManager_GetGenerationProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetGenerationProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_GetGenerationProgress_Statics::MapManager_eventGetGenerationProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetGenerationProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_GetGenerationProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_GetGenerationProgress_Statics::MapManager_eventGetGenerationProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_GetGenerationProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_GetGenerationProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execGetGenerationProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FMapGenerationProgress*)Z_Param__Result=P_THIS->GetGenerationProgress();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function GetGenerationProgress *********************************

// ********** Begin Class AMapManager Function GetLaneManager **************************************
struct Z_Construct_UFunction_AMapManager_GetLaneManager_Statics
{
	struct MapManager_eventGetLaneManager_Parms
	{
		ALaneManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Managers" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMapManager_GetLaneManager_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MapManager_eventGetLaneManager_Parms, ReturnValue), Z_Construct_UClass_ALaneManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_GetLaneManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_GetLaneManager_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetLaneManager_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_GetLaneManager_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "GetLaneManager", Z_Construct_UFunction_AMapManager_GetLaneManager_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetLaneManager_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_GetLaneManager_Statics::MapManager_eventGetLaneManager_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetLaneManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_GetLaneManager_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_GetLaneManager_Statics::MapManager_eventGetLaneManager_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_GetLaneManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_GetLaneManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execGetLaneManager)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ALaneManager**)Z_Param__Result=P_THIS->GetLaneManager();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function GetLaneManager ****************************************

// ********** Begin Class AMapManager Function GetLastValidationResult *****************************
struct Z_Construct_UFunction_AMapManager_GetLastValidationResult_Statics
{
	struct MapManager_eventGetLastValidationResult_Parms
	{
		FMapValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Status" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMapManager_GetLastValidationResult_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MapManager_eventGetLastValidationResult_Parms, ReturnValue), Z_Construct_UScriptStruct_FMapValidationResult, METADATA_PARAMS(0, nullptr) }; // 3451245038
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_GetLastValidationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_GetLastValidationResult_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetLastValidationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_GetLastValidationResult_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "GetLastValidationResult", Z_Construct_UFunction_AMapManager_GetLastValidationResult_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetLastValidationResult_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_GetLastValidationResult_Statics::MapManager_eventGetLastValidationResult_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetLastValidationResult_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_GetLastValidationResult_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_GetLastValidationResult_Statics::MapManager_eventGetLastValidationResult_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_GetLastValidationResult()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_GetLastValidationResult_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execGetLastValidationResult)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FMapValidationResult*)Z_Param__Result=P_THIS->GetLastValidationResult();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function GetLastValidationResult *******************************

// ********** Begin Class AMapManager Function GetMapStatistics ************************************
struct Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics
{
	struct MapManager_eventGetMapStatistics_Parms
	{
		TMap<FString,FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obt\xc3\xa9m estat\xc3\xadsticas detalhadas do mapa\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AMapManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obt\xc3\xa9m estat\xc3\xadsticas detalhadas do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MapManager_eventGetMapStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "GetMapStatistics", Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::MapManager_eventGetMapStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::MapManager_eventGetMapStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_GetMapStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_GetMapStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execGetMapStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,FString>*)Z_Param__Result=P_THIS->GetMapStatistics();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function GetMapStatistics **************************************

// ********** Begin Class AMapManager Function GetMinionManager ************************************
struct Z_Construct_UFunction_AMapManager_GetMinionManager_Statics
{
	struct MapManager_eventGetMinionManager_Parms
	{
		AMinionWaveManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Managers" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMapManager_GetMinionManager_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MapManager_eventGetMinionManager_Parms, ReturnValue), Z_Construct_UClass_AMinionWaveManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_GetMinionManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_GetMinionManager_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetMinionManager_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_GetMinionManager_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "GetMinionManager", Z_Construct_UFunction_AMapManager_GetMinionManager_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetMinionManager_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_GetMinionManager_Statics::MapManager_eventGetMinionManager_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetMinionManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_GetMinionManager_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_GetMinionManager_Statics::MapManager_eventGetMinionManager_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_GetMinionManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_GetMinionManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execGetMinionManager)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AMinionWaveManager**)Z_Param__Result=P_THIS->GetMinionManager();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function GetMinionManager **************************************

// ********** Begin Class AMapManager Function GetRiverManager *************************************
struct Z_Construct_UFunction_AMapManager_GetRiverManager_Statics
{
	struct MapManager_eventGetRiverManager_Parms
	{
		ARiverPrismalManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Managers" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMapManager_GetRiverManager_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MapManager_eventGetRiverManager_Parms, ReturnValue), Z_Construct_UClass_ARiverPrismalManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_GetRiverManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_GetRiverManager_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetRiverManager_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_GetRiverManager_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "GetRiverManager", Z_Construct_UFunction_AMapManager_GetRiverManager_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetRiverManager_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_GetRiverManager_Statics::MapManager_eventGetRiverManager_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetRiverManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_GetRiverManager_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_GetRiverManager_Statics::MapManager_eventGetRiverManager_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_GetRiverManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_GetRiverManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execGetRiverManager)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARiverPrismalManager**)Z_Param__Result=P_THIS->GetRiverManager();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function GetRiverManager ***************************************

// ********** Begin Class AMapManager Function GetWallManager **************************************
struct Z_Construct_UFunction_AMapManager_GetWallManager_Statics
{
	struct MapManager_eventGetWallManager_Parms
	{
		AWallCollisionManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Managers" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMapManager_GetWallManager_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MapManager_eventGetWallManager_Parms, ReturnValue), Z_Construct_UClass_AWallCollisionManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_GetWallManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_GetWallManager_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetWallManager_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_GetWallManager_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "GetWallManager", Z_Construct_UFunction_AMapManager_GetWallManager_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetWallManager_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_GetWallManager_Statics::MapManager_eventGetWallManager_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_GetWallManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_GetWallManager_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_GetWallManager_Statics::MapManager_eventGetWallManager_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_GetWallManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_GetWallManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execGetWallManager)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AWallCollisionManager**)Z_Param__Result=P_THIS->GetWallManager();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function GetWallManager ****************************************

// ********** Begin Class AMapManager Function IsGenerating ****************************************
struct Z_Construct_UFunction_AMapManager_IsGenerating_Statics
{
	struct MapManager_eventIsGenerating_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Status" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AMapManager_IsGenerating_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MapManager_eventIsGenerating_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMapManager_IsGenerating_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MapManager_eventIsGenerating_Parms), &Z_Construct_UFunction_AMapManager_IsGenerating_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_IsGenerating_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_IsGenerating_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_IsGenerating_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_IsGenerating_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "IsGenerating", Z_Construct_UFunction_AMapManager_IsGenerating_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_IsGenerating_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_IsGenerating_Statics::MapManager_eventIsGenerating_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_IsGenerating_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_IsGenerating_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_IsGenerating_Statics::MapManager_eventIsGenerating_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_IsGenerating()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_IsGenerating_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execIsGenerating)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsGenerating();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function IsGenerating ******************************************

// ********** Begin Class AMapManager Function IsValidating ****************************************
struct Z_Construct_UFunction_AMapManager_IsValidating_Statics
{
	struct MapManager_eventIsValidating_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Status" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AMapManager_IsValidating_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MapManager_eventIsValidating_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMapManager_IsValidating_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MapManager_eventIsValidating_Parms), &Z_Construct_UFunction_AMapManager_IsValidating_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_IsValidating_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_IsValidating_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_IsValidating_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_IsValidating_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "IsValidating", Z_Construct_UFunction_AMapManager_IsValidating_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_IsValidating_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_IsValidating_Statics::MapManager_eventIsValidating_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_IsValidating_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_IsValidating_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_IsValidating_Statics::MapManager_eventIsValidating_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_IsValidating()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_IsValidating_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execIsValidating)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsValidating();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function IsValidating ******************************************

// ********** Begin Class AMapManager Function RestartMapGeneration ********************************
struct Z_Construct_UFunction_AMapManager_RestartMapGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reinicia a gera\xc3\xa7\xc3\xa3o do mapa do zero\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AMapManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reinicia a gera\xc3\xa7\xc3\xa3o do mapa do zero" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_RestartMapGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "RestartMapGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_RestartMapGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_RestartMapGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMapManager_RestartMapGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_RestartMapGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execRestartMapGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RestartMapGeneration();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function RestartMapGeneration **********************************

// ********** Begin Class AMapManager Function SetDebugMode ****************************************
struct Z_Construct_UFunction_AMapManager_SetDebugMode_Statics
{
	struct MapManager_eventSetDebugMode_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativa/desativa modo debug\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AMapManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativa/desativa modo debug" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((MapManager_eventSetDebugMode_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MapManager_eventSetDebugMode_Parms), &Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "SetDebugMode", Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::MapManager_eventSetDebugMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::MapManager_eventSetDebugMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_SetDebugMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_SetDebugMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execSetDebugMode)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDebugMode(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class AMapManager Function SetDebugMode ******************************************

// ********** Begin Class AMapManager Function StartMapGeneration **********************************
struct Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics
{
	struct MapManager_eventStartMapGeneration_Parms
	{
		FMapConfiguration Config;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Inicia a gera\xc3\xa7\xc3\xa3o completa do mapa\n     * @param Config Configura\xc3\xa7\xc3\xa3o para a gera\xc3\xa7\xc3\xa3o\n     * @return true se a gera\xc3\xa7\xc3\xa3o foi iniciada com sucesso\n     */" },
#endif
		{ "CPP_Default_Config", "()" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicia a gera\xc3\xa7\xc3\xa3o completa do mapa\n@param Config Configura\xc3\xa7\xc3\xa3o para a gera\xc3\xa7\xc3\xa3o\n@return true se a gera\xc3\xa7\xc3\xa3o foi iniciada com sucesso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MapManager_eventStartMapGeneration_Parms, Config), Z_Construct_UScriptStruct_FMapConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 1381086787
void Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MapManager_eventStartMapGeneration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MapManager_eventStartMapGeneration_Parms), &Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "StartMapGeneration", Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::MapManager_eventStartMapGeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::MapManager_eventStartMapGeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_StartMapGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_StartMapGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execStartMapGeneration)
{
	P_GET_STRUCT_REF(FMapConfiguration,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartMapGeneration(Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class AMapManager Function StartMapGeneration ************************************

// ********** Begin Class AMapManager Function StopMapGeneration ***********************************
struct Z_Construct_UFunction_AMapManager_StopMapGeneration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Para a gera\xc3\xa7\xc3\xa3o do mapa em andamento\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AMapManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Para a gera\xc3\xa7\xc3\xa3o do mapa em andamento" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_StopMapGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "StopMapGeneration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_StopMapGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_StopMapGeneration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMapManager_StopMapGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_StopMapGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execStopMapGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopMapGeneration();
	P_NATIVE_END;
}
// ********** End Class AMapManager Function StopMapGeneration *************************************

// ********** Begin Class AMapManager Function ValidateMap *****************************************
struct Z_Construct_UFunction_AMapManager_ValidateMap_Statics
{
	struct MapManager_eventValidateMap_Parms
	{
		EMapValidationLevel ValidationLevel;
		FMapValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida a integridade do mapa atual\n     * @param ValidationLevel N\xc3\xadvel de valida\xc3\xa7\xc3\xa3o a ser executado\n     * @return Resultado da valida\xc3\xa7\xc3\xa3o\n     */" },
#endif
		{ "CPP_Default_ValidationLevel", "Standard" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida a integridade do mapa atual\n@param ValidationLevel N\xc3\xadvel de valida\xc3\xa7\xc3\xa3o a ser executado\n@return Resultado da valida\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ValidationLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ValidationLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMapManager_ValidateMap_Statics::NewProp_ValidationLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMapManager_ValidateMap_Statics::NewProp_ValidationLevel = { "ValidationLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MapManager_eventValidateMap_Parms, ValidationLevel), Z_Construct_UEnum_Aura_EMapValidationLevel, METADATA_PARAMS(0, nullptr) }; // 714687143
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMapManager_ValidateMap_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MapManager_eventValidateMap_Parms, ReturnValue), Z_Construct_UScriptStruct_FMapValidationResult, METADATA_PARAMS(0, nullptr) }; // 3451245038
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMapManager_ValidateMap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_ValidateMap_Statics::NewProp_ValidationLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_ValidateMap_Statics::NewProp_ValidationLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMapManager_ValidateMap_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_ValidateMap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMapManager_ValidateMap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMapManager, nullptr, "ValidateMap", Z_Construct_UFunction_AMapManager_ValidateMap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_ValidateMap_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMapManager_ValidateMap_Statics::MapManager_eventValidateMap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMapManager_ValidateMap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMapManager_ValidateMap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMapManager_ValidateMap_Statics::MapManager_eventValidateMap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMapManager_ValidateMap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMapManager_ValidateMap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMapManager::execValidateMap)
{
	P_GET_ENUM(EMapValidationLevel,Z_Param_ValidationLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FMapValidationResult*)Z_Param__Result=P_THIS->ValidateMap(EMapValidationLevel(Z_Param_ValidationLevel));
	P_NATIVE_END;
}
// ********** End Class AMapManager Function ValidateMap *******************************************

// ********** Begin Class AMapManager **************************************************************
void AMapManager::StaticRegisterNativesAMapManager()
{
	UClass* Class = AMapManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearMap", &AMapManager::execClearMap },
		{ "GetBaronManager", &AMapManager::execGetBaronManager },
		{ "GetCurrentPhase", &AMapManager::execGetCurrentPhase },
		{ "GetDragonManager", &AMapManager::execGetDragonManager },
		{ "GetGenerationProgress", &AMapManager::execGetGenerationProgress },
		{ "GetLaneManager", &AMapManager::execGetLaneManager },
		{ "GetLastValidationResult", &AMapManager::execGetLastValidationResult },
		{ "GetMapStatistics", &AMapManager::execGetMapStatistics },
		{ "GetMinionManager", &AMapManager::execGetMinionManager },
		{ "GetRiverManager", &AMapManager::execGetRiverManager },
		{ "GetWallManager", &AMapManager::execGetWallManager },
		{ "IsGenerating", &AMapManager::execIsGenerating },
		{ "IsValidating", &AMapManager::execIsValidating },
		{ "RestartMapGeneration", &AMapManager::execRestartMapGeneration },
		{ "SetDebugMode", &AMapManager::execSetDebugMode },
		{ "StartMapGeneration", &AMapManager::execStartMapGeneration },
		{ "StopMapGeneration", &AMapManager::execStopMapGeneration },
		{ "ValidateMap", &AMapManager::execValidateMap },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AMapManager;
UClass* AMapManager::GetPrivateStaticClass()
{
	using TClass = AMapManager;
	if (!Z_Registration_Info_UClass_AMapManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("MapManager"),
			Z_Registration_Info_UClass_AMapManager.InnerSingleton,
			StaticRegisterNativesAMapManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AMapManager.InnerSingleton;
}
UClass* Z_Construct_UClass_AMapManager_NoRegister()
{
	return AMapManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AMapManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * AMapManager - Controlador principal do sistema de gera\xc3\xa7\xc3\xa3o procedural do mapa\n * \n * Este manager coordena todos os outros managers espec\xc3\xad""ficos para criar\n * um mapa completo de forma procedural e autom\xc3\xa1tica.\n * \n * Responsabilidades:\n * - Coordenar a gera\xc3\xa7\xc3\xa3o sequencial de todos os componentes do mapa\n * - Gerenciar o progresso e status da gera\xc3\xa7\xc3\xa3o\n * - Validar a integridade geom\xc3\xa9trica do mapa\n * - Fornecer interface para configura\xc3\xa7\xc3\xa3o e controle\n * - Otimizar performance durante a gera\xc3\xa7\xc3\xa3o\n */" },
#endif
		{ "IncludePath", "AMapManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AMapManager - Controlador principal do sistema de gera\xc3\xa7\xc3\xa3o procedural do mapa\n\nEste manager coordena todos os outros managers espec\xc3\xad""ficos para criar\num mapa completo de forma procedural e autom\xc3\xa1tica.\n\nResponsabilidades:\n- Coordenar a gera\xc3\xa7\xc3\xa3o sequencial de todos os componentes do mapa\n- Gerenciar o progresso e status da gera\xc3\xa7\xc3\xa3o\n- Validar a integridade geom\xc3\xa9trica do mapa\n- Fornecer interface para configura\xc3\xa7\xc3\xa3o e controle\n- Otimizar performance durante a gera\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapConfig_MetaData[] = {
		{ "Category", "Map Configuration" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGenerateOnBeginPlay_MetaData[] = {
		{ "Category", "Map Configuration" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDebugInfo_MetaData[] = {
		{ "Category", "Map Configuration" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaronManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DragonManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinionManager_MetaData[] = {
		{ "Category", "Managers" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationProgress_MetaData[] = {
		{ "Category", "Generation Status" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastValidationResult_MetaData[] = {
		{ "Category", "Generation Status" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsGenerating_MetaData[] = {
		{ "Category", "Generation Status" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValidating_MetaData[] = {
		{ "Category", "Generation Status" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMapGenerationPhaseChanged_MetaData[] = {
		{ "Category", "Map Events" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMapGenerationProgress_MetaData[] = {
		{ "Category", "Map Events" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMapGenerationComplete_MetaData[] = {
		{ "Category", "Map Events" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMapValidationComplete_MetaData[] = {
		{ "Category", "Map Events" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMapGenerationError_MetaData[] = {
		{ "Category", "Map Events" },
		{ "ModuleRelativePath", "Public/AMapManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MapConfig;
	static void NewProp_bAutoGenerateOnBeginPlay_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGenerateOnBeginPlay;
	static void NewProp_bShowDebugInfo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDebugInfo;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LaneManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BaronManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DragonManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WallManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RiverManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MinionManager;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationProgress;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastValidationResult;
	static void NewProp_bIsGenerating_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsGenerating;
	static void NewProp_bIsValidating_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValidating;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMapGenerationPhaseChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMapGenerationProgress;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMapGenerationComplete;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMapValidationComplete;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMapGenerationError;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AMapManager_ClearMap, "ClearMap" }, // 3287385347
		{ &Z_Construct_UFunction_AMapManager_GetBaronManager, "GetBaronManager" }, // 2446276306
		{ &Z_Construct_UFunction_AMapManager_GetCurrentPhase, "GetCurrentPhase" }, // 3712263029
		{ &Z_Construct_UFunction_AMapManager_GetDragonManager, "GetDragonManager" }, // 412753315
		{ &Z_Construct_UFunction_AMapManager_GetGenerationProgress, "GetGenerationProgress" }, // 1582758573
		{ &Z_Construct_UFunction_AMapManager_GetLaneManager, "GetLaneManager" }, // 3861419395
		{ &Z_Construct_UFunction_AMapManager_GetLastValidationResult, "GetLastValidationResult" }, // 1993190062
		{ &Z_Construct_UFunction_AMapManager_GetMapStatistics, "GetMapStatistics" }, // 848601979
		{ &Z_Construct_UFunction_AMapManager_GetMinionManager, "GetMinionManager" }, // 2558635298
		{ &Z_Construct_UFunction_AMapManager_GetRiverManager, "GetRiverManager" }, // 2333988202
		{ &Z_Construct_UFunction_AMapManager_GetWallManager, "GetWallManager" }, // 1028529085
		{ &Z_Construct_UFunction_AMapManager_IsGenerating, "IsGenerating" }, // 3764032169
		{ &Z_Construct_UFunction_AMapManager_IsValidating, "IsValidating" }, // 1842795458
		{ &Z_Construct_UFunction_AMapManager_RestartMapGeneration, "RestartMapGeneration" }, // 4147143089
		{ &Z_Construct_UFunction_AMapManager_SetDebugMode, "SetDebugMode" }, // 3673834901
		{ &Z_Construct_UFunction_AMapManager_StartMapGeneration, "StartMapGeneration" }, // 2872226335
		{ &Z_Construct_UFunction_AMapManager_StopMapGeneration, "StopMapGeneration" }, // 2777419154
		{ &Z_Construct_UFunction_AMapManager_ValidateMap, "ValidateMap" }, // 330057936
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AMapManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_MapConfig = { "MapConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, MapConfig), Z_Construct_UScriptStruct_FMapConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapConfig_MetaData), NewProp_MapConfig_MetaData) }; // 1381086787
void Z_Construct_UClass_AMapManager_Statics::NewProp_bAutoGenerateOnBeginPlay_SetBit(void* Obj)
{
	((AMapManager*)Obj)->bAutoGenerateOnBeginPlay = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_bAutoGenerateOnBeginPlay = { "bAutoGenerateOnBeginPlay", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMapManager), &Z_Construct_UClass_AMapManager_Statics::NewProp_bAutoGenerateOnBeginPlay_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGenerateOnBeginPlay_MetaData), NewProp_bAutoGenerateOnBeginPlay_MetaData) };
void Z_Construct_UClass_AMapManager_Statics::NewProp_bShowDebugInfo_SetBit(void* Obj)
{
	((AMapManager*)Obj)->bShowDebugInfo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_bShowDebugInfo = { "bShowDebugInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMapManager), &Z_Construct_UClass_AMapManager_Statics::NewProp_bShowDebugInfo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDebugInfo_MetaData), NewProp_bShowDebugInfo_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_LaneManager = { "LaneManager", nullptr, (EPropertyFlags)0x0124080000020015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, LaneManager), Z_Construct_UClass_ALaneManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneManager_MetaData), NewProp_LaneManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_BaronManager = { "BaronManager", nullptr, (EPropertyFlags)0x0124080000020015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, BaronManager), Z_Construct_UClass_ABaronAuracronManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaronManager_MetaData), NewProp_BaronManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_DragonManager = { "DragonManager", nullptr, (EPropertyFlags)0x0124080000020015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, DragonManager), Z_Construct_UClass_ADragonPrismalManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DragonManager_MetaData), NewProp_DragonManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_WallManager = { "WallManager", nullptr, (EPropertyFlags)0x0124080000020015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, WallManager), Z_Construct_UClass_AWallCollisionManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallManager_MetaData), NewProp_WallManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_RiverManager = { "RiverManager", nullptr, (EPropertyFlags)0x0124080000020015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, RiverManager), Z_Construct_UClass_ARiverPrismalManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverManager_MetaData), NewProp_RiverManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_MinionManager = { "MinionManager", nullptr, (EPropertyFlags)0x0124080000020015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, MinionManager), Z_Construct_UClass_AMinionWaveManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinionManager_MetaData), NewProp_MinionManager_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_GenerationProgress = { "GenerationProgress", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, GenerationProgress), Z_Construct_UScriptStruct_FMapGenerationProgress, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationProgress_MetaData), NewProp_GenerationProgress_MetaData) }; // 1510933566
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_LastValidationResult = { "LastValidationResult", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, LastValidationResult), Z_Construct_UScriptStruct_FMapValidationResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastValidationResult_MetaData), NewProp_LastValidationResult_MetaData) }; // 3451245038
void Z_Construct_UClass_AMapManager_Statics::NewProp_bIsGenerating_SetBit(void* Obj)
{
	((AMapManager*)Obj)->bIsGenerating = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_bIsGenerating = { "bIsGenerating", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMapManager), &Z_Construct_UClass_AMapManager_Statics::NewProp_bIsGenerating_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsGenerating_MetaData), NewProp_bIsGenerating_MetaData) };
void Z_Construct_UClass_AMapManager_Statics::NewProp_bIsValidating_SetBit(void* Obj)
{
	((AMapManager*)Obj)->bIsValidating = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_bIsValidating = { "bIsValidating", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMapManager), &Z_Construct_UClass_AMapManager_Statics::NewProp_bIsValidating_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValidating_MetaData), NewProp_bIsValidating_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_OnMapGenerationPhaseChanged = { "OnMapGenerationPhaseChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, OnMapGenerationPhaseChanged), Z_Construct_UDelegateFunction_Aura_OnMapGenerationPhaseChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMapGenerationPhaseChanged_MetaData), NewProp_OnMapGenerationPhaseChanged_MetaData) }; // 1832201990
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_OnMapGenerationProgress = { "OnMapGenerationProgress", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, OnMapGenerationProgress), Z_Construct_UDelegateFunction_Aura_OnMapGenerationProgress__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMapGenerationProgress_MetaData), NewProp_OnMapGenerationProgress_MetaData) }; // 116084011
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_OnMapGenerationComplete = { "OnMapGenerationComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, OnMapGenerationComplete), Z_Construct_UDelegateFunction_Aura_OnMapGenerationComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMapGenerationComplete_MetaData), NewProp_OnMapGenerationComplete_MetaData) }; // 803594044
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_OnMapValidationComplete = { "OnMapValidationComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, OnMapValidationComplete), Z_Construct_UDelegateFunction_Aura_OnMapValidationComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMapValidationComplete_MetaData), NewProp_OnMapValidationComplete_MetaData) }; // 412652605
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AMapManager_Statics::NewProp_OnMapGenerationError = { "OnMapGenerationError", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMapManager, OnMapGenerationError), Z_Construct_UDelegateFunction_Aura_OnMapGenerationError__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMapGenerationError_MetaData), NewProp_OnMapGenerationError_MetaData) }; // 3707139156
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AMapManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_MapConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_bAutoGenerateOnBeginPlay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_bShowDebugInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_LaneManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_BaronManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_DragonManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_WallManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_RiverManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_MinionManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_GenerationProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_LastValidationResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_bIsGenerating,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_bIsValidating,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_OnMapGenerationPhaseChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_OnMapGenerationProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_OnMapGenerationComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_OnMapValidationComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMapManager_Statics::NewProp_OnMapGenerationError,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AMapManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AMapManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AMapManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AMapManager_Statics::ClassParams = {
	&AMapManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AMapManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AMapManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AMapManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AMapManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AMapManager()
{
	if (!Z_Registration_Info_UClass_AMapManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AMapManager.OuterSingleton, Z_Construct_UClass_AMapManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AMapManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AMapManager);
AMapManager::~AMapManager() {}
// ********** End Class AMapManager ****************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMapManager_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EMapGenerationPhase_StaticEnum, TEXT("EMapGenerationPhase"), &Z_Registration_Info_UEnum_EMapGenerationPhase, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1653464265U) },
		{ EMapSize_StaticEnum, TEXT("EMapSize"), &Z_Registration_Info_UEnum_EMapSize, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1167419830U) },
		{ EMapValidationLevel_StaticEnum, TEXT("EMapValidationLevel"), &Z_Registration_Info_UEnum_EMapValidationLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 714687143U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FMapConfiguration::StaticStruct, Z_Construct_UScriptStruct_FMapConfiguration_Statics::NewStructOps, TEXT("MapConfiguration"), &Z_Registration_Info_UScriptStruct_FMapConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMapConfiguration), 1381086787U) },
		{ FMapGenerationProgress::StaticStruct, Z_Construct_UScriptStruct_FMapGenerationProgress_Statics::NewStructOps, TEXT("MapGenerationProgress"), &Z_Registration_Info_UScriptStruct_FMapGenerationProgress, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMapGenerationProgress), 1510933566U) },
		{ FMapValidationResult::StaticStruct, Z_Construct_UScriptStruct_FMapValidationResult_Statics::NewStructOps, TEXT("MapValidationResult"), &Z_Registration_Info_UScriptStruct_FMapValidationResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMapValidationResult), 3451245038U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AMapManager, AMapManager::StaticClass, TEXT("AMapManager"), &Z_Registration_Info_UClass_AMapManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AMapManager), 3397702023U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMapManager_h__Script_Aura_3293417450(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMapManager_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMapManager_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMapManager_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMapManager_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMapManager_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMapManager_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
