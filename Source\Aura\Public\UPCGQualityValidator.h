#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Components/ActorComponent.h"
// UE5.6 PCG API includes - using correct paths
#include "PCGComponent.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGSettings.h"
#include "Engine/StaticMesh.h"
#include "Materials/Material.h"
#include "Runtime/Landscape/Classes/Landscape.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Engine/TextureRenderTarget2D.h"
#include "RenderTargetPool.h"
#include "RHI.h"
#include "RHIResources.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "Containers/Queue.h"
#include "UPCGQualityValidator.generated.h"

// Forward Declarations
class APCGWorldPartitionManager;
class APCGNaniteOptimizer;
class APCGLumenIntegrator;
class APCGStreamingManager;
class UPCGPerformanceProfiler;
class APCGCacheManager;
class APCGChaosIntegrator;

// Enums
UENUM(BlueprintType)
enum class EPCGValidationSeverity : uint8
{
	Info UMETA(DisplayName = "Info"),
	Warning UMETA(DisplayName = "Warning"),
	Error UMETA(DisplayName = "Error"),
	Critical UMETA(DisplayName = "Critical")
};

UENUM(BlueprintType)
enum class EPCGValidationCategory : uint8
{
	Geometry UMETA(DisplayName = "Geometry"),
	Performance UMETA(DisplayName = "Performance"),
	Memory UMETA(DisplayName = "Memory"),
	Rendering UMETA(DisplayName = "Rendering"),
	Physics UMETA(DisplayName = "Physics"),
	Streaming UMETA(DisplayName = "Streaming"),
	Cache UMETA(DisplayName = "Cache"),
	Quality UMETA(DisplayName = "Quality"),
	Compliance UMETA(DisplayName = "Compliance")
};

UENUM(BlueprintType)
enum class EPCGValidationMode : uint8
{
	Manual UMETA(DisplayName = "Manual"),
	Automatic UMETA(DisplayName = "Automatic"),
	RealTime UMETA(DisplayName = "Real Time"),
	Scheduled UMETA(DisplayName = "Scheduled")
};

UENUM(BlueprintType)
enum class EPCGValidationResult : uint8
{
	Passed UMETA(DisplayName = "Passed"),
	Failed UMETA(DisplayName = "Failed"),
	Skipped UMETA(DisplayName = "Skipped"),
	Pending UMETA(DisplayName = "Pending")
};

UENUM(BlueprintType)
enum class EPCGQualityLevel : uint8
{
	Low UMETA(DisplayName = "Low"),
	Medium UMETA(DisplayName = "Medium"),
	High UMETA(DisplayName = "High"),
	Ultra UMETA(DisplayName = "Ultra"),
	Cinematic UMETA(DisplayName = "Cinematic")
};

// Structs
USTRUCT(BlueprintType)
struct AURA_API FPCGValidationConfig
{
	GENERATED_BODY()

	// Validation Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
	EPCGValidationMode ValidationMode = EPCGValidationMode::Automatic;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
	TArray<EPCGValidationCategory> EnabledCategories;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
	EPCGValidationSeverity MinimumSeverity = EPCGValidationSeverity::Warning;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
	EPCGQualityLevel TargetQualityLevel = EPCGQualityLevel::High;

	// Performance Thresholds
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
	float MaxFrameTime = 16.67f; // 60 FPS

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
	float MaxMemoryUsage = 2048.0f; // MB

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
	int32 MaxDrawCalls = 1000;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
	int32 MaxTriangles = 1000000;

	// Quality Thresholds
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
	float MinLODDistance = 100.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
	float MaxTexelDensity = 512.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
	bool bEnableNaniteValidation = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
	bool bEnableLumenValidation = true;

	// Automation Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Automation")
	float ValidationInterval = 5.0f; // seconds

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Automation")
	bool bAutoFixIssues = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Automation")
	bool bGenerateReports = true;

	FPCGValidationConfig()
	{
		EnabledCategories = {
			EPCGValidationCategory::Geometry,
			EPCGValidationCategory::Performance,
			EPCGValidationCategory::Quality
		};
	}
};

USTRUCT(BlueprintType)
struct AURA_API FPCGValidationIssue
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Issue")
	FString IssueID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Issue")
	EPCGValidationSeverity Severity = EPCGValidationSeverity::Warning;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Issue")
	EPCGValidationCategory Category = EPCGValidationCategory::Quality;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Issue")
	FString Title;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Issue")
	FString Description;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Issue")
	FString Recommendation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Issue")
	TWeakObjectPtr<UObject> AffectedObject;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Issue")
	FVector Location = FVector::ZeroVector;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Issue")
	FDateTime Timestamp;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Issue")
	bool bCanAutoFix = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Issue")
	bool bIsFixed = false;

	FPCGValidationIssue()
	{
		Timestamp = FDateTime::Now();
		IssueID = FGuid::NewGuid().ToString();
	}
};

USTRUCT(BlueprintType)
struct AURA_API FPCGValidationReport
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Report")
	FString ReportID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Report")
	FDateTime GenerationTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Report")
	float ValidationDuration = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Report")
	TArray<FPCGValidationIssue> Issues;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Report")
	int32 TotalChecks = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Report")
	int32 PassedChecks = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Report")
	int32 FailedChecks = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Report")
	int32 SkippedChecks = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Report")
	EPCGQualityLevel OverallQuality = EPCGQualityLevel::Medium;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Report")
	float QualityScore = 0.0f; // 0-100

	FPCGValidationReport()
	{
		ReportID = FGuid::NewGuid().ToString();
		GenerationTime = FDateTime::Now();
	}
};

USTRUCT(BlueprintType)
struct AURA_API FPCGQualityMetrics
{
	GENERATED_BODY()

	// Performance Metrics
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
	float FrameTime = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
	float MemoryUsage = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
	int32 DrawCalls = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
	int32 TriangleCount = 0;

	// Quality Metrics
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
	float LODCoverage = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
	float TextureQuality = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
	float GeometryComplexity = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
	float LightingQuality = 0.0f;

	// Streaming Metrics
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
	float StreamingEfficiency = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
	float CacheHitRate = 0.0f;

	FPCGQualityMetrics() = default;
};

// Delegates
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnValidationStarted, const FString&, ValidationID);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnValidationCompleted, const FString&, ValidationID, const FPCGValidationReport&, Report);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnIssueDetected, const FString&, ValidationID, const FPCGValidationIssue&, Issue);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnIssueFixed, const FString&, ValidationID, const FPCGValidationIssue&, Issue);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnQualityChanged, EPCGQualityLevel, OldLevel, EPCGQualityLevel, NewLevel);

/**
 * UPCGQualityValidator - Advanced quality validation system for PCG content
 * Provides automated quality assurance, performance validation, and compliance checking
 * Integrates with UE5.6 modern APIs for comprehensive content validation
 */
UCLASS(BlueprintType, Blueprintable, Category = "PCG|Quality")
class AURA_API UPCGQualityValidator : public UObject
{
	GENERATED_BODY()

public:
	UPCGQualityValidator();

	// Core Validation Functions
	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Validation")
	FString StartValidation(const FPCGValidationConfig& Config);

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Validation")
	bool StopValidation(const FString& ValidationID);

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Validation")
	FPCGValidationReport GetValidationReport(const FString& ValidationID);

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Validation")
	TArray<FPCGValidationReport> GetAllReports();

	// Issue Management
	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Issues")
	TArray<FPCGValidationIssue> GetIssuesByCategory(EPCGValidationCategory Category);

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Issues")
	TArray<FPCGValidationIssue> GetIssuesBySeverity(EPCGValidationSeverity Severity);

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Issues")
	bool FixIssue(const FString& IssueID);

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Issues")
	int32 FixAllAutoFixableIssues();

	// Quality Assessment
	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Assessment")
	EPCGQualityLevel AssessOverallQuality();

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Assessment")
	float CalculateQualityScore();

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Assessment")
	FPCGQualityMetrics GetCurrentMetrics();

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Assessment")
	bool MeetsQualityStandards(EPCGQualityLevel RequiredLevel);

	// Configuration
	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Config")
	void SetValidationConfig(const FPCGValidationConfig& NewConfig);

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Config")
	FPCGValidationConfig GetValidationConfig() const;

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Config")
	void EnableCategory(EPCGValidationCategory Category);

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Config")
	void DisableCategory(EPCGValidationCategory Category);

	// Automation
	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Automation")
	void StartAutomaticValidation();

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Automation")
	void StopAutomaticValidation();

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Automation")
	bool IsAutomaticValidationRunning() const;

	// Reporting
	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Reporting")
	bool ExportReport(const FString& ValidationID, const FString& FilePath);

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Reporting")
	FString GenerateHTMLReport(const FString& ValidationID);

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Reporting")
	FString GenerateJSONReport(const FString& ValidationID);

	// Integration with PCG Systems
	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Integration")
	void ValidatePCGComponent(UPCGComponent* Component);

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Integration")
	void ValidatePCGGraph(UPCGGraph* Graph);

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Integration")
	void ValidateWorldPartition();

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Integration")
	void ValidateNaniteContent();

	UFUNCTION(BlueprintCallable, Category = "PCG|Quality|Integration")
	void ValidateLumenSetup();

	// Delegates
	UPROPERTY(BlueprintAssignable, Category = "PCG|Quality|Events")
	FOnValidationStarted OnValidationStarted;

	UPROPERTY(BlueprintAssignable, Category = "PCG|Quality|Events")
	FOnValidationCompleted OnValidationCompleted;

	UPROPERTY(BlueprintAssignable, Category = "PCG|Quality|Events")
	FOnIssueDetected OnIssueDetected;

	UPROPERTY(BlueprintAssignable, Category = "PCG|Quality|Events")
	FOnIssueFixed OnIssueFixed;

	UPROPERTY(BlueprintAssignable, Category = "PCG|Quality|Events")
	FOnQualityChanged OnQualityChanged;

private:
	// Configuration
	UPROPERTY()
	FPCGValidationConfig ValidationConfig;

	// Active Validations
	UPROPERTY()
	TMap<FString, FPCGValidationReport> ActiveValidations;

	// Validation History
	UPROPERTY()
	TArray<FPCGValidationReport> ValidationHistory;

	// Current Issues
	UPROPERTY()
	TArray<FPCGValidationIssue> CurrentIssues;

	// Automation
	FTimerHandle AutoValidationTimer;
	bool bAutomaticValidationEnabled = false;

	// Integration References
	UPROPERTY()
	TWeakObjectPtr<APCGWorldPartitionManager> WorldPartitionManager;

	UPROPERTY()
	TWeakObjectPtr<APCGNaniteOptimizer> NaniteOptimizer;

	UPROPERTY()
	TWeakObjectPtr<APCGLumenIntegrator> LumenIntegrator;

	UPROPERTY()
	TWeakObjectPtr<APCGStreamingManager> StreamingManager;

	UPROPERTY()
	TWeakObjectPtr<UPCGPerformanceProfiler> PerformanceProfiler;

	UPROPERTY()
	TWeakObjectPtr<APCGCacheManager> CacheManager;

	UPROPERTY()
	TWeakObjectPtr<APCGChaosIntegrator> ChaosIntegrator;

	// Internal Validation Functions
	void PerformGeometryValidation(FPCGValidationReport& Report);
	void PerformPerformanceValidation(FPCGValidationReport& Report);
	void PerformMemoryValidation(FPCGValidationReport& Report);
	void PerformRenderingValidation(FPCGValidationReport& Report);
	void PerformPhysicsValidation(FPCGValidationReport& Report);
	void PerformStreamingValidation(FPCGValidationReport& Report);
	void PerformCacheValidation(FPCGValidationReport& Report);
	void PerformQualityValidation(FPCGValidationReport& Report);
	void PerformComplianceValidation(FPCGValidationReport& Report);

	// Issue Detection
	void DetectLODIssues(FPCGValidationReport& Report);
	void DetectPerformanceBottlenecks(FPCGValidationReport& Report);
	void DetectMemoryLeaks(FPCGValidationReport& Report);
	void DetectRenderingIssues(FPCGValidationReport& Report);
	void DetectPhysicsProblems(FPCGValidationReport& Report);
	void DetectStreamingIssues(FPCGValidationReport& Report);
	void DetectCacheProblems(FPCGValidationReport& Report);
	void DetectDegenerateGeometry(FPCGValidationReport& Report);
	void DetectMemoryFragmentation(FPCGValidationReport& Report);

	// Auto-Fix Functions
	bool AutoFixLODIssue(const FPCGValidationIssue& Issue);
	bool AutoFixPerformanceIssue(const FPCGValidationIssue& Issue);
	bool AutoFixMemoryIssue(const FPCGValidationIssue& Issue);
	bool AutoFixRenderingIssue(const FPCGValidationIssue& Issue);
	bool AutoFixStreamingIssue(const FPCGValidationIssue& Issue);
	bool AutoFixPhysicsIssue(const FPCGValidationIssue& Issue);

	// Additional Validation Functions
	void PerformValidation(const FString& ValidationID);
	void ValidateComponentGeometry(UPCGComponent* Component, FPCGValidationReport& Report);
	void ValidateComponentPerformance(UPCGComponent* Component, FPCGValidationReport& Report);
	void ValidateComponentSettings(UPCGComponent* Component, FPCGValidationReport& Report);
	void ValidateGraphComplexity(UPCGGraph* Graph, FPCGValidationReport& Report);
	void ValidateGraphConnections(UPCGGraph* Graph, FPCGValidationReport& Report);
	void ValidateGraphSettings(UPCGGraph* Graph, FPCGValidationReport& Report);
	void ValidateWorldPartitionConfiguration(FPCGValidationReport& Report);
	void ValidateWorldPartitionPerformance(FPCGValidationReport& Report);
	void ValidateNaniteConfiguration(FPCGValidationReport& Report);
	void ValidateNanitePerformance(FPCGValidationReport& Report);
	void ValidateLumenConfiguration(FPCGValidationReport& Report);
	void ValidateLumenPerformance(FPCGValidationReport& Report);
	void ValidateMeshComplexity(FPCGValidationReport& Report);
	void ValidateUVMapping(FPCGValidationReport& Report);
	void ValidateFrameRate(FPCGValidationReport& Report);
	void ValidateDrawCalls(FPCGValidationReport& Report);
	void ValidateTriangleCount(FPCGValidationReport& Report);
	void ValidateMemoryUsage(FPCGValidationReport& Report);
	void ValidateMaterialSetup(FPCGValidationReport& Report);
	void ValidateLightingSetup(FPCGValidationReport& Report);
	void ValidateShadowQuality(FPCGValidationReport& Report);
	void ValidateCollisionSetup(FPCGValidationReport& Report);
	void ValidatePhysicsPerformance(FPCGValidationReport& Report);
	void ValidateStreamingSetup(FPCGValidationReport& Report);
	void ValidateStreamingPerformance(FPCGValidationReport& Report);
	void ValidateLODConfiguration(FPCGValidationReport& Report);
	void ValidateCacheEfficiency(FPCGValidationReport& Report);
	void ValidateCacheMemoryUsage(FPCGValidationReport& Report);
	void ValidateQualityMetrics(FPCGValidationReport& Report);
	void ValidateVisualQuality(FPCGValidationReport& Report);
	void ValidateConsistency(FPCGValidationReport& Report);
	void ValidatePlatformCompliance(FPCGValidationReport& Report);
	void ValidateNamingConventions(FPCGValidationReport& Report);
	void ValidateAssetOrganization(FPCGValidationReport& Report);

	// Utility Functions
	float CalculateCategoryScore(EPCGValidationCategory Category);
	EPCGQualityLevel DetermineQualityLevel(float Score);
	void UpdateQualityMetrics();
	void CleanupOldReports();
	int32 CalculateNodeDepth(UPCGNode* Node);

	// Funções de validação adicionais
	void ValidateMeshComplexity();
	void ValidateTextureResolution();
	void ValidateShaderComplexity();
	void ValidateRenderingPerformance();
	void ValidatePhysicsSimulation();
	void ValidateStreamingPerformance();
	void ValidateCacheEfficiency();
	void ValidateQualitySettings();
	void ValidateComplianceStandards();
	
	// Funções de cálculo de qualidade
    UFUNCTION(BlueprintCallable, Category = "PCG Quality")
    float CalculateTextureQuality();
    
    UFUNCTION(BlueprintCallable, Category = "PCG Quality")
    float CalculateGeometryComplexity();
    
    UFUNCTION(BlueprintCallable, Category = "PCG Quality")
    float CalculateLightingQuality();
    
    UFUNCTION(BlueprintCallable, Category = "PCG Quality")
    float CalculateLODCoverage();

	// Timer Callbacks
	void OnAutomaticValidationTimer();

	// Integration Helpers
	void InitializeIntegrations();
	void ValidateIntegrationHealth();
};