// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "testes_precisao_geometrica.h"

#ifdef AURA_testes_precisao_geometrica_generated_h
#error "testes_precisao_geometrica.generated.h already included, missing '#pragma once' in testes_precisao_geometrica.h"
#endif
#define AURA_testes_precisao_geometrica_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FResultadoTeste ***************************************************
#define FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h_14_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FResultadoTeste_Statics; \
	static class UScriptStruct* StaticStruct();


struct FResultadoTeste;
// ********** End ScriptStruct FResultadoTeste *****************************************************

// ********** Begin ScriptStruct FRelatorioCompleto ************************************************
#define FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h_47_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRelatorioCompleto_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRelatorioCompleto;
// ********** End ScriptStruct FRelatorioCompleto **************************************************

// ********** Begin Class UTestePrecisaoGeometrica *************************************************
#define FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h_86_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execTestarAcessoCovils); \
	DECLARE_FUNCTION(execTestarFluxoMinions); \
	DECLARE_FUNCTION(execTestarConectividadeLanes); \
	DECLARE_FUNCTION(execTestarColisoesBordas); \
	DECLARE_FUNCTION(execTestarCalculosDistancia); \
	DECLARE_FUNCTION(execTestarCalculosArea); \
	DECLARE_FUNCTION(execTestarCalculosAngulares); \
	DECLARE_FUNCTION(execTestarToleranciasCoordenadas); \
	DECLARE_FUNCTION(execTestarSistemaMinions); \
	DECLARE_FUNCTION(execTestarSistemaParedes); \
	DECLARE_FUNCTION(execTestarDimensoesCovils); \
	DECLARE_FUNCTION(execTestarEspecificacoesBases); \
	DECLARE_FUNCTION(execTestarPosicionamentoTorres); \
	DECLARE_FUNCTION(execTestarGeometriaCirculos); \
	DECLARE_FUNCTION(execTestarGeometriaElipses); \
	DECLARE_FUNCTION(execTestarGeometriaHexagonos); \
	DECLARE_FUNCTION(execTestarFuncaoSenoidalRio); \
	DECLARE_FUNCTION(execTestarPrecisaoLanes); \
	DECLARE_FUNCTION(execGerarRelatorioCompleto); \
	DECLARE_FUNCTION(execExecutarTodosOsTestes);


AURA_API UClass* Z_Construct_UClass_UTestePrecisaoGeometrica_NoRegister();

#define FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h_86_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUTestePrecisaoGeometrica(); \
	friend struct Z_Construct_UClass_UTestePrecisaoGeometrica_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_UTestePrecisaoGeometrica_NoRegister(); \
public: \
	DECLARE_CLASS2(UTestePrecisaoGeometrica, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_UTestePrecisaoGeometrica_NoRegister) \
	DECLARE_SERIALIZER(UTestePrecisaoGeometrica)


#define FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h_86_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UTestePrecisaoGeometrica(UTestePrecisaoGeometrica&&) = delete; \
	UTestePrecisaoGeometrica(const UTestePrecisaoGeometrica&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UTestePrecisaoGeometrica); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UTestePrecisaoGeometrica); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UTestePrecisaoGeometrica) \
	NO_API virtual ~UTestePrecisaoGeometrica();


#define FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h_83_PROLOG
#define FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h_86_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h_86_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h_86_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h_86_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UTestePrecisaoGeometrica;

// ********** End Class UTestePrecisaoGeometrica ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_testes_precisao_geometrica_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
