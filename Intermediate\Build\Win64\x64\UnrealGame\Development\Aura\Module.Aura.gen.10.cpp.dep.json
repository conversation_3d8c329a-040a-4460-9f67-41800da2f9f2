{"Version": "1.2", "Data": {"Source": "c:\\aura\\intermediate\\build\\win64\\x64\\unrealgame\\development\\aura\\module.aura.gen.10.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\intermediate\\build\\win64\\x64\\aura\\development\\engine\\sharedpch.engine.project.rtti.exceptions.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\intermediate\\build\\win64\\x64\\unrealgame\\development\\aura\\definitions.aura.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\aminionwavemanager.gen.cpp", "c:\\aura\\source\\aura\\public\\aminionwavemanager.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\aminionwavemanager.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}