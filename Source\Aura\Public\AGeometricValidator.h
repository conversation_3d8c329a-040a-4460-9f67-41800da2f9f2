// AGeometricValidator.h - Sistema de validação matemática em tempo real
// Unreal Engine 5.6 - APIs modernas
// 1 UU = 1 cm (Unreal Units)

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SceneComponent.h"
#include "Math/UnrealMathUtility.h"
#include "HAL/ThreadSafeBool.h"
#include "Async/AsyncWork.h"
#include "TimerManager.h"
#include "DrawDebugHelpers.h"
#include "Containers/Queue.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Math/Transform.h"
#include "Math/Box.h"
#include "Math/Sphere.h"
#include "Math/Plane.h"
#include "Math/Ray.h"
#include "Misc/DateTime.h"

#include "AGeometricValidator.generated.h"

// Tolerâncias para validação geométrica (em UU - Unreal Units)
#define GEOMETRIC_TOLERANCE_DISTANCE 1.0f    // 1 cm
#define GEOMETRIC_TOLERANCE_ANGLE 0.1f       // 0.1 graus
#define GEOMETRIC_TOLERANCE_AREA 100.0f      // 100 cm²
#define GEOMETRIC_TOLERANCE_VOLUME 1000.0f   // 1000 cm³

// Enums para tipos de validação
UENUM(BlueprintType)
enum class EValidationType : uint8
{
    Distance UMETA(DisplayName = "Distance"),
    Angle UMETA(DisplayName = "Angle"),
    Area UMETA(DisplayName = "Area"),
    Volume UMETA(DisplayName = "Volume"),
    Intersection UMETA(DisplayName = "Intersection"),
    Containment UMETA(DisplayName = "Containment"),
    Symmetry UMETA(DisplayName = "Symmetry"),
    Parallelism UMETA(DisplayName = "Parallelism"),
    Perpendicularity UMETA(DisplayName = "Perpendicularity"),
    Collinearity UMETA(DisplayName = "Collinearity")
};

UENUM(BlueprintType)
enum class EValidationSeverity : uint8
{
    Info UMETA(DisplayName = "Info"),
    Warning UMETA(DisplayName = "Warning"),
    Error UMETA(DisplayName = "Error"),
    Critical UMETA(DisplayName = "Critical")
};

UENUM(BlueprintType)
enum class EGeometricShape : uint8
{
    Point UMETA(DisplayName = "Point"),
    Line UMETA(DisplayName = "Line"),
    Ray UMETA(DisplayName = "Ray"),
    Segment UMETA(DisplayName = "Segment"),
    Circle UMETA(DisplayName = "Circle"),
    Ellipse UMETA(DisplayName = "Ellipse"),
    Rectangle UMETA(DisplayName = "Rectangle"),
    Polygon UMETA(DisplayName = "Polygon"),
    Hexagon UMETA(DisplayName = "Hexagon"),
    Sphere UMETA(DisplayName = "Sphere"),
    Box UMETA(DisplayName = "Box"),
    Cylinder UMETA(DisplayName = "Cylinder"),
    Plane UMETA(DisplayName = "Plane")
};

// Estruturas de dados para validação
USTRUCT(BlueprintType)
struct FValidationResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    bool bIsValid = true;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    EValidationType ValidationType = EValidationType::Distance;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    EValidationSeverity Severity = EValidationSeverity::Info;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    FString Description = TEXT("");

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    FVector Location = FVector::ZeroVector;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    float ExpectedValue = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    float ActualValue = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    float Tolerance = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    float Deviation = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    FDateTime Timestamp = FDateTime::Now();

    FValidationResult()
    {
        bIsValid = true;
        ValidationType = EValidationType::Distance;
        Severity = EValidationSeverity::Info;
        Description = TEXT("");
        Location = FVector::ZeroVector;
        ExpectedValue = 0.0f;
        ActualValue = 0.0f;
        Tolerance = 0.0f;
        Deviation = 0.0f;
        Timestamp = FDateTime::Now();
    }
};

USTRUCT(BlueprintType)
struct FGeometricShape
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shape")
    EGeometricShape ShapeType = EGeometricShape::Point;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shape")
    FVector Center = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shape")
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shape")
    FVector Scale = FVector::OneVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shape")
    TArray<FVector> Points;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shape")
    float Radius = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shape")
    FVector2D Dimensions = FVector2D(100.0f, 100.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shape")
    FString ShapeID = TEXT("");

    FGeometricShape()
    {
        ShapeType = EGeometricShape::Point;
        Center = FVector::ZeroVector;
        Rotation = FRotator::ZeroRotator;
        Scale = FVector::OneVector;
        Points.Empty();
        Radius = 100.0f;
        Dimensions = FVector2D(100.0f, 100.0f);
        ShapeID = TEXT("");
    }
};

USTRUCT(BlueprintType)
struct FValidationConfig
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Config")
    bool bEnableRealTimeValidation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Config")
    float ValidationInterval = 0.1f; // segundos

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Config")
    float DistanceTolerance = GEOMETRIC_TOLERANCE_DISTANCE;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Config")
    float AngleTolerance = GEOMETRIC_TOLERANCE_ANGLE;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Config")
    float AreaTolerance = GEOMETRIC_TOLERANCE_AREA;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Config")
    float VolumeTolerance = GEOMETRIC_TOLERANCE_VOLUME;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Config")
    bool bShowDebugVisualization = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Config")
    bool bLogValidationResults = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Config")
    bool bShowDebugText = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Config")
    int32 MaxValidationHistory = 1000;

    FValidationConfig()
    {
        bEnableRealTimeValidation = true;
        ValidationInterval = 0.1f;
        DistanceTolerance = GEOMETRIC_TOLERANCE_DISTANCE;
        AngleTolerance = GEOMETRIC_TOLERANCE_ANGLE;
        AreaTolerance = GEOMETRIC_TOLERANCE_AREA;
        VolumeTolerance = GEOMETRIC_TOLERANCE_VOLUME;
        bShowDebugVisualization = false;
        bLogValidationResults = true;
        bShowDebugText = false;
        MaxValidationHistory = 1000;
    }
};

// Delegates para eventos de validação
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnValidationResult, const FValidationResult&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnValidationError, const FString&, ErrorMessage, EValidationSeverity, Severity);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnValidationComplete, const TArray<FValidationResult>&, Results);

/**
 * AGeometricValidator - Sistema de validação matemática em tempo real
 * 
 * Este sistema valida a precisão geométrica de todos os elementos do mapa,
 * garantindo que as dimensões, ângulos, distâncias e formas estejam corretas
 * de acordo com as especificações matemáticas.
 * 
 * Responsabilidades:
 * - Validar geometria de lanes (largura, comprimento, ângulos)
 * - Validar formas hexagonais e elípticas dos objetivos
 * - Validar geometria senoidal do rio
 * - Validar colisões e intersecções
 * - Validar pathfinding e waypoints
 * - Fornecer feedback em tempo real
 */
UCLASS(BlueprintType, Blueprintable)
class AURA_API AGeometricValidator : public AActor
{
    GENERATED_BODY()

public:
    AGeometricValidator();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
    virtual void Tick(float DeltaTime) override;

    // ===== COMPONENTES =====
protected:
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USceneComponent> RootSceneComponent;

    // ===== CONFIGURAÇÃO =====
public:
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation Config")
    FValidationConfig ValidationConfig;

    // ===== STATUS =====
protected:
    UPROPERTY(BlueprintReadOnly, Category = "Validation Status")
    bool bIsValidating = false;

    UPROPERTY(BlueprintReadOnly, Category = "Validation Status")
    TArray<FValidationResult> ValidationHistory;

    UPROPERTY(BlueprintReadOnly, Category = "Validation Status")
    TArray<FGeometricShape> RegisteredShapes;

    // ===== TIMERS =====
protected:
    FTimerHandle ValidationTimerHandle;
    FThreadSafeBool bStopValidation;

    // ===== DELEGATES =====
public:
    UPROPERTY(BlueprintAssignable, Category = "Validation Events")
    FOnValidationResult OnValidationResult;

    UPROPERTY(BlueprintAssignable, Category = "Validation Events")
    FOnValidationError OnValidationError;

    UPROPERTY(BlueprintAssignable, Category = "Validation Events")
    FOnValidationComplete OnValidationComplete;

    // ===== FUNÇÕES PRINCIPAIS =====
public:
    /**
     * Inicia a validação em tempo real
     */
    UFUNCTION(BlueprintCallable, Category = "Validation")
    void StartRealTimeValidation();

    /**
     * Para a validação em tempo real
     */
    UFUNCTION(BlueprintCallable, Category = "Validation")
    void StopRealTimeValidation();

    /**
     * Executa uma validação completa única
     */
    UFUNCTION(BlueprintCallable, Category = "Validation")
    TArray<FValidationResult> PerformFullValidation();

    /**
     * Registra uma forma geométrica para validação
     */
    UFUNCTION(BlueprintCallable, Category = "Validation")
    void RegisterShape(const FGeometricShape& Shape);

    /**
     * Remove uma forma geométrica da validação
     */
    UFUNCTION(BlueprintCallable, Category = "Validation")
    void UnregisterShape(const FString& ShapeID);

    /**
     * Limpa todas as formas registradas
     */
    UFUNCTION(BlueprintCallable, Category = "Validation")
    void ClearRegisteredShapes();

    // ===== VALIDAÇÕES ESPECÍFICAS =====
public:
    /**
     * Valida distância entre dois pontos
     */
    UFUNCTION(BlueprintCallable, Category = "Geometric Validation")
    FValidationResult ValidateDistance(const FVector& Point1, const FVector& Point2, float ExpectedDistance, float Tolerance = 1.0f);

    /**
     * Valida ângulo entre três pontos
     */
    UFUNCTION(BlueprintCallable, Category = "Geometric Validation")
    FValidationResult ValidateAngle(const FVector& Point1, const FVector& Vertex, const FVector& Point2, float ExpectedAngle, float Tolerance = 0.1f);

    /**
     * Valida área de um polígono
     */
    UFUNCTION(BlueprintCallable, Category = "Geometric Validation")
    FValidationResult ValidatePolygonArea(const TArray<FVector>& Points, float ExpectedArea, float Tolerance = 100.0f);

    /**
     * Valida se um hexágono é regular
     */
    UFUNCTION(BlueprintCallable, Category = "Geometric Validation")
    FValidationResult ValidateRegularHexagon(const TArray<FVector>& HexagonPoints, float ExpectedSideLength, float Tolerance = 1.0f);

    /**
     * Valida geometria elíptica
     */
    UFUNCTION(BlueprintCallable, Category = "Geometric Validation")
    FValidationResult ValidateEllipse(const FVector& Center, float SemiMajorAxis, float SemiMinorAxis, const TArray<FVector>& EllipsePoints, float Tolerance = 1.0f);

    /**
     * Valida curva senoidal
     */
    UFUNCTION(BlueprintCallable, Category = "Geometric Validation")
    FValidationResult ValidateSinusoidalCurve(const TArray<FVector>& CurvePoints, float Amplitude, float Frequency, float Phase, float Tolerance = 1.0f);

    /**
     * Valida paralelismo entre duas linhas
     */
    UFUNCTION(BlueprintCallable, Category = "Geometric Validation")
    FValidationResult ValidateParallelism(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, float Tolerance = 0.1f);

    /**
     * Valida perpendicularidade entre duas linhas
     */
    UFUNCTION(BlueprintCallable, Category = "Geometric Validation")
    FValidationResult ValidatePerpendicularity(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, float Tolerance = 0.1f);

    /**
     * Valida intersecção entre duas linhas
     */
    UFUNCTION(BlueprintCallable, Category = "Geometric Validation")
    FValidationResult ValidateLineIntersection(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, const FVector& ExpectedIntersection, float Tolerance = 1.0f);

    /**
     * Valida se um ponto está dentro de um polígono
     */
    UFUNCTION(BlueprintCallable, Category = "Geometric Validation")
    FValidationResult ValidatePointInPolygon(const FVector& Point, const TArray<FVector>& PolygonPoints, bool bExpectedInside);

    /**
     * Valida simetria de pontos em relação a um eixo
     */
    UFUNCTION(BlueprintCallable, Category = "Geometric Validation")
    FValidationResult ValidateSymmetry(const TArray<FVector>& Points, const FVector& SymmetryAxis, float Tolerance = 1.0f);

    // ===== VALIDAÇÕES DE LANE =====
public:
    /**
     * Valida geometria de uma lane
     */
    UFUNCTION(BlueprintCallable, Category = "Lane Validation")
    TArray<FValidationResult> ValidateLaneGeometry(const TArray<FVector>& LanePoints, float ExpectedWidth, float ExpectedLength);

    /**
     * Valida waypoints de uma lane
     */
    UFUNCTION(BlueprintCallable, Category = "Lane Validation")
    TArray<FValidationResult> ValidateLaneWaypoints(const TArray<FVector>& Waypoints, float ExpectedSpacing);

    /**
     * Valida posições das torres
     */
    UFUNCTION(BlueprintCallable, Category = "Lane Validation")
    TArray<FValidationResult> ValidateTowerPositions(const TArray<FVector>& TowerPositions, const TArray<FVector>& LanePoints, float MinDistanceFromLane);

    // ===== VALIDAÇÕES DE OBJETIVOS =====
public:
    /**
     * Valida área hexagonal do Barão
     */
    UFUNCTION(BlueprintCallable, Category = "Objective Validation")
    TArray<FValidationResult> ValidateBaronHexagonalArea(const FVector& Center, float Radius, const TArray<FVector>& HexagonPoints);

    /**
     * Valida área elíptica do Dragão
     */
    UFUNCTION(BlueprintCallable, Category = "Objective Validation")
    TArray<FValidationResult> ValidateDragonEllipticalArea(const FVector& Center, float SemiMajorAxis, float SemiMinorAxis, const TArray<FVector>& EllipsePoints);

    // ===== VALIDAÇÕES DE RIO =====
public:
    /**
     * Valida geometria senoidal do rio
     */
    UFUNCTION(BlueprintCallable, Category = "River Validation")
    TArray<FValidationResult> ValidateRiverGeometry(const TArray<FVector>& RiverPoints, float Amplitude, float Frequency, float Width);

    /**
     * Valida ilha hexagonal no rio
     */
    UFUNCTION(BlueprintCallable, Category = "River Validation")
    TArray<FValidationResult> ValidateRiverIsland(const FVector& IslandCenter, float IslandRadius, const TArray<FVector>& RiverPoints);

    // ===== GETTERS =====
public:
    UFUNCTION(BlueprintPure, Category = "Validation Status")
    bool IsValidating() const { return bIsValidating; }

    UFUNCTION(BlueprintPure, Category = "Validation Status")
    TArray<FValidationResult> GetValidationHistory() const { return ValidationHistory; }

    UFUNCTION(BlueprintPure, Category = "Validation Status")
    TArray<FGeometricShape> GetRegisteredShapes() const { return RegisteredShapes; }

    UFUNCTION(BlueprintPure, Category = "Validation Status")
    int32 GetValidationCount() const { return ValidationHistory.Num(); }

    UFUNCTION(BlueprintPure, Category = "Validation Status")
    int32 GetErrorCount() const;

    UFUNCTION(BlueprintPure, Category = "Validation Status")
    int32 GetWarningCount() const;

    /**
     * Exporta relatório de validação para arquivo
     */
    UFUNCTION(BlueprintCallable, Category = "Validation Report")
    bool ExportValidationReport(const FString& FilePath);

    /**
     * Limpa histórico de validações
     */
    UFUNCTION(BlueprintCallable, Category = "Validation Management")
    void ClearValidationHistory();

    /**
     * Define nova configuração de validação
     */
    UFUNCTION(BlueprintCallable, Category = "Validation Management")
    void SetValidationConfig(const FValidationConfig& NewConfig);

    /**
     * Obtém configuração atual de validação
     */
    UFUNCTION(BlueprintPure, Category = "Validation Management")
    FValidationConfig GetValidationConfig() const;

    /**
     * Obtém histórico de validações por tipo
     */
    UFUNCTION(BlueprintPure, Category = "Validation Status")
    TArray<FValidationResult> GetValidationHistoryByType(EValidationType Type) const;

    /**
     * Obtém histórico de validações por severidade
     */
    UFUNCTION(BlueprintPure, Category = "Validation Status")
    TArray<FValidationResult> GetValidationHistoryBySeverity(EValidationSeverity Severity) const;

    /**
     * Obtém forma registrada por ID
     */
    UFUNCTION(BlueprintPure, Category = "Validation Status")
    FGeometricShape GetRegisteredShape(const FString& ShapeID) const;

    /**
     * Obtém progresso da validação atual
     */
    UFUNCTION(BlueprintPure, Category = "Validation Status")
    float GetValidationProgress() const;

    /**
     * Executa benchmark de performance
     */
    UFUNCTION(BlueprintCallable, Category = "Debug")
    void RunPerformanceBenchmark(int32 NumIterations = 100);

    /**
     * Obtém resumo das validações
     */
    UFUNCTION(BlueprintPure, Category = "Validation Status")
    FString GetValidationSummary() const;

    /**
     * Reseta completamente o validador
     */
    UFUNCTION(BlueprintCallable, Category = "Validation Management")
    void ResetValidator();

    // ===== FUNÇÕES INTERNAS =====
protected:
    /**
     * Executa validação periódica
     */
    void PerformPeriodicValidation();

    /**
     * Adiciona resultado à história
     */
    void AddValidationResult(const FValidationResult& Result);

    /**
     * Limpa histórico antigo
     */
    void CleanupValidationHistory();

    /**
     * Cria resultado de validação
     */
    FValidationResult CreateValidationResult(EValidationType Type, bool bIsValid, const FString& Description, const FVector& Location, float Expected, float Actual, float Tolerance, EValidationSeverity Severity = EValidationSeverity::Info);

    // ===== FUNÇÕES MATEMÁTICAS =====
protected:
    /**
     * Calcula distância entre dois pontos
     */
    float CalculateDistance(const FVector& Point1, const FVector& Point2) const;

    /**
     * Calcula ângulo entre três pontos
     */
    float CalculateAngle(const FVector& Point1, const FVector& Vertex, const FVector& Point2) const;

    /**
     * Calcula área de um polígono
     */
    float CalculatePolygonArea(const TArray<FVector>& Points) const;

    /**
     * Verifica se um ponto está dentro de um polígono
     */
    bool IsPointInPolygon(const FVector& Point, const TArray<FVector>& PolygonPoints) const;

    /**
     * Calcula ponto de intersecção entre duas linhas
     */
    bool CalculateLineIntersection(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, FVector& OutIntersection) const;

    /**
     * Verifica se duas linhas são paralelas
     */
    bool AreParallel(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, float Tolerance) const;

    /**
     * Verifica se duas linhas são perpendiculares
     */
    bool ArePerpendicular(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, float Tolerance) const;

    /**
     * Gera pontos de um hexágono regular
     */
    TArray<FVector> GenerateRegularHexagonPoints(const FVector& Center, float Radius) const;

    /**
     * Gera pontos de uma elipse
     */
    TArray<FVector> GenerateEllipsePoints(const FVector& Center, float SemiMajorAxis, float SemiMinorAxis, int32 NumPoints = 32) const;

    /**
     * Gera pontos de uma curva senoidal
     */
    TArray<FVector> GenerateSinusoidalPoints(const FVector& Start, const FVector& End, float Amplitude, float Frequency, float Phase, int32 NumPoints = 64) const;

    // ===== DEBUG =====
protected:
    /**
     * Desenha visualização de debug
     */
    void DrawDebugVisualization() const;

    /**
     * Desenha forma geométrica
     */
    void DrawDebugShape(const FGeometricShape& Shape, const FColor& Color) const;

    /**
     * Desenha resultado de validação
     */
    void DrawDebugValidationResult(const FValidationResult& Result) const;

    /**
     * Log de validação
     */
    void LogValidationResult(const FValidationResult& Result) const;

public:
    /**
     * Ativa/desativa visualização de debug
     */
    UFUNCTION(BlueprintCallable, Category = "Debug")
    void SetDebugVisualization(bool bEnabled) { ValidationConfig.bShowDebugVisualization = bEnabled; }

    /**
     * Obtém estatísticas de validação
     */
    UFUNCTION(BlueprintCallable, Category = "Debug")
    TMap<FString, int32> GetValidationStatistics() const;
};