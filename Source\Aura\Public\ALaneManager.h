#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SplineComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Containers/Array.h"
#include "UObject/ObjectMacros.h"
#include "implementacao_automatizada.h"
#include "Stats/Stats.h"
#include "ALaneManager.generated.h"

// Declarar stats para profiling
DECLARE_CYCLE_STAT(TEXT("LaneManager FindPath"), STAT_LaneManager_FindPath, STATGROUP_Game);

// Forward declarations
class ARiverPrismalManager;

// Enums para tipos de lanes e torres
UENUM(BlueprintType)
enum class ELaneManagerType : uint8
{
    Superior    UMETA(DisplayName = "Lane Superior"),
    Central     UMETA(DisplayName = "Lane Central"),
    Inferior    UMETA(DisplayName = "Lane Inferior")
};

// Enums movidos para implementacao_automatizada.h para evitar duplicação

// Estruturas de dados para lanes e torres
USTRUCT(BlueprintType)
struct FLaneData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane")
    ELaneManagerType LaneType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane")
    float LaneWidth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane")
    TArray<FVector> Waypoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane")
    FVector StartPosition;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane")
    FVector EndPosition;

    FLaneData()
    {
        LaneType = ELaneManagerType::Central;
        LaneWidth = 400.0f;
        StartPosition = FVector::ZeroVector;
        EndPosition = FVector::ZeroVector;
    }
};

USTRUCT(BlueprintType)
struct FTowerData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    ETowerType TowerType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    float BaseRadius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    float TopRadius;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    float Height;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    float AttackRange;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    int32 Health;

    // Campos adicionais necessários para compilação
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    int32 MaxHealth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    int32 HP;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    int32 MaxHP;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    float AttackDamage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    float AttackSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    bool bIsDestroyed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    bool bIsActive;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    ETeam Team;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower")
    float Radius;

    FTowerData()
    {
        TowerType = ETowerType::Externa;
        Position = FVector::ZeroVector;
        BaseRadius = 120.0f;
        TopRadius = 80.0f;
        Height = 600.0f;
        AttackRange = 800.0f;
        Health = 2000;
        MaxHealth = 2000;
        HP = 2000;
        MaxHP = 2000;
        AttackDamage = 100.0f;
        AttackSpeed = 1.0f;
        bIsDestroyed = false;
        bIsActive = true;
        Team = ETeam::Neutro;
        Radius = 88.0f;
    }
};

// Estrutura para pathfinding A*
USTRUCT(BlueprintType)
struct FPathNode
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float GCost;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float HCost;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float FCost;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    int32 ParentIndex;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    FVector Parent;

    FPathNode()
    {
        Position = FVector::ZeroVector;
        GCost = 0.0f;
        HCost = 0.0f;
        FCost = 0.0f;
        ParentIndex = -1;
        Parent = FVector::ZeroVector;
    }
};

// Estrutura para cache de pathfinding
USTRUCT(BlueprintType)
struct FCachedPath
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    TArray<FVector> Path;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    FDateTime Timestamp;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float PathLength;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    float CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pathfinding")
    bool bIsValid;

    FCachedPath()
    {
        Path.Empty();
        CreationTime = 0.0f;
        bIsValid = false;
        Timestamp = FDateTime::Now();
        PathLength = 0.0f;
    }
};

// Delegates para sistema de eventos
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTowerDestroyed, const FTowerData&, DestroyedTower);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTowerSpawned, const FTowerData&, SpawnedTower, AActor*, TowerActor);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPathfindingCacheUpdated, int32, CacheSize, float, UpdateTime);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLaneInitialized, ELaneManagerType, LaneType);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWaypointReached, const FVector&, WaypointPosition, ELaneManagerType, LaneType);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRiverIntegrationStatusChanged, bool, bIsConnected);

UCLASS(BlueprintType, Blueprintable)
class AURA_API ALaneManager : public AActor
{
    GENERATED_BODY()

public:
    ALaneManager();

protected:
    virtual void BeginPlay() override;

public:
    virtual void Tick(float DeltaTime) override;

    // Componentes principais
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class USceneComponent* RootSceneComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class USplineComponent* SuperiorLaneSpline;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class USplineComponent* CentralLaneSpline;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class USplineComponent* InferiorLaneSpline;

    // Dados das lanes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane Configuration")
    TArray<FLaneData> LanesData;

    // Dados das torres
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower Configuration")
    TArray<FTowerData> TowersData;

    // Configurações matemáticas das lanes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane Mathematics")
    float MapSize = 16000.0f; // 160m x 160m em UU (1 UU = 1 cm)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lane Mathematics")
    float PlayableArea = 14400.0f; // 144m x 144m em UU

    // Funções públicas para geometria das lanes
    UFUNCTION(BlueprintCallable, Category = "Lane Mathematics")
    FVector CalculateSuperiorLanePosition(float X) const;

    UFUNCTION(BlueprintCallable, Category = "Lane Mathematics")
    FVector CalculateCentralLanePosition(float X) const;

    UFUNCTION(BlueprintCallable, Category = "Lane Mathematics")
    FVector CalculateInferiorLanePosition(float X) const;

    // Funções de inicialização
    UFUNCTION(BlueprintCallable, Category = "Lane Setup")
    bool InitializeLanes();

    UFUNCTION(BlueprintCallable, Category = "Lane Setup")
    bool InitializeTowers();

    UFUNCTION(BlueprintCallable, Category = "Lane Setup")
    bool GenerateWaypoints();

    // Funções de pathfinding A*
    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    TArray<FVector> FindPath(const FVector& StartPos, const FVector& EndPos, ELaneManagerType LaneType) const;

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    float CalculateHeuristic(const FVector& Start, const FVector& End) const;

    UFUNCTION(BlueprintCallable, Category = "Pathfinding")
    TArray<FVector> GetNearbyWaypoints(const FVector& Position, ELaneManagerType LaneType, float Radius = 1200.0f) const;

    // Funções de torres
    UFUNCTION(BlueprintCallable, Category = "Tower Management")
    void SpawnTower(const FTowerData& TowerData);

    UFUNCTION(BlueprintCallable, Category = "Tower Management")
    bool SpawnTowerAtPosition(const FVector& Position, ETowerType TowerType, ETeam Team);

    UFUNCTION(BlueprintCallable, Category = "Tower Management")
    TArray<AActor*> FindTargetsInRange(const FVector& TowerPosition, float Range) const;

    UFUNCTION(BlueprintCallable, Category = "Tower Management")
    AActor* GetBestTarget(const FVector& TowerPosition, const TArray<AActor*>& PotentialTargets) const;

    // Funções de validação geométrica
    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool ValidateLaneGeometry() const;

    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool IsPositionInLane(const FVector& Position, ELaneManagerType LaneType, float Tolerance = 1.0f) const;

    UFUNCTION(BlueprintCallable, Category = "Validation")
    ELaneManagerType GetClosestLane(const FVector& Position) const;

    // Funções de integração com rio
    UFUNCTION(BlueprintCallable, Category = "River Integration")
    bool IsPositionInRiver(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "River Integration")
    FVector GetBridgePosition(ELaneManagerType LaneType) const;

    UFUNCTION(BlueprintCallable, Category = "River Integration")
    float GetMovementSpeedModifier(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "River Integration")
    bool IsPositionInWater(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "River Integration")
    float GetWaterSpeedMultiplier(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "River Integration")
    FVector GetNearestBridgePosition(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "River Integration")
    TArray<FVector> FindWaterPath(const FVector& Start, const FVector& End) const;

    // Sistema de eventos
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTowerDestroyed OnTowerDestroyed;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTowerSpawned OnTowerSpawned;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPathfindingCacheUpdated OnPathfindingCacheUpdated;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLaneInitialized OnLaneInitialized;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnWaypointReached OnWaypointReached;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnRiverIntegrationStatusChanged OnRiverIntegrationStatusChanged;

    // Funções de eventos
    UFUNCTION(BlueprintCallable, Category = "Events")
    void BroadcastTowerDestroyed(const FTowerData& DestroyedTower);

    UFUNCTION(BlueprintCallable, Category = "Events")
    void BroadcastTowerSpawned(const FTowerData& SpawnedTower, AActor* TowerActor);

    UFUNCTION(BlueprintCallable, Category = "Events")
    void BroadcastPathfindingCacheUpdated(int32 CacheSize, float UpdateTime);

    UFUNCTION(BlueprintCallable, Category = "Events")
    void BroadcastLaneInitialized(ELaneManagerType LaneType);

    UFUNCTION(BlueprintCallable, Category = "Events")
    void BroadcastWaypointReached(const FVector& WaypointPosition, ELaneManagerType LaneType);

    UFUNCTION(BlueprintCallable, Category = "Events")
    void BroadcastRiverIntegrationStatusChanged(bool bIsConnected);

    // Propriedades de integração com rio (movidas para público para BlueprintReadWrite)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Integration")
    float WaterSpeedMultiplier = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "River Integration")
    bool bEnableWaterDetection = true;

    // Classes de torres para spawning
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower Classes")
    TSubclassOf<AActor> TurretClass;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower Classes")
    TSubclassOf<AActor> InternalTowerClass;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower Classes")
    TSubclassOf<AActor> InhibitorClass;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tower Classes")
    TSubclassOf<AActor> DefaultTowerClass;

private:
    // Funções auxiliares privadas
    void SetupLaneSplines();
    void CalculateWaypointsForLane(ELaneManagerType LaneType);
    FVector GetLaneDirection(ELaneManagerType LaneType, const FVector& Position) const;
    bool IsValidWaypointPosition(const FVector& Position) const;

    // Funções auxiliares privadas adicionais
    void ValidateMathematicalConstants();
    void UpdatePathfindingCache();
    void CleanupPathfindingCache();

    // Funções de criação de torres
    void SetupExternalTowers();
    void SetupInternalTowers();
    void SetupInhibitorTowers();
    bool CreateTowerSet(float XPosition, ETowerType TowerType, const FString& SetName);
    bool CreateInhibitorTowers(float XPosition, const FString& SetName);
    bool IsValidTowerPosition(const FVector& Position) const;
    TSubclassOf<AActor> GetTowerClassForType(ETowerType TowerType) const;
    bool IsPositionInPlayableArea(const FVector& Position) const;
    void InvalidatePathfindingCache(const FVector& Position, float Radius);
    void ConfigureTowerProperties(FTowerData& TowerData) const;
    void ConfigureTowerProperties(AActor* Tower, const FTowerData& TowerData);

    // Funções de pathfinding auxiliares
    TArray<FVector> GetNeighbors(const FVector& Position) const;
    TArray<FVector> ReconstructPath(const FPathNode& EndNode, const TMap<FVector, FPathNode>& AllNodes) const;
    bool IsValidPathPosition(const FVector& Position) const;

    // Funções de integração com rio
    void FindRiverManager();
    void UpdateRiverIntegration();
    void UpdateWaterPathfinding();

    // Constantes de cache e pathfinding
    static constexpr int32 MAX_CACHE_ENTRIES = 500;
    static constexpr float PATHFINDING_CACHE_DURATION = 30.0f; // segundos
    static constexpr float PATHFINDING_NODE_SPACING = 100.0f; // UU
    static constexpr float PATHFINDING_MAX_SEARCH_DISTANCE = 5000.0f; // UU
    static constexpr int32 PATHFINDING_MAX_ITERATIONS = 10000;
    static constexpr float PATHFINDING_UPDATE_INTERVAL = 2.0f; // Segundos
    static constexpr float WAYPOINT_SNAP_DISTANCE = 100.0f; // UU
    
    // Constantes matemáticas das lanes (conforme mapaimplementacao.md)
    static constexpr float SUPERIOR_LANE_SLOPE = -0.577f;
    static constexpr float SUPERIOR_LANE_INTERCEPT = 6928.0f;
    static constexpr float INFERIOR_LANE_SLOPE = 0.577f;
    static constexpr float INFERIOR_LANE_INTERCEPT = -6928.0f;
    static constexpr float SUPERIOR_LANE_WIDTH = 300.0f;
    static constexpr float CENTRAL_LANE_WIDTH = 400.0f;
    static constexpr float INFERIOR_LANE_WIDTH = 300.0f;
    
    // Constantes de torres
    static constexpr float EXTERNAL_TOWER_HEALTH = 2500.0f;
    static constexpr float INTERNAL_TOWER_HEALTH = 3500.0f;
    static constexpr float INHIBITOR_TOWER_HEALTH = 4000.0f;
    static constexpr float EXTERNAL_TOWER_RANGE = 775.0f;
    static constexpr float INTERNAL_TOWER_RANGE = 775.0f;
    static constexpr float INHIBITOR_TOWER_RANGE = 775.0f;
    static constexpr float EXTERNAL_TOWER_DAMAGE = 152.0f;
    static constexpr float INTERNAL_TOWER_DAMAGE = 170.0f;
    static constexpr float INHIBITOR_TOWER_DAMAGE = 170.0f;
    static constexpr float TOWER_ATTACK_SPEED = 0.83f;
    
    // Constantes geométricas
    static constexpr float CYLINDRICAL_TOWER_RADIUS = 88.0f;
    static constexpr float OCTAGONAL_TOWER_RADIUS = 100.0f;
    static constexpr float TOWER_HEIGHT = 1200.0f;
    static constexpr int32 WAYPOINTS_PER_LANE = 12;
    static constexpr float MIN_WAYPOINT_DISTANCE = 800.0f;
    static constexpr float MAX_WAYPOINT_DISTANCE = 1200.0f;

    // Propriedades de cache (sem UPROPERTY para TMap com FCachedPath)
    TMap<FVector, FCachedPath> PathfindingCache;

    UPROPERTY()
    float LastCacheUpdate;

    // Array de torres ativas
    UPROPERTY()
    TArray<AActor*> ActiveTowers;

    // Integração com sistema de rio
    UPROPERTY()
    class ARiverPrismalManager* RiverManager;
};