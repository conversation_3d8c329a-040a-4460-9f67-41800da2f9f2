// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "implementacao_automatizada.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeimplementacao_automatizada() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_AImplementacaoAutomatizada();
AURA_API UClass* Z_Construct_UClass_AImplementacaoAutomatizada_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_ECovilType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EImplementacaoLaneType();
AURA_API UEnum* Z_Construct_UEnum_Aura_ETeam();
AURA_API UEnum* Z_Construct_UEnum_Aura_ETowerType();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FCovilEspecificacao();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FLaneEspecificacao();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ETeam *********************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ETeam;
static UEnum* ETeam_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ETeam.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ETeam.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_ETeam, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("ETeam"));
	}
	return Z_Registration_Info_UEnum_ETeam.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<ETeam>()
{
	return ETeam_StaticEnum();
}
struct Z_Construct_UEnum_Aura_ETeam_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Azul.DisplayName", "Azul" },
		{ "Azul.Name", "ETeam::Azul" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== ENUMS GLOBAIS ==========\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
		{ "Neutro.DisplayName", "Neutro" },
		{ "Neutro.Name", "ETeam::Neutro" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== ENUMS GLOBAIS ==========" },
#endif
		{ "Vermelho.DisplayName", "Vermelho" },
		{ "Vermelho.Name", "ETeam::Vermelho" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ETeam::Azul", (int64)ETeam::Azul },
		{ "ETeam::Vermelho", (int64)ETeam::Vermelho },
		{ "ETeam::Neutro", (int64)ETeam::Neutro },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_ETeam_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"ETeam",
	"ETeam",
	Z_Construct_UEnum_Aura_ETeam_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ETeam_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ETeam_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_ETeam_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_ETeam()
{
	if (!Z_Registration_Info_UEnum_ETeam.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ETeam.InnerSingleton, Z_Construct_UEnum_Aura_ETeam_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ETeam.InnerSingleton;
}
// ********** End Enum ETeam ***********************************************************************

// ********** Begin Enum ETowerType ****************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ETowerType;
static UEnum* ETowerType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ETowerType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ETowerType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_ETowerType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("ETowerType"));
	}
	return Z_Registration_Info_UEnum_ETowerType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<ETowerType>()
{
	return ETowerType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_ETowerType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Externa.DisplayName", "Torre Externa" },
		{ "Externa.Name", "ETowerType::Externa" },
		{ "Inibidor.DisplayName", "Torre Inibidor" },
		{ "Inibidor.Name", "ETowerType::Inibidor" },
		{ "Interna.DisplayName", "Torre Interna" },
		{ "Interna.Name", "ETowerType::Interna" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
		{ "Nexus.DisplayName", "Torre Nexus" },
		{ "Nexus.Name", "ETowerType::Nexus" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ETowerType::Externa", (int64)ETowerType::Externa },
		{ "ETowerType::Interna", (int64)ETowerType::Interna },
		{ "ETowerType::Inibidor", (int64)ETowerType::Inibidor },
		{ "ETowerType::Nexus", (int64)ETowerType::Nexus },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_ETowerType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"ETowerType",
	"ETowerType",
	Z_Construct_UEnum_Aura_ETowerType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ETowerType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ETowerType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_ETowerType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_ETowerType()
{
	if (!Z_Registration_Info_UEnum_ETowerType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ETowerType.InnerSingleton, Z_Construct_UEnum_Aura_ETowerType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ETowerType.InnerSingleton;
}
// ********** End Enum ETowerType ******************************************************************

// ********** Begin ScriptStruct FLaneEspecificacao ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FLaneEspecificacao;
class UScriptStruct* FLaneEspecificacao::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FLaneEspecificacao.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FLaneEspecificacao.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLaneEspecificacao, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("LaneEspecificacao"));
	}
	return Z_Registration_Info_UScriptStruct_FLaneEspecificacao.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FLaneEspecificacao_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== ESTRUTURAS DE DADOS PRECISAS (ESCOPO GLOBAL) ==========\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== ESTRUTURAS DE DADOS PRECISAS (ESCOPO GLOBAL) ==========" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CoeficienteAngular_MetaData[] = {
		{ "Category", "LaneEspecificacao" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CoeficienteLinear_MetaData[] = {
		{ "Category", "LaneEspecificacao" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// m da fun\xc3\xa7\xc3\xa3o y = mx + b\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "m da fun\xc3\xa7\xc3\xa3o y = mx + b" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Largura_MetaData[] = {
		{ "Category", "LaneEspecificacao" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// b da fun\xc3\xa7\xc3\xa3o y = mx + b\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "b da fun\xc3\xa7\xc3\xa3o y = mx + b" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PontoInicial_MetaData[] = {
		{ "Category", "LaneEspecificacao" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Largura total da lane\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Largura total da lane" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PontoFinal_MetaData[] = {
		{ "Category", "LaneEspecificacao" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CoeficienteAngular;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CoeficienteLinear;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Largura;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PontoInicial;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PontoFinal;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLaneEspecificacao>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::NewProp_CoeficienteAngular = { "CoeficienteAngular", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneEspecificacao, CoeficienteAngular), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CoeficienteAngular_MetaData), NewProp_CoeficienteAngular_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::NewProp_CoeficienteLinear = { "CoeficienteLinear", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneEspecificacao, CoeficienteLinear), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CoeficienteLinear_MetaData), NewProp_CoeficienteLinear_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::NewProp_Largura = { "Largura", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneEspecificacao, Largura), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Largura_MetaData), NewProp_Largura_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::NewProp_PontoInicial = { "PontoInicial", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneEspecificacao, PontoInicial), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PontoInicial_MetaData), NewProp_PontoInicial_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::NewProp_PontoFinal = { "PontoFinal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneEspecificacao, PontoFinal), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PontoFinal_MetaData), NewProp_PontoFinal_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::NewProp_CoeficienteAngular,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::NewProp_CoeficienteLinear,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::NewProp_Largura,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::NewProp_PontoInicial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::NewProp_PontoFinal,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"LaneEspecificacao",
	Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::PropPointers),
	sizeof(FLaneEspecificacao),
	alignof(FLaneEspecificacao),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLaneEspecificacao()
{
	if (!Z_Registration_Info_UScriptStruct_FLaneEspecificacao.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FLaneEspecificacao.InnerSingleton, Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FLaneEspecificacao.InnerSingleton;
}
// ********** End ScriptStruct FLaneEspecificacao **************************************************

// ********** Begin ScriptStruct FCovilEspecificacao ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FCovilEspecificacao;
class UScriptStruct* FCovilEspecificacao::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FCovilEspecificacao.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FCovilEspecificacao.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FCovilEspecificacao, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("CovilEspecificacao"));
	}
	return Z_Registration_Info_UScriptStruct_FCovilEspecificacao.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FCovilEspecificacao_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Centro_MetaData[] = {
		{ "Category", "CovilEspecificacao" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TipoGeometria_MetaData[] = {
		{ "Category", "CovilEspecificacao" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parametro1_MetaData[] = {
		{ "Category", "CovilEspecificacao" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// \"Hexagono\", \"Elipse\", \"Circulo\"\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\"Hexagono\", \"Elipse\", \"Circulo\"" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parametro2_MetaData[] = {
		{ "Category", "CovilEspecificacao" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Raio para c\xc3\xadrculo/hex\xc3\xa1gono, semi-eixo A para elipse\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio para c\xc3\xadrculo/hex\xc3\xa1gono, semi-eixo A para elipse" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotacaoGraus_MetaData[] = {
		{ "Category", "CovilEspecificacao" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// N\xc3\xa3o usado para c\xc3\xadrculo/hex\xc3\xa1gono, semi-eixo B para elipse\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xa3o usado para c\xc3\xadrculo/hex\xc3\xa1gono, semi-eixo B para elipse" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Centro;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TipoGeometria;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Parametro1;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Parametro2;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RotacaoGraus;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FCovilEspecificacao>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::NewProp_Centro = { "Centro", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCovilEspecificacao, Centro), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Centro_MetaData), NewProp_Centro_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::NewProp_TipoGeometria = { "TipoGeometria", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCovilEspecificacao, TipoGeometria), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TipoGeometria_MetaData), NewProp_TipoGeometria_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::NewProp_Parametro1 = { "Parametro1", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCovilEspecificacao, Parametro1), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parametro1_MetaData), NewProp_Parametro1_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::NewProp_Parametro2 = { "Parametro2", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCovilEspecificacao, Parametro2), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parametro2_MetaData), NewProp_Parametro2_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::NewProp_RotacaoGraus = { "RotacaoGraus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCovilEspecificacao, RotacaoGraus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotacaoGraus_MetaData), NewProp_RotacaoGraus_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::NewProp_Centro,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::NewProp_TipoGeometria,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::NewProp_Parametro1,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::NewProp_Parametro2,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::NewProp_RotacaoGraus,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"CovilEspecificacao",
	Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::PropPointers),
	sizeof(FCovilEspecificacao),
	alignof(FCovilEspecificacao),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FCovilEspecificacao()
{
	if (!Z_Registration_Info_UScriptStruct_FCovilEspecificacao.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FCovilEspecificacao.InnerSingleton, Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FCovilEspecificacao.InnerSingleton;
}
// ********** End ScriptStruct FCovilEspecificacao *************************************************

// ********** Begin Class AImplementacaoAutomatizada Function CalcularPontosRioSenoidal ************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics
{
	struct ImplementacaoAutomatizada_eventCalcularPontosRioSenoidal_Parms
	{
		int32 NumPontos;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "C\xc3\xa1lculos" },
		{ "CPP_Default_NumPontos", "100" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumPontos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::NewProp_NumPontos = { "NumPontos", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventCalcularPontosRioSenoidal_Parms, NumPontos), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventCalcularPontosRioSenoidal_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::NewProp_NumPontos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "CalcularPontosRioSenoidal", Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::PropPointers), sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::ImplementacaoAutomatizada_eventCalcularPontosRioSenoidal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::ImplementacaoAutomatizada_eventCalcularPontosRioSenoidal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execCalcularPontosRioSenoidal)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NumPontos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=AImplementacaoAutomatizada::CalcularPontosRioSenoidal(Z_Param_NumPontos);
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function CalcularPontosRioSenoidal **************

// ********** Begin Class AImplementacaoAutomatizada Function CalcularPosicaoMinion ****************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics
{
	struct ImplementacaoAutomatizada_eventCalcularPosicaoMinion_Parms
	{
		int32 LaneIndex;
		float DistanciaPercorrida;
		bool IsTeamAzul;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "C\xc3\xa1lculos" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_LaneIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanciaPercorrida;
	static void NewProp_IsTeamAzul_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_IsTeamAzul;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::NewProp_LaneIndex = { "LaneIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventCalcularPosicaoMinion_Parms, LaneIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::NewProp_DistanciaPercorrida = { "DistanciaPercorrida", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventCalcularPosicaoMinion_Parms, DistanciaPercorrida), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::NewProp_IsTeamAzul_SetBit(void* Obj)
{
	((ImplementacaoAutomatizada_eventCalcularPosicaoMinion_Parms*)Obj)->IsTeamAzul = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::NewProp_IsTeamAzul = { "IsTeamAzul", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ImplementacaoAutomatizada_eventCalcularPosicaoMinion_Parms), &Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::NewProp_IsTeamAzul_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventCalcularPosicaoMinion_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::NewProp_LaneIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::NewProp_DistanciaPercorrida,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::NewProp_IsTeamAzul,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "CalcularPosicaoMinion", Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::PropPointers), sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::ImplementacaoAutomatizada_eventCalcularPosicaoMinion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04822401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::ImplementacaoAutomatizada_eventCalcularPosicaoMinion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execCalcularPosicaoMinion)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_LaneIndex);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DistanciaPercorrida);
	P_GET_UBOOL(Z_Param_IsTeamAzul);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=AImplementacaoAutomatizada::CalcularPosicaoMinion(Z_Param_LaneIndex,Z_Param_DistanciaPercorrida,Z_Param_IsTeamAzul);
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function CalcularPosicaoMinion ******************

// ********** Begin Class AImplementacaoAutomatizada Function CalcularPosicaoTorre *****************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics
{
	struct ImplementacaoAutomatizada_eventCalcularPosicaoTorre_Parms
	{
		int32 LaneIndex;
		int32 TorreIndex;
		bool IsTeamAzul;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "C\xc3\xa1lculos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== FUN\xc3\x87\xc3\x95""ES DE C\xc3\x81LCULO AUTOM\xc3\x81TICO ==========\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== FUN\xc3\x87\xc3\x95""ES DE C\xc3\x81LCULO AUTOM\xc3\x81TICO ==========" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_LaneIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TorreIndex;
	static void NewProp_IsTeamAzul_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_IsTeamAzul;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::NewProp_LaneIndex = { "LaneIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventCalcularPosicaoTorre_Parms, LaneIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::NewProp_TorreIndex = { "TorreIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventCalcularPosicaoTorre_Parms, TorreIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::NewProp_IsTeamAzul_SetBit(void* Obj)
{
	((ImplementacaoAutomatizada_eventCalcularPosicaoTorre_Parms*)Obj)->IsTeamAzul = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::NewProp_IsTeamAzul = { "IsTeamAzul", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ImplementacaoAutomatizada_eventCalcularPosicaoTorre_Parms), &Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::NewProp_IsTeamAzul_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventCalcularPosicaoTorre_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::NewProp_LaneIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::NewProp_TorreIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::NewProp_IsTeamAzul,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "CalcularPosicaoTorre", Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::PropPointers), sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::ImplementacaoAutomatizada_eventCalcularPosicaoTorre_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04822401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::ImplementacaoAutomatizada_eventCalcularPosicaoTorre_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execCalcularPosicaoTorre)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_LaneIndex);
	P_GET_PROPERTY(FIntProperty,Z_Param_TorreIndex);
	P_GET_UBOOL(Z_Param_IsTeamAzul);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=AImplementacaoAutomatizada::CalcularPosicaoTorre(Z_Param_LaneIndex,Z_Param_TorreIndex,Z_Param_IsTeamAzul);
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function CalcularPosicaoTorre *******************

// ********** Begin Class AImplementacaoAutomatizada Function CalcularVerticesHexagono *************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics
{
	struct ImplementacaoAutomatizada_eventCalcularVerticesHexagono_Parms
	{
		FVector2D Centro;
		float Raio;
		float RotacaoGraus;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "C\xc3\xa1lculos" },
		{ "CPP_Default_RotacaoGraus", "0.000000" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Centro;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Raio;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RotacaoGraus;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::NewProp_Centro = { "Centro", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventCalcularVerticesHexagono_Parms, Centro), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::NewProp_Raio = { "Raio", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventCalcularVerticesHexagono_Parms, Raio), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::NewProp_RotacaoGraus = { "RotacaoGraus", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventCalcularVerticesHexagono_Parms, RotacaoGraus), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventCalcularVerticesHexagono_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::NewProp_Centro,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::NewProp_Raio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::NewProp_RotacaoGraus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "CalcularVerticesHexagono", Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::PropPointers), sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::ImplementacaoAutomatizada_eventCalcularVerticesHexagono_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04822401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::ImplementacaoAutomatizada_eventCalcularVerticesHexagono_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execCalcularVerticesHexagono)
{
	P_GET_STRUCT(FVector2D,Z_Param_Centro);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Raio);
	P_GET_PROPERTY(FFloatProperty,Z_Param_RotacaoGraus);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=AImplementacaoAutomatizada::CalcularVerticesHexagono(Z_Param_Centro,Z_Param_Raio,Z_Param_RotacaoGraus);
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function CalcularVerticesHexagono ***************

// ********** Begin Class AImplementacaoAutomatizada Function CorrigirDesviosPrecisao **************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_CorrigirDesviosPrecisao_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Verifica\xc3\xa7\xc3\xa3o" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_CorrigirDesviosPrecisao_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "CorrigirDesviosPrecisao", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CorrigirDesviosPrecisao_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_CorrigirDesviosPrecisao_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_CorrigirDesviosPrecisao()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_CorrigirDesviosPrecisao_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execCorrigirDesviosPrecisao)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CorrigirDesviosPrecisao();
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function CorrigirDesviosPrecisao ****************

// ********** Begin Class AImplementacaoAutomatizada Function CriarBases ***************************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_CriarBases_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cria\xc3\xa7\xc3\xa3o Autom\xc3\xa1tica" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_CriarBases_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "CriarBases", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CriarBases_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_CriarBases_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_CriarBases()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_CriarBases_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execCriarBases)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CriarBases();
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function CriarBases *****************************

// ********** Begin Class AImplementacaoAutomatizada Function CriarCovils **************************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_CriarCovils_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cria\xc3\xa7\xc3\xa3o Autom\xc3\xa1tica" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_CriarCovils_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "CriarCovils", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CriarCovils_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_CriarCovils_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_CriarCovils()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_CriarCovils_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execCriarCovils)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CriarCovils();
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function CriarCovils ****************************

// ********** Begin Class AImplementacaoAutomatizada Function CriarIlhaCentral *********************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_CriarIlhaCentral_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cria\xc3\xa7\xc3\xa3o Autom\xc3\xa1tica" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_CriarIlhaCentral_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "CriarIlhaCentral", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CriarIlhaCentral_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_CriarIlhaCentral_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_CriarIlhaCentral()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_CriarIlhaCentral_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execCriarIlhaCentral)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CriarIlhaCentral();
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function CriarIlhaCentral ***********************

// ********** Begin Class AImplementacaoAutomatizada Function CriarLanes ***************************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_CriarLanes_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cria\xc3\xa7\xc3\xa3o Autom\xc3\xa1tica" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_CriarLanes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "CriarLanes", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CriarLanes_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_CriarLanes_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_CriarLanes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_CriarLanes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execCriarLanes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CriarLanes();
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function CriarLanes *****************************

// ********** Begin Class AImplementacaoAutomatizada Function CriarMapaCompleto ********************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_CriarMapaCompleto_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cria\xc3\xa7\xc3\xa3o Autom\xc3\xa1tica" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== SISTEMA DE CRIA\xc3\x87\xc3\x83O AUTOM\xc3\x81TICA ==========\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== SISTEMA DE CRIA\xc3\x87\xc3\x83O AUTOM\xc3\x81TICA ==========" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_CriarMapaCompleto_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "CriarMapaCompleto", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CriarMapaCompleto_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_CriarMapaCompleto_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_CriarMapaCompleto()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_CriarMapaCompleto_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execCriarMapaCompleto)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CriarMapaCompleto();
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function CriarMapaCompleto **********************

// ********** Begin Class AImplementacaoAutomatizada Function CriarParedes *************************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_CriarParedes_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cria\xc3\xa7\xc3\xa3o Autom\xc3\xa1tica" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_CriarParedes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "CriarParedes", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CriarParedes_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_CriarParedes_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_CriarParedes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_CriarParedes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execCriarParedes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CriarParedes();
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function CriarParedes ***************************

// ********** Begin Class AImplementacaoAutomatizada Function CriarRioPrismal **********************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_CriarRioPrismal_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cria\xc3\xa7\xc3\xa3o Autom\xc3\xa1tica" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_CriarRioPrismal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "CriarRioPrismal", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CriarRioPrismal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_CriarRioPrismal_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_CriarRioPrismal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_CriarRioPrismal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execCriarRioPrismal)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CriarRioPrismal();
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function CriarRioPrismal ************************

// ********** Begin Class AImplementacaoAutomatizada Function CriarTorres **************************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_CriarTorres_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cria\xc3\xa7\xc3\xa3o Autom\xc3\xa1tica" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_CriarTorres_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "CriarTorres", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_CriarTorres_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_CriarTorres_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_CriarTorres()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_CriarTorres_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execCriarTorres)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CriarTorres();
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function CriarTorres ****************************

// ********** Begin Class AImplementacaoAutomatizada Function GerarRelatorioValidacao **************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao_Statics
{
	struct ImplementacaoAutomatizada_eventGerarRelatorioValidacao_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Verifica\xc3\xa7\xc3\xa3o" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventGerarRelatorioValidacao_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "GerarRelatorioValidacao", Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao_Statics::PropPointers), sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao_Statics::ImplementacaoAutomatizada_eventGerarRelatorioValidacao_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao_Statics::ImplementacaoAutomatizada_eventGerarRelatorioValidacao_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execGerarRelatorioValidacao)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GerarRelatorioValidacao();
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function GerarRelatorioValidacao ****************

// ********** Begin Class AImplementacaoAutomatizada Function ValidarPontoEmElipse *****************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics
{
	struct ImplementacaoAutomatizada_eventValidarPontoEmElipse_Parms
	{
		float X;
		float Y;
		FVector2D Centro;
		float SemiEixoA;
		float SemiEixoB;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Valida\xc3\xa7\xc3\xa3o" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_X;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Y;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Centro;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SemiEixoA;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SemiEixoB;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoEmElipse_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_Y = { "Y", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoEmElipse_Parms, Y), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_Centro = { "Centro", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoEmElipse_Parms, Centro), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_SemiEixoA = { "SemiEixoA", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoEmElipse_Parms, SemiEixoA), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_SemiEixoB = { "SemiEixoB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoEmElipse_Parms, SemiEixoB), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ImplementacaoAutomatizada_eventValidarPontoEmElipse_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ImplementacaoAutomatizada_eventValidarPontoEmElipse_Parms), &Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_Y,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_Centro,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_SemiEixoA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_SemiEixoB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "ValidarPontoEmElipse", Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::PropPointers), sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::ImplementacaoAutomatizada_eventValidarPontoEmElipse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04822401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::ImplementacaoAutomatizada_eventValidarPontoEmElipse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execValidarPontoEmElipse)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_X);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Y);
	P_GET_STRUCT(FVector2D,Z_Param_Centro);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SemiEixoA);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SemiEixoB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=AImplementacaoAutomatizada::ValidarPontoEmElipse(Z_Param_X,Z_Param_Y,Z_Param_Centro,Z_Param_SemiEixoA,Z_Param_SemiEixoB);
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function ValidarPontoEmElipse *******************

// ********** Begin Class AImplementacaoAutomatizada Function ValidarPontoEmHexagono ***************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics
{
	struct ImplementacaoAutomatizada_eventValidarPontoEmHexagono_Parms
	{
		float X;
		float Y;
		FVector2D Centro;
		float Raio;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Valida\xc3\xa7\xc3\xa3o" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_X;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Y;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Centro;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Raio;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoEmHexagono_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::NewProp_Y = { "Y", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoEmHexagono_Parms, Y), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::NewProp_Centro = { "Centro", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoEmHexagono_Parms, Centro), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::NewProp_Raio = { "Raio", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoEmHexagono_Parms, Raio), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ImplementacaoAutomatizada_eventValidarPontoEmHexagono_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ImplementacaoAutomatizada_eventValidarPontoEmHexagono_Parms), &Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::NewProp_Y,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::NewProp_Centro,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::NewProp_Raio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "ValidarPontoEmHexagono", Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::PropPointers), sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::ImplementacaoAutomatizada_eventValidarPontoEmHexagono_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04822401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::ImplementacaoAutomatizada_eventValidarPontoEmHexagono_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execValidarPontoEmHexagono)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_X);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Y);
	P_GET_STRUCT(FVector2D,Z_Param_Centro);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Raio);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=AImplementacaoAutomatizada::ValidarPontoEmHexagono(Z_Param_X,Z_Param_Y,Z_Param_Centro,Z_Param_Raio);
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function ValidarPontoEmHexagono *****************

// ********** Begin Class AImplementacaoAutomatizada Function ValidarPontoNaLane *******************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics
{
	struct ImplementacaoAutomatizada_eventValidarPontoNaLane_Parms
	{
		float X;
		float Y;
		int32 LaneIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Valida\xc3\xa7\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== FUN\xc3\x87\xc3\x95""ES DE VALIDA\xc3\x87\xc3\x83O GEOM\xc3\x89TRICA ==========\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== FUN\xc3\x87\xc3\x95""ES DE VALIDA\xc3\x87\xc3\x83O GEOM\xc3\x89TRICA ==========" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_X;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Y;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LaneIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoNaLane_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::NewProp_Y = { "Y", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoNaLane_Parms, Y), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::NewProp_LaneIndex = { "LaneIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoNaLane_Parms, LaneIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ImplementacaoAutomatizada_eventValidarPontoNaLane_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ImplementacaoAutomatizada_eventValidarPontoNaLane_Parms), &Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::NewProp_Y,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::NewProp_LaneIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "ValidarPontoNaLane", Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::PropPointers), sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::ImplementacaoAutomatizada_eventValidarPontoNaLane_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::ImplementacaoAutomatizada_eventValidarPontoNaLane_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execValidarPontoNaLane)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_X);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Y);
	P_GET_PROPERTY(FIntProperty,Z_Param_LaneIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=AImplementacaoAutomatizada::ValidarPontoNaLane(Z_Param_X,Z_Param_Y,Z_Param_LaneIndex);
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function ValidarPontoNaLane *********************

// ********** Begin Class AImplementacaoAutomatizada Function ValidarPontoNoRio ********************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics
{
	struct ImplementacaoAutomatizada_eventValidarPontoNoRio_Parms
	{
		float X;
		float Y;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Valida\xc3\xa7\xc3\xa3o" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_X;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Y;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoNoRio_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::NewProp_Y = { "Y", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ImplementacaoAutomatizada_eventValidarPontoNoRio_Parms, Y), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ImplementacaoAutomatizada_eventValidarPontoNoRio_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ImplementacaoAutomatizada_eventValidarPontoNoRio_Parms), &Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::NewProp_Y,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "ValidarPontoNoRio", Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::PropPointers), sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::ImplementacaoAutomatizada_eventValidarPontoNoRio_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::ImplementacaoAutomatizada_eventValidarPontoNoRio_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execValidarPontoNoRio)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_X);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Y);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=AImplementacaoAutomatizada::ValidarPontoNoRio(Z_Param_X,Z_Param_Y);
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function ValidarPontoNoRio **********************

// ********** Begin Class AImplementacaoAutomatizada Function VerificarPrecisaoCompleta ************
struct Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics
{
	struct ImplementacaoAutomatizada_eventVerificarPrecisaoCompleta_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Verifica\xc3\xa7\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== SISTEMA DE VERIFICA\xc3\x87\xc3\x83O CONT\xc3\x8dNUA ==========\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== SISTEMA DE VERIFICA\xc3\x87\xc3\x83O CONT\xc3\x8dNUA ==========" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ImplementacaoAutomatizada_eventVerificarPrecisaoCompleta_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ImplementacaoAutomatizada_eventVerificarPrecisaoCompleta_Parms), &Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AImplementacaoAutomatizada, nullptr, "VerificarPrecisaoCompleta", Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::PropPointers), sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::ImplementacaoAutomatizada_eventVerificarPrecisaoCompleta_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::Function_MetaDataParams), Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::ImplementacaoAutomatizada_eventVerificarPrecisaoCompleta_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AImplementacaoAutomatizada::execVerificarPrecisaoCompleta)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->VerificarPrecisaoCompleta();
	P_NATIVE_END;
}
// ********** End Class AImplementacaoAutomatizada Function VerificarPrecisaoCompleta **************

// ********** Begin Class AImplementacaoAutomatizada ***********************************************
void AImplementacaoAutomatizada::StaticRegisterNativesAImplementacaoAutomatizada()
{
	UClass* Class = AImplementacaoAutomatizada::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalcularPontosRioSenoidal", &AImplementacaoAutomatizada::execCalcularPontosRioSenoidal },
		{ "CalcularPosicaoMinion", &AImplementacaoAutomatizada::execCalcularPosicaoMinion },
		{ "CalcularPosicaoTorre", &AImplementacaoAutomatizada::execCalcularPosicaoTorre },
		{ "CalcularVerticesHexagono", &AImplementacaoAutomatizada::execCalcularVerticesHexagono },
		{ "CorrigirDesviosPrecisao", &AImplementacaoAutomatizada::execCorrigirDesviosPrecisao },
		{ "CriarBases", &AImplementacaoAutomatizada::execCriarBases },
		{ "CriarCovils", &AImplementacaoAutomatizada::execCriarCovils },
		{ "CriarIlhaCentral", &AImplementacaoAutomatizada::execCriarIlhaCentral },
		{ "CriarLanes", &AImplementacaoAutomatizada::execCriarLanes },
		{ "CriarMapaCompleto", &AImplementacaoAutomatizada::execCriarMapaCompleto },
		{ "CriarParedes", &AImplementacaoAutomatizada::execCriarParedes },
		{ "CriarRioPrismal", &AImplementacaoAutomatizada::execCriarRioPrismal },
		{ "CriarTorres", &AImplementacaoAutomatizada::execCriarTorres },
		{ "GerarRelatorioValidacao", &AImplementacaoAutomatizada::execGerarRelatorioValidacao },
		{ "ValidarPontoEmElipse", &AImplementacaoAutomatizada::execValidarPontoEmElipse },
		{ "ValidarPontoEmHexagono", &AImplementacaoAutomatizada::execValidarPontoEmHexagono },
		{ "ValidarPontoNaLane", &AImplementacaoAutomatizada::execValidarPontoNaLane },
		{ "ValidarPontoNoRio", &AImplementacaoAutomatizada::execValidarPontoNoRio },
		{ "VerificarPrecisaoCompleta", &AImplementacaoAutomatizada::execVerificarPrecisaoCompleta },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AImplementacaoAutomatizada;
UClass* AImplementacaoAutomatizada::GetPrivateStaticClass()
{
	using TClass = AImplementacaoAutomatizada;
	if (!Z_Registration_Info_UClass_AImplementacaoAutomatizada.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("ImplementacaoAutomatizada"),
			Z_Registration_Info_UClass_AImplementacaoAutomatizada.InnerSingleton,
			StaticRegisterNativesAImplementacaoAutomatizada,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AImplementacaoAutomatizada.InnerSingleton;
}
UClass* Z_Construct_UClass_AImplementacaoAutomatizada_NoRegister()
{
	return AImplementacaoAutomatizada::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AImplementacaoAutomatizada_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * SISTEMA DE IMPLEMENTA\xc3\x87\xc3\x83O AUTOMATIZADA - GEOMETRIA MATEM\xc3\x81TICA PRECISA\n * Garante 100% de precis\xc3\xa3o na cria\xc3\xa7\xc3\xa3o do mapa Aura\n * Todas as coordenadas, \xc3\xa2ngulos e formas s\xc3\xa3o calculadas matematicamente\n */" },
#endif
		{ "IncludePath", "implementacao_automatizada.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "SISTEMA DE IMPLEMENTA\xc3\x87\xc3\x83O AUTOMATIZADA - GEOMETRIA MATEM\xc3\x81TICA PRECISA\nGarante 100% de precis\xc3\xa3o na cria\xc3\xa7\xc3\xa3o do mapa Aura\nTodas as coordenadas, \xc3\xa2ngulos e formas s\xc3\xa3o calculadas matematicamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LanesEspecificacoes_MetaData[] = {
		{ "Category", "Especifica\xc3\xa7\xc3\xb5""es" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== ARRAYS DE ESPECIFICA\xc3\x87\xc3\x95""ES ==========\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== ARRAYS DE ESPECIFICA\xc3\x87\xc3\x95""ES ==========" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CovilsEspecificacoes_MetaData[] = {
		{ "Category", "Especifica\xc3\xa7\xc3\xb5""es" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Componentes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== COMPONENTES DE MESH ==========\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== COMPONENTES DE MESH ==========" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TorresCreated_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== ARRAYS DE OBJETOS CRIADOS ==========\n" },
#endif
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== ARRAYS DE OBJETOS CRIADOS ==========" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParedesCreated_MetaData[] = {
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CovilsCreated_MetaData[] = {
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_LanesEspecificacoes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LanesEspecificacoes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CovilsEspecificacoes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CovilsEspecificacoes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TorresCreated_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TorresCreated;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ParedesCreated_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ParedesCreated;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CovilsCreated_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CovilsCreated;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPontosRioSenoidal, "CalcularPontosRioSenoidal" }, // 2986873901
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoMinion, "CalcularPosicaoMinion" }, // 899355855
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularPosicaoTorre, "CalcularPosicaoTorre" }, // 3041074379
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_CalcularVerticesHexagono, "CalcularVerticesHexagono" }, // 3587862560
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_CorrigirDesviosPrecisao, "CorrigirDesviosPrecisao" }, // 3137493375
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_CriarBases, "CriarBases" }, // 2416758968
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_CriarCovils, "CriarCovils" }, // 4275818691
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_CriarIlhaCentral, "CriarIlhaCentral" }, // 2793189195
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_CriarLanes, "CriarLanes" }, // 4101215163
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_CriarMapaCompleto, "CriarMapaCompleto" }, // 1563632007
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_CriarParedes, "CriarParedes" }, // 2317471506
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_CriarRioPrismal, "CriarRioPrismal" }, // 3048884267
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_CriarTorres, "CriarTorres" }, // 401325123
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_GerarRelatorioValidacao, "GerarRelatorioValidacao" }, // 1164595593
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmElipse, "ValidarPontoEmElipse" }, // 3555736696
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoEmHexagono, "ValidarPontoEmHexagono" }, // 3346616764
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNaLane, "ValidarPontoNaLane" }, // 4069897142
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_ValidarPontoNoRio, "ValidarPontoNoRio" }, // 433059043
		{ &Z_Construct_UFunction_AImplementacaoAutomatizada_VerificarPrecisaoCompleta, "VerificarPrecisaoCompleta" }, // 1598501764
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AImplementacaoAutomatizada>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_LanesEspecificacoes_Inner = { "LanesEspecificacoes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FLaneEspecificacao, METADATA_PARAMS(0, nullptr) }; // 4131517384
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_LanesEspecificacoes = { "LanesEspecificacoes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AImplementacaoAutomatizada, LanesEspecificacoes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LanesEspecificacoes_MetaData), NewProp_LanesEspecificacoes_MetaData) }; // 4131517384
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_CovilsEspecificacoes_Inner = { "CovilsEspecificacoes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FCovilEspecificacao, METADATA_PARAMS(0, nullptr) }; // 32013358
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_CovilsEspecificacoes = { "CovilsEspecificacoes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AImplementacaoAutomatizada, CovilsEspecificacoes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CovilsEspecificacoes_MetaData), NewProp_CovilsEspecificacoes_MetaData) }; // 32013358
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x00400000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AImplementacaoAutomatizada, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_TorresCreated_Inner = { "TorresCreated", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_TorresCreated = { "TorresCreated", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AImplementacaoAutomatizada, TorresCreated), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TorresCreated_MetaData), NewProp_TorresCreated_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_ParedesCreated_Inner = { "ParedesCreated", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_ParedesCreated = { "ParedesCreated", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AImplementacaoAutomatizada, ParedesCreated), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParedesCreated_MetaData), NewProp_ParedesCreated_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_CovilsCreated_Inner = { "CovilsCreated", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_CovilsCreated = { "CovilsCreated", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AImplementacaoAutomatizada, CovilsCreated), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CovilsCreated_MetaData), NewProp_CovilsCreated_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AImplementacaoAutomatizada_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_LanesEspecificacoes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_LanesEspecificacoes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_CovilsEspecificacoes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_CovilsEspecificacoes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_TorresCreated_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_TorresCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_ParedesCreated_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_ParedesCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_CovilsCreated_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AImplementacaoAutomatizada_Statics::NewProp_CovilsCreated,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AImplementacaoAutomatizada_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AImplementacaoAutomatizada_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AImplementacaoAutomatizada_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AImplementacaoAutomatizada_Statics::ClassParams = {
	&AImplementacaoAutomatizada::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AImplementacaoAutomatizada_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AImplementacaoAutomatizada_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AImplementacaoAutomatizada_Statics::Class_MetaDataParams), Z_Construct_UClass_AImplementacaoAutomatizada_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AImplementacaoAutomatizada()
{
	if (!Z_Registration_Info_UClass_AImplementacaoAutomatizada.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AImplementacaoAutomatizada.OuterSingleton, Z_Construct_UClass_AImplementacaoAutomatizada_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AImplementacaoAutomatizada.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AImplementacaoAutomatizada);
AImplementacaoAutomatizada::~AImplementacaoAutomatizada() {}
// ********** End Class AImplementacaoAutomatizada *************************************************

// ********** Begin Enum EImplementacaoLaneType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EImplementacaoLaneType;
static UEnum* EImplementacaoLaneType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EImplementacaoLaneType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EImplementacaoLaneType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EImplementacaoLaneType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EImplementacaoLaneType"));
	}
	return Z_Registration_Info_UEnum_EImplementacaoLaneType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EImplementacaoLaneType>()
{
	return EImplementacaoLaneType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EImplementacaoLaneType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Central.DisplayName", "Lane Central" },
		{ "Central.Name", "EImplementacaoLaneType::Central" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ========== ENUMS PARA TIPOS ==========\n" },
#endif
		{ "Inferior.DisplayName", "Lane Inferior" },
		{ "Inferior.Name", "EImplementacaoLaneType::Inferior" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
		{ "Superior.DisplayName", "Lane Superior" },
		{ "Superior.Name", "EImplementacaoLaneType::Superior" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "========== ENUMS PARA TIPOS ==========" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EImplementacaoLaneType::Superior", (int64)EImplementacaoLaneType::Superior },
		{ "EImplementacaoLaneType::Central", (int64)EImplementacaoLaneType::Central },
		{ "EImplementacaoLaneType::Inferior", (int64)EImplementacaoLaneType::Inferior },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EImplementacaoLaneType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EImplementacaoLaneType",
	"EImplementacaoLaneType",
	Z_Construct_UEnum_Aura_EImplementacaoLaneType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EImplementacaoLaneType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EImplementacaoLaneType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EImplementacaoLaneType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EImplementacaoLaneType()
{
	if (!Z_Registration_Info_UEnum_EImplementacaoLaneType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EImplementacaoLaneType.InnerSingleton, Z_Construct_UEnum_Aura_EImplementacaoLaneType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EImplementacaoLaneType.InnerSingleton;
}
// ********** End Enum EImplementacaoLaneType ******************************************************

// ********** Begin Enum ECovilType ****************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ECovilType;
static UEnum* ECovilType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ECovilType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ECovilType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_ECovilType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("ECovilType"));
	}
	return Z_Registration_Info_UEnum_ECovilType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<ECovilType>()
{
	return ECovilType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_ECovilType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BaraoAuracron.DisplayName", "Bar\xc3\xa3o Auracron" },
		{ "BaraoAuracron.Name", "ECovilType::BaraoAuracron" },
		{ "BlueprintType", "true" },
		{ "DragaoPrismal.DisplayName", "Drag\xc3\xa3o Prismal" },
		{ "DragaoPrismal.Name", "ECovilType::DragaoPrismal" },
		{ "GuardiaoPortal.DisplayName", "Guardi\xc3\xa3o do Portal" },
		{ "GuardiaoPortal.Name", "ECovilType::GuardiaoPortal" },
		{ "ModuleRelativePath", "Public/implementacao_automatizada.h" },
		{ "SentinelaCristalina.DisplayName", "Sentinela Cristalina" },
		{ "SentinelaCristalina.Name", "ECovilType::SentinelaCristalina" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ECovilType::DragaoPrismal", (int64)ECovilType::DragaoPrismal },
		{ "ECovilType::BaraoAuracron", (int64)ECovilType::BaraoAuracron },
		{ "ECovilType::SentinelaCristalina", (int64)ECovilType::SentinelaCristalina },
		{ "ECovilType::GuardiaoPortal", (int64)ECovilType::GuardiaoPortal },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_ECovilType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"ECovilType",
	"ECovilType",
	Z_Construct_UEnum_Aura_ECovilType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ECovilType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ECovilType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_ECovilType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_ECovilType()
{
	if (!Z_Registration_Info_UEnum_ECovilType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ECovilType.InnerSingleton, Z_Construct_UEnum_Aura_ECovilType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ECovilType.InnerSingleton;
}
// ********** End Enum ECovilType ******************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_implementacao_automatizada_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ETeam_StaticEnum, TEXT("ETeam"), &Z_Registration_Info_UEnum_ETeam, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1024800466U) },
		{ ETowerType_StaticEnum, TEXT("ETowerType"), &Z_Registration_Info_UEnum_ETowerType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4294645771U) },
		{ EImplementacaoLaneType_StaticEnum, TEXT("EImplementacaoLaneType"), &Z_Registration_Info_UEnum_EImplementacaoLaneType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4000083275U) },
		{ ECovilType_StaticEnum, TEXT("ECovilType"), &Z_Registration_Info_UEnum_ECovilType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1644031951U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FLaneEspecificacao::StaticStruct, Z_Construct_UScriptStruct_FLaneEspecificacao_Statics::NewStructOps, TEXT("LaneEspecificacao"), &Z_Registration_Info_UScriptStruct_FLaneEspecificacao, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLaneEspecificacao), 4131517384U) },
		{ FCovilEspecificacao::StaticStruct, Z_Construct_UScriptStruct_FCovilEspecificacao_Statics::NewStructOps, TEXT("CovilEspecificacao"), &Z_Registration_Info_UScriptStruct_FCovilEspecificacao, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FCovilEspecificacao), 32013358U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AImplementacaoAutomatizada, AImplementacaoAutomatizada::StaticClass, TEXT("AImplementacaoAutomatizada"), &Z_Registration_Info_UClass_AImplementacaoAutomatizada, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AImplementacaoAutomatizada), 787763539U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_implementacao_automatizada_h__Script_Aura_1371584636(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_implementacao_automatizada_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_implementacao_automatizada_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_implementacao_automatizada_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_implementacao_automatizada_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_implementacao_automatizada_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_implementacao_automatizada_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
