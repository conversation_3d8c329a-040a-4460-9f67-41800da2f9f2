#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Engine/StaticMesh.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
// UE5.6 PCG API includes - using correct paths
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGSubsystem.h"

#include "Async/AsyncWork.h"
#include "HAL/ThreadSafeBool.h"
#include "Containers/Queue.h"
#include "RenderCommandFence.h"
#include "RHI.h"
#include "RHIResources.h"
// UE5.6 Nanite API includes - using correct paths
#include "Rendering/NaniteResources.h"
#include "Rendering/NaniteCoarseMeshStreamingManager.h"
// UE5.6 Editor subsystem for Nanite settings
// #include "EditorSubsystem.h" // Removido temporariamente - caminho não encontrado
#include "APCGNaniteOptimizer.generated.h"

// Forward Declarations
class UStaticMesh;
class UStaticMeshComponent;
class UPCGComponent;
class AProceduralMapGenerator;
class APCGWorldPartitionManager;

// Enums for Nanite Optimization
UENUM(BlueprintType)
enum class EPCGNaniteOptimizationLevel : uint8
{
    Disabled        UMETA(DisplayName = "Disabled"),
    Conservative    UMETA(DisplayName = "Conservative"),
    Balanced        UMETA(DisplayName = "Balanced"),
    Aggressive      UMETA(DisplayName = "Aggressive"),
    Maximum         UMETA(DisplayName = "Maximum")
};

UENUM(BlueprintType)
enum class EPCGNaniteLODStrategy : uint8
{
    Automatic       UMETA(DisplayName = "Automatic"),
    DistanceBased   UMETA(DisplayName = "Distance Based"),
    ScreenSize      UMETA(DisplayName = "Screen Size"),
    Hybrid          UMETA(DisplayName = "Hybrid"),
    Custom          UMETA(DisplayName = "Custom")
};

UENUM(BlueprintType)
enum class EPCGNaniteClusteringMode : uint8
{
    Default         UMETA(DisplayName = "Default"),
    Spatial         UMETA(DisplayName = "Spatial"),
    Material        UMETA(DisplayName = "Material"),
    Performance     UMETA(DisplayName = "Performance"),
    Quality         UMETA(DisplayName = "Quality")
};

UENUM(BlueprintType)
enum class EPCGNaniteCompressionLevel : uint8
{
    None            UMETA(DisplayName = "None"),
    Low             UMETA(DisplayName = "Low"),
    Medium          UMETA(DisplayName = "Medium"),
    High            UMETA(DisplayName = "High"),
    Maximum         UMETA(DisplayName = "Maximum")
};

// Structures for Nanite Configuration
USTRUCT(BlueprintType)
struct AURA_API FPCGNaniteOptimizationConfig
{
    GENERATED_BODY()

    // Overall optimization level
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    EPCGNaniteOptimizationLevel OptimizationLevel = EPCGNaniteOptimizationLevel::Balanced;

    // LOD strategy for Nanite meshes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    EPCGNaniteLODStrategy LODStrategy = EPCGNaniteLODStrategy::Hybrid;

    // Clustering mode for geometry optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clustering")
    EPCGNaniteClusteringMode ClusteringMode = EPCGNaniteClusteringMode::Performance;

    // Compression level for Nanite data
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Compression")
    EPCGNaniteCompressionLevel CompressionLevel = EPCGNaniteCompressionLevel::Medium;

    // Enable automatic Nanite conversion for PCG meshes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Automation")
    bool bAutoConvertToNanite = true;

    // Minimum triangle count for Nanite conversion
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    int32 MinTriangleCountForNanite = 1000;

    // Maximum triangle count per cluster
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clustering")
    int32 MaxTrianglesPerCluster = 512;

    // Target screen size for LOD transitions (in pixels)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float TargetScreenSize = 300.0f;

    // Enable fallback meshes for non-Nanite hardware
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fallback")
    bool bEnableFallbackMeshes = true;

    // Memory budget for Nanite streaming (in MB)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float NaniteMemoryBudgetMB = 512.0f;

    // Enable GPU-driven rendering optimizations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU")
    bool bEnableGPUDrivenRendering = true;

    // Enable occlusion culling optimizations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    bool bEnableOcclusionCulling = true;

    // Distance culling threshold (in UE units, 1 UU = 1 cm)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    float DistanceCullingThreshold = 1000000.0f; // 10km

    FPCGNaniteOptimizationConfig()
    {
        OptimizationLevel = EPCGNaniteOptimizationLevel::Balanced;
        LODStrategy = EPCGNaniteLODStrategy::Hybrid;
        ClusteringMode = EPCGNaniteClusteringMode::Performance;
        CompressionLevel = EPCGNaniteCompressionLevel::Medium;
        bAutoConvertToNanite = true;
        MinTriangleCountForNanite = 1000;
        MaxTrianglesPerCluster = 512;
        TargetScreenSize = 300.0f;
        bEnableFallbackMeshes = true;
        NaniteMemoryBudgetMB = 512.0f;
        bEnableGPUDrivenRendering = true;
        bEnableOcclusionCulling = true;
        DistanceCullingThreshold = 1000000.0f;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGNaniteMeshData
{
    GENERATED_BODY()

    // Original static mesh reference
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh")
    TSoftObjectPtr<UStaticMesh> OriginalMesh;

    // Nanite-optimized mesh reference
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh")
    TSoftObjectPtr<UStaticMesh> NaniteMesh;

    // Fallback mesh for non-Nanite hardware
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh")
    TSoftObjectPtr<UStaticMesh> FallbackMesh;

    // Triangle count of original mesh
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    int32 OriginalTriangleCount = 0;

    // Triangle count of Nanite mesh
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    int32 NaniteTriangleCount = 0;

    // CORRIGIDO: Campo TriangleCount para compatibilidade
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    int32 TriangleCount = 0;

    // Memory usage of Nanite mesh (in bytes)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    int64 NaniteMemoryUsage = 0;

    // Compression ratio achieved
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float CompressionRatio = 1.0f;

    // Is this mesh suitable for Nanite?
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    bool bIsNaniteSuitable = false;

    // Has this mesh been processed?
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsProcessed = false;

    // Processing error message (if any)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FString ProcessingError;

    FPCGNaniteMeshData()
    {
        OriginalTriangleCount = 0;
        NaniteTriangleCount = 0;
        NaniteMemoryUsage = 0;
        CompressionRatio = 1.0f;
        bIsNaniteSuitable = false;
        bIsProcessed = false;
        ProcessingError = TEXT("");
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGNaniteInstanceData
{
    GENERATED_BODY()

    // Instance transform
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform")
    FTransform InstanceTransform = FTransform::Identity;

    // Mesh data reference
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh")
    FPCGNaniteMeshData MeshData;

    // Material overrides
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    TArray<TSoftObjectPtr<UMaterialInterface>> MaterialOverrides;

    // LOD bias for this instance
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float LODBias = 0.0f;

    // Culling distance override
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    float CullingDistance = -1.0f; // -1 means use default

    // Is this instance visible?
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visibility")
    bool bIsVisible = true;

    // Instance priority for streaming
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float StreamingPriority = 1.0f;

    FPCGNaniteInstanceData()
    {
        InstanceTransform = FTransform::Identity;
        LODBias = 0.0f;
        CullingDistance = -1.0f;
        bIsVisible = true;
        StreamingPriority = 1.0f;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGNanitePerformanceStats
{
    GENERATED_BODY()

    // Total number of Nanite instances
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Counts")
    int32 TotalNaniteInstances = 0;

    // Number of visible Nanite instances
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Counts")
    int32 VisibleNaniteInstances = 0;

    // Number of culled instances
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Counts")
    int32 CulledInstances = 0;

    // Total triangles rendered
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Triangles")
    int64 TrianglesRendered = 0;

    // Total triangles culled
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Triangles")
    int64 TrianglesCulled = 0;

    // GPU memory usage (in MB)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float GPUMemoryUsageMB = 0.0f;

    // Streaming memory usage (in MB)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float StreamingMemoryUsageMB = 0.0f;

    // Average frame time for Nanite rendering (in ms)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float AverageFrameTimeMS = 0.0f;

    // GPU utilization percentage
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float GPUUtilizationPercent = 0.0f;

    // Compression efficiency
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Efficiency")
    float CompressionEfficiency = 0.0f;

    FPCGNanitePerformanceStats()
    {
        TotalNaniteInstances = 0;
        VisibleNaniteInstances = 0;
        CulledInstances = 0;
        TrianglesRendered = 0;
        TrianglesCulled = 0;
        GPUMemoryUsageMB = 0.0f;
        StreamingMemoryUsageMB = 0.0f;
        AverageFrameTimeMS = 0.0f;
        GPUUtilizationPercent = 0.0f;
        CompressionEfficiency = 0.0f;
    }
};

/**
 * APCGNaniteOptimizer
 * 
 * Advanced Nanite optimization system for PCG-generated content.
 * Provides automatic conversion, optimization, and management of static meshes
 * for Nanite virtualized geometry in UE5.6.
 * 
 * Features:
 * - Automatic Nanite mesh conversion and optimization
 * - Intelligent LOD strategy management
 * - GPU-driven rendering optimizations
 * - Memory-efficient streaming and caching
 * - Performance monitoring and analytics
 * - Fallback mesh support for non-Nanite hardware
 * - Integration with World Partition and PCG systems
 * - Real-time optimization parameter tuning
 */
UCLASS(BlueprintType, Blueprintable)
class AURA_API APCGNaniteOptimizer : public AActor
{
    GENERATED_BODY()

public:
    APCGNaniteOptimizer();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

public:
    // === Core Nanite Functions ===
    
    UFUNCTION(BlueprintCallable, Category = "PCG Nanite")
    bool InitializeNaniteOptimizer();
    
    UFUNCTION(BlueprintCallable, Category = "PCG Nanite")
    void ShutdownNaniteOptimizer();
    
    UFUNCTION(BlueprintCallable, Category = "PCG Nanite")
    bool SetOptimizationConfig(const FPCGNaniteOptimizationConfig& NewConfig);
    
    UFUNCTION(BlueprintCallable, Category = "PCG Nanite")
    FPCGNaniteOptimizationConfig GetOptimizationConfig() const { return OptimizationConfig; }
    
    // === Mesh Conversion Functions ===
    
    UFUNCTION(BlueprintCallable, Category = "Mesh Conversion")
    bool ConvertMeshToNanite(UStaticMesh* OriginalMesh, FPCGNaniteMeshData& OutMeshData);
    
    UFUNCTION(BlueprintCallable, Category = "Mesh Conversion")
    bool BatchConvertMeshesToNanite(const TArray<UStaticMesh*>& OriginalMeshes, TArray<FPCGNaniteMeshData>& OutMeshDataArray);
    
    UFUNCTION(BlueprintCallable, Category = "Mesh Conversion")
    bool ValidateMeshForNanite(UStaticMesh* Mesh, FString& OutValidationResult);
    
    UFUNCTION(BlueprintCallable, Category = "Mesh Conversion")
    bool OptimizeMeshForNanite(UStaticMesh* Mesh, const FPCGNaniteOptimizationConfig& Config);
    
    // === Instance Management ===
    
    UFUNCTION(BlueprintCallable, Category = "Instance Management")
    bool RegisterNaniteInstance(const FPCGNaniteInstanceData& InstanceData);
    
    UFUNCTION(BlueprintCallable, Category = "Instance Management")
    bool UnregisterNaniteInstance(const FTransform& InstanceTransform);
    
    UFUNCTION(BlueprintCallable, Category = "Instance Management")
    void ClearAllNaniteInstances();
    
    UFUNCTION(BlueprintCallable, Category = "Instance Management")
    TArray<FPCGNaniteInstanceData> GetNaniteInstances() const;
    
    UFUNCTION(BlueprintCallable, Category = "Instance Management")
    int32 GetNaniteInstanceCount() const;
    
    // === LOD Management ===
    
    UFUNCTION(BlueprintCallable, Category = "LOD Management")
    bool SetLODStrategy(EPCGNaniteLODStrategy NewStrategy);
    
    UFUNCTION(BlueprintCallable, Category = "LOD Management")
    bool UpdateLODParameters(float TargetScreenSize, float LODBias);
    
    UFUNCTION(BlueprintCallable, Category = "LOD Management")
    bool SetInstanceLODBias(const FTransform& InstanceTransform, float LODBias);
    
    UFUNCTION(BlueprintCallable, Category = "LOD Management")
    float CalculateOptimalLODBias(const FVector& ViewerLocation, const FTransform& InstanceTransform) const;
    
    // === Culling and Visibility ===
    
    UFUNCTION(BlueprintCallable, Category = "Culling")
    bool SetCullingParameters(float DistanceThreshold, bool bEnableOcclusion);
    
    UFUNCTION(BlueprintCallable, Category = "Culling")
    bool UpdateInstanceVisibility(const FTransform& InstanceTransform, bool bIsVisible);
    
    UFUNCTION(BlueprintCallable, Category = "Culling")
    void UpdateVisibilityFromViewpoint(const FVector& ViewerLocation, const FVector& ViewDirection);
    
    UFUNCTION(BlueprintCallable, Category = "Culling")
    TArray<FPCGNaniteInstanceData> GetVisibleInstances(const FVector& ViewerLocation, float ViewDistance) const;
    
    // === Performance Monitoring ===
    
    UFUNCTION(BlueprintCallable, Category = "Performance")
    FPCGNanitePerformanceStats GetPerformanceStats() const;
    
    UFUNCTION(BlueprintCallable, Category = "Performance")
    void ResetPerformanceStats();
    
    UFUNCTION(BlueprintCallable, Category = "Performance")
    bool EnablePerformanceMonitoring(bool bEnable);
    
    UFUNCTION(BlueprintCallable, Category = "Performance")
    float GetGPUMemoryUsage() const;
    
    UFUNCTION(BlueprintCallable, Category = "Performance")
    float GetCompressionEfficiency() const;
    
    // === Memory Management ===
    
    UFUNCTION(BlueprintCallable, Category = "Memory")
    bool SetMemoryBudget(float MemoryBudgetMB);
    
    UFUNCTION(BlueprintCallable, Category = "Memory")
    void OptimizeMemoryUsage();
    
    UFUNCTION(BlueprintCallable, Category = "Memory")
    bool StreamNaniteData(const FVector& StreamingCenter, float StreamingRadius);
    
    UFUNCTION(BlueprintCallable, Category = "Memory")
    void UnloadDistantNaniteData(const FVector& ViewerLocation, float UnloadDistance);
    
    // === Integration Functions ===
    
    UFUNCTION(BlueprintCallable, Category = "Integration")
    void SetProceduralMapGenerator(AProceduralMapGenerator* Generator);
    
    UFUNCTION(BlueprintCallable, Category = "Integration")
    void SetWorldPartitionManager(APCGWorldPartitionManager* Manager);
    
    UFUNCTION(BlueprintCallable, Category = "Integration")
    bool IntegrateWithPCGComponent(UPCGComponent* PCGComponent);
    
    UFUNCTION(BlueprintCallable, Category = "Integration")
    bool ProcessPCGGeneratedMeshes(const TArray<UStaticMeshComponent*>& MeshComponents);
    
    // === Utility Functions ===
    
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Utility")
    bool IsNaniteSupported() const;
    
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Utility")
    bool IsMeshNaniteEnabled(UStaticMesh* Mesh) const;
    
    UFUNCTION(BlueprintCallable, Category = "Utility")
    FString GetNaniteSystemInfo() const;
    
    UFUNCTION(BlueprintCallable, Category = "Utility")
    bool ExportNaniteOptimizationReport(const FString& FilePath) const;

protected:
    // === Core Properties ===
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FPCGNaniteOptimizationConfig OptimizationConfig;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    bool bIsInitialized;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    bool bIsOptimizing;

    // CORRIGIDO: Campo bNaniteSupported para detecção de suporte
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    bool bNaniteSupported;

    // CORRIGIDO: Campos adicionais para controle robusto
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    bool bAutoOptimizationEnabled;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    bool bMemoryOptimizationEnabled;

    // CORRIGIDO: Timers para controle de performance
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    float LastOptimizationTime;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    float LastPerformanceUpdate;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    float LastMemoryCheck;
    
    // === Mesh Data Storage ===
    
    UPROPERTY()
    TMap<TSoftObjectPtr<UStaticMesh>, FPCGNaniteMeshData> ProcessedMeshes;
    
    UPROPERTY()
    TArray<FPCGNaniteInstanceData> NaniteInstances;
    
    // === Performance Tracking ===
    
    UPROPERTY()
    FPCGNanitePerformanceStats PerformanceStats;
    
    UPROPERTY()
    bool bPerformanceMonitoringEnabled;
    
    // === Integration References ===
    
    UPROPERTY()
    TWeakObjectPtr<AProceduralMapGenerator> ProceduralMapGenerator;
    
    UPROPERTY()
    TWeakObjectPtr<APCGWorldPartitionManager> WorldPartitionManager;
    
    UPROPERTY()
    TArray<TWeakObjectPtr<UPCGComponent>> IntegratedPCGComponents;

    // CORRIGIDO: Containers adicionais usados no código
    UPROPERTY()
    TArray<FPCGNaniteMeshData> OptimizedMeshes;

    UPROPERTY()
    TArray<TSoftObjectPtr<UStaticMesh>> PendingMeshes;

    UPROPERTY()
    TArray<TWeakObjectPtr<UStaticMeshComponent>> NaniteEnabledComponents;

    // === Threading and Async ===

    FThreadSafeBool bIsProcessingMeshes;
    TQueue<TSoftObjectPtr<UStaticMesh>> PendingMeshConversions;
    FRenderCommandFence RenderFence;
    
    // === Internal Functions ===
    
    void UpdatePerformanceStats(float DeltaTime);
    void ProcessPendingMeshConversions();
    void UpdateInstanceLODs(float DeltaTime);
    void UpdateMemoryManagement();
    
    bool ConvertMeshToNaniteInternal(UStaticMesh* OriginalMesh, FPCGNaniteMeshData& OutMeshData);
    bool ValidateMeshForNaniteInternal(UStaticMesh* Mesh) const;
    void OptimizeMeshClustering(UStaticMesh* Mesh, const FPCGNaniteOptimizationConfig& Config);
    
    void UpdateInstanceVisibilityInternal();
    void CullDistantInstances(const FVector& ViewerLocation);
    void UpdateStreamingPriorities(const FVector& StreamingCenter);
    
    float CalculateInstancePriority(const FPCGNaniteInstanceData& Instance, const FVector& ViewerLocation) const;
    bool ShouldInstanceBeVisible(const FPCGNaniteInstanceData& Instance, const FVector& ViewerLocation, const FVector& ViewDirection) const;
    
    void InitializeNaniteSubsystems();
    void ValidateNaniteSupport();
    void SetupDefaultOptimizationSettings();
    
    // === Event Handlers ===
    
    UFUNCTION()
    void OnMeshConversionComplete(UStaticMesh* OriginalMesh, UStaticMesh* NaniteMesh);
    
    UFUNCTION()
    void OnNaniteStreamingUpdate(const FVector& StreamingCenter, float StreamingRadius);
    
    UFUNCTION()
    void OnPerformanceThresholdExceeded(float CurrentUsage, float ThresholdUsage);
};