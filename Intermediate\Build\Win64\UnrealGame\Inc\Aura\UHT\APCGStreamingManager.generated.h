// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "APCGStreamingManager.h"

#ifdef AURA_APCGStreamingManager_generated_h
#error "APCGStreamingManager.generated.h already included, missing '#pragma once' in APCGStreamingManager.h"
#endif
#define AURA_APCGStreamingManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class APCGNaniteOptimizer;
class APCGWorldPartitionManager;
class UObject;
class UPCGPerformanceProfiler;
enum class EPCGStreamingManagerPriority : uint8;
enum class EPCGStreamingMode : uint8;
struct FPCGStreamingAsset;
struct FPCGStreamingRegion;
struct FPCGStreamingStats;

// ********** Begin ScriptStruct FPCGStreamingConfig ***********************************************
#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_91_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGStreamingConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGStreamingConfig;
// ********** End ScriptStruct FPCGStreamingConfig *************************************************

// ********** Begin ScriptStruct FPCGStreamingAsset ************************************************
#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_145_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGStreamingAsset_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGStreamingAsset;
// ********** End ScriptStruct FPCGStreamingAsset **************************************************

// ********** Begin ScriptStruct FPCGStreamingRegion ***********************************************
#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_207_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGStreamingRegion_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGStreamingRegion;
// ********** End ScriptStruct FPCGStreamingRegion *************************************************

// ********** Begin ScriptStruct FPCGStreamingStats ************************************************
#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_256_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGStreamingStats_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGStreamingStats;
// ********** End ScriptStruct FPCGStreamingStats **************************************************

// ********** Begin Delegate FOnAssetLoaded ********************************************************
#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_304_DELEGATE \
AURA_API void FOnAssetLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnAssetLoaded, FPCGStreamingAsset const& Asset, UObject* LoadedObject);


// ********** End Delegate FOnAssetLoaded **********************************************************

// ********** Begin Delegate FOnAssetUnloaded ******************************************************
#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_305_DELEGATE \
AURA_API void FOnAssetUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnAssetUnloaded, FPCGStreamingAsset const& Asset);


// ********** End Delegate FOnAssetUnloaded ********************************************************

// ********** Begin Delegate FOnAssetLoadFailed ****************************************************
#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_306_DELEGATE \
AURA_API void FOnAssetLoadFailed_DelegateWrapper(const FMulticastScriptDelegate& OnAssetLoadFailed, FPCGStreamingAsset const& Asset, const FString& ErrorMessage);


// ********** End Delegate FOnAssetLoadFailed ******************************************************

// ********** Begin Delegate FOnRegionLoaded *******************************************************
#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_307_DELEGATE \
AURA_API void FOnRegionLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnRegionLoaded, const FString& RegionName, bool bSuccess);


// ********** End Delegate FOnRegionLoaded *********************************************************

// ********** Begin Delegate FOnRegionUnloaded *****************************************************
#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_308_DELEGATE \
AURA_API void FOnRegionUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnRegionUnloaded, const FString& RegionName);


// ********** End Delegate FOnRegionUnloaded *******************************************************

// ********** Begin Class APCGStreamingManager *****************************************************
#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_318_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execHandleAssetLoadFailure); \
	DECLARE_FUNCTION(execOnAssetLoadCompleted); \
	DECLARE_FUNCTION(execGetActiveViewers); \
	DECLARE_FUNCTION(execUpdateViewerPosition); \
	DECLARE_FUNCTION(execRemoveStreamingViewer); \
	DECLARE_FUNCTION(execAddStreamingViewer); \
	DECLARE_FUNCTION(execSynchronizeWithPCGSystem); \
	DECLARE_FUNCTION(execIntegrateWithPerformanceProfiler); \
	DECLARE_FUNCTION(execIntegrateWithNanite); \
	DECLARE_FUNCTION(execIntegrateWithWorldPartition); \
	DECLARE_FUNCTION(execGetStreamingEfficiency); \
	DECLARE_FUNCTION(execSetMaxConcurrentLoads); \
	DECLARE_FUNCTION(execSetMaxMemoryUsage); \
	DECLARE_FUNCTION(execClearCache); \
	DECLARE_FUNCTION(execOptimizeMemoryUsage); \
	DECLARE_FUNCTION(execGetStreamingStats); \
	DECLARE_FUNCTION(execPauseStreaming); \
	DECLARE_FUNCTION(execSetStreamingMode); \
	DECLARE_FUNCTION(execUnloadAssetsOutsideRadius); \
	DECLARE_FUNCTION(execPreloadAssetsInRadius); \
	DECLARE_FUNCTION(execForceUnloadAsset); \
	DECLARE_FUNCTION(execForceLoadAsset); \
	DECLARE_FUNCTION(execGetAllRegions); \
	DECLARE_FUNCTION(execIsRegionLoaded); \
	DECLARE_FUNCTION(execUnloadRegion); \
	DECLARE_FUNCTION(execLoadRegion); \
	DECLARE_FUNCTION(execUpdateRegion); \
	DECLARE_FUNCTION(execRemoveRegion); \
	DECLARE_FUNCTION(execCreateRegion); \
	DECLARE_FUNCTION(execGetAllAssets); \
	DECLARE_FUNCTION(execGetLoadedAsset); \
	DECLARE_FUNCTION(execIsAssetLoaded); \
	DECLARE_FUNCTION(execSetAssetPriority); \
	DECLARE_FUNCTION(execUpdateAssetPosition); \
	DECLARE_FUNCTION(execUnregisterAsset); \
	DECLARE_FUNCTION(execRegisterAsset);


AURA_API UClass* Z_Construct_UClass_APCGStreamingManager_NoRegister();

#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_318_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAPCGStreamingManager(); \
	friend struct Z_Construct_UClass_APCGStreamingManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_APCGStreamingManager_NoRegister(); \
public: \
	DECLARE_CLASS2(APCGStreamingManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_APCGStreamingManager_NoRegister) \
	DECLARE_SERIALIZER(APCGStreamingManager)


#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_318_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	APCGStreamingManager(APCGStreamingManager&&) = delete; \
	APCGStreamingManager(const APCGStreamingManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, APCGStreamingManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(APCGStreamingManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(APCGStreamingManager) \
	NO_API virtual ~APCGStreamingManager();


#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_315_PROLOG
#define FID_Aura_Source_Aura_Public_APCGStreamingManager_h_318_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_APCGStreamingManager_h_318_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_APCGStreamingManager_h_318_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_APCGStreamingManager_h_318_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class APCGStreamingManager;

// ********** End Class APCGStreamingManager *******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_APCGStreamingManager_h

// ********** Begin Enum EPCGStreamingManagerPriority **********************************************
#define FOREACH_ENUM_EPCGSTREAMINGMANAGERPRIORITY(op) \
	op(EPCGStreamingManagerPriority::VeryLow) \
	op(EPCGStreamingManagerPriority::Low) \
	op(EPCGStreamingManagerPriority::Medium) \
	op(EPCGStreamingManagerPriority::High) \
	op(EPCGStreamingManagerPriority::Critical) 

enum class EPCGStreamingManagerPriority : uint8;
template<> struct TIsUEnumClass<EPCGStreamingManagerPriority> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGStreamingManagerPriority>();
// ********** End Enum EPCGStreamingManagerPriority ************************************************

// ********** Begin Enum EPCGStreamingState ********************************************************
#define FOREACH_ENUM_EPCGSTREAMINGSTATE(op) \
	op(EPCGStreamingState::Unloaded) \
	op(EPCGStreamingState::Loading) \
	op(EPCGStreamingState::Loaded) \
	op(EPCGStreamingState::Unloading) \
	op(EPCGStreamingState::Failed) \
	op(EPCGStreamingState::Cached) 

enum class EPCGStreamingState : uint8;
template<> struct TIsUEnumClass<EPCGStreamingState> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGStreamingState>();
// ********** End Enum EPCGStreamingState **********************************************************

// ********** Begin Enum EPCGStreamingMode *********************************************************
#define FOREACH_ENUM_EPCGSTREAMINGMODE(op) \
	op(EPCGStreamingMode::Distance) \
	op(EPCGStreamingMode::Frustum) \
	op(EPCGStreamingMode::Hybrid) \
	op(EPCGStreamingMode::Manual) \
	op(EPCGStreamingMode::Adaptive) 

enum class EPCGStreamingMode : uint8;
template<> struct TIsUEnumClass<EPCGStreamingMode> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGStreamingMode>();
// ********** End Enum EPCGStreamingMode ***********************************************************

// ********** Begin Enum EPCGStreamingAssetType ****************************************************
#define FOREACH_ENUM_EPCGSTREAMINGASSETTYPE(op) \
	op(EPCGStreamingAssetType::StaticMesh) \
	op(EPCGStreamingAssetType::Material) \
	op(EPCGStreamingAssetType::Texture) \
	op(EPCGStreamingAssetType::Sound) \
	op(EPCGStreamingAssetType::ParticleSystem) \
	op(EPCGStreamingAssetType::Level) \
	op(EPCGStreamingAssetType::Blueprint) \
	op(EPCGStreamingAssetType::Animation) 

enum class EPCGStreamingAssetType : uint8;
template<> struct TIsUEnumClass<EPCGStreamingAssetType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGStreamingAssetType>();
// ********** End Enum EPCGStreamingAssetType ******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
