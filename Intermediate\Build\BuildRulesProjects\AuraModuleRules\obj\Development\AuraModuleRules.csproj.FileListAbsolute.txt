c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\liboo2corelinux64.so.9
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\liboo2corelinuxarm32.so.9
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\liboo2corelinuxarm64.so.9
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\liboo2coremac64.2.9.10.dylib
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\oo2core_9_win64.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\oo2core_9_win32.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\oo2core_9_winuwparm64.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\runtimes\win-x64\native\UbaHost.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\runtimes\win-x64\native\UbaDetours.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\AuraModuleRules.deps.json
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\AuraModuleRules.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\ref\AuraModuleRules.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\AuraModuleRules.pdb
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.Core.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.Horde.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.IoHash.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.MsBuild.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.OIDC.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.Oodle.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.Serialization.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.UBA.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.UHT.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.Core.pdb
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.Horde.pdb
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.Horde.xml
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.IoHash.pdb
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.MsBuild.pdb
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.OIDC.pdb
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.Oodle.pdb
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.Oodle.xml
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.Serialization.pdb
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.UBA.pdb
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.UBA.xml
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.UHT.pdb
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\bin\Development\EpicGames.UHT.xml
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\obj\Development\AuraModuleRules.csproj.AssemblyReference.cache
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\obj\Development\AuraModuleRules.GeneratedMSBuildEditorConfig.editorconfig
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\obj\Development\AuraModuleRules.AssemblyInfoInputs.cache
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\obj\Development\AuraModuleRules.AssemblyInfo.cs
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\obj\Development\AuraModuleRules.csproj.CoreCompileInputs.cache
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\obj\Development\AuraModu.AA473591.Up2Date
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\obj\Development\AuraModuleRules.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\obj\Development\refint\AuraModuleRules.dll
c:\Aura\Intermediate\Build\BuildRulesProjects\AuraModuleRules\obj\Development\AuraModuleRules.pdb
