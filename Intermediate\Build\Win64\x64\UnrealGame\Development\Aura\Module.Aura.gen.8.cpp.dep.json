{"Version": "1.2", "Data": {"Source": "c:\\aura\\intermediate\\build\\win64\\x64\\unrealgame\\development\\aura\\module.aura.gen.8.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\intermediate\\build\\win64\\x64\\aura\\development\\engine\\sharedpch.engine.project.rtti.exceptions.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\intermediate\\build\\win64\\x64\\unrealgame\\development\\aura\\definitions.aura.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\lanesysteminterface.gen.cpp", "c:\\aura\\source\\aura\\public\\interfaces\\lanesysteminterface.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\lanesysteminterface.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\riversysteminterface.gen.cpp", "c:\\aura\\source\\aura\\public\\interfaces\\riversysteminterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\splinecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\curves\\spline.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\spline.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\splinecomponent.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\riversysteminterface.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\testes_precisao_geometrica.gen.cpp", "c:\\aura\\source\\aura\\public\\testes_precisao_geometrica.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\noexporttypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\serialization\\testundeclaredscriptstructobjectreferences.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\polyglottextdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\assetregistry\\arfilter.h", "c:\\aura\\source\\aura\\public\\implementacao_automatizada.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\implementacao_automatizada.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\testes_precisao_geometrica.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\upcgperformanceprofiler.gen.cpp", "c:\\aura\\source\\aura\\public\\upcgperformanceprofiler.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\stats\\statsdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendercore.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\upcgperformanceprofiler.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}