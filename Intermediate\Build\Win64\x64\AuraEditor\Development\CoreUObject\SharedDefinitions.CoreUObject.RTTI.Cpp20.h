// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared Definitions for CoreUObject.RTTI.Cpp20
#pragma once
#define IS_PROGRAM 0
#define UE_EDITOR 1
#define USE_SHADER_COMPILER_WORKER_TRACE 0
#define UE_REFERENCE_COLLECTOR_REQUIRE_OBJECTPTR 1
#define WITH_VERSE_VM 0
#define ENABLE_PGO_PROFILE 0
#define USE_VORBIS_FOR_STREAMING 1
#define USE_XMA2_FOR_STREAMING 1
#define WITH_DEV_AUTOMATION_TESTS 1
#define WITH_PERF_AUTOMATION_TESTS 1
#define WITH_LOW_LEVEL_TESTS 0
#define EXPLICIT_TESTS_TARGET 0
#define WITH_TESTS 1
#define UNICODE 1
#define _UNICODE 1
#define __UNREAL__ 1
#define IS_MONOLITHIC 0
#define IS_MERGEDMODULES 0
#define WITH_ENGINE 1
#define WITH_UNREAL_DEVELOPER_TOOLS 1
#define WITH_UNREAL_TARGET_DEVELOPER_TOOLS 1
#define WITH_APPLICATION_CORE 1
#define WITH_COREUOBJECT 1
#define UE_TRACE_ENABLED 1
#define UE_TRACE_FORCE_ENABLED 0
#define WITH_VERSE 1
#define UE_USE_VERSE_PATHS 1
#define WITH_VERSE_BPVM 1
#define USE_STATS_WITHOUT_ENGINE 0
#define WITH_PLUGIN_SUPPORT 0
#define WITH_ACCESSIBILITY 1
#define WITH_PERFCOUNTERS 1
#define WITH_FIXED_TIME_STEP_SUPPORT 1
#define USE_LOGGING_IN_SHIPPING 0
#define ALLOW_CONSOLE_IN_SHIPPING 0
#define ALLOW_PROFILEGPU_IN_TEST 0
#define ALLOW_PROFILEGPU_IN_SHIPPING 0
#define WITH_LOGGING_TO_MEMORY 0
#define USE_CACHE_FREED_OS_ALLOCS 1
#define USE_CHECKS_IN_SHIPPING 0
#define USE_UTF8_TCHARS 0
#define USE_ESTIMATED_UTCNOW 0
#define UE_ALLOW_EXEC_COMMANDS_IN_SHIPPING 1
#define WITH_EDITOR 1
#define WITH_IOSTORE_IN_EDITOR 1
#define WITH_CLIENT_CODE 1
#define WITH_SERVER_CODE 1
#define UE_FNAME_OUTLINE_NUMBER 0
#define WITH_PUSH_MODEL 1
#define WITH_CEF3 1
#define WITH_LIVE_CODING 1
#define WITH_CPP_MODULES 0
#define WITH_CPP_COROUTINES 0
#define WITH_PROCESS_PRIORITY_CONTROL 0
#define UBT_MODULE_MANIFEST "UnrealEditor.modules"
#define UBT_MODULE_MANIFEST_DEBUGGAME "UnrealEditor-Win64-DebugGame.modules"
#define UBT_COMPILED_PLATFORM Win64
#define UBT_COMPILED_TARGET Editor
#define UE_APP_NAME "UnrealEditor"
#define UE_WARNINGS_AS_ERRORS 0
#define FORCE_ANSI_ALLOCATOR 0
#define USE_MALLOC_BINNED2 1
#define USE_MALLOC_BINNED3 0
#define NDIS_MINIPORT_MAJOR_VERSION 0
#define WIN32 1
#define _WIN32_WINNT 0x0601
#define WINVER 0x0601
#define PLATFORM_WINDOWS 1
#define PLATFORM_MICROSOFT 1
#define OVERRIDE_PLATFORM_HEADER_NAME Windows
#define RHI_RAYTRACING 1
#define WINDOWS_MAX_NUM_TLS_SLOTS 2048
#define WINDOWS_MAX_NUM_THREADS_WITH_TLS_SLOTS 512
#define NDEBUG 1
#define UE_BUILD_DEVELOPMENT 1
#define UE_IS_ENGINE_MODULE 1
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 0
#define UE_VALIDATE_EXPERIMENTAL_API 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define WITH_VERSE_COMPILER 1
#define COREUOBJECT_API DLLIMPORT
#define UE_MEMORY_TAGS_TRACE_ENABLED 1
#define UE_ENABLE_ICU 1
#define WITH_ADDITIONAL_CRASH_CONTEXTS 1
#define UE_WITH_IRIS 1
#define PLATFORM_SUPPORTS_PLATFORM_EVENTS 1
#define PLATFORM_SUPPORTS_TRACE_WIN32_VIRTUAL_MEMORY_HOOKS 1
#define PLATFORM_SUPPORTS_TRACE_WIN32_MODULE_DIAGNOSTICS 1
#define PLATFORM_SUPPORTS_TRACE_WIN32_CALLSTACK 1
#define UE_MEMORY_TRACE_AVAILABLE 1
#define WITH_MALLOC_STOMP 1
#define UE_MERGED_MODULES 0
#define CORE_API DLLIMPORT
#define GSL_NO_IOSTREAMS 1
#define TRACELOG_API DLLIMPORT
#define AUTORTFM_API DLLIMPORT
#define IMAGECORE_API DLLIMPORT
#define COREPRECISEFP_API DLLIMPORT
