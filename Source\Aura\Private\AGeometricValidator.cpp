// AGeometricValidator.cpp - Implementação do sistema de validação matemática em tempo real
// Unreal Engine 5.6 - APIs modernas
// 1 UU = 1 cm (Unreal Units)

#include "AGeometricValidator.h"
#include "TimerManager.h"
#include "DrawDebugHelpers.h"
#include "Math/UnrealMathUtility.h"
#include "Kismet/KismetMathLibrary.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

AGeometricValidator::AGeometricValidator()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.TickInterval = 0.1f; // 10 FPS para validação

    // Configurar componente raiz
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    RootComponent = RootSceneComponent;

    // Configuração padrão
    ValidationConfig = FValidationConfig();
    bIsValidating = false;
    bStopValidation = false;

    // Inicializar arrays
    ValidationHistory.Empty();
    RegisteredShapes.Empty();
}

void AGeometricValidator::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Sistema de validação matemática iniciado"));

    // Iniciar validação automática se configurado
    if (ValidationConfig.bEnableRealTimeValidation)
    {
        StartRealTimeValidation();
    }
}

void AGeometricValidator::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    StopRealTimeValidation();
    Super::EndPlay(EndPlayReason);
}

void AGeometricValidator::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Desenhar visualização de debug se habilitado
    if (ValidationConfig.bShowDebugVisualization)
    {
        DrawDebugVisualization();
    }
}

// ===== FUNÇÕES PRINCIPAIS =====

void AGeometricValidator::StartRealTimeValidation()
{
    if (bIsValidating)
    {
        UE_LOG(LogTemp, Warning, TEXT("AGeometricValidator: Validação já está em execução"));
        return;
    }

    bIsValidating = true;
    bStopValidation = false;

    // Configurar timer para validação periódica
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().SetTimer(
            ValidationTimerHandle,
            this,
            &AGeometricValidator::PerformPeriodicValidation,
            ValidationConfig.ValidationInterval,
            true // Loop
        );
    }

    UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Validação em tempo real iniciada (Intervalo: %.2f segundos)"), ValidationConfig.ValidationInterval);
}

void AGeometricValidator::StopRealTimeValidation()
{
    if (!bIsValidating)
    {
        return;
    }

    bIsValidating = false;
    bStopValidation = true;

    // Limpar timer
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(ValidationTimerHandle);
    }

    UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Validação em tempo real parada"));
}

TArray<FValidationResult> AGeometricValidator::PerformFullValidation()
{
    TArray<FValidationResult> Results;

    UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Executando validação completa de %d formas registradas"), RegisteredShapes.Num());

    // Validar todas as formas registradas
    for (const FGeometricShape& Shape : RegisteredShapes)
    {
        switch (Shape.ShapeType)
        {
            case EGeometricShape::Hexagon:
            {
                FValidationResult HexResult = ValidateRegularHexagon(Shape.Points, Shape.Radius, ValidationConfig.DistanceTolerance);
                Results.Add(HexResult);
                break;
            }
            case EGeometricShape::Ellipse:
            {
                FValidationResult EllipseResult = ValidateEllipse(Shape.Center, Shape.Dimensions.X, Shape.Dimensions.Y, Shape.Points, ValidationConfig.DistanceTolerance);
                Results.Add(EllipseResult);
                break;
            }
            case EGeometricShape::Rectangle:
            {
                if (Shape.Points.Num() >= 4)
                {
                    float ExpectedArea = Shape.Dimensions.X * Shape.Dimensions.Y;
                    FValidationResult AreaResult = ValidatePolygonArea(Shape.Points, ExpectedArea, ValidationConfig.AreaTolerance);
                    Results.Add(AreaResult);
                }
                break;
            }
            case EGeometricShape::Circle:
            {
                // Validar se os pontos formam um círculo
                for (int32 i = 0; i < Shape.Points.Num(); ++i)
                {
                    float Distance = FVector::Dist(Shape.Center, Shape.Points[i]);
                    FValidationResult DistResult = ValidateDistance(Shape.Center, Shape.Points[i], Shape.Radius, ValidationConfig.DistanceTolerance);
                    Results.Add(DistResult);
                }
                break;
            }
            default:
                break;
        }
    }

    // Broadcast evento de validação completa
    OnValidationComplete.Broadcast(Results);

    UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Validação completa finalizada com %d resultados"), Results.Num());

    return Results;
}

void AGeometricValidator::RegisterShape(const FGeometricShape& Shape)
{
    // Verificar se já existe uma forma com o mesmo ID
    for (int32 i = 0; i < RegisteredShapes.Num(); ++i)
    {
        if (RegisteredShapes[i].ShapeID == Shape.ShapeID)
        {
            RegisteredShapes[i] = Shape; // Atualizar forma existente
            UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Forma '%s' atualizada"), *Shape.ShapeID);
            return;
        }
    }

    // Adicionar nova forma
    RegisteredShapes.Add(Shape);
    UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Forma '%s' registrada (Tipo: %d)"), *Shape.ShapeID, (int32)Shape.ShapeType);
}

void AGeometricValidator::UnregisterShape(const FString& ShapeID)
{
    for (int32 i = RegisteredShapes.Num() - 1; i >= 0; --i)
    {
        if (RegisteredShapes[i].ShapeID == ShapeID)
        {
            RegisteredShapes.RemoveAt(i);
            UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Forma '%s' removida"), *ShapeID);
            return;
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("AGeometricValidator: Forma '%s' não encontrada para remoção"), *ShapeID);
}

void AGeometricValidator::ClearRegisteredShapes()
{
    int32 Count = RegisteredShapes.Num();
    RegisteredShapes.Empty();
    UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: %d formas removidas"), Count);
}

// ===== VALIDAÇÕES ESPECÍFICAS =====

FValidationResult AGeometricValidator::ValidateDistance(const FVector& Point1, const FVector& Point2, float ExpectedDistance, float Tolerance)
{
    float ActualDistance = CalculateDistance(Point1, Point2);
    float Deviation = FMath::Abs(ActualDistance - ExpectedDistance);
    bool bIsValid = Deviation <= Tolerance;

    EValidationSeverity Severity = EValidationSeverity::Info;
    if (Deviation > Tolerance * 2.0f)
    {
        Severity = EValidationSeverity::Error;
    }
    else if (Deviation > Tolerance)
    {
        Severity = EValidationSeverity::Warning;
    }

    FVector MidPoint = (Point1 + Point2) * 0.5f;
    FString Description = FString::Printf(TEXT("Distância: Esperado=%.2f, Atual=%.2f, Desvio=%.2f"), ExpectedDistance, ActualDistance, Deviation);

    FValidationResult Result = CreateValidationResult(
        EValidationType::Distance,
        bIsValid,
        Description,
        MidPoint,
        ExpectedDistance,
        ActualDistance,
        Tolerance,
        Severity
    );

    AddValidationResult(Result);
    return Result;
}

FValidationResult AGeometricValidator::ValidateAngle(const FVector& Point1, const FVector& Vertex, const FVector& Point2, float ExpectedAngle, float Tolerance)
{
    float ActualAngle = CalculateAngle(Point1, Vertex, Point2);
    float Deviation = FMath::Abs(ActualAngle - ExpectedAngle);
    bool bIsValid = Deviation <= Tolerance;

    EValidationSeverity Severity = EValidationSeverity::Info;
    if (Deviation > Tolerance * 2.0f)
    {
        Severity = EValidationSeverity::Error;
    }
    else if (Deviation > Tolerance)
    {
        Severity = EValidationSeverity::Warning;
    }

    FString Description = FString::Printf(TEXT("Ângulo: Esperado=%.2f°, Atual=%.2f°, Desvio=%.2f°"), ExpectedAngle, ActualAngle, Deviation);

    FValidationResult Result = CreateValidationResult(
        EValidationType::Angle,
        bIsValid,
        Description,
        Vertex,
        ExpectedAngle,
        ActualAngle,
        Tolerance,
        Severity
    );

    AddValidationResult(Result);
    return Result;
}

FValidationResult AGeometricValidator::ValidatePolygonArea(const TArray<FVector>& Points, float ExpectedArea, float Tolerance)
{
    float ActualArea = CalculatePolygonArea(Points);
    float Deviation = FMath::Abs(ActualArea - ExpectedArea);
    bool bIsValid = Deviation <= Tolerance;

    EValidationSeverity Severity = EValidationSeverity::Info;
    if (Deviation > Tolerance * 2.0f)
    {
        Severity = EValidationSeverity::Error;
    }
    else if (Deviation > Tolerance)
    {
        Severity = EValidationSeverity::Warning;
    }

    // Calcular centroide
    FVector Centroid = FVector::ZeroVector;
    for (const FVector& Point : Points)
    {
        Centroid += Point;
    }
    if (Points.Num() > 0)
    {
        Centroid /= Points.Num();
    }

    FString Description = FString::Printf(TEXT("Área do Polígono: Esperado=%.2f, Atual=%.2f, Desvio=%.2f"), ExpectedArea, ActualArea, Deviation);

    FValidationResult Result = CreateValidationResult(
        EValidationType::Area,
        bIsValid,
        Description,
        Centroid,
        ExpectedArea,
        ActualArea,
        Tolerance,
        Severity
    );

    AddValidationResult(Result);
    return Result;
}

FValidationResult AGeometricValidator::ValidateRegularHexagon(const TArray<FVector>& HexagonPoints, float ExpectedSideLength, float Tolerance)
{
    if (HexagonPoints.Num() != 6)
    {
        FValidationResult Result = CreateValidationResult(
            EValidationType::Distance,
            false,
            TEXT("Hexágono deve ter exatamente 6 pontos"),
            FVector::ZeroVector,
            6.0f,
            HexagonPoints.Num(),
            0.0f,
            EValidationSeverity::Error
        );
        AddValidationResult(Result);
        return Result;
    }

    // Validar comprimento de cada lado
    float TotalDeviation = 0.0f;
    int32 ValidSides = 0;

    for (int32 i = 0; i < 6; ++i)
    {
        int32 NextIndex = (i + 1) % 6;
        float SideLength = CalculateDistance(HexagonPoints[i], HexagonPoints[NextIndex]);
        float SideDeviation = FMath::Abs(SideLength - ExpectedSideLength);
        
        if (SideDeviation <= Tolerance)
        {
            ValidSides++;
        }
        
        TotalDeviation += SideDeviation;
    }

    float AverageDeviation = TotalDeviation / 6.0f;
    bool bIsValid = ValidSides >= 5; // Permitir 1 lado com desvio maior

    EValidationSeverity Severity = EValidationSeverity::Info;
    if (ValidSides < 4)
    {
        Severity = EValidationSeverity::Error;
    }
    else if (ValidSides < 6)
    {
        Severity = EValidationSeverity::Warning;
    }

    // Calcular centro do hexágono
    FVector Center = FVector::ZeroVector;
    for (const FVector& Point : HexagonPoints)
    {
        Center += Point;
    }
    Center /= 6.0f;

    FString Description = FString::Printf(TEXT("Hexágono Regular: %d/6 lados válidos, Desvio médio=%.2f"), ValidSides, AverageDeviation);

    FValidationResult Result = CreateValidationResult(
        EValidationType::Distance,
        bIsValid,
        Description,
        Center,
        ExpectedSideLength,
        ExpectedSideLength - AverageDeviation,
        Tolerance,
        Severity
    );

    AddValidationResult(Result);
    return Result;
}

FValidationResult AGeometricValidator::ValidateEllipse(const FVector& Center, float SemiMajorAxis, float SemiMinorAxis, const TArray<FVector>& EllipsePoints, float Tolerance)
{
    if (EllipsePoints.Num() == 0)
    {
        FValidationResult Result = CreateValidationResult(
            EValidationType::Distance,
            false,
            TEXT("Elipse deve ter pelo menos 1 ponto"),
            Center,
            1.0f,
            0.0f,
            0.0f,
            EValidationSeverity::Error
        );
        AddValidationResult(Result);
        return Result;
    }

    float TotalDeviation = 0.0f;
    int32 ValidPoints = 0;

    for (const FVector& Point : EllipsePoints)
    {
        // Calcular distância do ponto ao centro
        FVector RelativePoint = Point - Center;
        
        // Calcular se o ponto está na elipse usando a equação da elipse
        float X = RelativePoint.X;
        float Y = RelativePoint.Y;
        
        float EllipseValue = (X * X) / (SemiMajorAxis * SemiMajorAxis) + (Y * Y) / (SemiMinorAxis * SemiMinorAxis);
        float Deviation = FMath::Abs(EllipseValue - 1.0f) * FMath::Min(SemiMajorAxis, SemiMinorAxis);
        
        if (Deviation <= Tolerance)
        {
            ValidPoints++;
        }
        
        TotalDeviation += Deviation;
    }

    float AverageDeviation = TotalDeviation / EllipsePoints.Num();
    float ValidPercentage = (float)ValidPoints / EllipsePoints.Num();
    bool bIsValid = ValidPercentage >= 0.8f; // 80% dos pontos devem ser válidos

    EValidationSeverity Severity = EValidationSeverity::Info;
    if (ValidPercentage < 0.6f)
    {
        Severity = EValidationSeverity::Error;
    }
    else if (ValidPercentage < 0.8f)
    {
        Severity = EValidationSeverity::Warning;
    }

    FString Description = FString::Printf(TEXT("Elipse: %d/%d pontos válidos (%.1f%%), Desvio médio=%.2f"), ValidPoints, EllipsePoints.Num(), ValidPercentage * 100.0f, AverageDeviation);

    FValidationResult Result = CreateValidationResult(
        EValidationType::Distance,
        bIsValid,
        Description,
        Center,
        1.0f,
        ValidPercentage,
        Tolerance,
        Severity
    );

    AddValidationResult(Result);
    return Result;
}

FValidationResult AGeometricValidator::ValidateSinusoidalCurve(const TArray<FVector>& CurvePoints, float Amplitude, float Frequency, float Phase, float Tolerance)
{
    if (CurvePoints.Num() < 2)
    {
        FValidationResult Result = CreateValidationResult(
            EValidationType::Distance,
            false,
            TEXT("Curva senoidal deve ter pelo menos 2 pontos"),
            FVector::ZeroVector,
            2.0f,
            CurvePoints.Num(),
            0.0f,
            EValidationSeverity::Error
        );
        AddValidationResult(Result);
        return Result;
    }

    float TotalDeviation = 0.0f;
    int32 ValidPoints = 0;
    FVector CurveCenter = FVector::ZeroVector;

    // Assumir que os pontos estão ordenados ao longo do eixo X
    float MinX = CurvePoints[0].X;
    float MaxX = CurvePoints[0].X;
    
    for (const FVector& Point : CurvePoints)
    {
        MinX = FMath::Min(MinX, Point.X);
        MaxX = FMath::Max(MaxX, Point.X);
        CurveCenter += Point;
    }
    CurveCenter /= CurvePoints.Num();

    float CurveLength = MaxX - MinX;
    
    for (const FVector& Point : CurvePoints)
    {
        // Normalizar X para [0, 1]
        float NormalizedX = (Point.X - MinX) / CurveLength;
        
        // Calcular Y esperado usando função senoidal
        float ExpectedY = CurveCenter.Y + Amplitude * FMath::Sin(2.0f * PI * Frequency * NormalizedX + Phase);
        
        float Deviation = FMath::Abs(Point.Y - ExpectedY);
        
        if (Deviation <= Tolerance)
        {
            ValidPoints++;
        }
        
        TotalDeviation += Deviation;
    }

    float AverageDeviation = TotalDeviation / CurvePoints.Num();
    float ValidPercentage = (float)ValidPoints / CurvePoints.Num();
    bool bIsValid = ValidPercentage >= 0.8f;

    EValidationSeverity Severity = EValidationSeverity::Info;
    if (ValidPercentage < 0.6f)
    {
        Severity = EValidationSeverity::Error;
    }
    else if (ValidPercentage < 0.8f)
    {
        Severity = EValidationSeverity::Warning;
    }

    FString Description = FString::Printf(TEXT("Curva Senoidal: %d/%d pontos válidos (%.1f%%), Desvio médio=%.2f"), ValidPoints, CurvePoints.Num(), ValidPercentage * 100.0f, AverageDeviation);

    FValidationResult Result = CreateValidationResult(
        EValidationType::Distance,
        bIsValid,
        Description,
        CurveCenter,
        Amplitude,
        Amplitude - AverageDeviation,
        Tolerance,
        Severity
    );

    AddValidationResult(Result);
    return Result;
}

FValidationResult AGeometricValidator::ValidateParallelism(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, float Tolerance)
{
    bool bAreParallel = AreParallel(Line1Start, Line1End, Line2Start, Line2End, Tolerance);
    
    FVector Line1Dir = (Line1End - Line1Start).GetSafeNormal();
    FVector Line2Dir = (Line2End - Line2Start).GetSafeNormal();
    
    float DotProduct = FMath::Abs(FVector::DotProduct(Line1Dir, Line2Dir));
    float AngleDeviation = FMath::RadiansToDegrees(FMath::Acos(FMath::Clamp(DotProduct, 0.0f, 1.0f)));
    
    // Para linhas paralelas, o ângulo deve ser 0° ou 180°
    float ParallelDeviation = FMath::Min(AngleDeviation, FMath::Abs(180.0f - AngleDeviation));
    
    EValidationSeverity Severity = EValidationSeverity::Info;
    if (ParallelDeviation > Tolerance * 2.0f)
    {
        Severity = EValidationSeverity::Error;
    }
    else if (ParallelDeviation > Tolerance)
    {
        Severity = EValidationSeverity::Warning;
    }

    FVector MidPoint = (Line1Start + Line1End + Line2Start + Line2End) * 0.25f;
    FString Description = FString::Printf(TEXT("Paralelismo: Desvio angular=%.2f°"), ParallelDeviation);

    FValidationResult Result = CreateValidationResult(
        EValidationType::Parallelism,
        bAreParallel,
        Description,
        MidPoint,
        0.0f,
        ParallelDeviation,
        Tolerance,
        Severity
    );

    AddValidationResult(Result);
    return Result;
}

FValidationResult AGeometricValidator::ValidatePerpendicularity(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, float Tolerance)
{
    bool bArePerpendicular = ArePerpendicular(Line1Start, Line1End, Line2Start, Line2End, Tolerance);
    
    FVector Line1Dir = (Line1End - Line1Start).GetSafeNormal();
    FVector Line2Dir = (Line2End - Line2Start).GetSafeNormal();
    
    float DotProduct = FMath::Abs(FVector::DotProduct(Line1Dir, Line2Dir));
    float AngleDeviation = FMath::Abs(90.0f - FMath::RadiansToDegrees(FMath::Acos(FMath::Clamp(DotProduct, 0.0f, 1.0f))));
    
    EValidationSeverity Severity = EValidationSeverity::Info;
    if (AngleDeviation > Tolerance * 2.0f)
    {
        Severity = EValidationSeverity::Error;
    }
    else if (AngleDeviation > Tolerance)
    {
        Severity = EValidationSeverity::Warning;
    }

    FVector MidPoint = (Line1Start + Line1End + Line2Start + Line2End) * 0.25f;
    FString Description = FString::Printf(TEXT("Perpendicularidade: Desvio de 90°=%.2f°"), AngleDeviation);

    FValidationResult Result = CreateValidationResult(
        EValidationType::Perpendicularity,
        bArePerpendicular,
        Description,
        MidPoint,
        90.0f,
        90.0f - AngleDeviation,
        Tolerance,
        Severity
    );

    AddValidationResult(Result);
    return Result;
}

FValidationResult AGeometricValidator::ValidateLineIntersection(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, const FVector& ExpectedIntersection, float Tolerance)
{
    FVector ActualIntersection;
    bool bHasIntersection = CalculateLineIntersection(Line1Start, Line1End, Line2Start, Line2End, ActualIntersection);
    
    if (!bHasIntersection)
    {
        FValidationResult Result = CreateValidationResult(
            EValidationType::Intersection,
            false,
            TEXT("Linhas não se intersectam"),
            ExpectedIntersection,
            0.0f,
            -1.0f,
            Tolerance,
            EValidationSeverity::Error
        );
        AddValidationResult(Result);
        return Result;
    }

    float IntersectionDeviation = CalculateDistance(ActualIntersection, ExpectedIntersection);
    bool bIsValid = IntersectionDeviation <= Tolerance;

    EValidationSeverity Severity = EValidationSeverity::Info;
    if (IntersectionDeviation > Tolerance * 2.0f)
    {
        Severity = EValidationSeverity::Error;
    }
    else if (IntersectionDeviation > Tolerance)
    {
        Severity = EValidationSeverity::Warning;
    }

    FString Description = FString::Printf(TEXT("Intersecção: Desvio=%.2f"), IntersectionDeviation);

    FValidationResult Result = CreateValidationResult(
        EValidationType::Intersection,
        bIsValid,
        Description,
        ActualIntersection,
        0.0f,
        IntersectionDeviation,
        Tolerance,
        Severity
    );

    AddValidationResult(Result);
    return Result;
}

FValidationResult AGeometricValidator::ValidatePointInPolygon(const FVector& Point, const TArray<FVector>& PolygonPoints, bool bExpectedInside)
{
    bool bActuallyInside = IsPointInPolygon(Point, PolygonPoints);
    bool bIsValid = (bActuallyInside == bExpectedInside);

    EValidationSeverity Severity = bIsValid ? EValidationSeverity::Info : EValidationSeverity::Error;

    FString Description = FString::Printf(TEXT("Ponto no Polígono: Esperado=%s, Atual=%s"), 
        bExpectedInside ? TEXT("Dentro") : TEXT("Fora"),
        bActuallyInside ? TEXT("Dentro") : TEXT("Fora"));

    FValidationResult Result = CreateValidationResult(
        EValidationType::Containment,
        bIsValid,
        Description,
        Point,
        bExpectedInside ? 1.0f : 0.0f,
        bActuallyInside ? 1.0f : 0.0f,
        0.0f,
        Severity
    );

    AddValidationResult(Result);
    return Result;
}

FValidationResult AGeometricValidator::ValidateSymmetry(const TArray<FVector>& Points, const FVector& SymmetryAxis, float Tolerance)
{
    if (Points.Num() % 2 != 0)
    {
        FValidationResult Result = CreateValidationResult(
            EValidationType::Symmetry,
            false,
            TEXT("Número ímpar de pontos para validação de simetria"),
            SymmetryAxis,
            Points.Num(),
            Points.Num(),
            0.0f,
            EValidationSeverity::Warning
        );
        AddValidationResult(Result);
        return Result;
    }

    int32 ValidPairs = 0;
    float TotalDeviation = 0.0f;
    int32 HalfPoints = Points.Num() / 2;

    for (int32 i = 0; i < HalfPoints; ++i)
    {
        FVector Point1 = Points[i];
        FVector Point2 = Points[Points.Num() - 1 - i];
        
        // Calcular ponto espelhado de Point1 em relação ao eixo de simetria
        FVector MirroredPoint1 = Point1 + 2.0f * (SymmetryAxis - Point1);
        
        float Deviation = CalculateDistance(MirroredPoint1, Point2);
        
        if (Deviation <= Tolerance)
        {
            ValidPairs++;
        }
        
        TotalDeviation += Deviation;
    }

    float AverageDeviation = TotalDeviation / HalfPoints;
    float ValidPercentage = (float)ValidPairs / HalfPoints;
    bool bIsValid = ValidPercentage >= 0.8f;

    EValidationSeverity Severity = EValidationSeverity::Info;
    if (ValidPercentage < 0.6f)
    {
        Severity = EValidationSeverity::Error;
    }
    else if (ValidPercentage < 0.8f)
    {
        Severity = EValidationSeverity::Warning;
    }

    FString Description = FString::Printf(TEXT("Simetria: %d/%d pares válidos (%.1f%%), Desvio médio=%.2f"), ValidPairs, HalfPoints, ValidPercentage * 100.0f, AverageDeviation);

    FValidationResult Result = CreateValidationResult(
        EValidationType::Symmetry,
        bIsValid,
        Description,
        SymmetryAxis,
        1.0f,
        ValidPercentage,
        Tolerance,
        Severity
    );

    AddValidationResult(Result);
    return Result;
}

// ===== VALIDAÇÕES DE LANE =====

TArray<FValidationResult> AGeometricValidator::ValidateLaneGeometry(const TArray<FVector>& LanePoints, float ExpectedWidth, float ExpectedLength)
{
    TArray<FValidationResult> Results;

    if (LanePoints.Num() < 2)
    {
        FValidationResult Result = CreateValidationResult(
            EValidationType::Distance,
            false,
            TEXT("Lane deve ter pelo menos 2 pontos"),
            FVector::ZeroVector,
            2.0f,
            LanePoints.Num(),
            0.0f,
            EValidationSeverity::Error
        );
        Results.Add(Result);
        return Results;
    }

    // Validar comprimento da lane
    float ActualLength = 0.0f;
    for (int32 i = 0; i < LanePoints.Num() - 1; ++i)
    {
        ActualLength += CalculateDistance(LanePoints[i], LanePoints[i + 1]);
    }

    FValidationResult LengthResult = ValidateDistance(LanePoints[0], LanePoints.Last(), ExpectedLength, ValidationConfig.DistanceTolerance);
    Results.Add(LengthResult);

    // Validar linearidade (pontos devem estar aproximadamente em linha reta)
    if (LanePoints.Num() > 2)
    {
        FVector StartToEnd = (LanePoints.Last() - LanePoints[0]).GetSafeNormal();
        float MaxDeviation = 0.0f;
        
        for (int32 i = 1; i < LanePoints.Num() - 1; ++i)
        {
            FVector PointOnLine = LanePoints[0] + StartToEnd * FVector::DotProduct(LanePoints[i] - LanePoints[0], StartToEnd);
            float Deviation = CalculateDistance(LanePoints[i], PointOnLine);
            MaxDeviation = FMath::Max(MaxDeviation, Deviation);
        }

        bool bIsLinear = MaxDeviation <= ExpectedWidth * 0.1f; // 10% da largura
        EValidationSeverity Severity = bIsLinear ? EValidationSeverity::Info : EValidationSeverity::Warning;
        
        FValidationResult LinearityResult = CreateValidationResult(
            EValidationType::Distance,
            bIsLinear,
            FString::Printf(TEXT("Linearidade da Lane: Desvio máximo=%.2f"), MaxDeviation),
            LanePoints[LanePoints.Num() / 2],
            0.0f,
            MaxDeviation,
            ExpectedWidth * 0.1f,
            Severity
        );
        Results.Add(LinearityResult);
    }

    return Results;
}

TArray<FValidationResult> AGeometricValidator::ValidateLaneWaypoints(const TArray<FVector>& Waypoints, float ExpectedSpacing)
{
    TArray<FValidationResult> Results;

    if (Waypoints.Num() < 2)
    {
        FValidationResult Result = CreateValidationResult(
            EValidationType::Distance,
            false,
            TEXT("Waypoints devem ter pelo menos 2 pontos"),
            FVector::ZeroVector,
            2.0f,
            Waypoints.Num(),
            0.0f,
            EValidationSeverity::Error
        );
        Results.Add(Result);
        return Results;
    }

    // Validar espaçamento entre waypoints
    for (int32 i = 0; i < Waypoints.Num() - 1; ++i)
    {
        FValidationResult SpacingResult = ValidateDistance(Waypoints[i], Waypoints[i + 1], ExpectedSpacing, ValidationConfig.DistanceTolerance);
        Results.Add(SpacingResult);
    }

    return Results;
}

TArray<FValidationResult> AGeometricValidator::ValidateTowerPositions(const TArray<FVector>& TowerPositions, const TArray<FVector>& LanePoints, float MinDistanceFromLane)
{
    TArray<FValidationResult> Results;

    for (const FVector& TowerPos : TowerPositions)
    {
        float MinDistance = FLT_MAX;
        FVector ClosestLanePoint = FVector::ZeroVector;
        
        // Encontrar ponto mais próximo na lane
        for (const FVector& LanePoint : LanePoints)
        {
            float Distance = CalculateDistance(TowerPos, LanePoint);
            if (Distance < MinDistance)
            {
                MinDistance = Distance;
                ClosestLanePoint = LanePoint;
            }
        }

        bool bIsValidDistance = MinDistance >= MinDistanceFromLane;
        EValidationSeverity Severity = bIsValidDistance ? EValidationSeverity::Info : EValidationSeverity::Error;
        
        FValidationResult DistanceResult = CreateValidationResult(
            EValidationType::Distance,
            bIsValidDistance,
            FString::Printf(TEXT("Distância Torre-Lane: %.2f (Mín: %.2f)"), MinDistance, MinDistanceFromLane),
            TowerPos,
            MinDistanceFromLane,
            MinDistance,
            ValidationConfig.DistanceTolerance,
            Severity
        );
        Results.Add(DistanceResult);
    }

    return Results;
}

// ===== VALIDAÇÕES DE OBJETIVOS =====

TArray<FValidationResult> AGeometricValidator::ValidateBaronHexagonalArea(const FVector& Center, float Radius, const TArray<FVector>& HexagonPoints)
{
    TArray<FValidationResult> Results;

    // Validar se é um hexágono regular
    FValidationResult HexResult = ValidateRegularHexagon(HexagonPoints, Radius, ValidationConfig.DistanceTolerance);
    Results.Add(HexResult);

    // Validar se o centro está correto
    FVector CalculatedCenter = FVector::ZeroVector;
    for (const FVector& Point : HexagonPoints)
    {
        CalculatedCenter += Point;
    }
    if (HexagonPoints.Num() > 0)
    {
        CalculatedCenter /= HexagonPoints.Num();
    }

    FValidationResult CenterResult = ValidateDistance(Center, CalculatedCenter, 0.0f, ValidationConfig.DistanceTolerance);
    Results.Add(CenterResult);

    return Results;
}

TArray<FValidationResult> AGeometricValidator::ValidateDragonEllipticalArea(const FVector& Center, float SemiMajorAxis, float SemiMinorAxis, const TArray<FVector>& EllipsePoints)
{
    TArray<FValidationResult> Results;

    // Validar geometria elíptica
    FValidationResult EllipseResult = ValidateEllipse(Center, SemiMajorAxis, SemiMinorAxis, EllipsePoints, ValidationConfig.DistanceTolerance);
    Results.Add(EllipseResult);

    // Validar área da elipse
    float ExpectedArea = PI * SemiMajorAxis * SemiMinorAxis;
    FValidationResult AreaResult = ValidatePolygonArea(EllipsePoints, ExpectedArea, ValidationConfig.AreaTolerance);
    Results.Add(AreaResult);

    return Results;
}

// ===== VALIDAÇÕES DE RIO =====

TArray<FValidationResult> AGeometricValidator::ValidateRiverGeometry(const TArray<FVector>& RiverPoints, float Amplitude, float Frequency, float Width)
{
    TArray<FValidationResult> Results;

    // Validar curva senoidal
    FValidationResult CurveResult = ValidateSinusoidalCurve(RiverPoints, Amplitude, Frequency, 0.0f, ValidationConfig.DistanceTolerance);
    Results.Add(CurveResult);

    // Validar largura do rio (se temos pontos das bordas)
    if (RiverPoints.Num() >= 4)
    {
        // Assumir que metade dos pontos são de uma borda e metade da outra
        int32 HalfPoints = RiverPoints.Num() / 2;
        
        for (int32 i = 0; i < HalfPoints; ++i)
        {
            if (i + HalfPoints < RiverPoints.Num())
            {
                FValidationResult WidthResult = ValidateDistance(RiverPoints[i], RiverPoints[i + HalfPoints], Width, ValidationConfig.DistanceTolerance);
                Results.Add(WidthResult);
            }
        }
    }

    return Results;
}

TArray<FValidationResult> AGeometricValidator::ValidateRiverIsland(const FVector& IslandCenter, float IslandRadius, const TArray<FVector>& RiverPoints)
{
    TArray<FValidationResult> Results;

    // Validar se a ilha não intersecta com o rio
    for (const FVector& RiverPoint : RiverPoints)
    {
        float DistanceToIsland = CalculateDistance(IslandCenter, RiverPoint);
        bool bIsSafeDistance = DistanceToIsland >= IslandRadius;
        
        EValidationSeverity Severity = bIsSafeDistance ? EValidationSeverity::Info : EValidationSeverity::Error;
        
        FValidationResult SafetyResult = CreateValidationResult(
            EValidationType::Distance,
            bIsSafeDistance,
            FString::Printf(TEXT("Distância Ilha-Rio: %.2f (Raio: %.2f)"), DistanceToIsland, IslandRadius),
            RiverPoint,
            IslandRadius,
            DistanceToIsland,
            ValidationConfig.DistanceTolerance,
            Severity
        );
        Results.Add(SafetyResult);
    }

    return Results;
}

// ===== GETTERS =====

// ===== FUNÇÕES INTERNAS =====

void AGeometricValidator::PerformPeriodicValidation()
{
    if (bStopValidation || !bIsValidating)
    {
        return;
    }

    // Executar validação assíncrona para não bloquear o thread principal
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this]()
    {
        if (!bStopValidation)
        {
            TArray<FValidationResult> Results = PerformFullValidation();
            
            // Voltar ao thread principal para broadcast
            AsyncTask(ENamedThreads::GameThread, [this, Results]()
            {
                for (const FValidationResult& Result : Results)
                {
                    OnValidationResult.Broadcast(Result);
                    
                    if (!Result.bIsValid && Result.Severity >= EValidationSeverity::Warning)
                    {
                        OnValidationError.Broadcast(Result.Description, Result.Severity);
                    }
                }
            });
        }
    });
}

void AGeometricValidator::AddValidationResult(const FValidationResult& Result)
{
    ValidationHistory.Add(Result);
    
    // Limpar histórico se necessário
    if (ValidationHistory.Num() > ValidationConfig.MaxValidationHistory)
    {
        CleanupValidationHistory();
    }

    // Log se habilitado
    if (ValidationConfig.bLogValidationResults)
    {
        LogValidationResult(Result);
    }

    // Broadcast evento
    OnValidationResult.Broadcast(Result);
    
    if (!Result.bIsValid && Result.Severity >= EValidationSeverity::Warning)
    {
        OnValidationError.Broadcast(Result.Description, Result.Severity);
    }
}

void AGeometricValidator::CleanupValidationHistory()
{
    // Remover metade dos resultados mais antigos
    int32 RemoveCount = ValidationHistory.Num() / 2;
    if (RemoveCount > 0)
    {
        ValidationHistory.RemoveAt(0, RemoveCount);
        UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: %d resultados antigos removidos do histórico"), RemoveCount);
    }
}

FValidationResult AGeometricValidator::CreateValidationResult(EValidationType Type, bool bIsValid, const FString& Description, const FVector& Location, float Expected, float Actual, float Tolerance, EValidationSeverity Severity)
{
    FValidationResult Result;
    Result.bIsValid = bIsValid;
    Result.ValidationType = Type;
    Result.Severity = Severity;
    Result.Description = Description;
    Result.Location = Location;
    Result.ExpectedValue = Expected;
    Result.ActualValue = Actual;
    Result.Tolerance = Tolerance;
    Result.Deviation = FMath::Abs(Expected - Actual);
    Result.Timestamp = FDateTime::Now();
    
    return Result;
}

// ===== FUNÇÕES MATEMÁTICAS =====

float AGeometricValidator::CalculateDistance(const FVector& Point1, const FVector& Point2) const
{
    return FVector::Dist(Point1, Point2);
}

float AGeometricValidator::CalculateAngle(const FVector& Point1, const FVector& Vertex, const FVector& Point2) const
{
    FVector Vec1 = (Point1 - Vertex).GetSafeNormal();
    FVector Vec2 = (Point2 - Vertex).GetSafeNormal();
    
    float DotProduct = FVector::DotProduct(Vec1, Vec2);
    DotProduct = FMath::Clamp(DotProduct, -1.0f, 1.0f);
    
    return FMath::RadiansToDegrees(FMath::Acos(DotProduct));
}

float AGeometricValidator::CalculatePolygonArea(const TArray<FVector>& Points) const
{
    if (Points.Num() < 3)
    {
        return 0.0f;
    }

    // Usar fórmula do Shoelace (algoritmo de Gauss)
    float Area = 0.0f;
    int32 NumPoints = Points.Num();
    
    for (int32 i = 0; i < NumPoints; ++i)
    {
        int32 j = (i + 1) % NumPoints;
        Area += Points[i].X * Points[j].Y;
        Area -= Points[j].X * Points[i].Y;
    }
    
    return FMath::Abs(Area) * 0.5f;
}

bool AGeometricValidator::IsPointInPolygon(const FVector& Point, const TArray<FVector>& PolygonPoints) const
{
    if (PolygonPoints.Num() < 3)
    {
        return false;
    }

    // Algoritmo Ray Casting
    bool bInside = false;
    int32 NumPoints = PolygonPoints.Num();
    
    for (int32 i = 0, j = NumPoints - 1; i < NumPoints; j = i++)
    {
        const FVector& Pi = PolygonPoints[i];
        const FVector& Pj = PolygonPoints[j];
        
        if (((Pi.Y > Point.Y) != (Pj.Y > Point.Y)) &&
            (Point.X < (Pj.X - Pi.X) * (Point.Y - Pi.Y) / (Pj.Y - Pi.Y) + Pi.X))
        {
            bInside = !bInside;
        }
    }
    
    return bInside;
}

bool AGeometricValidator::CalculateLineIntersection(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, FVector& OutIntersection) const
{
    FVector Dir1 = Line1End - Line1Start;
    FVector Dir2 = Line2End - Line2Start;
    FVector StartDiff = Line2Start - Line1Start;
    
    float Cross = Dir1.X * Dir2.Y - Dir1.Y * Dir2.X;
    
    // Linhas paralelas
    if (FMath::IsNearlyZero(Cross, KINDA_SMALL_NUMBER))
    {
        return false;
    }
    
    float t1 = (StartDiff.X * Dir2.Y - StartDiff.Y * Dir2.X) / Cross;
    float t2 = (StartDiff.X * Dir1.Y - StartDiff.Y * Dir1.X) / Cross;
    
    // Verificar se a intersecção está dentro dos segmentos
    if (t1 >= 0.0f && t1 <= 1.0f && t2 >= 0.0f && t2 <= 1.0f)
    {
        OutIntersection = Line1Start + t1 * Dir1;
        return true;
    }
    
    return false;
}

bool AGeometricValidator::AreParallel(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, float Tolerance) const
{
    FVector Dir1 = (Line1End - Line1Start).GetSafeNormal();
    FVector Dir2 = (Line2End - Line2Start).GetSafeNormal();
    
    float DotProduct = FMath::Abs(FVector::DotProduct(Dir1, Dir2));
    float AngleDifference = FMath::RadiansToDegrees(FMath::Acos(FMath::Clamp(DotProduct, 0.0f, 1.0f)));
    
    // Para linhas paralelas, o ângulo deve ser 0° ou 180°
    float ParallelDeviation = FMath::Min(AngleDifference, FMath::Abs(180.0f - AngleDifference));
    
    return ParallelDeviation <= Tolerance;
}

bool AGeometricValidator::ArePerpendicular(const FVector& Line1Start, const FVector& Line1End, const FVector& Line2Start, const FVector& Line2End, float Tolerance) const
{
    FVector Dir1 = (Line1End - Line1Start).GetSafeNormal();
    FVector Dir2 = (Line2End - Line2Start).GetSafeNormal();
    
    float DotProduct = FMath::Abs(FVector::DotProduct(Dir1, Dir2));
    float AngleDifference = FMath::RadiansToDegrees(FMath::Acos(FMath::Clamp(DotProduct, 0.0f, 1.0f)));
    
    // Para linhas perpendiculares, o ângulo deve ser 90°
    float PerpendicularDeviation = FMath::Abs(90.0f - AngleDifference);
    
    return PerpendicularDeviation <= Tolerance;
}

TArray<FVector> AGeometricValidator::GenerateRegularHexagonPoints(const FVector& Center, float Radius) const
{
    TArray<FVector> Points;
    Points.Reserve(6);
    
    for (int32 i = 0; i < 6; ++i)
    {
        float Angle = (i * 60.0f) * PI / 180.0f; // 60 graus em radianos
        FVector Point = Center + FVector(
            Radius * FMath::Cos(Angle),
            Radius * FMath::Sin(Angle),
            Center.Z
        );
        Points.Add(Point);
    }
    
    return Points;
}

TArray<FVector> AGeometricValidator::GenerateEllipsePoints(const FVector& Center, float SemiMajorAxis, float SemiMinorAxis, int32 NumPoints) const
{
    TArray<FVector> Points;
    Points.Reserve(NumPoints);
    
    for (int32 i = 0; i < NumPoints; ++i)
    {
        float Angle = (i * 360.0f / NumPoints) * PI / 180.0f;
        FVector Point = Center + FVector(
            SemiMajorAxis * FMath::Cos(Angle),
            SemiMinorAxis * FMath::Sin(Angle),
            Center.Z
        );
        Points.Add(Point);
    }
    
    return Points;
}

TArray<FVector> AGeometricValidator::GenerateSinusoidalPoints(const FVector& Start, const FVector& End, float Amplitude, float Frequency, float Phase, int32 NumPoints) const
{
    TArray<FVector> Points;
    Points.Reserve(NumPoints);
    
    FVector Direction = End - Start;
    float Length = Direction.Size();
    Direction = Direction.GetSafeNormal();
    
    FVector Perpendicular = FVector::CrossProduct(Direction, FVector::UpVector).GetSafeNormal();
    
    for (int32 i = 0; i < NumPoints; ++i)
    {
        float t = (float)i / (NumPoints - 1);
        float x = t * Length;
        float y = Amplitude * FMath::Sin(2.0f * PI * Frequency * t + Phase);
        
        FVector Point = Start + Direction * x + Perpendicular * y;
        Points.Add(Point);
    }
    
    return Points;
}

// ===== DEBUG =====

void AGeometricValidator::DrawDebugVisualization() const
{
    if (!ValidationConfig.bShowDebugVisualization || !GetWorld())
    {
        return;
    }

    // Desenhar formas registradas
    for (const FGeometricShape& Shape : RegisteredShapes)
    {
        DrawDebugShape(Shape, FColor::Blue);
    }

    // Desenhar resultados de validação recentes
    int32 RecentCount = FMath::Min(10, ValidationHistory.Num());
    for (int32 i = ValidationHistory.Num() - RecentCount; i < ValidationHistory.Num(); ++i)
    {
        if (i >= 0)
        {
            DrawDebugValidationResult(ValidationHistory[i]);
        }
    }
}

void AGeometricValidator::DrawDebugShape(const FGeometricShape& Shape, const FColor& Color) const
{
    if (!GetWorld())
    {
        return;
    }

    switch (Shape.ShapeType)
    {
        case EGeometricShape::Point:
            DrawDebugSphere(GetWorld(), Shape.Center, 5.0f, 8, Color, false, -1.0f, 0, 1.0f);
            break;
            
        case EGeometricShape::Circle:
            DrawDebugCircle(GetWorld(), Shape.Center, Shape.Radius, 32, Color, false, -1.0f, 0, 1.0f, FVector::ForwardVector, FVector::RightVector);
            break;
            
        case EGeometricShape::Hexagon:
        case EGeometricShape::Polygon:
            if (Shape.Points.Num() >= 3)
            {
                for (int32 i = 0; i < Shape.Points.Num(); ++i)
                {
                    int32 NextIndex = (i + 1) % Shape.Points.Num();
                    DrawDebugLine(GetWorld(), Shape.Points[i], Shape.Points[NextIndex], Color, false, -1.0f, 0, 2.0f);
                }
            }
            break;
            
        case EGeometricShape::Line:
        case EGeometricShape::Segment:
            if (Shape.Points.Num() >= 2)
            {
                DrawDebugLine(GetWorld(), Shape.Points[0], Shape.Points[1], Color, false, -1.0f, 0, 2.0f);
            }
            break;
            
        case EGeometricShape::Rectangle:
        case EGeometricShape::Box:
            if (Shape.Points.Num() >= 4)
            {
                for (int32 i = 0; i < 4; ++i)
                {
                    int32 NextIndex = (i + 1) % 4;
                    DrawDebugLine(GetWorld(), Shape.Points[i], Shape.Points[NextIndex], Color, false, -1.0f, 0, 2.0f);
                }
            }
            break;
            
        default:
            break;
    }
}

void AGeometricValidator::DrawDebugValidationResult(const FValidationResult& Result) const
{
    if (!GetWorld())
    {
        return;
    }

    FColor ResultColor = FColor::Green;
    if (!Result.bIsValid)
    {
        switch (Result.Severity)
        {
            case EValidationSeverity::Warning:
                ResultColor = FColor::Yellow;
                break;
            case EValidationSeverity::Error:
                ResultColor = FColor::Red;
                break;
            case EValidationSeverity::Critical:
                ResultColor = FColor::Magenta;
                break;
            default:
                ResultColor = FColor::Orange;
                break;
        }
    }

    // Desenhar esfera no local da validação
    DrawDebugSphere(GetWorld(), Result.Location, 3.0f, 8, ResultColor, false, 1.0f, 0, 1.0f);
    
    // Desenhar texto de debug
    if (ValidationConfig.bShowDebugText)
    {
        FString DebugText = FString::Printf(TEXT("%s\nDesvio: %.2f"), *Result.Description, Result.Deviation);
        DrawDebugString(GetWorld(), Result.Location + FVector(0, 0, 10), DebugText, nullptr, ResultColor, 1.0f);
    }
}

void AGeometricValidator::LogValidationResult(const FValidationResult& Result) const
{
    FString SeverityString;
    switch (Result.Severity)
    {
        case EValidationSeverity::Info:
            SeverityString = TEXT("INFO");
            break;
        case EValidationSeverity::Warning:
            SeverityString = TEXT("WARNING");
            break;
        case EValidationSeverity::Error:
            SeverityString = TEXT("ERROR");
            break;
        case EValidationSeverity::Critical:
            SeverityString = TEXT("CRITICAL");
            break;
        default:
            SeverityString = TEXT("UNKNOWN");
            break;
    }

    FString TypeString;
    switch (Result.ValidationType)
    {
        case EValidationType::Distance:
            TypeString = TEXT("DISTANCE");
            break;
        case EValidationType::Angle:
            TypeString = TEXT("ANGLE");
            break;
        case EValidationType::Area:
            TypeString = TEXT("AREA");
            break;
        case EValidationType::Parallelism:
            TypeString = TEXT("PARALLELISM");
            break;
        case EValidationType::Perpendicularity:
            TypeString = TEXT("PERPENDICULARITY");
            break;
        case EValidationType::Intersection:
            TypeString = TEXT("INTERSECTION");
            break;
        case EValidationType::Containment:
            TypeString = TEXT("CONTAINMENT");
            break;
        case EValidationType::Symmetry:
            TypeString = TEXT("SYMMETRY");
            break;
        default:
            TypeString = TEXT("UNKNOWN");
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("[%s] %s: %s | Esperado=%.2f, Atual=%.2f, Desvio=%.2f, Tolerância=%.2f | Local=(%.2f, %.2f, %.2f)"),
        *SeverityString,
        *TypeString,
        *Result.Description,
        Result.ExpectedValue,
        Result.ActualValue,
        Result.Deviation,
        Result.Tolerance,
        Result.Location.X,
        Result.Location.Y,
        Result.Location.Z
    );
}

bool AGeometricValidator::ExportValidationReport(const FString& FilePath)
{
    TArray<FString> ReportLines;
    
    // Cabeçalho do relatório
    ReportLines.Add(TEXT("=== RELATÓRIO DE VALIDAÇÃO GEOMÉTRICA ==="));
    ReportLines.Add(FString::Printf(TEXT("Data/Hora: %s"), *FDateTime::Now().ToString()));
    ReportLines.Add(FString::Printf(TEXT("Total de Validações: %d"), ValidationHistory.Num()));
    ReportLines.Add(FString::Printf(TEXT("Erros: %d"), GetErrorCount()));
    ReportLines.Add(FString::Printf(TEXT("Avisos: %d"), GetWarningCount()));
    ReportLines.Add(TEXT(""));
    
    // Configuração atual
    ReportLines.Add(TEXT("=== CONFIGURAÇÃO ==="));
    ReportLines.Add(FString::Printf(TEXT("Tolerância de Distância: %.2f"), ValidationConfig.DistanceTolerance));
    ReportLines.Add(FString::Printf(TEXT("Tolerância de Ângulo: %.2f°"), ValidationConfig.AngleTolerance));
    ReportLines.Add(FString::Printf(TEXT("Tolerância de Área: %.2f"), ValidationConfig.AreaTolerance));
    ReportLines.Add(FString::Printf(TEXT("Validação em Tempo Real: %s"), ValidationConfig.bEnableRealTimeValidation ? TEXT("Ativada") : TEXT("Desativada")));
    ReportLines.Add(FString::Printf(TEXT("Intervalo de Validação: %.2f segundos"), ValidationConfig.ValidationInterval));
    ReportLines.Add(TEXT(""));
    
    // Formas registradas
    ReportLines.Add(TEXT("=== FORMAS REGISTRADAS ==="));
    for (const FGeometricShape& Shape : RegisteredShapes)
    {
        FString ShapeTypeString;
        switch (Shape.ShapeType)
        {
            case EGeometricShape::Point: ShapeTypeString = TEXT("Ponto"); break;
            case EGeometricShape::Line: ShapeTypeString = TEXT("Linha"); break;
            case EGeometricShape::Circle: ShapeTypeString = TEXT("Círculo"); break;
            case EGeometricShape::Hexagon: ShapeTypeString = TEXT("Hexágono"); break;
            case EGeometricShape::Ellipse: ShapeTypeString = TEXT("Elipse"); break;
            case EGeometricShape::Rectangle: ShapeTypeString = TEXT("Retângulo"); break;
            case EGeometricShape::Polygon: ShapeTypeString = TEXT("Polígono"); break;
            default: ShapeTypeString = TEXT("Desconhecido"); break;
        }
        
        ReportLines.Add(FString::Printf(TEXT("ID: %s | Tipo: %s | Pontos: %d | Centro: (%.2f, %.2f, %.2f)"),
            *Shape.ShapeID,
            *ShapeTypeString,
            Shape.Points.Num(),
            Shape.Center.X,
            Shape.Center.Y,
            Shape.Center.Z));
    }
    ReportLines.Add(TEXT(""));
    
    // Histórico de validações
    ReportLines.Add(TEXT("=== HISTÓRICO DE VALIDAÇÕES ==="));
    for (const FValidationResult& Result : ValidationHistory)
    {
        FString StatusString = Result.bIsValid ? TEXT("✓") : TEXT("✗");
        FString SeverityString;
        
        switch (Result.Severity)
        {
            case EValidationSeverity::Info: SeverityString = TEXT("INFO"); break;
            case EValidationSeverity::Warning: SeverityString = TEXT("AVISO"); break;
            case EValidationSeverity::Error: SeverityString = TEXT("ERRO"); break;
            case EValidationSeverity::Critical: SeverityString = TEXT("CRÍTICO"); break;
            default: SeverityString = TEXT("DESCONHECIDO"); break;
        }
        
        ReportLines.Add(FString::Printf(TEXT("%s [%s] %s | Desvio: %.2f | %s"),
            *StatusString,
            *SeverityString,
            *Result.Description,
            Result.Deviation,
            *Result.Timestamp.ToString()));
    }
    
    // Salvar arquivo
    FString ReportContent = FString::Join(ReportLines, TEXT("\n"));
    
    if (!FFileHelper::SaveStringToFile(ReportContent, *FilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("AGeometricValidator: Falha ao salvar relatório em %s"), *FilePath);
        return false;
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Relatório salvo em %s"), *FilePath);
        return true;
    }
}

void AGeometricValidator::ClearValidationHistory()
{
    int32 Count = ValidationHistory.Num();
    ValidationHistory.Empty();
    UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Histórico de validação limpo (%d resultados removidos)"), Count);
}

void AGeometricValidator::SetValidationConfig(const FValidationConfig& NewConfig)
{
    ValidationConfig = NewConfig;
    UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Configuração de validação atualizada"));
    
    // Reiniciar validação em tempo real se necessário
    if (bIsValidating && ValidationConfig.bEnableRealTimeValidation)
    {
        StopRealTimeValidation();
        StartRealTimeValidation();
    }
    else if (!ValidationConfig.bEnableRealTimeValidation)
    {
        StopRealTimeValidation();
    }
}

FValidationConfig AGeometricValidator::GetValidationConfig() const
{
    return ValidationConfig;
}



TArray<FValidationResult> AGeometricValidator::GetValidationHistoryByType(EValidationType Type) const
{
    TArray<FValidationResult> FilteredResults;
    
    for (const FValidationResult& Result : ValidationHistory)
    {
        if (Result.ValidationType == Type)
        {
            FilteredResults.Add(Result);
        }
    }
    
    return FilteredResults;
}

TArray<FValidationResult> AGeometricValidator::GetValidationHistoryBySeverity(EValidationSeverity Severity) const
{
    TArray<FValidationResult> FilteredResults;
    
    for (const FValidationResult& Result : ValidationHistory)
    {
        if (Result.Severity == Severity)
        {
            FilteredResults.Add(Result);
        }
    }
    
    return FilteredResults;
}



FGeometricShape AGeometricValidator::GetRegisteredShape(const FString& ShapeID) const
{
    for (const FGeometricShape& Shape : RegisteredShapes)
    {
        if (Shape.ShapeID == ShapeID)
        {
            return Shape;
        }
    }
    // Retornar uma forma vazia se não encontrada
    return FGeometricShape();
}



float AGeometricValidator::GetValidationProgress() const
{
    // Retornar progresso baseado no número de formas validadas
    if (RegisteredShapes.Num() == 0)
    {
        return 1.0f; // 100% se não há formas para validar
    }
    
    // Calcular baseado no histórico recente
    int32 RecentValidations = 0;
    FDateTime RecentTime = FDateTime::Now() - FTimespan::FromSeconds(ValidationConfig.ValidationInterval * 2.0);
    
    for (const FValidationResult& Result : ValidationHistory)
    {
        if (Result.Timestamp > RecentTime)
        {
            RecentValidations++;
        }
    }
    
    return FMath::Clamp((float)RecentValidations / RegisteredShapes.Num(), 0.0f, 1.0f);
}

// ===== FUNÇÕES DE BENCHMARK =====

void AGeometricValidator::RunPerformanceBenchmark(int32 NumIterations)
{
    UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Iniciando benchmark de performance (%d iterações)"), NumIterations);
    
    double TotalTime = 0.0;
    
    for (int32 i = 0; i < NumIterations; ++i)
    {
        double StartTime = FPlatformTime::Seconds();
        
        // Executar validação completa
        PerformFullValidation();
        
        double EndTime = FPlatformTime::Seconds();
        TotalTime += (EndTime - StartTime);
    }
    
    double AverageTime = TotalTime / NumIterations;
    double ValidationsPerSecond = 1.0 / AverageTime;
    
    UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Benchmark concluído"));
    UE_LOG(LogTemp, Log, TEXT("  - Tempo médio por validação: %.6f segundos"), AverageTime);
    UE_LOG(LogTemp, Log, TEXT("  - Validações por segundo: %.2f"), ValidationsPerSecond);
    UE_LOG(LogTemp, Log, TEXT("  - Tempo total: %.6f segundos"), TotalTime);
    UE_LOG(LogTemp, Log, TEXT("  - Formas registradas: %d"), RegisteredShapes.Num());
    UE_LOG(LogTemp, Log, TEXT("  - Histórico de validações: %d"), ValidationHistory.Num());
}

// ===== FUNÇÕES DE UTILIDADE =====

int32 AGeometricValidator::GetErrorCount() const
{
    int32 ErrorCount = 0;
    for (const FValidationResult& Result : ValidationHistory)
    {
        if (Result.Severity == EValidationSeverity::Error || Result.Severity == EValidationSeverity::Critical)
        {
            ErrorCount++;
        }
    }
    return ErrorCount;
}

int32 AGeometricValidator::GetWarningCount() const
{
    int32 WarningCount = 0;
    for (const FValidationResult& Result : ValidationHistory)
    {
        if (Result.Severity == EValidationSeverity::Warning)
        {
            WarningCount++;
        }
    }
    return WarningCount;
}

TMap<FString, int32> AGeometricValidator::GetValidationStatistics() const
{
    TMap<FString, int32> Statistics;
    
    Statistics.Add(TEXT("Total"), ValidationHistory.Num());
    Statistics.Add(TEXT("Erros"), GetErrorCount());
    Statistics.Add(TEXT("Avisos"), GetWarningCount());
    Statistics.Add(TEXT("Formas Registradas"), RegisteredShapes.Num());
    
    int32 ValidCount = 0;
    for (const FValidationResult& Result : ValidationHistory)
    {
        if (Result.bIsValid)
        {
            ValidCount++;
        }
    }
    Statistics.Add(TEXT("Válidas"), ValidCount);
    
    return Statistics;
}

FString AGeometricValidator::GetValidationSummary() const
{
    int32 TotalValidations = ValidationHistory.Num();
    int32 ValidCount = 0;
    int32 ErrorCount = this->GetErrorCount();
    int32 WarningCount = this->GetWarningCount();
    
    for (const FValidationResult& Result : ValidationHistory)
    {
        if (Result.bIsValid)
        {
            ValidCount++;
        }
    }
    
    float SuccessRate = TotalValidations > 0 ? (float)ValidCount / TotalValidations * 100.0f : 0.0f;
    
    return FString::Printf(TEXT("Validações: %d | Sucessos: %d (%.1f%%) | Erros: %d | Avisos: %d | Formas: %d"),
        TotalValidations,
        ValidCount,
        SuccessRate,
        ErrorCount,
        WarningCount,
        RegisteredShapes.Num());
}

void AGeometricValidator::ResetValidator()
{
    StopRealTimeValidation();
    ClearValidationHistory();
    ClearRegisteredShapes();
    
    // Resetar configuração para padrão
    ValidationConfig = FValidationConfig();
    
    UE_LOG(LogTemp, Log, TEXT("AGeometricValidator: Validador resetado completamente"));
}