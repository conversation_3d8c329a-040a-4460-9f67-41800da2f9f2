#include "testes_precisao_geometrica.h"
#include "implementacao_automatizada.h"

#if WITH_DEV_AUTOMATION_TESTS
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#endif

UTestePrecisaoGeometrica::UTestePrecisaoGeometrica()
{
    // Construtor vazio - classe estática
}

// ========== IMPLEMENTAÇÃO DOS TESTES PRINCIPAIS ==========

bool UTestePrecisaoGeometrica::ExecutarTodosOsTestes()
{
    UE_LOG(LogTemp, Log, TEXT("=== INICIANDO BATERIA COMPLETA DE TESTES GEOMÉTRICOS ==="));
    
    TArray<bool> ResultadosTestes;
    
    // Executar todos os testes individuais
    ResultadosTestes.Add(TestarPrecisaoLanes());
    ResultadosTestes.Add(TestarFuncaoSenoidalRio());
    ResultadosTestes.Add(TestarGeometriaHexagonos());
    ResultadosTestes.Add(TestarGeometriaElipses());
    ResultadosTestes.Add(TestarGeometriaCirculos());
    ResultadosTestes.Add(TestarPosicionamentoTorres());
    ResultadosTestes.Add(TestarEspecificacoesBases());
    ResultadosTestes.Add(TestarDimensoesCovils());
    ResultadosTestes.Add(TestarSistemaParedes());
    ResultadosTestes.Add(TestarSistemaMinions());
    ResultadosTestes.Add(TestarToleranciasCoordenadas());
    ResultadosTestes.Add(TestarCalculosAngulares());
    ResultadosTestes.Add(TestarCalculosArea());
    ResultadosTestes.Add(TestarCalculosDistancia());
    ResultadosTestes.Add(TestarColisoesBordas());
    ResultadosTestes.Add(TestarConectividadeLanes());
    ResultadosTestes.Add(TestarFluxoMinions());
    ResultadosTestes.Add(TestarAcessoCovils());
    
    // Contar sucessos e falhas
    int32 Sucessos = 0;
    int32 Falhas = 0;
    
    for (bool Resultado : ResultadosTestes)
    {
        if (Resultado)
            Sucessos++;
        else
            Falhas++;
    }
    
    float PrecisaoGeral = (float)Sucessos / (float)ResultadosTestes.Num() * 100.0f;
    
    UE_LOG(LogTemp, Log, TEXT("=== RESULTADO FINAL ==="));
    UE_LOG(LogTemp, Log, TEXT("Testes Executados: %d"), ResultadosTestes.Num());
    UE_LOG(LogTemp, Log, TEXT("Sucessos: %d"), Sucessos);
    UE_LOG(LogTemp, Log, TEXT("Falhas: %d"), Falhas);
    UE_LOG(LogTemp, Log, TEXT("Precisão Geral: %.2f%%"), PrecisaoGeral);
    
    bool MapaAprovado = (PrecisaoGeral >= 99.9f); // 99.9% de precisão mínima
    
    if (MapaAprovado)
    {
        UE_LOG(LogTemp, Log, TEXT("🎯 MAPA APROVADO - PRECISÃO GARANTIDA!"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("❌ MAPA REPROVADO - CORREÇÕES NECESSÁRIAS!"));
    }
    
    return MapaAprovado;
}

FString UTestePrecisaoGeometrica::GerarRelatorioCompleto()
{
    FString Relatorio = TEXT("\n");
    Relatorio += TEXT("╔══════════════════════════════════════════════════════════════╗\n");
    Relatorio += TEXT("║                 RELATÓRIO DE VALIDAÇÃO GEOMÉTRICA            ║\n");
    Relatorio += TEXT("║                        MAPA AURA - UE5.6                     ║\n");
    Relatorio += TEXT("╚══════════════════════════════════════════════════════════════╝\n\n");
    
    // Executar testes e coletar resultados detalhados
    TArray<FString> ResultadosDetalhados;
    
    // Teste das Lanes
    bool LanesOK = TestarPrecisaoLanes();
    ResultadosDetalhados.Add(FString::Printf(TEXT("✓ LANES: %s"), LanesOK ? TEXT("APROVADO") : TEXT("REPROVADO")));
    
    // Teste do Rio
    bool RioOK = TestarFuncaoSenoidalRio();
    ResultadosDetalhados.Add(FString::Printf(TEXT("✓ RIO SENOIDAL: %s"), RioOK ? TEXT("APROVADO") : TEXT("REPROVADO")));
    
    // Teste das Geometrias
    bool HexOK = TestarGeometriaHexagonos();
    bool EliOK = TestarGeometriaElipses();
    bool CirOK = TestarGeometriaCirculos();
    ResultadosDetalhados.Add(FString::Printf(TEXT("✓ HEXÁGONOS: %s"), HexOK ? TEXT("APROVADO") : TEXT("REPROVADO")));
    ResultadosDetalhados.Add(FString::Printf(TEXT("✓ ELIPSES: %s"), EliOK ? TEXT("APROVADO") : TEXT("REPROVADO")));
    ResultadosDetalhados.Add(FString::Printf(TEXT("✓ CÍRCULOS: %s"), CirOK ? TEXT("APROVADO") : TEXT("REPROVADO")));
    
    // Teste das Torres
    bool TorresOK = TestarPosicionamentoTorres();
    ResultadosDetalhados.Add(FString::Printf(TEXT("✓ TORRES: %s"), TorresOK ? TEXT("APROVADO") : TEXT("REPROVADO")));
    
    // Teste das Bases
    bool BasesOK = TestarEspecificacoesBases();
    ResultadosDetalhados.Add(FString::Printf(TEXT("✓ BASES: %s"), BasesOK ? TEXT("APROVADO") : TEXT("REPROVADO")));
    
    // Teste dos Covils
    bool CovilsOK = TestarDimensoesCovils();
    ResultadosDetalhados.Add(FString::Printf(TEXT("✓ COVILS: %s"), CovilsOK ? TEXT("APROVADO") : TEXT("REPROVADO")));
    
    // Adicionar resultados ao relatório
    Relatorio += TEXT("📊 RESULTADOS DOS TESTES:\n\n");
    for (const FString& Resultado : ResultadosDetalhados)
    {
        Relatorio += Resultado + TEXT("\n");
    }
    
    // Calcular estatísticas
    int32 TotalTestes = ResultadosDetalhados.Num();
    int32 TestesAprovados = 0;
    
    for (const FString& Resultado : ResultadosDetalhados)
    {
        if (Resultado.Contains(TEXT("APROVADO")))
            TestesAprovados++;
    }
    
    float PrecisaoGeral = (float)TestesAprovados / (float)TotalTestes * 100.0f;
    
    Relatorio += TEXT("\n📈 ESTATÍSTICAS:\n");
    Relatorio += FString::Printf(TEXT("   • Total de Testes: %d\n"), TotalTestes);
    Relatorio += FString::Printf(TEXT("   • Testes Aprovados: %d\n"), TestesAprovados);
    Relatorio += FString::Printf(TEXT("   • Testes Reprovados: %d\n"), TotalTestes - TestesAprovados);
    Relatorio += FString::Printf(TEXT("   • Precisão Geral: %.2f%%\n\n"), PrecisaoGeral);
    
    // Veredicto final
    if (PrecisaoGeral >= 99.9f)
    {
        Relatorio += TEXT("🎯 VEREDICTO: MAPA APROVADO PARA IMPLEMENTAÇÃO\n");
        Relatorio += TEXT("   Todas as especificações geométricas estão corretas.\n");
        Relatorio += TEXT("   O mapa pode ser implementado sem ajustes adicionais.\n");
    }
    else
    {
        Relatorio += TEXT("❌ VEREDICTO: MAPA REPROVADO - CORREÇÕES NECESSÁRIAS\n");
        Relatorio += TEXT("   Algumas especificações geométricas precisam de ajustes.\n");
        Relatorio += TEXT("   Revisar os testes que falharam antes da implementação.\n");
    }
    
    Relatorio += TEXT("\n═══════════════════════════════════════════════════════════════\n");
    
    return Relatorio;
}

// ========== TESTES ESPECÍFICOS POR COMPONENTE ==========

bool UTestePrecisaoGeometrica::TestarPrecisaoLanes()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando precisão das lanes..."));
    
    bool TodosTestesPassaram = true;
    
    // Teste Lane Superior: Y = -0.577X + 6928
    {
        // Pontos de teste conhecidos
        TArray<FVector2D> PontosTeste = {
            FVector2D(-6000.0f, 10392.0f),  // Ponto inicial
            FVector2D(0.0f, 6928.0f),       // Ponto central
            FVector2D(6000.0f, -3464.0f)    // Ponto final
        };
        
        for (const FVector2D& Ponto : PontosTeste)
        {
            float YCalculado = LANE_SUPERIOR_M * Ponto.X + LANE_SUPERIOR_B;
            if (!CompararFloat(YCalculado, Ponto.Y, TOLERANCIA_TESTE_COORDENADA))
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Lane Superior falhou em (%.1f, %.1f). Esperado Y: %.1f, Calculado: %.1f"), 
                       Ponto.X, Ponto.Y, Ponto.Y, YCalculado);
                TodosTestesPassaram = false;
            }
        }
    }
    
    // Teste Lane Central: Y = 0
    {
        TArray<float> PontosX = {-4800.0f, -2400.0f, 0.0f, 2400.0f, 4800.0f};
        
        for (float X : PontosX)
        {
            if (!AImplementacaoAutomatizada::ValidarPontoNaLane(X, 0.0f, 1))
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Lane Central falhou em X: %.1f"), X);
                TodosTestesPassaram = false;
            }
        }
    }
    
    // Teste Lane Inferior: Y = 0.577X - 6928
    {
        TArray<FVector2D> PontosTeste = {
            FVector2D(-6000.0f, -10392.0f), // Ponto inicial
            FVector2D(0.0f, -6928.0f),      // Ponto central
            FVector2D(6000.0f, 3464.0f)     // Ponto final
        };
        
        for (const FVector2D& Ponto : PontosTeste)
        {
            float YCalculado = LANE_INFERIOR_M * Ponto.X + LANE_INFERIOR_B;
            if (!CompararFloat(YCalculado, Ponto.Y, TOLERANCIA_TESTE_COORDENADA))
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Lane Inferior falhou em (%.1f, %.1f). Esperado Y: %.1f, Calculado: %.1f"), 
                       Ponto.X, Ponto.Y, Ponto.Y, YCalculado);
                TodosTestesPassaram = false;
            }
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste das lanes: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarFuncaoSenoidalRio()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando função senoidal do rio..."));
    
    bool TodosTestesPassaram = true;
    
    // Pontos críticos da função senoidal
    TArray<FVector2D> PontosCriticos = {
        FVector2D(-4800.0f, 0.0f),    // sin(-π) = 0
        FVector2D(-2400.0f, -200.0f), // sin(-π/2) = -1
        FVector2D(0.0f, 0.0f),        // sin(0) = 0
        FVector2D(2400.0f, 200.0f),   // sin(π/2) = 1
        FVector2D(4800.0f, 0.0f)      // sin(π) = 0
    };
    
    for (const FVector2D& Ponto : PontosCriticos)
    {
        float YCalculado = RIO_AMPLITUDE * FMath::Sin(PI * Ponto.X / 4800.0f);
        
        if (!CompararFloat(YCalculado, Ponto.Y, TOLERANCIA_TESTE_COORDENADA))
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Rio senoidal falhou em X: %.1f. Esperado Y: %.1f, Calculado: %.1f"), 
                   Ponto.X, Ponto.Y, YCalculado);
            TodosTestesPassaram = false;
        }
        
        // Testar se o ponto está dentro do rio
        if (!AImplementacaoAutomatizada::ValidarPontoNoRio(Ponto.X, Ponto.Y))
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Ponto (%.1f, %.1f) deveria estar no rio mas não está"), 
                   Ponto.X, Ponto.Y);
            TodosTestesPassaram = false;
        }
    }
    
    // Testar largura variável do rio
    TArray<FVector2D> PontosLargura = {
        FVector2D(-2400.0f, 1400.0f), // Largura máxima
        FVector2D(0.0f, 1200.0f),     // Largura média
        FVector2D(2400.0f, 1400.0f)   // Largura máxima
    };
    
    for (const FVector2D& Ponto : PontosLargura)
    {
        float LarguraCalculada = 1200.0f + 200.0f * FMath::Abs(FMath::Sin(PI * Ponto.X / 2400.0f));
        
        if (!CompararFloat(LarguraCalculada, Ponto.Y, TOLERANCIA_TESTE_COORDENADA))
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Largura do rio falhou em X: %.1f. Esperado: %.1f, Calculado: %.1f"), 
                   Ponto.X, Ponto.Y, LarguraCalculada);
            TodosTestesPassaram = false;
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste do rio senoidal: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarGeometriaHexagonos()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando geometria dos hexágonos..."));
    
    bool TodosTestesPassaram = true;
    
    // Testar hexágono da Base Azul
    {
        FVector2D Centro(-6000.0f, -6000.0f);
        float Raio = BASE_RAIO;
        
        TArray<FVector> Vertices = AImplementacaoAutomatizada::CalcularVerticesHexagono(Centro, Raio, 90.0f);
        
        // Verificar se temos 6 vértices
        if (Vertices.Num() != 6)
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Hexágono da base deveria ter 6 vértices, mas tem %d"), Vertices.Num());
            TodosTestesPassaram = false;
        }
        
        // Verificar se todos os vértices estão à distância correta do centro
        for (int32 i = 0; i < Vertices.Num(); i++)
        {
            float Distancia = FVector2D::Distance(
                FVector2D(Vertices[i].X, Vertices[i].Y), 
                Centro
            );
            
            if (!CompararFloat(Distancia, Raio, TOLERANCIA_TESTE_DISTANCIA))
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Vértice %d do hexágono está a %.2f UU do centro, deveria estar a %.2f UU"), 
                       i, Distancia, Raio);
                TodosTestesPassaram = false;
            }
        }
        
        // Verificar área do hexágono
        float AreaCalculada = (3.0f * FMath::Sqrt(3.0f) / 2.0f) * Raio * Raio;
        if (!CompararFloat(AreaCalculada, BASE_AREA_ESPERADA, BASE_AREA_ESPERADA * TOLERANCIA_TESTE_AREA))
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Área do hexágono da base: %.2f, esperado: %.2f"), 
                   AreaCalculada, BASE_AREA_ESPERADA);
            TodosTestesPassaram = false;
        }
    }
    
    // Testar hexágono da Ilha Central
    {
        FVector2D Centro(0.0f, 0.0f);
        float Raio = ILHA_RAIO;
        
        TArray<FVector> Vertices = AImplementacaoAutomatizada::CalcularVerticesHexagono(Centro, Raio, 0.0f);
        
        // Verificar área da ilha
        float AreaCalculada = (3.0f * FMath::Sqrt(3.0f) / 2.0f) * Raio * Raio;
        if (!CompararFloat(AreaCalculada, ILHA_AREA_ESPERADA, ILHA_AREA_ESPERADA * TOLERANCIA_TESTE_AREA))
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Área da ilha central: %.2f, esperado: %.2f"), 
                   AreaCalculada, ILHA_AREA_ESPERADA);
            TodosTestesPassaram = false;
        }
    }
    
    // Testar hexágono do Barão Auracron
    {
        FVector2D Centro(0.0f, -4800.0f);
        float Raio = BARAO_RAIO;
        
        float AreaCalculada = (3.0f * FMath::Sqrt(3.0f) / 2.0f) * Raio * Raio;
        if (!CompararFloat(AreaCalculada, BARAO_AREA_ESPERADA, BARAO_AREA_ESPERADA * TOLERANCIA_TESTE_AREA))
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Área do covil do Barão: %.2f, esperado: %.2f"), 
                   AreaCalculada, BARAO_AREA_ESPERADA);
            TodosTestesPassaram = false;
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste dos hexágonos: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarGeometriaElipses()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando geometria das elipses..."));
    
    bool TodosTestesPassaram = true;
    
    // Testar elipse do Dragão Prismal
    {
        FVector2D Centro(0.0f, 4800.0f);
        float SemiEixoA = DRAGAO_SEMI_A;
        float SemiEixoB = DRAGAO_SEMI_B;
        
        // Testar pontos na borda da elipse
        TArray<FVector2D> PontosBorda = {
            FVector2D(Centro.X + SemiEixoA, Centro.Y),     // Ponto direito
            FVector2D(Centro.X - SemiEixoA, Centro.Y),     // Ponto esquerdo
            FVector2D(Centro.X, Centro.Y + SemiEixoB),     // Ponto superior
            FVector2D(Centro.X, Centro.Y - SemiEixoB)      // Ponto inferior
        };
        
        for (const FVector2D& Ponto : PontosBorda)
        {
            if (!AImplementacaoAutomatizada::ValidarPontoEmElipse(Ponto.X, Ponto.Y, Centro, SemiEixoA, SemiEixoB))
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Ponto (%.1f, %.1f) deveria estar na elipse do Dragão"), 
                       Ponto.X, Ponto.Y);
                TodosTestesPassaram = false;
            }
        }
        
        // Verificar área da elipse
        float AreaCalculada = PI * SemiEixoA * SemiEixoB;
        if (!CompararFloat(AreaCalculada, DRAGAO_AREA_ESPERADA, DRAGAO_AREA_ESPERADA * TOLERANCIA_TESTE_AREA))
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Área da elipse do Dragão: %.2f, esperado: %.2f"), 
                   AreaCalculada, DRAGAO_AREA_ESPERADA);
            TodosTestesPassaram = false;
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste das elipses: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarGeometriaCirculos()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando geometria dos círculos..."));
    
    bool TodosTestesPassaram = true;
    
    // Testar círculos das Sentinelas Cristalinas
    TArray<FVector2D> CentrosSentinelas = {
        FVector2D(3000.0f, 2400.0f),
        FVector2D(-3000.0f, 2400.0f),
        FVector2D(3000.0f, -2400.0f),
        FVector2D(-3000.0f, -2400.0f)
    };
    
    for (int32 i = 0; i < CentrosSentinelas.Num(); i++)
    {
        FVector2D Centro = CentrosSentinelas[i];
        float Raio = SENTINELA_RAIO;
        
        // Testar pontos na borda do círculo
        TArray<FVector2D> PontosBorda = {
            FVector2D(Centro.X + Raio, Centro.Y),     // Direita
            FVector2D(Centro.X - Raio, Centro.Y),     // Esquerda
            FVector2D(Centro.X, Centro.Y + Raio),     // Cima
            FVector2D(Centro.X, Centro.Y - Raio)      // Baixo
        };
        
        for (const FVector2D& Ponto : PontosBorda)
        {
            float Distancia = FVector2D::Distance(Ponto, Centro);
            if (!CompararFloat(Distancia, Raio, TOLERANCIA_TESTE_DISTANCIA))
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Ponto (%.1f, %.1f) da Sentinela %d está a %.2f do centro, deveria estar a %.2f"), 
                       Ponto.X, Ponto.Y, i, Distancia, Raio);
                TodosTestesPassaram = false;
            }
        }
        
        // Verificar área do círculo
        float AreaCalculada = PI * Raio * Raio;
        if (!CompararFloat(AreaCalculada, SENTINELA_AREA_ESPERADA, SENTINELA_AREA_ESPERADA * TOLERANCIA_TESTE_AREA))
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Área do círculo da Sentinela %d: %.2f, esperado: %.2f"), 
                   i, AreaCalculada, SENTINELA_AREA_ESPERADA);
            TodosTestesPassaram = false;
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste dos círculos: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarPosicionamentoTorres()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando posicionamento das torres..."));
    
    bool TodosTestesPassaram = true;
    
    // Testar torres de cada lane
    for (int32 Lane = 0; Lane < 3; Lane++)
    {
        for (int32 Torre = 0; Torre < 4; Torre++)
        {
            // Torres do time azul
            FVector PosAzul = AImplementacaoAutomatizada::CalcularPosicaoTorre(Lane, Torre, true);
            
            // Verificar se a torre está na lane correta
            if (!AImplementacaoAutomatizada::ValidarPontoNaLane(PosAzul.X, PosAzul.Y, Lane))
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Torre azul L%d T%d em (%.1f, %.1f) não está na lane correta"), 
                       Lane, Torre, PosAzul.X, PosAzul.Y);
                TodosTestesPassaram = false;
            }
            
            // Torres do time vermelho
            FVector PosVermelho = AImplementacaoAutomatizada::CalcularPosicaoTorre(Lane, Torre, false);
            
            // Verificar se a torre está na lane correta
            if (!AImplementacaoAutomatizada::ValidarPontoNaLane(PosVermelho.X, PosVermelho.Y, Lane))
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Torre vermelha L%d T%d em (%.1f, %.1f) não está na lane correta"), 
                       Lane, Torre, PosVermelho.X, PosVermelho.Y);
                TodosTestesPassaram = false;
            }
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste das torres: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarEspecificacoesBases()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando especificações das bases..."));
    
    bool TodosTestesPassaram = true;
    
    // Testar Base Azul
    {
        FVector2D CentroAzul(-6000.0f, -6000.0f);
        TArray<FVector> VerticesAzul = AImplementacaoAutomatizada::CalcularVerticesHexagono(CentroAzul, BASE_RAIO, 90.0f);
        
        // Verificar se o primeiro vértice está na posição correta (rotação 90°)
        FVector VerticeEsperado(-6000.0f, -4800.0f, 0.0f); // Norte da base
        if (!CompararVetor(VerticesAzul[0], VerticeEsperado, TOLERANCIA_TESTE_COORDENADA))
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Primeiro vértice da base azul: (%.1f, %.1f), esperado: (%.1f, %.1f)"), 
                   VerticesAzul[0].X, VerticesAzul[0].Y, VerticeEsperado.X, VerticeEsperado.Y);
            TodosTestesPassaram = false;
        }
    }
    
    // Testar Base Vermelha
    {
        FVector2D CentroVermelho(6000.0f, 6000.0f);
        TArray<FVector> VerticesVermelho = AImplementacaoAutomatizada::CalcularVerticesHexagono(CentroVermelho, BASE_RAIO, 90.0f);
        
        // Verificar simetria entre as bases
        FVector2D CentroAzul(-6000.0f, -6000.0f);
        TArray<FVector> VerticesAzul = AImplementacaoAutomatizada::CalcularVerticesHexagono(CentroAzul, BASE_RAIO, 90.0f);
        
        for (int32 i = 0; i < 6; i++)
        {
            // A base vermelha deve ser uma translação de (+12000, +12000) da base azul
            FVector VerticeEsperado = VerticesAzul[i] + FVector(12000.0f, 12000.0f, 0.0f);
            
            if (!CompararVetor(VerticesVermelho[i], VerticeEsperado, TOLERANCIA_TESTE_COORDENADA))
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Simetria das bases falhou no vértice %d"), i);
                TodosTestesPassaram = false;
            }
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste das bases: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarDimensoesCovils()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando dimensões dos covils..."));
    
    bool TodosTestesPassaram = true;
    
    // Verificar se todos os covils estão nas posições corretas
    TArray<FVector> PosicoesEsperadas = {
        FVector(0.0f, 4800.0f, 0.0f),    // Dragão Prismal
        FVector(0.0f, -4800.0f, 0.0f),   // Barão Auracron
        FVector(3000.0f, 2400.0f, 0.0f), // Sentinela NE
        FVector(-3000.0f, 2400.0f, 0.0f), // Sentinela NW
        FVector(3000.0f, -2400.0f, 0.0f), // Sentinela SE
        FVector(-3000.0f, -2400.0f, 0.0f) // Sentinela SW
    };
    
    TArray<FString> NomesCovils = {
        TEXT("Dragão Prismal"),
        TEXT("Barão Auracron"),
        TEXT("Sentinela NE"),
        TEXT("Sentinela NW"),
        TEXT("Sentinela SE"),
        TEXT("Sentinela SW")
    };
    
    for (int32 i = 0; i < PosicoesEsperadas.Num(); i++)
    {
        FVector PosicaoEsperada = PosicoesEsperadas[i];
        
        // Verificar se a posição está dentro dos limites do mapa
        if (FMath::Abs(PosicaoEsperada.X) > 8000.0f || FMath::Abs(PosicaoEsperada.Y) > 8000.0f)
        {
            UE_LOG(LogTemp, Error, TEXT("❌ %s está fora dos limites do mapa: (%.1f, %.1f)"), 
                   *NomesCovils[i], PosicaoEsperada.X, PosicaoEsperada.Y);
            TodosTestesPassaram = false;
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste dos covils: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarSistemaParedes()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando sistema de paredes..."));
    
    bool TodosTestesPassaram = true;
    
    // Testar limites do mapa
    float LimiteX = 8000.0f;
    float LimiteY = 8000.0f;
    
    // Pontos que devem estar dentro do mapa
    TArray<FVector2D> PontosDentro = {
        FVector2D(0.0f, 0.0f),           // Centro
        FVector2D(7999.0f, 7999.0f),    // Quase no limite
        FVector2D(-7999.0f, -7999.0f),  // Quase no limite oposto
    };
    
    for (const FVector2D& Ponto : PontosDentro)
    {
        if (FMath::Abs(Ponto.X) >= LimiteX || FMath::Abs(Ponto.Y) >= LimiteY)
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Ponto (%.1f, %.1f) deveria estar dentro do mapa"), 
                   Ponto.X, Ponto.Y);
            TodosTestesPassaram = false;
        }
    }
    
    // Pontos que devem estar fora do mapa
    TArray<FVector2D> PontosFora = {
        FVector2D(8001.0f, 0.0f),       // Fora do limite X
        FVector2D(0.0f, 8001.0f),       // Fora do limite Y
        FVector2D(-8001.0f, -8001.0f),  // Fora de ambos os limites
    };
    
    for (const FVector2D& Ponto : PontosFora)
    {
        if (FMath::Abs(Ponto.X) < LimiteX && FMath::Abs(Ponto.Y) < LimiteY)
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Ponto (%.1f, %.1f) deveria estar fora do mapa"), 
                   Ponto.X, Ponto.Y);
            TodosTestesPassaram = false;
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste das paredes: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarSistemaMinions()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando sistema de minions..."));
    
    bool TodosTestesPassaram = true;
    
    // Testar posicionamento de minions em diferentes pontos das lanes
    for (int32 Lane = 0; Lane < 3; Lane++)
    {
        TArray<float> DistanciasPercorridas = {0.0f, 0.25f, 0.5f, 0.75f, 1.0f};
        
        for (float Distancia : DistanciasPercorridas)
        {
            // Minion do time azul
            FVector PosAzul = AImplementacaoAutomatizada::CalcularPosicaoMinion(Lane, Distancia * 13856.0f, true);
            
            // Verificar se o minion está na lane
            if (!AImplementacaoAutomatizada::ValidarPontoNaLane(PosAzul.X, PosAzul.Y, Lane))
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Minion azul L%d em distância %.2f não está na lane: (%.1f, %.1f)"), 
                       Lane, Distancia, PosAzul.X, PosAzul.Y);
                TodosTestesPassaram = false;
            }
            
            // Minion do time vermelho
            FVector PosVermelho = AImplementacaoAutomatizada::CalcularPosicaoMinion(Lane, Distancia * 13856.0f, false);
            
            // Verificar se o minion está na lane
            if (!AImplementacaoAutomatizada::ValidarPontoNaLane(PosVermelho.X, PosVermelho.Y, Lane))
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Minion vermelho L%d em distância %.2f não está na lane: (%.1f, %.1f)"), 
                       Lane, Distancia, PosVermelho.X, PosVermelho.Y);
                TodosTestesPassaram = false;
            }
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste dos minions: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

// ========== TESTES DE PRECISÃO MATEMÁTICA ==========

bool UTestePrecisaoGeometrica::TestarToleranciasCoordenadas()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando tolerâncias de coordenadas..."));
    
    bool TodosTestesPassaram = true;
    
    // Testar se as tolerâncias estão dentro dos limites aceitáveis
    if (TOLERANCIA_TESTE_COORDENADA > 1.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("❌ Tolerância de coordenada muito alta: %.2f UU"), TOLERANCIA_TESTE_COORDENADA);
        TodosTestesPassaram = false;
    }
    
    if (TOLERANCIA_TESTE_ANGULO > 0.1f)
    {
        UE_LOG(LogTemp, Error, TEXT("❌ Tolerância de ângulo muito alta: %.3f°"), TOLERANCIA_TESTE_ANGULO);
        TodosTestesPassaram = false;
    }
    
    if (TOLERANCIA_TESTE_AREA > 0.01f)
    {
        UE_LOG(LogTemp, Error, TEXT("❌ Tolerância de área muito alta: %.4f%%"), TOLERANCIA_TESTE_AREA * 100.0f);
        TodosTestesPassaram = false;
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste das tolerâncias: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarCalculosAngulares()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando cálculos angulares..."));
    
    bool TodosTestesPassaram = true;
    
    // Testar ângulo de 30° das lanes
    float Angulo30Rad = FMath::DegreesToRadians(30.0f);
    float TangenteCalculada = FMath::Tan(Angulo30Rad);
    
    if (!CompararFloat(TangenteCalculada, LANE_ANGULO_30_GRAUS, TOLERANCIA_TESTE_ANGULO))
    {
        UE_LOG(LogTemp, Error, TEXT("❌ Tangente de 30°: %.6f, esperado: %.6f"), 
               TangenteCalculada, LANE_ANGULO_30_GRAUS);
        TodosTestesPassaram = false;
    }
    
    // Testar ângulos dos vértices do hexágono (60° entre vértices)
    for (int32 i = 0; i < 6; i++)
    {
        float AnguloEsperado = (PI / 3.0f) * i; // 60° em radianos
        float AnguloCalculado = (PI / 3.0f) * i;
        
        if (!CompararFloat(AnguloCalculado, AnguloEsperado, FMath::DegreesToRadians(TOLERANCIA_TESTE_ANGULO)))
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Ângulo do vértice %d: %.6f rad, esperado: %.6f rad"), 
                   i, AnguloCalculado, AnguloEsperado);
            TodosTestesPassaram = false;
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste dos cálculos angulares: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarCalculosArea()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando cálculos de área..."));
    
    bool TodosTestesPassaram = true;
    
    // Testar área do mapa total
    float AreaMapaCalculada = 16000.0f * 16000.0f;
    float AreaMapaEsperada = 256000000.0f; // 256 milhões de UU²
    
    if (!CompararFloat(AreaMapaCalculada, AreaMapaEsperada, AreaMapaEsperada * TOLERANCIA_TESTE_AREA))
    {
        UE_LOG(LogTemp, Error, TEXT("❌ Área do mapa: %.0f UU², esperado: %.0f UU²"), 
               AreaMapaCalculada, AreaMapaEsperada);
        TodosTestesPassaram = false;
    }
    
    // Testar área dos hexágonos usando fórmula: (3√3/2) × r²
    float Sqrt3 = FMath::Sqrt(3.0f);
    float FatorHexagono = (3.0f * Sqrt3) / 2.0f;
    
    // Base
    float AreaBaseCalculada = FatorHexagono * BASE_RAIO * BASE_RAIO;
    if (!CompararFloat(AreaBaseCalculada, BASE_AREA_ESPERADA, BASE_AREA_ESPERADA * TOLERANCIA_TESTE_AREA))
    {
        UE_LOG(LogTemp, Error, TEXT("❌ Área da base: %.2f UU², esperado: %.2f UU²"), 
               AreaBaseCalculada, BASE_AREA_ESPERADA);
        TodosTestesPassaram = false;
    }
    
    // Ilha
    float AreaIlhaCalculada = FatorHexagono * ILHA_RAIO * ILHA_RAIO;
    if (!CompararFloat(AreaIlhaCalculada, ILHA_AREA_ESPERADA, ILHA_AREA_ESPERADA * TOLERANCIA_TESTE_AREA))
    {
        UE_LOG(LogTemp, Error, TEXT("❌ Área da ilha: %.2f UU², esperado: %.2f UU²"), 
               AreaIlhaCalculada, ILHA_AREA_ESPERADA);
        TodosTestesPassaram = false;
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste dos cálculos de área: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarCalculosDistancia()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando cálculos de distância..."));
    
    bool TodosTestesPassaram = true;
    
    // Testar distância diagonal do mapa
    float DiagonalCalculada = FMath::Sqrt(16000.0f * 16000.0f + 16000.0f * 16000.0f);
    float DiagonalEsperada = 16000.0f * FMath::Sqrt(2.0f); // 16000√2
    
    if (!CompararFloat(DiagonalCalculada, DiagonalEsperada, TOLERANCIA_TESTE_DISTANCIA))
    {
        UE_LOG(LogTemp, Error, TEXT("❌ Diagonal do mapa: %.2f UU, esperado: %.2f UU"), 
               DiagonalCalculada, DiagonalEsperada);
        TodosTestesPassaram = false;
    }
    
    // Testar comprimento das lanes
    float ComprimentoLaneSuperior = FMath::Sqrt(12000.0f * 12000.0f + 13856.0f * 13856.0f);
    float ComprimentoEsperado = 18384.0f; // Aproximadamente
    
    if (!CompararFloat(ComprimentoLaneSuperior, ComprimentoEsperado, 100.0f)) // Tolerância maior para este cálculo
    {
        UE_LOG(LogTemp, Warning, TEXT("⚠️ Comprimento da lane superior: %.2f UU, esperado aproximadamente: %.2f UU"), 
               ComprimentoLaneSuperior, ComprimentoEsperado);
        // Não falha o teste, apenas aviso
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste dos cálculos de distância: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

// ========== TESTES DE INTEGRAÇÃO ==========

bool UTestePrecisaoGeometrica::TestarColisoesBordas()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando colisões nas bordas..."));
    
    bool TodosTestesPassaram = true;
    
    // Testar se as lanes não ultrapassam os limites do mapa
    for (int32 Lane = 0; Lane < 3; Lane++)
    {
        // Testar vários pontos ao longo da lane
        for (float X = -8000.0f; X <= 8000.0f; X += 1000.0f)
        {
            if (AImplementacaoAutomatizada::ValidarPontoNaLane(X, 0.0f, Lane))
            {
                // Se o ponto está na lane, verificar se está dentro do mapa
                if (FMath::Abs(X) > 8000.0f)
                {
                    UE_LOG(LogTemp, Error, TEXT("❌ Lane %d ultrapassa limite do mapa em X: %.1f"), Lane, X);
                    TodosTestesPassaram = false;
                }
            }
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste das colisões nas bordas: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarConectividadeLanes()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando conectividade das lanes..."));
    
    bool TodosTestesPassaram = true;
    
    // Verificar se as lanes se conectam corretamente às bases
    FVector2D BaseAzul(-6000.0f, -6000.0f);
    FVector2D BaseVermelha(6000.0f, 6000.0f);
    
    // Verificar distâncias das lanes às bases
    for (int32 Lane = 0; Lane < 3; Lane++)
    {
        FVector PosInicialAzul = AImplementacaoAutomatizada::CalcularPosicaoTorre(Lane, 0, true);
        FVector PosInicialVermelho = AImplementacaoAutomatizada::CalcularPosicaoTorre(Lane, 0, false);
        
        float DistanciaBaseAzul = FVector2D::Distance(
            FVector2D(PosInicialAzul.X, PosInicialAzul.Y), 
            BaseAzul
        );
        
        float DistanciaBaseVermelha = FVector2D::Distance(
            FVector2D(PosInicialVermelho.X, PosInicialVermelho.Y), 
            BaseVermelha
        );
        
        // As torres devem estar a uma distância razoável das bases
        if (DistanciaBaseAzul > 2000.0f || DistanciaBaseVermelha > 2000.0f)
        {
            UE_LOG(LogTemp, Error, TEXT("❌ Lane %d muito distante das bases: Azul %.1f UU, Vermelha %.1f UU"), 
                   Lane, DistanciaBaseAzul, DistanciaBaseVermelha);
            TodosTestesPassaram = false;
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste da conectividade das lanes: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarFluxoMinions()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando fluxo de minions..."));
    
    bool TodosTestesPassaram = true;
    
    // Testar se os minions podem percorrer toda a extensão das lanes
    for (int32 Lane = 0; Lane < 3; Lane++)
    {
        // Simular movimento do minion do início ao fim da lane
        for (float Progresso = 0.0f; Progresso <= 1.0f; Progresso += 0.1f)
        {
            FVector PosMinion = AImplementacaoAutomatizada::CalcularPosicaoMinion(Lane, Progresso * 13856.0f, true);
            
            // Verificar se o minion está sempre na lane
            if (!AImplementacaoAutomatizada::ValidarPontoNaLane(PosMinion.X, PosMinion.Y, Lane))
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Minion saiu da lane %d em progresso %.1f: (%.1f, %.1f)"), 
                       Lane, Progresso, PosMinion.X, PosMinion.Y);
                TodosTestesPassaram = false;
            }
            
            // Verificar se o minion está dentro do mapa
            if (FMath::Abs(PosMinion.X) > 8000.0f || FMath::Abs(PosMinion.Y) > 8000.0f)
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Minion saiu do mapa na lane %d: (%.1f, %.1f)"), 
                       Lane, PosMinion.X, PosMinion.Y);
                TodosTestesPassaram = false;
            }
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste do fluxo de minions: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

bool UTestePrecisaoGeometrica::TestarAcessoCovils()
{
    UE_LOG(LogTemp, Log, TEXT("🧪 Testando acesso aos covils..."));
    
    bool TodosTestesPassaram = true;
    
    // Verificar se todos os covils são acessíveis (não estão bloqueados por paredes)
    TArray<FVector> PosicoesCovis = {
        FVector(0.0f, 4800.0f, 0.0f),    // Dragão Prismal
        FVector(0.0f, -4800.0f, 0.0f),   // Barão Auracron
        FVector(3000.0f, 2400.0f, 0.0f), // Sentinela NE
        FVector(-3000.0f, 2400.0f, 0.0f), // Sentinela NW
        FVector(3000.0f, -2400.0f, 0.0f), // Sentinela SE
        FVector(-3000.0f, -2400.0f, 0.0f) // Sentinela SW
    };
    
    for (int32 i = 0; i < PosicoesCovis.Num(); i++)
    {
        FVector PosicaoCovil = PosicoesCovis[i];
        
        // Verificar se há um caminho claro do centro do mapa ao covil
        FVector Centro(0.0f, 0.0f, 0.0f);
        FVector Direcao = (PosicaoCovil - Centro).GetSafeNormal();
        
        // Testar pontos ao longo do caminho
        for (float Distancia = 0.0f; Distancia <= FVector::Dist(Centro, PosicaoCovil); Distancia += 500.0f)
        {
            FVector PontoTeste = Centro + Direcao * Distancia;
            
            // Verificar se o ponto está dentro do mapa
            if (FMath::Abs(PontoTeste.X) > 8000.0f || FMath::Abs(PontoTeste.Y) > 8000.0f)
            {
                UE_LOG(LogTemp, Error, TEXT("❌ Caminho para covil %d sai do mapa em (%.1f, %.1f)"), 
                       i, PontoTeste.X, PontoTeste.Y);
                TodosTestesPassaram = false;
                break;
            }
        }
    }
    
    if (TodosTestesPassaram)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ Teste do acesso aos covils: APROVADO"));
    }
    
    return TodosTestesPassaram;
}

// ========== FUNÇÕES AUXILIARES ==========

FResultadoTeste UTestePrecisaoGeometrica::CriarResultadoTeste(const FString& Nome, bool Sucesso, const FString& Detalhes)
{
    FResultadoTeste Resultado;
    Resultado.NomeTeste = Nome;
    Resultado.Sucesso = Sucesso;
    Resultado.Detalhes = Detalhes;
    Resultado.PrecisaoObtida = Sucesso ? 100.0f : 0.0f;
    return Resultado;
}

bool UTestePrecisaoGeometrica::CompararFloat(float Valor1, float Valor2, float Tolerancia)
{
    return FMath::Abs(Valor1 - Valor2) <= Tolerancia;
}

bool UTestePrecisaoGeometrica::CompararVetor(const FVector& Vetor1, const FVector& Vetor2, float Tolerancia)
{
    return CompararFloat(Vetor1.X, Vetor2.X, Tolerancia) &&
           CompararFloat(Vetor1.Y, Vetor2.Y, Tolerancia) &&
           CompararFloat(Vetor1.Z, Vetor2.Z, Tolerancia);
}

void UTestePrecisaoGeometrica::LogResultadoTeste(const FString& NomeTeste, bool Sucesso, const FString& Detalhes)
{
    if (Sucesso)
    {
        UE_LOG(LogTemp, Log, TEXT("✅ %s: APROVADO - %s"), *NomeTeste, *Detalhes);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("❌ %s: REPROVADO - %s"), *NomeTeste, *Detalhes);
    }
}

// ========== COMANDOS DE CONSOLE ==========

static FAutoConsoleCommand ExecutarTestesCommand(
    TEXT("Aura.ExecutarTestes"),
    TEXT("Executa todos os testes de precisão geométrica do mapa"),
    FConsoleCommandDelegate::CreateStatic([]() {
        bool Resultado = UTestePrecisaoGeometrica::ExecutarTodosOsTestes();
        if (Resultado)
        {
            UE_LOG(LogTemp, Log, TEXT("🎯 TODOS OS TESTES APROVADOS - MAPA PRONTO PARA IMPLEMENTAÇÃO!"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("❌ ALGUNS TESTES FALHARAM - REVISAR ESPECIFICAÇÕES!"));
        }
    })
);

FAutoConsoleCommand GerarRelatorioCommand(
    TEXT("Aura.GerarRelatorio"),
    TEXT("Gera relatório completo de validação geométrica"),
    FConsoleCommandDelegate::CreateStatic([]() {
        FString Relatorio = UTestePrecisaoGeometrica::GerarRelatorioCompleto();
        UE_LOG(LogTemp, Log, TEXT("%s"), *Relatorio);

        // Salvar relatório em arquivo
        FString CaminhoArquivo = FPaths::ProjectDir() + TEXT("relatorio_validacao_geometrica.txt");
        FFileHelper::SaveStringToFile(Relatorio, *CaminhoArquivo);
        UE_LOG(LogTemp, Log, TEXT("📄 Relatório salvo em: %s"), *CaminhoArquivo);
    })
);

FAutoConsoleCommand TestarLanesCommand(
    TEXT("Aura.TestarLanes"),
    TEXT("Testa apenas a precisão das lanes"),
    FConsoleCommandDelegate::CreateStatic([]() {
        bool Resultado = UTestePrecisaoGeometrica::TestarPrecisaoLanes();
        UE_LOG(LogTemp, Log, TEXT("Resultado do teste das lanes: %s"), Resultado ? TEXT("APROVADO") : TEXT("REPROVADO"));
    })
);

FAutoConsoleCommand TestarRioCommand(
    TEXT("Aura.TestarRio"),
    TEXT("Testa apenas a função senoidal do rio"),
    FConsoleCommandDelegate::CreateStatic([]() {
        bool Resultado = UTestePrecisaoGeometrica::TestarFuncaoSenoidalRio();
        UE_LOG(LogTemp, Log, TEXT("Resultado do teste do rio: %s"), Resultado ? TEXT("APROVADO") : TEXT("REPROVADO"));
    })
);

// TestarGeometriasCommand já declarado no header

static FAutoConsoleCommand ValidarMapaCompletoCommand(
    TEXT("Aura.ValidarMapaCompleto"),
    TEXT("Executa validação completa e gera relatório detalhado"),
    FConsoleCommandDelegate::CreateStatic([]() {
        UE_LOG(LogTemp, Log, TEXT("🔍 INICIANDO VALIDAÇÃO COMPLETA DO MAPA AURA..."));
        
        // Executar todos os testes
        bool ResultadoGeral = UTestePrecisaoGeometrica::ExecutarTodosOsTestes();
        
        // Gerar relatório
        FString Relatorio = UTestePrecisaoGeometrica::GerarRelatorioCompleto();
        
        // Salvar em arquivo com timestamp
        FDateTime Agora = FDateTime::Now();
        FString Timestamp = Agora.ToString(TEXT("%Y%m%d_%H%M%S"));
        FString NomeArquivo = FString::Printf(TEXT("validacao_mapa_aura_%s.txt"), *Timestamp);
        FString CaminhoCompleto = FPaths::ProjectDir() + NomeArquivo;
        
        FFileHelper::SaveStringToFile(Relatorio, *CaminhoCompleto);
        
        // Log final
        if (ResultadoGeral)
        {
            UE_LOG(LogTemp, Log, TEXT("🎯 VALIDAÇÃO COMPLETA: MAPA APROVADO!"));
            UE_LOG(LogTemp, Log, TEXT("📄 Relatório salvo em: %s"), *CaminhoCompleto);
            UE_LOG(LogTemp, Log, TEXT("O mapa pode ser implementado com 100%% de confianca!"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("❌ VALIDAÇÃO COMPLETA: MAPA REPROVADO!"));
            UE_LOG(LogTemp, Error, TEXT("📄 Relatório com detalhes dos erros salvo em: %s"), *CaminhoCompleto);
            UE_LOG(LogTemp, Error, TEXT("🔧 Revisar especificações antes da implementação!"));
        }
    })
);

// ========== TESTES AUTOMATIZADOS DO UNREAL ENGINE ==========

#if WITH_DEV_AUTOMATION_TESTS

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTesteLanes, "Aura.Geometria.Lanes",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ClientContext | EAutomationTestFlags::ProductFilter)

bool FTesteLanes::RunTest(const FString& Parameters)
{
    bool Resultado = UTestePrecisaoGeometrica::TestarPrecisaoLanes();
    TestTrue(TEXT("Precisão das lanes deve estar correta"), Resultado);
    return Resultado;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTesteRio, "Aura.Geometria.Rio",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ClientContext | EAutomationTestFlags::ProductFilter)

bool FTesteRio::RunTest(const FString& Parameters)
{
    bool Resultado = UTestePrecisaoGeometrica::TestarFuncaoSenoidalRio();
    TestTrue(TEXT("Função senoidal do rio deve estar correta"), Resultado);
    return Resultado;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTesteHexagonos, "Aura.Geometria.Hexagonos",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ClientContext | EAutomationTestFlags::ProductFilter)

bool FTesteHexagonos::RunTest(const FString& Parameters)
{
    bool Resultado = UTestePrecisaoGeometrica::TestarGeometriaHexagonos();
    TestTrue(TEXT("Geometria dos hexágonos deve estar correta"), Resultado);
    return Resultado;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTesteElipses, "Aura.Geometria.Elipses",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ClientContext | EAutomationTestFlags::ProductFilter)

bool FTesteElipses::RunTest(const FString& Parameters)
{
    bool Resultado = UTestePrecisaoGeometrica::TestarGeometriaElipses();
    TestTrue(TEXT("Geometria das elipses deve estar correta"), Resultado);
    return Resultado;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTesteTorres, "Aura.Geometria.Torres",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ClientContext | EAutomationTestFlags::ProductFilter)

bool FTesteTorres::RunTest(const FString& Parameters)
{
    bool Resultado = UTestePrecisaoGeometrica::TestarPosicionamentoTorres();
    TestTrue(TEXT("Posicionamento das torres deve estar correto"), Resultado);
    return Resultado;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTesteBases, "Aura.Geometria.Bases",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ClientContext | EAutomationTestFlags::ProductFilter)

bool FTesteBases::RunTest(const FString& Parameters)
{
    bool Resultado = UTestePrecisaoGeometrica::TestarEspecificacoesBases();
    TestTrue(TEXT("Especificações das bases devem estar corretas"), Resultado);
    return Resultado;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTesteCovils, "Aura.Geometria.Covils",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ClientContext | EAutomationTestFlags::ProductFilter)

bool FTesteCovils::RunTest(const FString& Parameters)
{
    bool Resultado = UTestePrecisaoGeometrica::TestarDimensoesCovils();
    TestTrue(TEXT("Dimensões dos covils devem estar corretas"), Resultado);
    return Resultado;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTesteParedes, "Aura.Geometria.Paredes",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ClientContext | EAutomationTestFlags::ProductFilter)

bool FTesteParedes::RunTest(const FString& Parameters)
{
    bool Resultado = UTestePrecisaoGeometrica::TestarSistemaParedes();
    TestTrue(TEXT("Sistema de paredes deve estar correto"), Resultado);
    return Resultado;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTesteMinions, "Aura.Gameplay.Minions",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ClientContext | EAutomationTestFlags::ProductFilter)

bool FTesteMinions::RunTest(const FString& Parameters)
{
    bool Resultado = UTestePrecisaoGeometrica::TestarSistemaMinions();
    TestTrue(TEXT("Sistema de minions deve estar correto"), Resultado);
    return Resultado;
}

IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTestePrecisaoGeral, "Aura.Validacao.PrecisaoGeral",
    EAutomationTestFlags::EditorContext | EAutomationTestFlags::ClientContext | EAutomationTestFlags::ProductFilter)

bool FTestePrecisaoGeral::RunTest(const FString& Parameters)
{
    bool Resultado = UTestePrecisaoGeometrica::ExecutarTodosOsTestes();
    TestTrue(TEXT("Todos os testes geométricos devem passar"), Resultado);

    if (Resultado)
    {
        AddInfo(TEXT("🎯 MAPA AURA VALIDADO COM 100% DE PRECISÃO!"));
    }
    else
    {
        AddError(TEXT("❌ Mapa falhou na validação - revisar especificações!"));
    }

    return Resultado;
}

#endif // WITH_DEV_AUTOMATION_TESTS