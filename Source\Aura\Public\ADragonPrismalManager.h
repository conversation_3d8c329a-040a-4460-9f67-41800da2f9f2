#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "Components/SceneComponent.h"
#include "Components/AudioComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Sound/SoundBase.h"
#include "Sound/SoundCue.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Math/UnrealMathUtility.h"
#include "Containers/Array.h"
#include "UObject/ObjectMacros.h"
#include "TimerManager.h"
#include "Engine/TimerHandle.h"
#include "Engine/World.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Logging/LogMacros.h"
#include "ADragonPrismalManager.generated.h"

// Enum para tipos de dano
UENUM(BlueprintType)
enum class EDamageType : uint8
{
    Physical    UMETA(DisplayName = "Physical"),
    Magical     UMETA(DisplayName = "Magical"),
    TrueDamage  UMETA(DisplayName = "True Damage")
};

// Enum para tipos de ataque do dragão
UENUM(BlueprintType)
enum class EDragonAttackType : uint8
{
    SoproPrismal    UMETA(DisplayName = "Sopro Prismal"),
    Cristalizacao   UMETA(DisplayName = "Cristalização"),
    AreaDamage      UMETA(DisplayName = "Area Damage")
};

// Enum para elementos do dragão
UENUM(BlueprintType)
enum class EDragonElement : uint8
{
    Prismal     UMETA(DisplayName = "Prismal"),
    Fire        UMETA(DisplayName = "Fire"),
    Ice         UMETA(DisplayName = "Ice"),
    Lightning   UMETA(DisplayName = "Lightning"),
    Water       UMETA(DisplayName = "Water"),
    Earth       UMETA(DisplayName = "Earth"),
    Air         UMETA(DisplayName = "Air")
};

// Enums para estados e tipos do Dragão
UENUM(BlueprintType)
enum class EDragonState : uint8
{
    Dormant     UMETA(DisplayName = "Dormindo"),
    Spawning    UMETA(DisplayName = "Aparecendo"),
    Active      UMETA(DisplayName = "Ativo"),
    Combat      UMETA(DisplayName = "Em Combate"),
    Patrolling  UMETA(DisplayName = "Patrulhando"),
    Flying      UMETA(DisplayName = "Voando"),
    Landing     UMETA(DisplayName = "Pousando"),
    Dead        UMETA(DisplayName = "Morto")
};

UENUM(BlueprintType)
enum class EDragonType : uint8
{
    Infernal    UMETA(DisplayName = "Dragão Infernal"),
    Ocean       UMETA(DisplayName = "Dragão Oceânico"),
    Mountain    UMETA(DisplayName = "Dragão da Montanha"),
    Cloud       UMETA(DisplayName = "Dragão das Nuvens")
};

// Estruturas de dados para o Dragão e sistema de recompensas
USTRUCT(BlueprintType)
struct FDragonData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon")
    FVector Position;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon")
    EDragonType Type;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon")
    EDragonElement Element;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon")
    float BaseHealth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon")
    float CurrentHealth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon")
    float HealthScaling;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon")
    float SpawnTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon")
    float RespawnTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon")
    float FlightHeight;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon")
    float FlightSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon")
    EDragonState CurrentState;

    // Propriedades de combate faltantes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon Combat")
    bool bIsInCombat;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon Combat")
    bool bFuriaCrescenteActive;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon Combat")
    float DamageMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon Combat")
    float SpeedMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon Combat")
    float TotalDamageDealt;

    FDragonData()
    {
        Position = FVector(0.0f, 4800.0f, 0.0f);
        Type = EDragonType::Infernal;
        Element = EDragonElement::Prismal;
        BaseHealth = 8000.0f;
        CurrentHealth = 8000.0f;
        HealthScaling = 500.0f;
        SpawnTime = 1800.0f; // 30 minutos
        RespawnTime = 420.0f; // 7 minutos
        FlightHeight = 800.0f;
        FlightSpeed = 600.0f;
        CurrentState = EDragonState::Dormant;
        bIsInCombat = false;
        bFuriaCrescenteActive = false;
        DamageMultiplier = 1.0f;
        SpeedMultiplier = 1.0f;
        TotalDamageDealt = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct FEllipticalArea
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    FVector Center;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float SemiMajorAxis; // Eixo maior (a)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float SemiMinorAxis; // Eixo menor (b)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float Eccentricity; // Excentricidade da elipse

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float Area;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float Perimeter;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    TArray<FVector> EllipsePoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    TArray<FVector> LandingZones;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Geometry")
    float RotationAngle; // Rotação da elipse em graus

    FEllipticalArea()
    {
        Center = FVector(0.0f, 4800.0f, 0.0f);
        SemiMajorAxis = 900.0f;
        SemiMinorAxis = 600.0f;
        Eccentricity = 0.0f;
        Area = 0.0f;
        Perimeter = 0.0f;
        RotationAngle = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct FDragonReward
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    float BaseGold;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    float BaseExperience;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    float GoldScaling;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    float ExperienceScaling;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    float TeamBonusMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    float SoloBonusMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    int32 MaxParticipants;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward")
    float ParticipationThreshold; // % de dano mínimo para recompensa

    FDragonReward()
    {
        BaseGold = 500.0f;
        BaseExperience = 1200.0f;
        GoldScaling = 25.0f;
        ExperienceScaling = 40.0f;
        TeamBonusMultiplier = 1.5f;
        SoloBonusMultiplier = 2.0f;
        MaxParticipants = 5;
        ParticipationThreshold = 0.05f; // 5%
    }
};

USTRUCT(BlueprintType)
struct FDragonAttack
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
    FString AttackName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
    float Damage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
    float Range;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
    float Cooldown;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
    EDragonElement Element;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
    bool bIsAreaAttack;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
    float AreaRadius;

    FDragonAttack()
    {
        AttackName = TEXT("Breath Attack");
        Damage = 800.0f;
        Range = 1200.0f;
        Cooldown = 5.0f;
        Element = EDragonElement::Fire;
        bIsAreaAttack = true;
        AreaRadius = 400.0f;
    }
};

USTRUCT(BlueprintType)
struct FFlightPath
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flight")
    TArray<FVector> Waypoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flight")
    float PathLength;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flight")
    float FlightDuration;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flight")
    bool bIsCircular;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flight")
    int32 CurrentWaypointIndex;

    FFlightPath()
    {
        PathLength = 0.0f;
        FlightDuration = 0.0f;
        bIsCircular = true;
        CurrentWaypointIndex = 0;
    }
};

UCLASS(BlueprintType, Blueprintable)
class AURA_API ADragonPrismalManager : public AActor
{
    GENERATED_BODY()

public:
    ADragonPrismalManager();

protected:
    virtual void BeginPlay() override;

public:
    virtual void Tick(float DeltaTime) override;

    // Componentes principais
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class USceneComponent* RootSceneComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UStaticMeshComponent> DragonMesh;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USphereComponent> CombatArea;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USphereComponent> DetectionArea;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USphereComponent> RewardArea;

    // Componentes de efeitos visuais e sonoros
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Effects")
    TObjectPtr<UNiagaraComponent> SoproPrismalParticles;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Effects")
    TObjectPtr<UNiagaraComponent> FuriaCrescenteParticles;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Effects")
    TObjectPtr<UAudioComponent> DragonAudioComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Effects")
    TObjectPtr<UAudioComponent> CombatAudioComponent;

    // Dados do Dragão Prismal
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dragon Configuration")
    FDragonData DragonData;

    // Geometria elíptica do covil
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elliptical Geometry")
    FEllipticalArea EllipticalCovil;

    // Sistema de recompensas
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reward System")
    FDragonReward RewardSystem;

    // Assets de efeitos visuais e sonoros
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects Assets")
    TObjectPtr<UNiagaraSystem> SoproPrismalEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects Assets")
    TObjectPtr<UNiagaraSystem> FuriaCrescenteEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects Assets")
    TObjectPtr<UNiagaraSystem> CrystalShatterEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects Assets")
    TObjectPtr<USoundBase> SoproPrismalSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects Assets")
    TObjectPtr<USoundBase> FuriaCrescenteSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects Assets")
    TObjectPtr<USoundBase> DragonRoarSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects Assets")
    TObjectPtr<USoundBase> CrystalBreakSound;

    // Sistema de ataques
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat System")
    TArray<FDragonAttack> DragonAttacks;

    // Sistema de voo
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flight System")
    FFlightPath FlightPath;

    // Configurações de timing
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
    float GameStartTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
    float CurrentGameTime;

    // Propriedades de debug faltantes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowDebugInfo;

    // Timers para combate
    UPROPERTY()
    FTimerHandle CombatExitTimer;

    // Constantes de combate
    static constexpr float BASE_HEALTH = 3000.0f;
    static constexpr float HEALTH_PER_MINUTE = 200.0f;
    static constexpr float SOPRO_PRISMAL_RANGE = 1000.0f;
    static constexpr float SOPRO_PRISMAL_ANGLE = 60.0f;

    // Participantes do combate
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Tracking")
    TMap<AActor*, float> CombatParticipants; // Actor -> Dano causado

    // Funções públicas para geometria elíptica
    UFUNCTION(BlueprintCallable, Category = "Elliptical Geometry")
    void CalculateEllipticalGeometry();

    UFUNCTION(BlueprintCallable, Category = "Elliptical Geometry")
    float CalculateEllipticalArea() const;

    UFUNCTION(BlueprintCallable, Category = "Elliptical Geometry")
    float CalculateEllipticalPerimeter() const;

    UFUNCTION(BlueprintCallable, Category = "Elliptical Geometry")
    float CalculateEccentricity() const;

    UFUNCTION(BlueprintCallable, Category = "Elliptical Geometry")
    TArray<FVector> GenerateEllipsePoints(int32 NumPoints = 64) const;

    UFUNCTION(BlueprintCallable, Category = "Elliptical Geometry")
    bool IsPositionInEllipse(const FVector& Position) const;

    UFUNCTION(BlueprintCallable, Category = "Elliptical Geometry")
    FVector GetClosestPointOnEllipse(const FVector& FromPosition) const;

    UFUNCTION(BlueprintCallable, Category = "Elliptical Geometry")
    TArray<FVector> CalculateLandingZones() const;

    // Funções de spawn e controle do Dragão
    UFUNCTION(BlueprintCallable, Category = "Dragon Management")
    void SpawnDragon();

    UFUNCTION(BlueprintCallable, Category = "Dragon Management")
    void DespawnDragon();

    UFUNCTION(BlueprintCallable, Category = "Dragon Management")
    void UpdateDragonHealth();

    UFUNCTION(BlueprintCallable, Category = "Dragon Management")
    void StartDragonFlight();

    UFUNCTION(BlueprintCallable, Category = "Dragon Management")
    void LandDragon();

    // Funções de combate
    UFUNCTION(BlueprintCallable, Category = "Combat")
    void StartCombat(AActor* Attacker);

    UFUNCTION(BlueprintCallable, Category = "Combat")
    void EndCombat();

    UFUNCTION(BlueprintCallable, Category = "Combat")
    virtual float TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser) override;

    UFUNCTION(BlueprintCallable, Category = "Combat")
    void OnDragonDeath(AActor* Killer);

    UFUNCTION(BlueprintCallable, Category = "Combat")
    void PerformAttack(int32 AttackIndex);

    UFUNCTION(BlueprintCallable, Category = "Combat")
    void ExecuteBreathAttack(const FDragonAttack& Attack);

    UFUNCTION(BlueprintCallable, Category = "Combat")
    void ExecuteAreaAttack(const FDragonAttack& Attack);

    // Sistema de voo
    UFUNCTION(BlueprintCallable, Category = "Flight System")
    void GenerateFlightPath();

    UFUNCTION(BlueprintCallable, Category = "Flight System")
    void UpdateFlightMovement(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Flight System")
    FVector GetNextWaypoint() const;

    UFUNCTION(BlueprintCallable, Category = "Flight System")
    void AdvanceToNextWaypoint();

    UFUNCTION(BlueprintCallable, Category = "Flight System")
    float CalculateFlightPathLength() const;

    // Sistema de recompensas
    UFUNCTION(BlueprintCallable, Category = "Reward System")
    void CalculateAndDistributeRewards();

    UFUNCTION(BlueprintCallable, Category = "Reward System")
    TArray<AActor*> GetEligibleParticipants() const;

    UFUNCTION(BlueprintCallable, Category = "Reward System")
    float CalculateParticipationPercentage(AActor* Participant) const;

    UFUNCTION(BlueprintCallable, Category = "Reward System")
    void GiveRewardToParticipant(AActor* Participant, float GoldAmount, float ExperienceAmount);

    UFUNCTION(BlueprintCallable, Category = "Reward System")
    float CalculateTeamBonus(const TArray<AActor*>& TeamMembers) const;

    UFUNCTION(BlueprintCallable, Category = "Reward System")
    float CalculateSoloBonus(AActor* SoloPlayer) const;

    // Funções de validação e debug
    UFUNCTION(BlueprintCallable, Category = "Validation")
    bool ValidateEllipticalGeometry() const;

    UFUNCTION(BlueprintCallable, Category = "Validation")
    void DrawDebugEllipse() const;

    UFUNCTION(BlueprintCallable, Category = "Validation")
    void DrawDebugFlightPath() const;

    UFUNCTION(BlueprintCallable, Category = "Validation")
    void DrawDebugLandingZones() const;

    // Funções de timing e estado
    UFUNCTION(BlueprintCallable, Category = "Timing")
    bool ShouldSpawnDragon() const;

    UFUNCTION(BlueprintCallable, Category = "Timing")
    float GetTimeUntilSpawn() const;

    UFUNCTION(BlueprintCallable, Category = "Timing")
    float GetTimeUntilRespawn() const;

    UFUNCTION(BlueprintCallable, Category = "State")
    void SetDragonState(EDragonState NewState);

    UFUNCTION(BlueprintCallable, Category = "State")
    EDragonState GetDragonState() const { return DragonData.CurrentState; }

    // Funções de elementos e tipos
    UFUNCTION(BlueprintCallable, Category = "Dragon Type")
    void SetDragonType(EDragonType NewType);

    UFUNCTION(BlueprintCallable, Category = "Dragon Type")
    void SetDragonElement(EDragonElement NewElement);

    UFUNCTION(BlueprintCallable, Category = "Dragon Type")
    void UpdateAttacksForElement();

private:
    // Funções auxiliares privadas
    void InitializeDragonData();
    void InitializeEllipticalArea();
    void InitializeFlightPath();

public:
    // CORRIGIDO: Funções movidas para público para AMapManager
    UFUNCTION(BlueprintCallable, Category = "Dragon Combat")
    void SetupCombatMechanics();

    UFUNCTION(BlueprintCallable, Category = "Dragon Rewards")
    void InitializeRewardSystem();

private:
    void UpdateGameTime(float DeltaTime);
    void CheckSpawnConditions();
    void UpdateDragonBehavior(float DeltaTime);
    void HandleCombatLogic();
    void HandleFlightLogic();
    void ProcessRewards();
    void CleanupInactiveParticipants();
    
    // Mecânicas específicas
    TArray<AActor*> FindTargetsInCone(const FVector& StartLocation, const FVector& Direction, float Range, float AngleDegrees) const;
    void ApplyDamageToTarget(ACharacter* Target, float Damage, EDamageType DamageType);
    void PlaySoproPrismalEffects();
    void PlayFuriaCrescenteEffects();
    void StopFuriaCrescenteEffects();
    void UpdateCombatTracking(float DeltaTime);
    void UpdateCombatTrackingTimer(); // Wrapper sem parâmetros para timer

    // Funções de ataque faltantes
    void PerformSoproPrismal();
    void ApplyCrystallizationEffect(ACharacter* Target);
    void ActivateFuriaCrescente();
    void UpdateFuriaCrescente();
    void DeactivateFuriaCrescente();
    void ExitCombatState();
    EDragonAttackType DetermineNextAttack();
    void ExecuteSoproPrismalAttack();
    void ExecuteCristalizacaoAttack();
    void ExecuteAreaDamageAttack();

    // Funções de área de combate
    UFUNCTION()
    void OnCombatAreaEnter(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

    UFUNCTION()
    void OnCombatAreaExit(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);

    UFUNCTION()
    void OnDetectionAreaEnter(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

    UFUNCTION()
    void OnDetectionAreaExit(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);

    UFUNCTION()
    void OnRewardAreaEnter(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

    UFUNCTION()
    void OnRewardAreaExit(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);
    
    // Funções matemáticas para geometria elíptica
    FVector CalculateEllipsePoint(float AngleRadians) const;
    FVector CalculateEllipsePoint(float AngleRadians, const FVector& Center, float SemiMajor, float SemiMinor, float RotationAngle) const;
    bool IsPointInsideEllipse(const FVector& Point) const;
    float CalculateDistanceToEllipseEdge(const FVector& Point) const;
    
    // Funções de rotação matemática
    FVector RotatePoint2D(const FVector& Point, float AngleDegrees) const;
    FMatrix GetEllipseTransformMatrix() const;
    
    // Funções de voo e movimento
    FVector InterpolateFlightPosition(float Alpha) const;
    void UpdateDragonRotation(const FVector& TargetDirection);
    
    // Funções de combate auxiliares
    void AddCombatParticipant(AActor* Participant, float Damage);
    void RemoveInactiveCombatParticipants();
    float GetTotalDamageDealt() const;
    
    // Timers para controle de eventos
    FTimerHandle DragonSpawnTimer;
    FTimerHandle FlightTimer;
    FTimerHandle AttackTimer;
    FTimerHandle CombatTrackingTimer;
    
    // Constantes do sistema
    static constexpr float DRAGON_SPAWN_TIME = 300.0f;        // 5 minutos
    static constexpr float DRAGON_RESPAWN_TIME = 360.0f;      // 6 minutos
    static constexpr float COMBAT_TIMEOUT = 180.0f;           // 3 minutos
    static constexpr float FLIGHT_HEIGHT = 1000.0f;          // Altura de voo
    static constexpr float FLIGHT_SPEED = 800.0f;            // Velocidade de voo
    static constexpr int32 FLIGHT_WAYPOINTS = 8;             // Pontos de voo
    static constexpr float REWARD_DISTRIBUTION_DELAY = 3.0f;  // Delay para recompensas
    
    // Constantes de geometria elíptica
    static constexpr float ELLIPSE_CENTER_X = 0.0f;
    static constexpr float ELLIPSE_CENTER_Y = 4800.0f;
    static constexpr float ELLIPSE_SEMI_MAJOR_AXIS = 800.0f;
    static constexpr float ELLIPSE_SEMI_MINOR_AXIS = 600.0f;
    
    // Constantes de mecânicas (removidas duplicatas)
    static constexpr float MAGIC_RESISTANCE = 0.4f;          // 40% resistência mágica
    static constexpr float PHYSICAL_RESISTANCE = 0.2f;       // 20% resistência física
    
    // Constantes de HP escalável
    static constexpr float BASE_HP = 3000.0f;                // HP base
    static constexpr float HP_SCALING_PER_MINUTE = 200.0f;   // HP por minuto
    
    // Constantes matemáticas
    static const float ELLIPSE_SEMI_MAJOR;
    static const float ELLIPSE_SEMI_MINOR;
    
    // Configurações de voo
    static constexpr float LANDING_SPEED = 200.0f;
    
    // Configurações de combate
    static constexpr float COMBAT_DETECTION_RADIUS = 1500.0f;
    static constexpr float REWARD_DISTRIBUTION_RADIUS = 2000.0f;
    static constexpr float ATTACK_COOLDOWN_BASE = 3.0f;
    
    // Configurações de recompensas
    static constexpr float BASE_GOLD_REWARD = 500.0f;
    static constexpr float BASE_EXPERIENCE_REWARD = 1200.0f;
    static constexpr float PARTICIPATION_THRESHOLD = 0.05f; // 5%
    
    // Configurações de timing
    static constexpr float FLIGHT_DURATION = 120.0f; // 2 minutos
    static constexpr float LANDING_DURATION = 300.0f; // 5 minutos
};