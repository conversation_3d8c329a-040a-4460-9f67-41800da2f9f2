// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AMinionWaveManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAMinionWaveManager() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_ABaronAuracronManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_ADragonPrismalManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_ALaneManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AMinionWaveManager();
AURA_API UClass* Z_Construct_UClass_AMinionWaveManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_ARiverPrismalManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_AWallCollisionManager_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EFormationType();
AURA_API UEnum* Z_Construct_UEnum_Aura_ELaneType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EMinionState();
AURA_API UEnum* Z_Construct_UEnum_Aura_EMinionType();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPathfindingAlgorithm();
AURA_API UEnum* Z_Construct_UEnum_Aura_EWaveType();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FMinionData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FMinionFormation();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FMinionSpawnPoint();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPathfindingGrid();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPathfindingNode();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FRewardFormula();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FWaveConfiguration();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FWaveData();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EMinionType ***************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMinionType;
static UEnum* EMinionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMinionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMinionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EMinionType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EMinionType"));
	}
	return Z_Registration_Info_UEnum_EMinionType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EMinionType>()
{
	return EMinionType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EMinionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Caster.DisplayName", "Caster" },
		{ "Caster.Name", "EMinionType::Caster" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums para o sistema de minions\n" },
#endif
		{ "Melee.DisplayName", "Melee" },
		{ "Melee.Name", "EMinionType::Melee" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
		{ "Ranged.DisplayName", "Ranged" },
		{ "Ranged.Name", "EMinionType::Ranged" },
		{ "Siege.DisplayName", "Siege" },
		{ "Siege.Name", "EMinionType::Siege" },
		{ "Super.DisplayName", "Super" },
		{ "Super.Name", "EMinionType::Super" },
		{ "Support.DisplayName", "Support" },
		{ "Support.Name", "EMinionType::Support" },
		{ "Tank.DisplayName", "Tank" },
		{ "Tank.Name", "EMinionType::Tank" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums para o sistema de minions" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMinionType::Melee", (int64)EMinionType::Melee },
		{ "EMinionType::Ranged", (int64)EMinionType::Ranged },
		{ "EMinionType::Caster", (int64)EMinionType::Caster },
		{ "EMinionType::Tank", (int64)EMinionType::Tank },
		{ "EMinionType::Support", (int64)EMinionType::Support },
		{ "EMinionType::Siege", (int64)EMinionType::Siege },
		{ "EMinionType::Super", (int64)EMinionType::Super },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EMinionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EMinionType",
	"EMinionType",
	Z_Construct_UEnum_Aura_EMinionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EMinionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EMinionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EMinionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EMinionType()
{
	if (!Z_Registration_Info_UEnum_EMinionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMinionType.InnerSingleton, Z_Construct_UEnum_Aura_EMinionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMinionType.InnerSingleton;
}
// ********** End Enum EMinionType *****************************************************************

// ********** Begin Enum EWaveType *****************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EWaveType;
static UEnum* EWaveType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EWaveType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EWaveType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EWaveType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EWaveType"));
	}
	return Z_Registration_Info_UEnum_EWaveType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EWaveType>()
{
	return EWaveType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EWaveType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Boss.DisplayName", "Boss" },
		{ "Boss.Name", "EWaveType::Boss" },
		{ "Cannon.DisplayName", "Cannon" },
		{ "Cannon.Name", "EWaveType::Cannon" },
		{ "Elite.DisplayName", "Elite" },
		{ "Elite.Name", "EWaveType::Elite" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "EWaveType::Normal" },
		{ "Siege.DisplayName", "Siege" },
		{ "Siege.Name", "EWaveType::Siege" },
		{ "Super.DisplayName", "Super" },
		{ "Super.Name", "EWaveType::Super" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EWaveType::Normal", (int64)EWaveType::Normal },
		{ "EWaveType::Elite", (int64)EWaveType::Elite },
		{ "EWaveType::Boss", (int64)EWaveType::Boss },
		{ "EWaveType::Siege", (int64)EWaveType::Siege },
		{ "EWaveType::Cannon", (int64)EWaveType::Cannon },
		{ "EWaveType::Super", (int64)EWaveType::Super },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EWaveType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EWaveType",
	"EWaveType",
	Z_Construct_UEnum_Aura_EWaveType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EWaveType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EWaveType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EWaveType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EWaveType()
{
	if (!Z_Registration_Info_UEnum_EWaveType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EWaveType.InnerSingleton, Z_Construct_UEnum_Aura_EWaveType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EWaveType.InnerSingleton;
}
// ********** End Enum EWaveType *******************************************************************

// ********** Begin Enum EMinionState **************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMinionState;
static UEnum* EMinionState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMinionState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMinionState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EMinionState, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EMinionState"));
	}
	return Z_Registration_Info_UEnum_EMinionState.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EMinionState>()
{
	return EMinionState_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EMinionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Dead.DisplayName", "Dead" },
		{ "Dead.Name", "EMinionState::Dead" },
		{ "Fighting.DisplayName", "Fighting" },
		{ "Fighting.Name", "EMinionState::Fighting" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
		{ "Moving.DisplayName", "Moving" },
		{ "Moving.Name", "EMinionState::Moving" },
		{ "Retreating.DisplayName", "Retreating" },
		{ "Retreating.Name", "EMinionState::Retreating" },
		{ "Spawning.DisplayName", "Spawning" },
		{ "Spawning.Name", "EMinionState::Spawning" },
		{ "Waiting.DisplayName", "Waiting" },
		{ "Waiting.Name", "EMinionState::Waiting" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMinionState::Spawning", (int64)EMinionState::Spawning },
		{ "EMinionState::Moving", (int64)EMinionState::Moving },
		{ "EMinionState::Fighting", (int64)EMinionState::Fighting },
		{ "EMinionState::Retreating", (int64)EMinionState::Retreating },
		{ "EMinionState::Dead", (int64)EMinionState::Dead },
		{ "EMinionState::Waiting", (int64)EMinionState::Waiting },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EMinionState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EMinionState",
	"EMinionState",
	Z_Construct_UEnum_Aura_EMinionState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EMinionState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EMinionState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EMinionState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EMinionState()
{
	if (!Z_Registration_Info_UEnum_EMinionState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMinionState.InnerSingleton, Z_Construct_UEnum_Aura_EMinionState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMinionState.InnerSingleton;
}
// ********** End Enum EMinionState ****************************************************************

// ********** Begin Enum ELaneType *****************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ELaneType;
static UEnum* ELaneType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ELaneType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ELaneType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_ELaneType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("ELaneType"));
	}
	return Z_Registration_Info_UEnum_ELaneType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<ELaneType>()
{
	return ELaneType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_ELaneType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Bottom.DisplayName", "Bottom" },
		{ "Bottom.Name", "ELaneType::Bottom" },
		{ "Middle.DisplayName", "Middle" },
		{ "Middle.Name", "ELaneType::Middle" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
		{ "Top.DisplayName", "Top" },
		{ "Top.Name", "ELaneType::Top" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ELaneType::Top", (int64)ELaneType::Top },
		{ "ELaneType::Middle", (int64)ELaneType::Middle },
		{ "ELaneType::Bottom", (int64)ELaneType::Bottom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_ELaneType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"ELaneType",
	"ELaneType",
	Z_Construct_UEnum_Aura_ELaneType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ELaneType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ELaneType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_ELaneType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_ELaneType()
{
	if (!Z_Registration_Info_UEnum_ELaneType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ELaneType.InnerSingleton, Z_Construct_UEnum_Aura_ELaneType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ELaneType.InnerSingleton;
}
// ********** End Enum ELaneType *******************************************************************

// ********** Begin Enum EFormationType ************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EFormationType;
static UEnum* EFormationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EFormationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EFormationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EFormationType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EFormationType"));
	}
	return Z_Registration_Info_UEnum_EFormationType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EFormationType>()
{
	return EFormationType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EFormationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Box.DisplayName", "Box" },
		{ "Box.Name", "EFormationType::Box" },
		{ "Column.DisplayName", "Column" },
		{ "Column.Name", "EFormationType::Column" },
		{ "Line.DisplayName", "Line" },
		{ "Line.Name", "EFormationType::Line" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
		{ "Scattered.DisplayName", "Scattered" },
		{ "Scattered.Name", "EFormationType::Scattered" },
		{ "Wedge.DisplayName", "Wedge" },
		{ "Wedge.Name", "EFormationType::Wedge" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EFormationType::Line", (int64)EFormationType::Line },
		{ "EFormationType::Column", (int64)EFormationType::Column },
		{ "EFormationType::Wedge", (int64)EFormationType::Wedge },
		{ "EFormationType::Box", (int64)EFormationType::Box },
		{ "EFormationType::Scattered", (int64)EFormationType::Scattered },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EFormationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EFormationType",
	"EFormationType",
	Z_Construct_UEnum_Aura_EFormationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EFormationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EFormationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EFormationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EFormationType()
{
	if (!Z_Registration_Info_UEnum_EFormationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EFormationType.InnerSingleton, Z_Construct_UEnum_Aura_EFormationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EFormationType.InnerSingleton;
}
// ********** End Enum EFormationType **************************************************************

// ********** Begin Enum EPathfindingAlgorithm *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPathfindingAlgorithm;
static UEnum* EPathfindingAlgorithm_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPathfindingAlgorithm.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPathfindingAlgorithm.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPathfindingAlgorithm, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPathfindingAlgorithm"));
	}
	return Z_Registration_Info_UEnum_EPathfindingAlgorithm.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPathfindingAlgorithm>()
{
	return EPathfindingAlgorithm_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPathfindingAlgorithm_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AStar.DisplayName", "A*" },
		{ "AStar.Name", "EPathfindingAlgorithm::AStar" },
		{ "BFS.DisplayName", "Breadth-First Search" },
		{ "BFS.Name", "EPathfindingAlgorithm::BFS" },
		{ "BlueprintType", "true" },
		{ "DFS.DisplayName", "Depth-First Search" },
		{ "DFS.Name", "EPathfindingAlgorithm::DFS" },
		{ "Dijkstra.DisplayName", "Dijkstra" },
		{ "Dijkstra.Name", "EPathfindingAlgorithm::Dijkstra" },
		{ "JPS.DisplayName", "Jump Point Search" },
		{ "JPS.Name", "EPathfindingAlgorithm::JPS" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPathfindingAlgorithm::AStar", (int64)EPathfindingAlgorithm::AStar },
		{ "EPathfindingAlgorithm::Dijkstra", (int64)EPathfindingAlgorithm::Dijkstra },
		{ "EPathfindingAlgorithm::BFS", (int64)EPathfindingAlgorithm::BFS },
		{ "EPathfindingAlgorithm::DFS", (int64)EPathfindingAlgorithm::DFS },
		{ "EPathfindingAlgorithm::JPS", (int64)EPathfindingAlgorithm::JPS },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPathfindingAlgorithm_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPathfindingAlgorithm",
	"EPathfindingAlgorithm",
	Z_Construct_UEnum_Aura_EPathfindingAlgorithm_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPathfindingAlgorithm_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPathfindingAlgorithm_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPathfindingAlgorithm_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPathfindingAlgorithm()
{
	if (!Z_Registration_Info_UEnum_EPathfindingAlgorithm.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPathfindingAlgorithm.InnerSingleton, Z_Construct_UEnum_Aura_EPathfindingAlgorithm_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPathfindingAlgorithm.InnerSingleton;
}
// ********** End Enum EPathfindingAlgorithm *******************************************************

// ********** Begin ScriptStruct FRewardFormula ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRewardFormula;
class UScriptStruct* FRewardFormula::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRewardFormula.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRewardFormula.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRewardFormula, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("RewardFormula"));
	}
	return Z_Registration_Info_UScriptStruct_FRewardFormula.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRewardFormula_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// CORRIGIDO: Estruturas adicionais para sistema de recompensas conforme documenta\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "CORRIGIDO: Estruturas adicionais para sistema de recompensas conforme documenta\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinionType_MetaData[] = {
		{ "Category", "Reward Formula" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseGold_MetaData[] = {
		{ "Category", "Reward Formula" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GoldPerWave_MetaData[] = {
		{ "Category", "Reward Formula" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseXP_MetaData[] = {
		{ "Category", "Reward Formula" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_XPPerWave_MetaData[] = {
		{ "Category", "Reward Formula" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxXP_MetaData[] = {
		{ "Category", "Reward Formula" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinionType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseGold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GoldPerWave;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseXP;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_XPPerWave;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxXP;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRewardFormula>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_MinionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_MinionType = { "MinionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRewardFormula, MinionType), Z_Construct_UEnum_Aura_EMinionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinionType_MetaData), NewProp_MinionType_MetaData) }; // 1491242637
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_BaseGold = { "BaseGold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRewardFormula, BaseGold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseGold_MetaData), NewProp_BaseGold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_GoldPerWave = { "GoldPerWave", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRewardFormula, GoldPerWave), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GoldPerWave_MetaData), NewProp_GoldPerWave_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_BaseXP = { "BaseXP", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRewardFormula, BaseXP), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseXP_MetaData), NewProp_BaseXP_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_XPPerWave = { "XPPerWave", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRewardFormula, XPPerWave), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_XPPerWave_MetaData), NewProp_XPPerWave_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_MaxXP = { "MaxXP", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRewardFormula, MaxXP), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxXP_MetaData), NewProp_MaxXP_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRewardFormula_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_MinionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_MinionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_BaseGold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_GoldPerWave,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_BaseXP,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_XPPerWave,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRewardFormula_Statics::NewProp_MaxXP,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRewardFormula_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRewardFormula_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"RewardFormula",
	Z_Construct_UScriptStruct_FRewardFormula_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRewardFormula_Statics::PropPointers),
	sizeof(FRewardFormula),
	alignof(FRewardFormula),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRewardFormula_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRewardFormula_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRewardFormula()
{
	if (!Z_Registration_Info_UScriptStruct_FRewardFormula.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRewardFormula.InnerSingleton, Z_Construct_UScriptStruct_FRewardFormula_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRewardFormula.InnerSingleton;
}
// ********** End ScriptStruct FRewardFormula ******************************************************

// ********** Begin ScriptStruct FWaveConfiguration ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FWaveConfiguration;
class UScriptStruct* FWaveConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FWaveConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FWaveConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FWaveConfiguration, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("WaveConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FWaveConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FWaveConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// CORRIGIDO: Estrutura para configura\xc3\xa7\xc3\xa3o de ondas\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "CORRIGIDO: Estrutura para configura\xc3\xa7\xc3\xa3o de ondas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveNumber_MetaData[] = {
		{ "Category", "Wave Configuration" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveType_MetaData[] = {
		{ "Category", "Wave Configuration" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinionComposition_MetaData[] = {
		{ "Category", "Wave Configuration" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnTime_MetaData[] = {
		{ "Category", "Wave Configuration" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_WaveNumber;
	static const UECodeGen_Private::FBytePropertyParams NewProp_WaveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WaveType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinionComposition_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinionComposition_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinionComposition_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_MinionComposition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FWaveConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_WaveNumber = { "WaveNumber", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveConfiguration, WaveNumber), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveNumber_MetaData), NewProp_WaveNumber_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_WaveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_WaveType = { "WaveType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveConfiguration, WaveType), Z_Construct_UEnum_Aura_EWaveType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveType_MetaData), NewProp_WaveType_MetaData) }; // 1826622286
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_MinionComposition_ValueProp = { "MinionComposition", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_MinionComposition_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_MinionComposition_Key_KeyProp = { "MinionComposition_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Aura_EMinionType, METADATA_PARAMS(0, nullptr) }; // 1491242637
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_MinionComposition = { "MinionComposition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveConfiguration, MinionComposition), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinionComposition_MetaData), NewProp_MinionComposition_MetaData) }; // 1491242637
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_SpawnTime = { "SpawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveConfiguration, SpawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnTime_MetaData), NewProp_SpawnTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FWaveConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_WaveNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_WaveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_WaveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_MinionComposition_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_MinionComposition_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_MinionComposition_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_MinionComposition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewProp_SpawnTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWaveConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FWaveConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"WaveConfiguration",
	Z_Construct_UScriptStruct_FWaveConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWaveConfiguration_Statics::PropPointers),
	sizeof(FWaveConfiguration),
	alignof(FWaveConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWaveConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FWaveConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FWaveConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FWaveConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FWaveConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FWaveConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FWaveConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FWaveConfiguration **************************************************

// ********** Begin ScriptStruct FMinionData *******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMinionData;
class UScriptStruct* FMinionData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMinionData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMinionData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMinionData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("MinionData"));
	}
	return Z_Registration_Info_UScriptStruct_FMinionData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMinionData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estruturas de dados para minions\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estruturas de dados para minions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Velocity_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_State_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Damage_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Speed_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackRange_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackSpeed_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssignedLane_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathWaypoints_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentWaypointIndex_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Target_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAttackTime_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsElite_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveNumber_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinionID_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODLevel_MetaData[] = {
		{ "Category", "Minion Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Velocity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_State_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_State;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Health;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Speed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackSpeed;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AssignedLane_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AssignedLane;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PathWaypoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PathWaypoints;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentWaypointIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastAttackTime;
	static void NewProp_bIsElite_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsElite;
	static const UECodeGen_Private::FIntPropertyParams NewProp_WaveNumber;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinionID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMinionData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, Type), Z_Construct_UEnum_Aura_EMinionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Type_MetaData), NewProp_Type_MetaData) }; // 1491242637
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Velocity = { "Velocity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, Velocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Velocity_MetaData), NewProp_Velocity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_State_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, State), Z_Construct_UEnum_Aura_EMinionState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_State_MetaData), NewProp_State_MetaData) }; // **********
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Damage = { "Damage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, Damage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Damage_MetaData), NewProp_Damage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Speed = { "Speed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, Speed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Speed_MetaData), NewProp_Speed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_AttackRange = { "AttackRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, AttackRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackRange_MetaData), NewProp_AttackRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_AttackSpeed = { "AttackSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, AttackSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackSpeed_MetaData), NewProp_AttackSpeed_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_AssignedLane_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_AssignedLane = { "AssignedLane", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, AssignedLane), Z_Construct_UEnum_Aura_ELaneType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssignedLane_MetaData), NewProp_AssignedLane_MetaData) }; // 3945053688
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_PathWaypoints_Inner = { "PathWaypoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_PathWaypoints = { "PathWaypoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, PathWaypoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathWaypoints_MetaData), NewProp_PathWaypoints_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_CurrentWaypointIndex = { "CurrentWaypointIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, CurrentWaypointIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentWaypointIndex_MetaData), NewProp_CurrentWaypointIndex_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, Target), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Target_MetaData), NewProp_Target_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_LastAttackTime = { "LastAttackTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, LastAttackTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAttackTime_MetaData), NewProp_LastAttackTime_MetaData) };
void Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_bIsElite_SetBit(void* Obj)
{
	((FMinionData*)Obj)->bIsElite = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_bIsElite = { "bIsElite", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMinionData), &Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_bIsElite_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsElite_MetaData), NewProp_bIsElite_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_WaveNumber = { "WaveNumber", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, WaveNumber), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveNumber_MetaData), NewProp_WaveNumber_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_MinionID = { "MinionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, MinionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinionID_MetaData), NewProp_MinionID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionData, LODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODLevel_MetaData), NewProp_LODLevel_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FMinionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Velocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_State_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Health,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Damage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Speed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_AttackRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_AttackSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_AssignedLane_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_AssignedLane,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_PathWaypoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_PathWaypoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_CurrentWaypointIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_LastAttackTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_bIsElite,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_WaveNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_MinionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionData_Statics::NewProp_LODLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMinionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMinionData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"MinionData",
	Z_Construct_UScriptStruct_FMinionData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMinionData_Statics::PropPointers),
	sizeof(FMinionData),
	alignof(FMinionData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMinionData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMinionData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMinionData()
{
	if (!Z_Registration_Info_UScriptStruct_FMinionData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMinionData.InnerSingleton, Z_Construct_UScriptStruct_FMinionData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMinionData.InnerSingleton;
}
// ********** End ScriptStruct FMinionData *********************************************************

// ********** Begin ScriptStruct FWaveData *********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FWaveData;
class UScriptStruct* FWaveData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FWaveData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FWaveData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FWaveData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("WaveData"));
	}
	return Z_Registration_Info_UScriptStruct_FWaveData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FWaveData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveNumber_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveType_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinionComposition_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalMinions_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnInterval_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveStartTime_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveEndTime_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrimaryLane_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Formation_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthMultiplier_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageMultiplier_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpeedMultiplier_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasSuperMinion_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnPositions_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_WaveNumber;
	static const UECodeGen_Private::FBytePropertyParams NewProp_WaveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WaveType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinionComposition_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinionComposition_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MinionComposition;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalMinions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WaveStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WaveEndTime;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PrimaryLane_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PrimaryLane;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Formation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Formation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpeedMultiplier;
	static void NewProp_bHasSuperMinion_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasSuperMinion;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnPositions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpawnPositions;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FWaveData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_WaveNumber = { "WaveNumber", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveData, WaveNumber), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveNumber_MetaData), NewProp_WaveNumber_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_WaveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_WaveType = { "WaveType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveData, WaveType), Z_Construct_UEnum_Aura_EWaveType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveType_MetaData), NewProp_WaveType_MetaData) }; // 1826622286
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_MinionComposition_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_MinionComposition_Inner = { "MinionComposition", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Aura_EMinionType, METADATA_PARAMS(0, nullptr) }; // 1491242637
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_MinionComposition = { "MinionComposition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveData, MinionComposition), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinionComposition_MetaData), NewProp_MinionComposition_MetaData) }; // 1491242637
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_TotalMinions = { "TotalMinions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveData, TotalMinions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalMinions_MetaData), NewProp_TotalMinions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_SpawnInterval = { "SpawnInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveData, SpawnInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnInterval_MetaData), NewProp_SpawnInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_WaveStartTime = { "WaveStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveData, WaveStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveStartTime_MetaData), NewProp_WaveStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_WaveEndTime = { "WaveEndTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveData, WaveEndTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveEndTime_MetaData), NewProp_WaveEndTime_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_PrimaryLane_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_PrimaryLane = { "PrimaryLane", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveData, PrimaryLane), Z_Construct_UEnum_Aura_ELaneType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrimaryLane_MetaData), NewProp_PrimaryLane_MetaData) }; // 3945053688
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_Formation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_Formation = { "Formation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveData, Formation), Z_Construct_UEnum_Aura_EFormationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Formation_MetaData), NewProp_Formation_MetaData) }; // **********
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_HealthMultiplier = { "HealthMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveData, HealthMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthMultiplier_MetaData), NewProp_HealthMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_DamageMultiplier = { "DamageMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveData, DamageMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageMultiplier_MetaData), NewProp_DamageMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_SpeedMultiplier = { "SpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveData, SpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpeedMultiplier_MetaData), NewProp_SpeedMultiplier_MetaData) };
void Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_bHasSuperMinion_SetBit(void* Obj)
{
	((FWaveData*)Obj)->bHasSuperMinion = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_bHasSuperMinion = { "bHasSuperMinion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWaveData), &Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_bHasSuperMinion_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasSuperMinion_MetaData), NewProp_bHasSuperMinion_MetaData) };
void Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FWaveData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWaveData), &Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_SpawnPositions_Inner = { "SpawnPositions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_SpawnPositions = { "SpawnPositions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWaveData, SpawnPositions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnPositions_MetaData), NewProp_SpawnPositions_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FWaveData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_WaveNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_WaveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_WaveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_MinionComposition_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_MinionComposition_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_MinionComposition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_TotalMinions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_SpawnInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_WaveStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_WaveEndTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_PrimaryLane_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_PrimaryLane,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_Formation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_Formation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_HealthMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_DamageMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_SpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_bHasSuperMinion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_SpawnPositions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWaveData_Statics::NewProp_SpawnPositions,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWaveData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FWaveData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"WaveData",
	Z_Construct_UScriptStruct_FWaveData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWaveData_Statics::PropPointers),
	sizeof(FWaveData),
	alignof(FWaveData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWaveData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FWaveData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FWaveData()
{
	if (!Z_Registration_Info_UScriptStruct_FWaveData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FWaveData.InnerSingleton, Z_Construct_UScriptStruct_FWaveData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FWaveData.InnerSingleton;
}
// ********** End ScriptStruct FWaveData ***********************************************************

// ********** Begin ScriptStruct FPathfindingNode **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPathfindingNode;
class UScriptStruct* FPathfindingNode::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPathfindingNode.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPathfindingNode.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPathfindingNode, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PathfindingNode"));
	}
	return Z_Registration_Info_UScriptStruct_FPathfindingNode.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPathfindingNode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPosition_MetaData[] = {
		{ "Category", "Pathfinding" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// CORRIGIDO: Adicionar campos necess\xc3\xa1rios para implementa\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "CORRIGIDO: Adicionar campos necess\xc3\xa1rios para implementa\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridCoordinate_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridX_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridY_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GCost_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HCost_MetaData[] = {
		{ "Category", "Pathfinding" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custo do in\xc3\xad""cio at\xc3\xa9 este n\xc3\xb3\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custo do in\xc3\xad""cio at\xc3\xa9 este n\xc3\xb3" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FCost_MetaData[] = {
		{ "Category", "Pathfinding" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Heur\xc3\xadstica (custo estimado at\xc3\xa9 o objetivo)\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Heur\xc3\xadstica (custo estimado at\xc3\xa9 o objetivo)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsWalkable_MetaData[] = {
		{ "Category", "Pathfinding" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// GCost + HCost\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GCost + HCost" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementCost_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInOpenSet_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInClosedSet_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GridCoordinate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridX;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridY;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GCost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HCost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FCost;
	static void NewProp_bIsWalkable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsWalkable;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementCost;
	static void NewProp_bIsInOpenSet_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInOpenSet;
	static void NewProp_bIsInClosedSet_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInClosedSet;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPathfindingNode>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingNode, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_WorldPosition = { "WorldPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingNode, WorldPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPosition_MetaData), NewProp_WorldPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_GridCoordinate = { "GridCoordinate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingNode, GridCoordinate), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridCoordinate_MetaData), NewProp_GridCoordinate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_GridX = { "GridX", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingNode, GridX), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridX_MetaData), NewProp_GridX_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_GridY = { "GridY", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingNode, GridY), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridY_MetaData), NewProp_GridY_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_GCost = { "GCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingNode, GCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GCost_MetaData), NewProp_GCost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_HCost = { "HCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingNode, HCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HCost_MetaData), NewProp_HCost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_FCost = { "FCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingNode, FCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FCost_MetaData), NewProp_FCost_MetaData) };
void Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_bIsWalkable_SetBit(void* Obj)
{
	((FPathfindingNode*)Obj)->bIsWalkable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_bIsWalkable = { "bIsWalkable", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPathfindingNode), &Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_bIsWalkable_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsWalkable_MetaData), NewProp_bIsWalkable_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_MovementCost = { "MovementCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingNode, MovementCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementCost_MetaData), NewProp_MovementCost_MetaData) };
void Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_bIsInOpenSet_SetBit(void* Obj)
{
	((FPathfindingNode*)Obj)->bIsInOpenSet = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_bIsInOpenSet = { "bIsInOpenSet", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPathfindingNode), &Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_bIsInOpenSet_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInOpenSet_MetaData), NewProp_bIsInOpenSet_MetaData) };
void Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_bIsInClosedSet_SetBit(void* Obj)
{
	((FPathfindingNode*)Obj)->bIsInClosedSet = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_bIsInClosedSet = { "bIsInClosedSet", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPathfindingNode), &Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_bIsInClosedSet_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInClosedSet_MetaData), NewProp_bIsInClosedSet_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPathfindingNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_WorldPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_GridCoordinate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_GridX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_GridY,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_GCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_HCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_FCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_bIsWalkable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_MovementCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_bIsInOpenSet,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewProp_bIsInClosedSet,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPathfindingNode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPathfindingNode_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PathfindingNode",
	Z_Construct_UScriptStruct_FPathfindingNode_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPathfindingNode_Statics::PropPointers),
	sizeof(FPathfindingNode),
	alignof(FPathfindingNode),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPathfindingNode_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPathfindingNode_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPathfindingNode()
{
	if (!Z_Registration_Info_UScriptStruct_FPathfindingNode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPathfindingNode.InnerSingleton, Z_Construct_UScriptStruct_FPathfindingNode_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPathfindingNode.InnerSingleton;
}
// ********** End ScriptStruct FPathfindingNode ****************************************************

// ********** Begin ScriptStruct FPathfindingGrid **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPathfindingGrid;
class UScriptStruct* FPathfindingGrid::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPathfindingGrid.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPathfindingGrid.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPathfindingGrid, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PathfindingGrid"));
	}
	return Z_Registration_Info_UScriptStruct_FPathfindingGrid.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPathfindingGrid_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Nodes_MetaData[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridWidth_MetaData[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "Category", "Grid" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// CORRIGIDO: Adicionar campos necess\xc3\xa1rios\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "CORRIGIDO: Adicionar campos necess\xc3\xa1rios" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridHeight_MetaData[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Height_MetaData[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellSize_MetaData[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Origin_MetaData[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridOrigin_MetaData[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridBounds_MetaData[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Nodes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Nodes;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridWidth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Width;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridHeight;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Height;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CellSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Origin;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GridOrigin;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GridBounds;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPathfindingGrid>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_Nodes_Inner = { "Nodes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPathfindingNode, METADATA_PARAMS(0, nullptr) }; // 3466120308
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_Nodes = { "Nodes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingGrid, Nodes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Nodes_MetaData), NewProp_Nodes_MetaData) }; // 3466120308
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_GridWidth = { "GridWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingGrid, GridWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridWidth_MetaData), NewProp_GridWidth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingGrid, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_GridHeight = { "GridHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingGrid, GridHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridHeight_MetaData), NewProp_GridHeight_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingGrid, Height), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Height_MetaData), NewProp_Height_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_CellSize = { "CellSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingGrid, CellSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellSize_MetaData), NewProp_CellSize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_Origin = { "Origin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingGrid, Origin), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Origin_MetaData), NewProp_Origin_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_GridOrigin = { "GridOrigin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingGrid, GridOrigin), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridOrigin_MetaData), NewProp_GridOrigin_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_GridBounds = { "GridBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathfindingGrid, GridBounds), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridBounds_MetaData), NewProp_GridBounds_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPathfindingGrid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_Nodes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_Nodes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_GridWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_GridHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_CellSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_Origin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_GridOrigin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewProp_GridBounds,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPathfindingGrid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPathfindingGrid_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PathfindingGrid",
	Z_Construct_UScriptStruct_FPathfindingGrid_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPathfindingGrid_Statics::PropPointers),
	sizeof(FPathfindingGrid),
	alignof(FPathfindingGrid),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPathfindingGrid_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPathfindingGrid_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPathfindingGrid()
{
	if (!Z_Registration_Info_UScriptStruct_FPathfindingGrid.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPathfindingGrid.InnerSingleton, Z_Construct_UScriptStruct_FPathfindingGrid_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPathfindingGrid.InnerSingleton;
}
// ********** End ScriptStruct FPathfindingGrid ****************************************************

// ********** Begin ScriptStruct FMinionFormation **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMinionFormation;
class UScriptStruct* FMinionFormation::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMinionFormation.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMinionFormation.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMinionFormation, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("MinionFormation"));
	}
	return Z_Registration_Info_UScriptStruct_FMinionFormation.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMinionFormation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationType_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Positions_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterPosition_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Orientation_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Spacing_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxUnits_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bMaintainFormation_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_FormationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FormationType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Positions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Positions;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Orientation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Spacing;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxUnits;
	static void NewProp_bMaintainFormation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMaintainFormation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMinionFormation>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_FormationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_FormationType = { "FormationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionFormation, FormationType), Z_Construct_UEnum_Aura_EFormationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationType_MetaData), NewProp_FormationType_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_Positions_Inner = { "Positions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_Positions = { "Positions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionFormation, Positions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Positions_MetaData), NewProp_Positions_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_CenterPosition = { "CenterPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionFormation, CenterPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterPosition_MetaData), NewProp_CenterPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_Orientation = { "Orientation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionFormation, Orientation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Orientation_MetaData), NewProp_Orientation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_Spacing = { "Spacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionFormation, Spacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Spacing_MetaData), NewProp_Spacing_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_MaxUnits = { "MaxUnits", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionFormation, MaxUnits), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxUnits_MetaData), NewProp_MaxUnits_MetaData) };
void Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_bMaintainFormation_SetBit(void* Obj)
{
	((FMinionFormation*)Obj)->bMaintainFormation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_bMaintainFormation = { "bMaintainFormation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMinionFormation), &Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_bMaintainFormation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bMaintainFormation_MetaData), NewProp_bMaintainFormation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FMinionFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_FormationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_FormationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_Positions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_Positions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_CenterPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_Orientation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_Spacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_MaxUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionFormation_Statics::NewProp_bMaintainFormation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMinionFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMinionFormation_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"MinionFormation",
	Z_Construct_UScriptStruct_FMinionFormation_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMinionFormation_Statics::PropPointers),
	sizeof(FMinionFormation),
	alignof(FMinionFormation),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMinionFormation_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMinionFormation_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMinionFormation()
{
	if (!Z_Registration_Info_UScriptStruct_FMinionFormation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMinionFormation.InnerSingleton, Z_Construct_UScriptStruct_FMinionFormation_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMinionFormation.InnerSingleton;
}
// ********** End ScriptStruct FMinionFormation ****************************************************

// ********** Begin ScriptStruct FMinionSpawnPoint *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMinionSpawnPoint;
class UScriptStruct* FMinionSpawnPoint::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMinionSpawnPoint.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMinionSpawnPoint.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMinionSpawnPoint, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("MinionSpawnPoint"));
	}
	return Z_Registration_Info_UScriptStruct_FMinionSpawnPoint.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Spawn Point" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Spawn Point" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneType_MetaData[] = {
		{ "Category", "Spawn Point" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Spawn Point" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownTime_MetaData[] = {
		{ "Category", "Spawn Point" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastSpawnTime_MetaData[] = {
		{ "Category", "Spawn Point" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentSpawns_MetaData[] = {
		{ "Category", "Spawn Point" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentSpawnCount_MetaData[] = {
		{ "Category", "Spawn Point" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastSpawnTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentSpawns;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentSpawnCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMinionSpawnPoint>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionSpawnPoint, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionSpawnPoint, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionSpawnPoint, LaneType), Z_Construct_UEnum_Aura_ELaneType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneType_MetaData), NewProp_LaneType_MetaData) }; // 3945053688
void Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FMinionSpawnPoint*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMinionSpawnPoint), &Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_CooldownTime = { "CooldownTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionSpawnPoint, CooldownTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownTime_MetaData), NewProp_CooldownTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_LastSpawnTime = { "LastSpawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionSpawnPoint, LastSpawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastSpawnTime_MetaData), NewProp_LastSpawnTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_MaxConcurrentSpawns = { "MaxConcurrentSpawns", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionSpawnPoint, MaxConcurrentSpawns), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentSpawns_MetaData), NewProp_MaxConcurrentSpawns_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_CurrentSpawnCount = { "CurrentSpawnCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMinionSpawnPoint, CurrentSpawnCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentSpawnCount_MetaData), NewProp_CurrentSpawnCount_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_LaneType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_CooldownTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_LastSpawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_MaxConcurrentSpawns,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewProp_CurrentSpawnCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"MinionSpawnPoint",
	Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::PropPointers),
	sizeof(FMinionSpawnPoint),
	alignof(FMinionSpawnPoint),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMinionSpawnPoint()
{
	if (!Z_Registration_Info_UScriptStruct_FMinionSpawnPoint.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMinionSpawnPoint.InnerSingleton, Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMinionSpawnPoint.InnerSingleton;
}
// ********** End ScriptStruct FMinionSpawnPoint ***************************************************

// ********** Begin Class AMinionWaveManager Function AddSpawnPoint ********************************
struct Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics
{
	struct MinionWaveManager_eventAddSpawnPoint_Parms
	{
		FVector Position;
		FRotator Rotation;
		ELaneType LaneType;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spawn Points" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventAddSpawnPoint_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventAddSpawnPoint_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventAddSpawnPoint_Parms, LaneType), Z_Construct_UEnum_Aura_ELaneType, METADATA_PARAMS(0, nullptr) }; // 3945053688
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventAddSpawnPoint_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::NewProp_LaneType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "AddSpawnPoint", Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::MinionWaveManager_eventAddSpawnPoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::MinionWaveManager_eventAddSpawnPoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execAddSpawnPoint)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Rotation);
	P_GET_ENUM(ELaneType,Z_Param_LaneType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->AddSpawnPoint(Z_Param_Out_Position,Z_Param_Out_Rotation,ELaneType(Z_Param_LaneType));
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function AddSpawnPoint **********************************

// ********** Begin Class AMinionWaveManager Function AssignMinionsToFormation *********************
struct Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics
{
	struct MinionWaveManager_eventAssignMinionsToFormation_Parms
	{
		FMinionFormation Formation;
		TArray<int32> MinionIDs;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Formation System" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Formation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Formation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinionIDs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MinionIDs;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::NewProp_Formation = { "Formation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventAssignMinionsToFormation_Parms, Formation), Z_Construct_UScriptStruct_FMinionFormation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Formation_MetaData), NewProp_Formation_MetaData) }; // 2163295996
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::NewProp_MinionIDs_Inner = { "MinionIDs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::NewProp_MinionIDs = { "MinionIDs", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventAssignMinionsToFormation_Parms, MinionIDs), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::NewProp_Formation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::NewProp_MinionIDs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::NewProp_MinionIDs,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "AssignMinionsToFormation", Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::MinionWaveManager_eventAssignMinionsToFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::MinionWaveManager_eventAssignMinionsToFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execAssignMinionsToFormation)
{
	P_GET_STRUCT_REF(FMinionFormation,Z_Param_Out_Formation);
	P_GET_TARRAY_REF(int32,Z_Param_Out_MinionIDs);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AssignMinionsToFormation(Z_Param_Out_Formation,Z_Param_Out_MinionIDs);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function AssignMinionsToFormation ***********************

// ********** Begin Class AMinionWaveManager Function CalculateBoxFormation ************************
struct Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics
{
	struct MinionWaveManager_eventCalculateBoxFormation_Parms
	{
		FVector CenterPos;
		FRotator Orientation;
		int32 UnitCount;
		float Spacing;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Formation System" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Orientation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Orientation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UnitCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Spacing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::NewProp_CenterPos = { "CenterPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateBoxFormation_Parms, CenterPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterPos_MetaData), NewProp_CenterPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::NewProp_Orientation = { "Orientation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateBoxFormation_Parms, Orientation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Orientation_MetaData), NewProp_Orientation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::NewProp_UnitCount = { "UnitCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateBoxFormation_Parms, UnitCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::NewProp_Spacing = { "Spacing", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateBoxFormation_Parms, Spacing), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateBoxFormation_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::NewProp_CenterPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::NewProp_Orientation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::NewProp_UnitCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::NewProp_Spacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "CalculateBoxFormation", Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::MinionWaveManager_eventCalculateBoxFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::MinionWaveManager_eventCalculateBoxFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execCalculateBoxFormation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CenterPos);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Orientation);
	P_GET_PROPERTY(FIntProperty,Z_Param_UnitCount);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Spacing);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->CalculateBoxFormation(Z_Param_Out_CenterPos,Z_Param_Out_Orientation,Z_Param_UnitCount,Z_Param_Spacing);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function CalculateBoxFormation **************************

// ********** Begin Class AMinionWaveManager Function CalculateColumnFormation *********************
struct Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics
{
	struct MinionWaveManager_eventCalculateColumnFormation_Parms
	{
		FVector CenterPos;
		FRotator Orientation;
		int32 UnitCount;
		float Spacing;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Formation System" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Orientation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Orientation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UnitCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Spacing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::NewProp_CenterPos = { "CenterPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateColumnFormation_Parms, CenterPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterPos_MetaData), NewProp_CenterPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::NewProp_Orientation = { "Orientation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateColumnFormation_Parms, Orientation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Orientation_MetaData), NewProp_Orientation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::NewProp_UnitCount = { "UnitCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateColumnFormation_Parms, UnitCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::NewProp_Spacing = { "Spacing", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateColumnFormation_Parms, Spacing), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateColumnFormation_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::NewProp_CenterPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::NewProp_Orientation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::NewProp_UnitCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::NewProp_Spacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "CalculateColumnFormation", Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::MinionWaveManager_eventCalculateColumnFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::MinionWaveManager_eventCalculateColumnFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execCalculateColumnFormation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CenterPos);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Orientation);
	P_GET_PROPERTY(FIntProperty,Z_Param_UnitCount);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Spacing);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->CalculateColumnFormation(Z_Param_Out_CenterPos,Z_Param_Out_Orientation,Z_Param_UnitCount,Z_Param_Spacing);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function CalculateColumnFormation ***********************

// ********** Begin Class AMinionWaveManager Function CalculateHeuristic ***************************
struct Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics
{
	struct MinionWaveManager_eventCalculateHeuristic_Parms
	{
		FVector StartPos;
		FVector EndPos;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateHeuristic_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateHeuristic_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateHeuristic_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "CalculateHeuristic", Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::MinionWaveManager_eventCalculateHeuristic_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::MinionWaveManager_eventCalculateHeuristic_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execCalculateHeuristic)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateHeuristic(Z_Param_Out_StartPos,Z_Param_Out_EndPos);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function CalculateHeuristic *****************************

// ********** Begin Class AMinionWaveManager Function CalculateLineFormation ***********************
struct Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics
{
	struct MinionWaveManager_eventCalculateLineFormation_Parms
	{
		FVector CenterPos;
		FRotator Orientation;
		int32 UnitCount;
		float Spacing;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Formation System" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Orientation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Orientation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UnitCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Spacing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::NewProp_CenterPos = { "CenterPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateLineFormation_Parms, CenterPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterPos_MetaData), NewProp_CenterPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::NewProp_Orientation = { "Orientation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateLineFormation_Parms, Orientation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Orientation_MetaData), NewProp_Orientation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::NewProp_UnitCount = { "UnitCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateLineFormation_Parms, UnitCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::NewProp_Spacing = { "Spacing", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateLineFormation_Parms, Spacing), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateLineFormation_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::NewProp_CenterPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::NewProp_Orientation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::NewProp_UnitCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::NewProp_Spacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "CalculateLineFormation", Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::MinionWaveManager_eventCalculateLineFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::MinionWaveManager_eventCalculateLineFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execCalculateLineFormation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CenterPos);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Orientation);
	P_GET_PROPERTY(FIntProperty,Z_Param_UnitCount);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Spacing);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->CalculateLineFormation(Z_Param_Out_CenterPos,Z_Param_Out_Orientation,Z_Param_UnitCount,Z_Param_Spacing);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function CalculateLineFormation *************************

// ********** Begin Class AMinionWaveManager Function CalculateWaveScaling *************************
struct Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics
{
	struct MinionWaveManager_eventCalculateWaveScaling_Parms
	{
		int32 WaveNumber;
		float HealthMultiplier;
		float DamageMultiplier;
		float SpeedMultiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wave Generation" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_WaveNumber;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpeedMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::NewProp_WaveNumber = { "WaveNumber", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateWaveScaling_Parms, WaveNumber), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::NewProp_HealthMultiplier = { "HealthMultiplier", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateWaveScaling_Parms, HealthMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::NewProp_DamageMultiplier = { "DamageMultiplier", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateWaveScaling_Parms, DamageMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::NewProp_SpeedMultiplier = { "SpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateWaveScaling_Parms, SpeedMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::NewProp_WaveNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::NewProp_HealthMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::NewProp_DamageMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::NewProp_SpeedMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "CalculateWaveScaling", Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::MinionWaveManager_eventCalculateWaveScaling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::MinionWaveManager_eventCalculateWaveScaling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execCalculateWaveScaling)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_WaveNumber);
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_HealthMultiplier);
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_DamageMultiplier);
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_SpeedMultiplier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CalculateWaveScaling(Z_Param_WaveNumber,Z_Param_Out_HealthMultiplier,Z_Param_Out_DamageMultiplier,Z_Param_Out_SpeedMultiplier);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function CalculateWaveScaling ***************************

// ********** Begin Class AMinionWaveManager Function CalculateWedgeFormation **********************
struct Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics
{
	struct MinionWaveManager_eventCalculateWedgeFormation_Parms
	{
		FVector CenterPos;
		FRotator Orientation;
		int32 UnitCount;
		float Spacing;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Formation System" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Orientation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Orientation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UnitCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Spacing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::NewProp_CenterPos = { "CenterPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateWedgeFormation_Parms, CenterPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterPos_MetaData), NewProp_CenterPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::NewProp_Orientation = { "Orientation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateWedgeFormation_Parms, Orientation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Orientation_MetaData), NewProp_Orientation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::NewProp_UnitCount = { "UnitCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateWedgeFormation_Parms, UnitCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::NewProp_Spacing = { "Spacing", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateWedgeFormation_Parms, Spacing), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCalculateWedgeFormation_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::NewProp_CenterPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::NewProp_Orientation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::NewProp_UnitCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::NewProp_Spacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "CalculateWedgeFormation", Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::MinionWaveManager_eventCalculateWedgeFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::MinionWaveManager_eventCalculateWedgeFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execCalculateWedgeFormation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CenterPos);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Orientation);
	P_GET_PROPERTY(FIntProperty,Z_Param_UnitCount);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Spacing);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->CalculateWedgeFormation(Z_Param_Out_CenterPos,Z_Param_Out_Orientation,Z_Param_UnitCount,Z_Param_Spacing);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function CalculateWedgeFormation ************************

// ********** Begin Class AMinionWaveManager Function CanMinionAttack ******************************
struct Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics
{
	struct MinionWaveManager_eventCanMinionAttack_Parms
	{
		FMinionData Minion;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Minion Behavior" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Minion_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Minion;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::NewProp_Minion = { "Minion", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCanMinionAttack_Parms, Minion), Z_Construct_UScriptStruct_FMinionData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Minion_MetaData), NewProp_Minion_MetaData) }; // 65818637
void Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MinionWaveManager_eventCanMinionAttack_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MinionWaveManager_eventCanMinionAttack_Parms), &Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::NewProp_Minion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "CanMinionAttack", Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::MinionWaveManager_eventCanMinionAttack_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::MinionWaveManager_eventCanMinionAttack_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execCanMinionAttack)
{
	P_GET_STRUCT_REF(FMinionData,Z_Param_Out_Minion);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanMinionAttack(Z_Param_Out_Minion);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function CanMinionAttack ********************************

// ********** Begin Class AMinionWaveManager Function ConfigurePathfinding *************************
struct Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics
{
	struct MinionWaveManager_eventConfigurePathfinding_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configura pathfinding A* com 12 waypoints por lane\n     * Dist\xc3\xa2ncias: 800-1200 UU, rec\xc3\xa1lculo a cada 2 segundos\n     * @return true se configurado com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura pathfinding A* com 12 waypoints por lane\nDist\xc3\xa2ncias: 800-1200 UU, rec\xc3\xa1lculo a cada 2 segundos\n@return true se configurado com sucesso" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MinionWaveManager_eventConfigurePathfinding_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MinionWaveManager_eventConfigurePathfinding_Parms), &Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "ConfigurePathfinding", Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::MinionWaveManager_eventConfigurePathfinding_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::MinionWaveManager_eventConfigurePathfinding_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execConfigurePathfinding)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConfigurePathfinding();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function ConfigurePathfinding ***************************

// ********** Begin Class AMinionWaveManager Function ConfigureRewardSystem ************************
struct Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics
{
	struct MinionWaveManager_eventConfigureRewardSystem_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configura sistema de recompensas escal\xc3\xa1veis\n     * Ouro Melee: 20 + (1 \xc3\x97 onda), Ouro Ranged: 25 + (1.5 \xc3\x97 onda)\n     * @return true se configurado com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura sistema de recompensas escal\xc3\xa1veis\nOuro Melee: 20 + (1 \xc3\x97 onda), Ouro Ranged: 25 + (1.5 \xc3\x97 onda)\n@return true se configurado com sucesso" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MinionWaveManager_eventConfigureRewardSystem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MinionWaveManager_eventConfigureRewardSystem_Parms), &Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "ConfigureRewardSystem", Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::MinionWaveManager_eventConfigureRewardSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::MinionWaveManager_eventConfigureRewardSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execConfigureRewardSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConfigureRewardSystem();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function ConfigureRewardSystem **************************

// ********** Begin Class AMinionWaveManager Function ConfigureWaveSystem **************************
struct Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics
{
	struct MinionWaveManager_eventConfigureWaveSystem_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Map Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configura o sistema de ondas com progress\xc3\xa3o matem\xc3\xa1tica conforme documenta\xc3\xa7\xc3\xa3o\n     * Fun\xc3\xa7\xc3\xa3o temporal: Onda_n = 90 + (n-1) \xc3\x97 30 segundos\n     * @return true se configurado com sucesso\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura o sistema de ondas com progress\xc3\xa3o matem\xc3\xa1tica conforme documenta\xc3\xa7\xc3\xa3o\nFun\xc3\xa7\xc3\xa3o temporal: Onda_n = 90 + (n-1) \xc3\x97 30 segundos\n@return true se configurado com sucesso" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MinionWaveManager_eventConfigureWaveSystem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MinionWaveManager_eventConfigureWaveSystem_Parms), &Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "ConfigureWaveSystem", Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::MinionWaveManager_eventConfigureWaveSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::MinionWaveManager_eventConfigureWaveSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execConfigureWaveSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConfigureWaveSystem();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function ConfigureWaveSystem ****************************

// ********** Begin Class AMinionWaveManager Function CreateFormation ******************************
struct Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics
{
	struct MinionWaveManager_eventCreateFormation_Parms
	{
		EFormationType Type;
		FVector CenterPos;
		int32 UnitCount;
		FMinionFormation ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Formation System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de forma\xc3\xa7\xc3\xb5""es\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de forma\xc3\xa7\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterPos;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UnitCount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCreateFormation_Parms, Type), Z_Construct_UEnum_Aura_EFormationType, METADATA_PARAMS(0, nullptr) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::NewProp_CenterPos = { "CenterPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCreateFormation_Parms, CenterPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterPos_MetaData), NewProp_CenterPos_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::NewProp_UnitCount = { "UnitCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCreateFormation_Parms, UnitCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventCreateFormation_Parms, ReturnValue), Z_Construct_UScriptStruct_FMinionFormation, METADATA_PARAMS(0, nullptr) }; // 2163295996
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::NewProp_CenterPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::NewProp_UnitCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "CreateFormation", Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::MinionWaveManager_eventCreateFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::MinionWaveManager_eventCreateFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_CreateFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_CreateFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execCreateFormation)
{
	P_GET_ENUM(EFormationType,Z_Param_Type);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CenterPos);
	P_GET_PROPERTY(FIntProperty,Z_Param_UnitCount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FMinionFormation*)Z_Param__Result=P_THIS->CreateFormation(EFormationType(Z_Param_Type),Z_Param_Out_CenterPos,Z_Param_UnitCount);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function CreateFormation ********************************

// ********** Begin Class AMinionWaveManager Function CullDistantMinions ***************************
struct Z_Construct_UFunction_AMinionWaveManager_CullDistantMinions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_CullDistantMinions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "CullDistantMinions", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_CullDistantMinions_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_CullDistantMinions_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_CullDistantMinions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_CullDistantMinions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execCullDistantMinions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CullDistantMinions();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function CullDistantMinions *****************************

// ********** Begin Class AMinionWaveManager Function DestroyAllMinions ****************************
struct Z_Construct_UFunction_AMinionWaveManager_DestroyAllMinions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Minion Spawning" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_DestroyAllMinions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "DestroyAllMinions", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_DestroyAllMinions_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_DestroyAllMinions_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_DestroyAllMinions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_DestroyAllMinions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execDestroyAllMinions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DestroyAllMinions();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function DestroyAllMinions ******************************

// ********** Begin Class AMinionWaveManager Function DestroyMinion ********************************
struct Z_Construct_UFunction_AMinionWaveManager_DestroyMinion_Statics
{
	struct MinionWaveManager_eventDestroyMinion_Parms
	{
		int32 MinionID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Minion Spawning" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinionID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_DestroyMinion_Statics::NewProp_MinionID = { "MinionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventDestroyMinion_Parms, MinionID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_DestroyMinion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_DestroyMinion_Statics::NewProp_MinionID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_DestroyMinion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_DestroyMinion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "DestroyMinion", Z_Construct_UFunction_AMinionWaveManager_DestroyMinion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_DestroyMinion_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_DestroyMinion_Statics::MinionWaveManager_eventDestroyMinion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_DestroyMinion_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_DestroyMinion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_DestroyMinion_Statics::MinionWaveManager_eventDestroyMinion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_DestroyMinion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_DestroyMinion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execDestroyMinion)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MinionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DestroyMinion(Z_Param_MinionID);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function DestroyMinion **********************************

// ********** Begin Class AMinionWaveManager Function DrawDebugFormations **************************
struct Z_Construct_UFunction_AMinionWaveManager_DrawDebugFormations_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_DrawDebugFormations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "DrawDebugFormations", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_DrawDebugFormations_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_DrawDebugFormations_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_DrawDebugFormations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_DrawDebugFormations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execDrawDebugFormations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugFormations();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function DrawDebugFormations ****************************

// ********** Begin Class AMinionWaveManager Function DrawDebugMinionPaths *************************
struct Z_Construct_UFunction_AMinionWaveManager_DrawDebugMinionPaths_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_DrawDebugMinionPaths_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "DrawDebugMinionPaths", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_DrawDebugMinionPaths_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_DrawDebugMinionPaths_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_DrawDebugMinionPaths()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_DrawDebugMinionPaths_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execDrawDebugMinionPaths)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugMinionPaths();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function DrawDebugMinionPaths ***************************

// ********** Begin Class AMinionWaveManager Function DrawDebugPathfindingGrid *********************
struct Z_Construct_UFunction_AMinionWaveManager_DrawDebugPathfindingGrid_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de debug\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de debug" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_DrawDebugPathfindingGrid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "DrawDebugPathfindingGrid", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_DrawDebugPathfindingGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_DrawDebugPathfindingGrid_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_DrawDebugPathfindingGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_DrawDebugPathfindingGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execDrawDebugPathfindingGrid)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugPathfindingGrid();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function DrawDebugPathfindingGrid ***********************

// ********** Begin Class AMinionWaveManager Function DrawDebugSpawnPoints *************************
struct Z_Construct_UFunction_AMinionWaveManager_DrawDebugSpawnPoints_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_DrawDebugSpawnPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "DrawDebugSpawnPoints", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_DrawDebugSpawnPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_DrawDebugSpawnPoints_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_DrawDebugSpawnPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_DrawDebugSpawnPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execDrawDebugSpawnPoints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugSpawnPoints();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function DrawDebugSpawnPoints ***************************

// ********** Begin Class AMinionWaveManager Function ExecuteMinionAttack **************************
struct Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics
{
	struct MinionWaveManager_eventExecuteMinionAttack_Parms
	{
		FMinionData Minion;
		AActor* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Minion Behavior" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Minion;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::NewProp_Minion = { "Minion", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventExecuteMinionAttack_Parms, Minion), Z_Construct_UScriptStruct_FMinionData, METADATA_PARAMS(0, nullptr) }; // 65818637
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventExecuteMinionAttack_Parms, Target), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::NewProp_Minion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "ExecuteMinionAttack", Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::MinionWaveManager_eventExecuteMinionAttack_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::MinionWaveManager_eventExecuteMinionAttack_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execExecuteMinionAttack)
{
	P_GET_STRUCT_REF(FMinionData,Z_Param_Out_Minion);
	P_GET_OBJECT(AActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExecuteMinionAttack(Z_Param_Out_Minion,Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function ExecuteMinionAttack ****************************

// ********** Begin Class AMinionWaveManager Function FindNearestTarget ****************************
struct Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics
{
	struct MinionWaveManager_eventFindNearestTarget_Parms
	{
		FMinionData Minion;
		AActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Minion Behavior" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Minion_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Minion;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::NewProp_Minion = { "Minion", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventFindNearestTarget_Parms, Minion), Z_Construct_UScriptStruct_FMinionData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Minion_MetaData), NewProp_Minion_MetaData) }; // 65818637
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventFindNearestTarget_Parms, ReturnValue), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::NewProp_Minion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "FindNearestTarget", Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::MinionWaveManager_eventFindNearestTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::MinionWaveManager_eventFindNearestTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execFindNearestTarget)
{
	P_GET_STRUCT_REF(FMinionData,Z_Param_Out_Minion);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AActor**)Z_Param__Result=P_THIS->FindNearestTarget(Z_Param_Out_Minion);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function FindNearestTarget ******************************

// ********** Begin Class AMinionWaveManager Function FindPath *************************************
struct Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics
{
	struct MinionWaveManager_eventFindPath_Parms
	{
		FVector StartPos;
		FVector EndPos;
		bool bAvoidUnits;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
		{ "CPP_Default_bAvoidUnits", "true" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static void NewProp_bAvoidUnits_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAvoidUnits;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventFindPath_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventFindPath_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
void Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::NewProp_bAvoidUnits_SetBit(void* Obj)
{
	((MinionWaveManager_eventFindPath_Parms*)Obj)->bAvoidUnits = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::NewProp_bAvoidUnits = { "bAvoidUnits", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MinionWaveManager_eventFindPath_Parms), &Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::NewProp_bAvoidUnits_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventFindPath_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::NewProp_bAvoidUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "FindPath", Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::MinionWaveManager_eventFindPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::MinionWaveManager_eventFindPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_FindPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_FindPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execFindPath)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_GET_UBOOL(Z_Param_bAvoidUnits);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->FindPath(Z_Param_Out_StartPos,Z_Param_Out_EndPos,Z_Param_bAvoidUnits);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function FindPath ***************************************

// ********** Begin Class AMinionWaveManager Function FindPathAStar ********************************
struct Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics
{
	struct MinionWaveManager_eventFindPathAStar_Parms
	{
		FVector StartPos;
		FVector EndPos;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventFindPathAStar_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventFindPathAStar_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventFindPathAStar_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "FindPathAStar", Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::MinionWaveManager_eventFindPathAStar_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::MinionWaveManager_eventFindPathAStar_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_FindPathAStar()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_FindPathAStar_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execFindPathAStar)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->FindPathAStar(Z_Param_Out_StartPos,Z_Param_Out_EndPos);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function FindPathAStar **********************************

// ********** Begin Class AMinionWaveManager Function FindPathDijkstra *****************************
struct Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics
{
	struct MinionWaveManager_eventFindPathDijkstra_Parms
	{
		FVector StartPos;
		FVector EndPos;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventFindPathDijkstra_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventFindPathDijkstra_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventFindPathDijkstra_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "FindPathDijkstra", Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::MinionWaveManager_eventFindPathDijkstra_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::MinionWaveManager_eventFindPathDijkstra_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execFindPathDijkstra)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->FindPathDijkstra(Z_Param_Out_StartPos,Z_Param_Out_EndPos);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function FindPathDijkstra *******************************

// ********** Begin Class AMinionWaveManager Function GenerateMinionComposition ********************
struct Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics
{
	struct MinionWaveManager_eventGenerateMinionComposition_Parms
	{
		int32 WaveNumber;
		EWaveType WaveType;
		TArray<EMinionType> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wave Generation" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_WaveNumber;
	static const UECodeGen_Private::FBytePropertyParams NewProp_WaveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WaveType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::NewProp_WaveNumber = { "WaveNumber", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGenerateMinionComposition_Parms, WaveNumber), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::NewProp_WaveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::NewProp_WaveType = { "WaveType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGenerateMinionComposition_Parms, WaveType), Z_Construct_UEnum_Aura_EWaveType, METADATA_PARAMS(0, nullptr) }; // 1826622286
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::NewProp_ReturnValue_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Aura_EMinionType, METADATA_PARAMS(0, nullptr) }; // 1491242637
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGenerateMinionComposition_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1491242637
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::NewProp_WaveNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::NewProp_WaveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::NewProp_WaveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::NewProp_ReturnValue_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "GenerateMinionComposition", Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::MinionWaveManager_eventGenerateMinionComposition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::MinionWaveManager_eventGenerateMinionComposition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execGenerateMinionComposition)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_WaveNumber);
	P_GET_ENUM(EWaveType,Z_Param_WaveType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<EMinionType>*)Z_Param__Result=P_THIS->GenerateMinionComposition(Z_Param_WaveNumber,EWaveType(Z_Param_WaveType));
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function GenerateMinionComposition **********************

// ********** Begin Class AMinionWaveManager Function GenerateWaveData *****************************
struct Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics
{
	struct MinionWaveManager_eventGenerateWaveData_Parms
	{
		int32 WaveNumber;
		EWaveType WaveType;
		FWaveData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wave Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de gera\xc3\xa7\xc3\xa3o de ondas\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de gera\xc3\xa7\xc3\xa3o de ondas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_WaveNumber;
	static const UECodeGen_Private::FBytePropertyParams NewProp_WaveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WaveType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::NewProp_WaveNumber = { "WaveNumber", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGenerateWaveData_Parms, WaveNumber), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::NewProp_WaveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::NewProp_WaveType = { "WaveType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGenerateWaveData_Parms, WaveType), Z_Construct_UEnum_Aura_EWaveType, METADATA_PARAMS(0, nullptr) }; // 1826622286
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGenerateWaveData_Parms, ReturnValue), Z_Construct_UScriptStruct_FWaveData, METADATA_PARAMS(0, nullptr) }; // 416780774
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::NewProp_WaveNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::NewProp_WaveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::NewProp_WaveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "GenerateWaveData", Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::MinionWaveManager_eventGenerateWaveData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::MinionWaveManager_eventGenerateWaveData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execGenerateWaveData)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_WaveNumber);
	P_GET_ENUM(EWaveType,Z_Param_WaveType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FWaveData*)Z_Param__Result=P_THIS->GenerateWaveData(Z_Param_WaveNumber,EWaveType(Z_Param_WaveType));
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function GenerateWaveData *******************************

// ********** Begin Class AMinionWaveManager Function GetActiveMinionCount *************************
struct Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount_Statics
{
	struct MinionWaveManager_eventGetActiveMinionCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Getters\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Getters" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetActiveMinionCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "GetActiveMinionCount", Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount_Statics::MinionWaveManager_eventGetActiveMinionCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount_Statics::MinionWaveManager_eventGetActiveMinionCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execGetActiveMinionCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveMinionCount();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function GetActiveMinionCount ***************************

// ********** Begin Class AMinionWaveManager Function GetAvailableSpawnPoint ***********************
struct Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics
{
	struct MinionWaveManager_eventGetAvailableSpawnPoint_Parms
	{
		ELaneType LaneType;
		FMinionSpawnPoint OutSpawnPoint;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spawn Points" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutSpawnPoint;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetAvailableSpawnPoint_Parms, LaneType), Z_Construct_UEnum_Aura_ELaneType, METADATA_PARAMS(0, nullptr) }; // 3945053688
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::NewProp_OutSpawnPoint = { "OutSpawnPoint", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetAvailableSpawnPoint_Parms, OutSpawnPoint), Z_Construct_UScriptStruct_FMinionSpawnPoint, METADATA_PARAMS(0, nullptr) }; // 3125342975
void Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MinionWaveManager_eventGetAvailableSpawnPoint_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MinionWaveManager_eventGetAvailableSpawnPoint_Parms), &Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::NewProp_LaneType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::NewProp_OutSpawnPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "GetAvailableSpawnPoint", Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::MinionWaveManager_eventGetAvailableSpawnPoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::MinionWaveManager_eventGetAvailableSpawnPoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execGetAvailableSpawnPoint)
{
	P_GET_ENUM(ELaneType,Z_Param_LaneType);
	P_GET_STRUCT_REF(FMinionSpawnPoint,Z_Param_Out_OutSpawnPoint);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GetAvailableSpawnPoint(ELaneType(Z_Param_LaneType),Z_Param_Out_OutSpawnPoint);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function GetAvailableSpawnPoint *************************

// ********** Begin Class AMinionWaveManager Function GetCurrentWaveNumber *************************
struct Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber_Statics
{
	struct MinionWaveManager_eventGetCurrentWaveNumber_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetCurrentWaveNumber_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "GetCurrentWaveNumber", Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber_Statics::MinionWaveManager_eventGetCurrentWaveNumber_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber_Statics::MinionWaveManager_eventGetCurrentWaveNumber_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execGetCurrentWaveNumber)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetCurrentWaveNumber();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function GetCurrentWaveNumber ***************************

// ********** Begin Class AMinionWaveManager Function GetMinionsInLane *****************************
struct Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics
{
	struct MinionWaveManager_eventGetMinionsInLane_Parms
	{
		ELaneType Lane;
		TArray<FMinionData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Lane_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Lane;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::NewProp_Lane_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::NewProp_Lane = { "Lane", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetMinionsInLane_Parms, Lane), Z_Construct_UEnum_Aura_ELaneType, METADATA_PARAMS(0, nullptr) }; // 3945053688
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FMinionData, METADATA_PARAMS(0, nullptr) }; // 65818637
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetMinionsInLane_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 65818637
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::NewProp_Lane_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::NewProp_Lane,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "GetMinionsInLane", Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::MinionWaveManager_eventGetMinionsInLane_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::MinionWaveManager_eventGetMinionsInLane_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execGetMinionsInLane)
{
	P_GET_ENUM(ELaneType,Z_Param_Lane);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FMinionData>*)Z_Param__Result=P_THIS->GetMinionsInLane(ELaneType(Z_Param_Lane));
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function GetMinionsInLane *******************************

// ********** Begin Class AMinionWaveManager Function GetMinionsInRadius ***************************
struct Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics
{
	struct MinionWaveManager_eventGetMinionsInRadius_Parms
	{
		FVector Center;
		float Radius;
		TArray<FMinionData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetMinionsInRadius_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetMinionsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FMinionData, METADATA_PARAMS(0, nullptr) }; // 65818637
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetMinionsInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 65818637
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "GetMinionsInRadius", Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::MinionWaveManager_eventGetMinionsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::MinionWaveManager_eventGetMinionsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execGetMinionsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FMinionData>*)Z_Param__Result=P_THIS->GetMinionsInRadius(Z_Param_Out_Center,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function GetMinionsInRadius *****************************

// ********** Begin Class AMinionWaveManager Function GetNeighborNodeIndices ***********************
struct Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics
{
	struct MinionWaveManager_eventGetNeighborNodeIndices_Parms
	{
		int32 NodeIndex;
		TArray<int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es internas movidas para private devido ao uso de ponteiros\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es internas movidas para private devido ao uso de ponteiros" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NodeIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::NewProp_NodeIndex = { "NodeIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetNeighborNodeIndices_Parms, NodeIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetNeighborNodeIndices_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::NewProp_NodeIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "GetNeighborNodeIndices", Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::MinionWaveManager_eventGetNeighborNodeIndices_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::MinionWaveManager_eventGetNeighborNodeIndices_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execGetNeighborNodeIndices)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NodeIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<int32>*)Z_Param__Result=P_THIS->GetNeighborNodeIndices(Z_Param_NodeIndex);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function GetNeighborNodeIndices *************************

// ********** Begin Class AMinionWaveManager Function GetNodeAtGridPosition ************************
struct Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics
{
	struct MinionWaveManager_eventGetNodeAtGridPosition_Parms
	{
		int32 X;
		int32 Y;
		FPathfindingNode OutNode;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_X;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Y;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutNode;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetNodeAtGridPosition_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::NewProp_Y = { "Y", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetNodeAtGridPosition_Parms, Y), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::NewProp_OutNode = { "OutNode", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetNodeAtGridPosition_Parms, OutNode), Z_Construct_UScriptStruct_FPathfindingNode, METADATA_PARAMS(0, nullptr) }; // 3466120308
void Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MinionWaveManager_eventGetNodeAtGridPosition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MinionWaveManager_eventGetNodeAtGridPosition_Parms), &Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::NewProp_Y,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::NewProp_OutNode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "GetNodeAtGridPosition", Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::MinionWaveManager_eventGetNodeAtGridPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::MinionWaveManager_eventGetNodeAtGridPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execGetNodeAtGridPosition)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_X);
	P_GET_PROPERTY(FIntProperty,Z_Param_Y);
	P_GET_STRUCT_REF(FPathfindingNode,Z_Param_Out_OutNode);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GetNodeAtGridPosition(Z_Param_X,Z_Param_Y,Z_Param_Out_OutNode);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function GetNodeAtGridPosition **************************

// ********** Begin Class AMinionWaveManager Function GetWaveProgress ******************************
struct Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress_Statics
{
	struct MinionWaveManager_eventGetWaveProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Getters" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGetWaveProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "GetWaveProgress", Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress_Statics::MinionWaveManager_eventGetWaveProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress_Statics::MinionWaveManager_eventGetWaveProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execGetWaveProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetWaveProgress();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function GetWaveProgress ********************************

// ********** Begin Class AMinionWaveManager Function GridToWorldPosition **************************
struct Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics
{
	struct MinionWaveManager_eventGridToWorldPosition_Parms
	{
		FVector2D GridCoordinate;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridCoordinate_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_GridCoordinate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::NewProp_GridCoordinate = { "GridCoordinate", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGridToWorldPosition_Parms, GridCoordinate), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridCoordinate_MetaData), NewProp_GridCoordinate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventGridToWorldPosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::NewProp_GridCoordinate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "GridToWorldPosition", Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::MinionWaveManager_eventGridToWorldPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::MinionWaveManager_eventGridToWorldPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execGridToWorldPosition)
{
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_GridCoordinate);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GridToWorldPosition(Z_Param_Out_GridCoordinate);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function GridToWorldPosition ****************************

// ********** Begin Class AMinionWaveManager Function InitializePathfindingGrid ********************
struct Z_Construct_UFunction_AMinionWaveManager_InitializePathfindingGrid_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de pathfinding A*\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de pathfinding A*" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_InitializePathfindingGrid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "InitializePathfindingGrid", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_InitializePathfindingGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_InitializePathfindingGrid_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_InitializePathfindingGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_InitializePathfindingGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execInitializePathfindingGrid)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializePathfindingGrid();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function InitializePathfindingGrid **********************

// ********** Begin Class AMinionWaveManager Function InitializeSpawnPoints ************************
struct Z_Construct_UFunction_AMinionWaveManager_InitializeSpawnPoints_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spawn Points" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de spawn points\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de spawn points" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_InitializeSpawnPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "InitializeSpawnPoints", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_InitializeSpawnPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_InitializeSpawnPoints_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_InitializeSpawnPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_InitializeSpawnPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execInitializeSpawnPoints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeSpawnPoints();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function InitializeSpawnPoints **************************

// ********** Begin Class AMinionWaveManager Function InitializeWaveSystem *************************
struct Z_Construct_UFunction_AMinionWaveManager_InitializeWaveSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wave System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es principais do sistema de ondas\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es principais do sistema de ondas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_InitializeWaveSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "InitializeWaveSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_InitializeWaveSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_InitializeWaveSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_InitializeWaveSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_InitializeWaveSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execInitializeWaveSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeWaveSystem();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function InitializeWaveSystem ***************************

// ********** Begin Class AMinionWaveManager Function IsPositionWalkable ***************************
struct Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics
{
	struct MinionWaveManager_eventIsPositionWalkable_Parms
	{
		FVector Position;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventIsPositionWalkable_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
void Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MinionWaveManager_eventIsPositionWalkable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MinionWaveManager_eventIsPositionWalkable_Parms), &Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "IsPositionWalkable", Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::MinionWaveManager_eventIsPositionWalkable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::MinionWaveManager_eventIsPositionWalkable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execIsPositionWalkable)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPositionWalkable(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function IsPositionWalkable *****************************

// ********** Begin Class AMinionWaveManager Function LogMinionStatistics **************************
struct Z_Construct_UFunction_AMinionWaveManager_LogMinionStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_LogMinionStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "LogMinionStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_LogMinionStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_LogMinionStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_LogMinionStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_LogMinionStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execLogMinionStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogMinionStatistics();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function LogMinionStatistics ****************************

// ********** Begin Class AMinionWaveManager Function LogWaveStatistics ****************************
struct Z_Construct_UFunction_AMinionWaveManager_LogWaveStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_LogWaveStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "LogWaveStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_LogWaveStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_LogWaveStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_LogWaveStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_LogWaveStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execLogWaveStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogWaveStatistics();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function LogWaveStatistics ******************************

// ********** Begin Class AMinionWaveManager Function OptimizePathfinding **************************
struct Z_Construct_UFunction_AMinionWaveManager_OptimizePathfinding_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_OptimizePathfinding_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "OptimizePathfinding", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_OptimizePathfinding_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_OptimizePathfinding_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_OptimizePathfinding()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_OptimizePathfinding_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execOptimizePathfinding)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizePathfinding();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function OptimizePathfinding ****************************

// ********** Begin Class AMinionWaveManager Function PauseWaveSystem ******************************
struct Z_Construct_UFunction_AMinionWaveManager_PauseWaveSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wave System" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_PauseWaveSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "PauseWaveSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_PauseWaveSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_PauseWaveSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_PauseWaveSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_PauseWaveSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execPauseWaveSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PauseWaveSystem();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function PauseWaveSystem ********************************

// ********** Begin Class AMinionWaveManager Function RemoveSpawnPoint *****************************
struct Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint_Statics
{
	struct MinionWaveManager_eventRemoveSpawnPoint_Parms
	{
		int32 SpawnPointIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spawn Points" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SpawnPointIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint_Statics::NewProp_SpawnPointIndex = { "SpawnPointIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventRemoveSpawnPoint_Parms, SpawnPointIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint_Statics::NewProp_SpawnPointIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "RemoveSpawnPoint", Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint_Statics::MinionWaveManager_eventRemoveSpawnPoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint_Statics::MinionWaveManager_eventRemoveSpawnPoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execRemoveSpawnPoint)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SpawnPointIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveSpawnPoint(Z_Param_SpawnPointIndex);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function RemoveSpawnPoint *******************************

// ********** Begin Class AMinionWaveManager Function ResetWaveSystem ******************************
struct Z_Construct_UFunction_AMinionWaveManager_ResetWaveSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wave System" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_ResetWaveSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "ResetWaveSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ResetWaveSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_ResetWaveSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_ResetWaveSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_ResetWaveSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execResetWaveSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetWaveSystem();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function ResetWaveSystem ********************************

// ********** Begin Class AMinionWaveManager Function ResumeWaveSystem *****************************
struct Z_Construct_UFunction_AMinionWaveManager_ResumeWaveSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wave System" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_ResumeWaveSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "ResumeWaveSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ResumeWaveSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_ResumeWaveSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_ResumeWaveSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_ResumeWaveSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execResumeWaveSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResumeWaveSystem();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function ResumeWaveSystem *******************************

// ********** Begin Class AMinionWaveManager Function SetGridCellSize ******************************
struct Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize_Statics
{
	struct MinionWaveManager_eventSetGridCellSize_Parms
	{
		float NewCellSize;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setters" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewCellSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize_Statics::NewProp_NewCellSize = { "NewCellSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventSetGridCellSize_Parms, NewCellSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize_Statics::NewProp_NewCellSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "SetGridCellSize", Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize_Statics::MinionWaveManager_eventSetGridCellSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize_Statics::MinionWaveManager_eventSetGridCellSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execSetGridCellSize)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewCellSize);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGridCellSize(Z_Param_NewCellSize);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function SetGridCellSize ********************************

// ********** Begin Class AMinionWaveManager Function SetPathfindingAlgorithm **********************
struct Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics
{
	struct MinionWaveManager_eventSetPathfindingAlgorithm_Parms
	{
		EPathfindingAlgorithm NewAlgorithm;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setters" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewAlgorithm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewAlgorithm;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::NewProp_NewAlgorithm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::NewProp_NewAlgorithm = { "NewAlgorithm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventSetPathfindingAlgorithm_Parms, NewAlgorithm), Z_Construct_UEnum_Aura_EPathfindingAlgorithm, METADATA_PARAMS(0, nullptr) }; // 3485225044
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::NewProp_NewAlgorithm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::NewProp_NewAlgorithm,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "SetPathfindingAlgorithm", Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::MinionWaveManager_eventSetPathfindingAlgorithm_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::MinionWaveManager_eventSetPathfindingAlgorithm_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execSetPathfindingAlgorithm)
{
	P_GET_ENUM(EPathfindingAlgorithm,Z_Param_NewAlgorithm);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPathfindingAlgorithm(EPathfindingAlgorithm(Z_Param_NewAlgorithm));
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function SetPathfindingAlgorithm ************************

// ********** Begin Class AMinionWaveManager Function SetupWaveFormation ***************************
struct Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation_Statics
{
	struct MinionWaveManager_eventSetupWaveFormation_Parms
	{
		FWaveData WaveData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wave Generation" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WaveData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation_Statics::NewProp_WaveData = { "WaveData", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventSetupWaveFormation_Parms, WaveData), Z_Construct_UScriptStruct_FWaveData, METADATA_PARAMS(0, nullptr) }; // 416780774
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation_Statics::NewProp_WaveData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "SetupWaveFormation", Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation_Statics::MinionWaveManager_eventSetupWaveFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation_Statics::MinionWaveManager_eventSetupWaveFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execSetupWaveFormation)
{
	P_GET_STRUCT_REF(FWaveData,Z_Param_Out_WaveData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupWaveFormation(Z_Param_Out_WaveData);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function SetupWaveFormation *****************************

// ********** Begin Class AMinionWaveManager Function SetWaveInterval ******************************
struct Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval_Statics
{
	struct MinionWaveManager_eventSetWaveInterval_Parms
	{
		float NewInterval;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Setters\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Setters" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewInterval;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval_Statics::NewProp_NewInterval = { "NewInterval", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventSetWaveInterval_Parms, NewInterval), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval_Statics::NewProp_NewInterval,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "SetWaveInterval", Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval_Statics::MinionWaveManager_eventSetWaveInterval_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval_Statics::MinionWaveManager_eventSetWaveInterval_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execSetWaveInterval)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewInterval);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWaveInterval(Z_Param_NewInterval);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function SetWaveInterval ********************************

// ********** Begin Class AMinionWaveManager Function SetWaveScalingFactor *************************
struct Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor_Statics
{
	struct MinionWaveManager_eventSetWaveScalingFactor_Parms
	{
		float NewScalingFactor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setters" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewScalingFactor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor_Statics::NewProp_NewScalingFactor = { "NewScalingFactor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventSetWaveScalingFactor_Parms, NewScalingFactor), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor_Statics::NewProp_NewScalingFactor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "SetWaveScalingFactor", Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor_Statics::MinionWaveManager_eventSetWaveScalingFactor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor_Statics::MinionWaveManager_eventSetWaveScalingFactor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execSetWaveScalingFactor)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewScalingFactor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWaveScalingFactor(Z_Param_NewScalingFactor);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function SetWaveScalingFactor ***************************

// ********** Begin Class AMinionWaveManager Function SpawnMinion **********************************
struct Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics
{
	struct MinionWaveManager_eventSpawnMinion_Parms
	{
		EMinionType Type;
		FVector Position;
		ELaneType Lane;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Minion Spawning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de spawn de minions\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de spawn de minions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Lane_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Lane;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventSpawnMinion_Parms, Type), Z_Construct_UEnum_Aura_EMinionType, METADATA_PARAMS(0, nullptr) }; // 1491242637
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventSpawnMinion_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::NewProp_Lane_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::NewProp_Lane = { "Lane", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventSpawnMinion_Parms, Lane), Z_Construct_UEnum_Aura_ELaneType, METADATA_PARAMS(0, nullptr) }; // 3945053688
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventSpawnMinion_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::NewProp_Lane_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::NewProp_Lane,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "SpawnMinion", Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::MinionWaveManager_eventSpawnMinion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::MinionWaveManager_eventSpawnMinion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_SpawnMinion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_SpawnMinion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execSpawnMinion)
{
	P_GET_ENUM(EMinionType,Z_Param_Type);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_ENUM(ELaneType,Z_Param_Lane);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->SpawnMinion(EMinionType(Z_Param_Type),Z_Param_Out_Position,ELaneType(Z_Param_Lane));
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function SpawnMinion ************************************

// ********** Begin Class AMinionWaveManager Function SpawnWaveMinions *****************************
struct Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions_Statics
{
	struct MinionWaveManager_eventSpawnWaveMinions_Parms
	{
		FWaveData WaveData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Minion Spawning" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WaveData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions_Statics::NewProp_WaveData = { "WaveData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventSpawnWaveMinions_Parms, WaveData), Z_Construct_UScriptStruct_FWaveData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveData_MetaData), NewProp_WaveData_MetaData) }; // 416780774
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions_Statics::NewProp_WaveData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "SpawnWaveMinions", Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions_Statics::MinionWaveManager_eventSpawnWaveMinions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions_Statics::MinionWaveManager_eventSpawnWaveMinions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execSpawnWaveMinions)
{
	P_GET_STRUCT_REF(FWaveData,Z_Param_Out_WaveData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnWaveMinions(Z_Param_Out_WaveData);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function SpawnWaveMinions *******************************

// ********** Begin Class AMinionWaveManager Function StartNextWave ********************************
struct Z_Construct_UFunction_AMinionWaveManager_StartNextWave_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wave System" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_StartNextWave_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "StartNextWave", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_StartNextWave_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_StartNextWave_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_StartNextWave()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_StartNextWave_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execStartNextWave)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartNextWave();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function StartNextWave **********************************

// ********** Begin Class AMinionWaveManager Function StopCurrentWave ******************************
struct Z_Construct_UFunction_AMinionWaveManager_StopCurrentWave_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Wave System" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_StopCurrentWave_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "StopCurrentWave", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_StopCurrentWave_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_StopCurrentWave_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_StopCurrentWave()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_StopCurrentWave_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execStopCurrentWave)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopCurrentWave();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function StopCurrentWave ********************************

// ********** Begin Class AMinionWaveManager Function UpdateFormationPositions *********************
struct Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics
{
	struct MinionWaveManager_eventUpdateFormationPositions_Parms
	{
		FMinionFormation Formation;
		FVector NewCenterPos;
		FRotator NewOrientation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Formation System" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewCenterPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewOrientation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Formation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewCenterPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewOrientation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::NewProp_Formation = { "Formation", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventUpdateFormationPositions_Parms, Formation), Z_Construct_UScriptStruct_FMinionFormation, METADATA_PARAMS(0, nullptr) }; // 2163295996
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::NewProp_NewCenterPos = { "NewCenterPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventUpdateFormationPositions_Parms, NewCenterPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewCenterPos_MetaData), NewProp_NewCenterPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::NewProp_NewOrientation = { "NewOrientation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventUpdateFormationPositions_Parms, NewOrientation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewOrientation_MetaData), NewProp_NewOrientation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::NewProp_Formation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::NewProp_NewCenterPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::NewProp_NewOrientation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "UpdateFormationPositions", Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::MinionWaveManager_eventUpdateFormationPositions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::MinionWaveManager_eventUpdateFormationPositions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execUpdateFormationPositions)
{
	P_GET_STRUCT_REF(FMinionFormation,Z_Param_Out_Formation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_NewCenterPos);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_NewOrientation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateFormationPositions(Z_Param_Out_Formation,Z_Param_Out_NewCenterPos,Z_Param_Out_NewOrientation);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function UpdateFormationPositions ***********************

// ********** Begin Class AMinionWaveManager Function UpdateMinionBehavior *************************
struct Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics
{
	struct MinionWaveManager_eventUpdateMinionBehavior_Parms
	{
		FMinionData Minion;
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Minion Behavior" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Minion;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::NewProp_Minion = { "Minion", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventUpdateMinionBehavior_Parms, Minion), Z_Construct_UScriptStruct_FMinionData, METADATA_PARAMS(0, nullptr) }; // 65818637
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventUpdateMinionBehavior_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::NewProp_Minion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "UpdateMinionBehavior", Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::MinionWaveManager_eventUpdateMinionBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::MinionWaveManager_eventUpdateMinionBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execUpdateMinionBehavior)
{
	P_GET_STRUCT_REF(FMinionData,Z_Param_Out_Minion);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateMinionBehavior(Z_Param_Out_Minion,Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function UpdateMinionBehavior ***************************

// ********** Begin Class AMinionWaveManager Function UpdateMinionCombat ***************************
struct Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics
{
	struct MinionWaveManager_eventUpdateMinionCombat_Parms
	{
		FMinionData Minion;
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Minion Behavior" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Minion;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::NewProp_Minion = { "Minion", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventUpdateMinionCombat_Parms, Minion), Z_Construct_UScriptStruct_FMinionData, METADATA_PARAMS(0, nullptr) }; // 65818637
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventUpdateMinionCombat_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::NewProp_Minion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "UpdateMinionCombat", Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::MinionWaveManager_eventUpdateMinionCombat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::MinionWaveManager_eventUpdateMinionCombat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execUpdateMinionCombat)
{
	P_GET_STRUCT_REF(FMinionData,Z_Param_Out_Minion);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateMinionCombat(Z_Param_Out_Minion,Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function UpdateMinionCombat *****************************

// ********** Begin Class AMinionWaveManager Function UpdateMinionLOD ******************************
struct Z_Construct_UFunction_AMinionWaveManager_UpdateMinionLOD_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_UpdateMinionLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "UpdateMinionLOD", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_UpdateMinionLOD_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_UpdateMinionLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_UpdateMinionLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execUpdateMinionLOD)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateMinionLOD();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function UpdateMinionLOD ********************************

// ********** Begin Class AMinionWaveManager Function UpdateMinionMovement *************************
struct Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics
{
	struct MinionWaveManager_eventUpdateMinionMovement_Parms
	{
		FMinionData Minion;
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Minion Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de movimento e comportamento\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de movimento e comportamento" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Minion;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::NewProp_Minion = { "Minion", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventUpdateMinionMovement_Parms, Minion), Z_Construct_UScriptStruct_FMinionData, METADATA_PARAMS(0, nullptr) }; // 65818637
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventUpdateMinionMovement_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::NewProp_Minion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "UpdateMinionMovement", Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::MinionWaveManager_eventUpdateMinionMovement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::MinionWaveManager_eventUpdateMinionMovement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execUpdateMinionMovement)
{
	P_GET_STRUCT_REF(FMinionData,Z_Param_Out_Minion);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateMinionMovement(Z_Param_Out_Minion,Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function UpdateMinionMovement ***************************

// ********** Begin Class AMinionWaveManager Function UpdateNavigationGrid *************************
struct Z_Construct_UFunction_AMinionWaveManager_UpdateNavigationGrid_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_UpdateNavigationGrid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "UpdateNavigationGrid", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateNavigationGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_UpdateNavigationGrid_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AMinionWaveManager_UpdateNavigationGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_UpdateNavigationGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execUpdateNavigationGrid)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateNavigationGrid();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function UpdateNavigationGrid ***************************

// ********** Begin Class AMinionWaveManager Function UpdateSpawnPointCooldowns ********************
struct Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns_Statics
{
	struct MinionWaveManager_eventUpdateSpawnPointCooldowns_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Spawn Points" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventUpdateSpawnPointCooldowns_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "UpdateSpawnPointCooldowns", Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns_Statics::MinionWaveManager_eventUpdateSpawnPointCooldowns_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns_Statics::MinionWaveManager_eventUpdateSpawnPointCooldowns_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execUpdateSpawnPointCooldowns)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateSpawnPointCooldowns(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function UpdateSpawnPointCooldowns **********************

// ********** Begin Class AMinionWaveManager Function ValidateMinionData ***************************
struct Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics
{
	struct MinionWaveManager_eventValidateMinionData_Parms
	{
		FMinionData Minion;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Minion_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Minion;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::NewProp_Minion = { "Minion", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventValidateMinionData_Parms, Minion), Z_Construct_UScriptStruct_FMinionData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Minion_MetaData), NewProp_Minion_MetaData) }; // 65818637
void Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MinionWaveManager_eventValidateMinionData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MinionWaveManager_eventValidateMinionData_Parms), &Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::NewProp_Minion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "ValidateMinionData", Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::MinionWaveManager_eventValidateMinionData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::MinionWaveManager_eventValidateMinionData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execValidateMinionData)
{
	P_GET_STRUCT_REF(FMinionData,Z_Param_Out_Minion);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateMinionData(Z_Param_Out_Minion);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function ValidateMinionData *****************************

// ********** Begin Class AMinionWaveManager Function ValidatePathfindingGrid **********************
struct Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics
{
	struct MinionWaveManager_eventValidatePathfindingGrid_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MinionWaveManager_eventValidatePathfindingGrid_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MinionWaveManager_eventValidatePathfindingGrid_Parms), &Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "ValidatePathfindingGrid", Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::MinionWaveManager_eventValidatePathfindingGrid_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::MinionWaveManager_eventValidatePathfindingGrid_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execValidatePathfindingGrid)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidatePathfindingGrid();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function ValidatePathfindingGrid ************************

// ********** Begin Class AMinionWaveManager Function ValidateWaveSystem ***************************
struct Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics
{
	struct MinionWaveManager_eventValidateWaveSystem_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o e otimiza\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o e otimiza\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MinionWaveManager_eventValidateWaveSystem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MinionWaveManager_eventValidateWaveSystem_Parms), &Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "ValidateWaveSystem", Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::MinionWaveManager_eventValidateWaveSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::MinionWaveManager_eventValidateWaveSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execValidateWaveSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateWaveSystem();
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function ValidateWaveSystem *****************************

// ********** Begin Class AMinionWaveManager Function WorldToGridCoordinate ************************
struct Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics
{
	struct MinionWaveManager_eventWorldToGridCoordinate_Parms
	{
		FVector WorldPosition;
		FVector2D ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::NewProp_WorldPosition = { "WorldPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventWorldToGridCoordinate_Parms, WorldPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPosition_MetaData), NewProp_WorldPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MinionWaveManager_eventWorldToGridCoordinate_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::NewProp_WorldPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AMinionWaveManager, nullptr, "WorldToGridCoordinate", Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::MinionWaveManager_eventWorldToGridCoordinate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::MinionWaveManager_eventWorldToGridCoordinate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMinionWaveManager::execWorldToGridCoordinate)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldPosition);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector2D*)Z_Param__Result=P_THIS->WorldToGridCoordinate(Z_Param_Out_WorldPosition);
	P_NATIVE_END;
}
// ********** End Class AMinionWaveManager Function WorldToGridCoordinate **************************

// ********** Begin Class AMinionWaveManager *******************************************************
void AMinionWaveManager::StaticRegisterNativesAMinionWaveManager()
{
	UClass* Class = AMinionWaveManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddSpawnPoint", &AMinionWaveManager::execAddSpawnPoint },
		{ "AssignMinionsToFormation", &AMinionWaveManager::execAssignMinionsToFormation },
		{ "CalculateBoxFormation", &AMinionWaveManager::execCalculateBoxFormation },
		{ "CalculateColumnFormation", &AMinionWaveManager::execCalculateColumnFormation },
		{ "CalculateHeuristic", &AMinionWaveManager::execCalculateHeuristic },
		{ "CalculateLineFormation", &AMinionWaveManager::execCalculateLineFormation },
		{ "CalculateWaveScaling", &AMinionWaveManager::execCalculateWaveScaling },
		{ "CalculateWedgeFormation", &AMinionWaveManager::execCalculateWedgeFormation },
		{ "CanMinionAttack", &AMinionWaveManager::execCanMinionAttack },
		{ "ConfigurePathfinding", &AMinionWaveManager::execConfigurePathfinding },
		{ "ConfigureRewardSystem", &AMinionWaveManager::execConfigureRewardSystem },
		{ "ConfigureWaveSystem", &AMinionWaveManager::execConfigureWaveSystem },
		{ "CreateFormation", &AMinionWaveManager::execCreateFormation },
		{ "CullDistantMinions", &AMinionWaveManager::execCullDistantMinions },
		{ "DestroyAllMinions", &AMinionWaveManager::execDestroyAllMinions },
		{ "DestroyMinion", &AMinionWaveManager::execDestroyMinion },
		{ "DrawDebugFormations", &AMinionWaveManager::execDrawDebugFormations },
		{ "DrawDebugMinionPaths", &AMinionWaveManager::execDrawDebugMinionPaths },
		{ "DrawDebugPathfindingGrid", &AMinionWaveManager::execDrawDebugPathfindingGrid },
		{ "DrawDebugSpawnPoints", &AMinionWaveManager::execDrawDebugSpawnPoints },
		{ "ExecuteMinionAttack", &AMinionWaveManager::execExecuteMinionAttack },
		{ "FindNearestTarget", &AMinionWaveManager::execFindNearestTarget },
		{ "FindPath", &AMinionWaveManager::execFindPath },
		{ "FindPathAStar", &AMinionWaveManager::execFindPathAStar },
		{ "FindPathDijkstra", &AMinionWaveManager::execFindPathDijkstra },
		{ "GenerateMinionComposition", &AMinionWaveManager::execGenerateMinionComposition },
		{ "GenerateWaveData", &AMinionWaveManager::execGenerateWaveData },
		{ "GetActiveMinionCount", &AMinionWaveManager::execGetActiveMinionCount },
		{ "GetAvailableSpawnPoint", &AMinionWaveManager::execGetAvailableSpawnPoint },
		{ "GetCurrentWaveNumber", &AMinionWaveManager::execGetCurrentWaveNumber },
		{ "GetMinionsInLane", &AMinionWaveManager::execGetMinionsInLane },
		{ "GetMinionsInRadius", &AMinionWaveManager::execGetMinionsInRadius },
		{ "GetNeighborNodeIndices", &AMinionWaveManager::execGetNeighborNodeIndices },
		{ "GetNodeAtGridPosition", &AMinionWaveManager::execGetNodeAtGridPosition },
		{ "GetWaveProgress", &AMinionWaveManager::execGetWaveProgress },
		{ "GridToWorldPosition", &AMinionWaveManager::execGridToWorldPosition },
		{ "InitializePathfindingGrid", &AMinionWaveManager::execInitializePathfindingGrid },
		{ "InitializeSpawnPoints", &AMinionWaveManager::execInitializeSpawnPoints },
		{ "InitializeWaveSystem", &AMinionWaveManager::execInitializeWaveSystem },
		{ "IsPositionWalkable", &AMinionWaveManager::execIsPositionWalkable },
		{ "LogMinionStatistics", &AMinionWaveManager::execLogMinionStatistics },
		{ "LogWaveStatistics", &AMinionWaveManager::execLogWaveStatistics },
		{ "OptimizePathfinding", &AMinionWaveManager::execOptimizePathfinding },
		{ "PauseWaveSystem", &AMinionWaveManager::execPauseWaveSystem },
		{ "RemoveSpawnPoint", &AMinionWaveManager::execRemoveSpawnPoint },
		{ "ResetWaveSystem", &AMinionWaveManager::execResetWaveSystem },
		{ "ResumeWaveSystem", &AMinionWaveManager::execResumeWaveSystem },
		{ "SetGridCellSize", &AMinionWaveManager::execSetGridCellSize },
		{ "SetPathfindingAlgorithm", &AMinionWaveManager::execSetPathfindingAlgorithm },
		{ "SetupWaveFormation", &AMinionWaveManager::execSetupWaveFormation },
		{ "SetWaveInterval", &AMinionWaveManager::execSetWaveInterval },
		{ "SetWaveScalingFactor", &AMinionWaveManager::execSetWaveScalingFactor },
		{ "SpawnMinion", &AMinionWaveManager::execSpawnMinion },
		{ "SpawnWaveMinions", &AMinionWaveManager::execSpawnWaveMinions },
		{ "StartNextWave", &AMinionWaveManager::execStartNextWave },
		{ "StopCurrentWave", &AMinionWaveManager::execStopCurrentWave },
		{ "UpdateFormationPositions", &AMinionWaveManager::execUpdateFormationPositions },
		{ "UpdateMinionBehavior", &AMinionWaveManager::execUpdateMinionBehavior },
		{ "UpdateMinionCombat", &AMinionWaveManager::execUpdateMinionCombat },
		{ "UpdateMinionLOD", &AMinionWaveManager::execUpdateMinionLOD },
		{ "UpdateMinionMovement", &AMinionWaveManager::execUpdateMinionMovement },
		{ "UpdateNavigationGrid", &AMinionWaveManager::execUpdateNavigationGrid },
		{ "UpdateSpawnPointCooldowns", &AMinionWaveManager::execUpdateSpawnPointCooldowns },
		{ "ValidateMinionData", &AMinionWaveManager::execValidateMinionData },
		{ "ValidatePathfindingGrid", &AMinionWaveManager::execValidatePathfindingGrid },
		{ "ValidateWaveSystem", &AMinionWaveManager::execValidateWaveSystem },
		{ "WorldToGridCoordinate", &AMinionWaveManager::execWorldToGridCoordinate },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AMinionWaveManager;
UClass* AMinionWaveManager::GetPrivateStaticClass()
{
	using TClass = AMinionWaveManager;
	if (!Z_Registration_Info_UClass_AMinionWaveManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("MinionWaveManager"),
			Z_Registration_Info_UClass_AMinionWaveManager.InnerSingleton,
			StaticRegisterNativesAMinionWaveManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AMinionWaveManager.InnerSingleton;
}
UClass* Z_Construct_UClass_AMinionWaveManager_NoRegister()
{
	return AMinionWaveManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AMinionWaveManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Gerenciador do sistema de ondas de minions com pathfinding A* avan\xc3\xa7""ado\n * Controla spawn, movimento, combate e forma\xc3\xa7\xc3\xb5""es de minions\n */" },
#endif
		{ "IncludePath", "AMinionWaveManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerenciador do sistema de ondas de minions com pathfinding A* avan\xc3\xa7""ado\nControla spawn, movimento, combate e forma\xc3\xa7\xc3\xb5""es de minions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes principais\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes principais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveInterval_MetaData[] = {
		{ "Category", "Wave Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es do sistema de ondas\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es do sistema de ondas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentWaveNumber_MetaData[] = {
		{ "Category", "Wave Settings" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxWaves_MetaData[] = {
		{ "Category", "Wave Settings" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoStartWaves_MetaData[] = {
		{ "Category", "Wave Settings" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInfiniteWaves_MetaData[] = {
		{ "Category", "Wave Settings" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveScalingFactor_MetaData[] = {
		{ "Category", "Wave Settings" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathfindingAlgorithm_MetaData[] = {
		{ "Category", "Pathfinding Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de pathfinding\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de pathfinding" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridCellSize_MetaData[] = {
		{ "Category", "Pathfinding Settings" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPathfindingIterations_MetaData[] = {
		{ "Category", "Pathfinding Settings" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDynamicPathfinding_MetaData[] = {
		{ "Category", "Pathfinding Settings" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAvoidOtherMinions_MetaData[] = {
		{ "Category", "Pathfinding Settings" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathUpdateInterval_MetaData[] = {
		{ "Category", "Pathfinding Settings" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveMinions_MetaData[] = {
		{ "Category", "Minion Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Arrays de dados\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Arrays de dados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaveQueue_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentWave_MetaData[] = {
		{ "Category", "Wave Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnPoints_MetaData[] = {
		{ "Category", "Spawn Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NavigationGrid_MetaData[] = {
		{ "Category", "Pathfinding Data" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneManager_MetaData[] = {
		{ "Category", "Manager References" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Refer\xc3\xaancias para outros managers\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancias para outros managers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaronManager_MetaData[] = {
		{ "Category", "Manager References" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DragonManager_MetaData[] = {
		{ "Category", "Manager References" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WallManager_MetaData[] = {
		{ "Category", "Manager References" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverManager_MetaData[] = {
		{ "Category", "Manager References" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinionMeshes_MetaData[] = {
		{ "Category", "Visual Assets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Materiais e meshes\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Materiais e meshes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinionMaterials_MetaData[] = {
		{ "Category", "Visual Assets" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnEffectMesh_MetaData[] = {
		{ "Category", "Visual Assets" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnEffectMaterial_MetaData[] = {
		{ "Category", "Visual Assets" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowPathfindingGrid_MetaData[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es de debug\n" },
#endif
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de debug" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowMinionPaths_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowFormations_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowSpawnPoints_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDetailedLogging_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AMinionWaveManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WaveInterval;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentWaveNumber;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxWaves;
	static void NewProp_bAutoStartWaves_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoStartWaves;
	static void NewProp_bInfiniteWaves_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInfiniteWaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WaveScalingFactor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PathfindingAlgorithm_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PathfindingAlgorithm;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GridCellSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPathfindingIterations;
	static void NewProp_bUseDynamicPathfinding_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDynamicPathfinding;
	static void NewProp_bAvoidOtherMinions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAvoidOtherMinions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PathUpdateInterval;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveMinions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveMinions;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WaveQueue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WaveQueue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentWave;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpawnPoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NavigationGrid;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LaneManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BaronManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DragonManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WallManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RiverManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MinionMeshes_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinionMeshes_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinionMeshes_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_MinionMeshes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MinionMaterials_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinionMaterials_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinionMaterials_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_MinionMaterials;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpawnEffectMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpawnEffectMaterial;
	static void NewProp_bShowPathfindingGrid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowPathfindingGrid;
	static void NewProp_bShowMinionPaths_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowMinionPaths;
	static void NewProp_bShowFormations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowFormations;
	static void NewProp_bShowSpawnPoints_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowSpawnPoints;
	static void NewProp_bEnableDetailedLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDetailedLogging;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AMinionWaveManager_AddSpawnPoint, "AddSpawnPoint" }, // 2755807510
		{ &Z_Construct_UFunction_AMinionWaveManager_AssignMinionsToFormation, "AssignMinionsToFormation" }, // 1142336303
		{ &Z_Construct_UFunction_AMinionWaveManager_CalculateBoxFormation, "CalculateBoxFormation" }, // 1485266990
		{ &Z_Construct_UFunction_AMinionWaveManager_CalculateColumnFormation, "CalculateColumnFormation" }, // 2312320783
		{ &Z_Construct_UFunction_AMinionWaveManager_CalculateHeuristic, "CalculateHeuristic" }, // 467403914
		{ &Z_Construct_UFunction_AMinionWaveManager_CalculateLineFormation, "CalculateLineFormation" }, // 2741962910
		{ &Z_Construct_UFunction_AMinionWaveManager_CalculateWaveScaling, "CalculateWaveScaling" }, // 2351361037
		{ &Z_Construct_UFunction_AMinionWaveManager_CalculateWedgeFormation, "CalculateWedgeFormation" }, // 2123795751
		{ &Z_Construct_UFunction_AMinionWaveManager_CanMinionAttack, "CanMinionAttack" }, // 3636234371
		{ &Z_Construct_UFunction_AMinionWaveManager_ConfigurePathfinding, "ConfigurePathfinding" }, // 1387879902
		{ &Z_Construct_UFunction_AMinionWaveManager_ConfigureRewardSystem, "ConfigureRewardSystem" }, // 2159214450
		{ &Z_Construct_UFunction_AMinionWaveManager_ConfigureWaveSystem, "ConfigureWaveSystem" }, // 4115231773
		{ &Z_Construct_UFunction_AMinionWaveManager_CreateFormation, "CreateFormation" }, // 630917580
		{ &Z_Construct_UFunction_AMinionWaveManager_CullDistantMinions, "CullDistantMinions" }, // 2833927566
		{ &Z_Construct_UFunction_AMinionWaveManager_DestroyAllMinions, "DestroyAllMinions" }, // 2196077720
		{ &Z_Construct_UFunction_AMinionWaveManager_DestroyMinion, "DestroyMinion" }, // 3180799242
		{ &Z_Construct_UFunction_AMinionWaveManager_DrawDebugFormations, "DrawDebugFormations" }, // 1425025227
		{ &Z_Construct_UFunction_AMinionWaveManager_DrawDebugMinionPaths, "DrawDebugMinionPaths" }, // 2831692770
		{ &Z_Construct_UFunction_AMinionWaveManager_DrawDebugPathfindingGrid, "DrawDebugPathfindingGrid" }, // 1393856855
		{ &Z_Construct_UFunction_AMinionWaveManager_DrawDebugSpawnPoints, "DrawDebugSpawnPoints" }, // 3969207521
		{ &Z_Construct_UFunction_AMinionWaveManager_ExecuteMinionAttack, "ExecuteMinionAttack" }, // 1449174159
		{ &Z_Construct_UFunction_AMinionWaveManager_FindNearestTarget, "FindNearestTarget" }, // 1184268344
		{ &Z_Construct_UFunction_AMinionWaveManager_FindPath, "FindPath" }, // 1427154778
		{ &Z_Construct_UFunction_AMinionWaveManager_FindPathAStar, "FindPathAStar" }, // 1390710175
		{ &Z_Construct_UFunction_AMinionWaveManager_FindPathDijkstra, "FindPathDijkstra" }, // 3103914489
		{ &Z_Construct_UFunction_AMinionWaveManager_GenerateMinionComposition, "GenerateMinionComposition" }, // 4200178358
		{ &Z_Construct_UFunction_AMinionWaveManager_GenerateWaveData, "GenerateWaveData" }, // 416215936
		{ &Z_Construct_UFunction_AMinionWaveManager_GetActiveMinionCount, "GetActiveMinionCount" }, // 567084889
		{ &Z_Construct_UFunction_AMinionWaveManager_GetAvailableSpawnPoint, "GetAvailableSpawnPoint" }, // 2904241130
		{ &Z_Construct_UFunction_AMinionWaveManager_GetCurrentWaveNumber, "GetCurrentWaveNumber" }, // 2389644724
		{ &Z_Construct_UFunction_AMinionWaveManager_GetMinionsInLane, "GetMinionsInLane" }, // 1713688154
		{ &Z_Construct_UFunction_AMinionWaveManager_GetMinionsInRadius, "GetMinionsInRadius" }, // 1740031047
		{ &Z_Construct_UFunction_AMinionWaveManager_GetNeighborNodeIndices, "GetNeighborNodeIndices" }, // 3643163270
		{ &Z_Construct_UFunction_AMinionWaveManager_GetNodeAtGridPosition, "GetNodeAtGridPosition" }, // 2190342782
		{ &Z_Construct_UFunction_AMinionWaveManager_GetWaveProgress, "GetWaveProgress" }, // 3044766448
		{ &Z_Construct_UFunction_AMinionWaveManager_GridToWorldPosition, "GridToWorldPosition" }, // 2665006084
		{ &Z_Construct_UFunction_AMinionWaveManager_InitializePathfindingGrid, "InitializePathfindingGrid" }, // 1475646509
		{ &Z_Construct_UFunction_AMinionWaveManager_InitializeSpawnPoints, "InitializeSpawnPoints" }, // 2498083158
		{ &Z_Construct_UFunction_AMinionWaveManager_InitializeWaveSystem, "InitializeWaveSystem" }, // 3956243178
		{ &Z_Construct_UFunction_AMinionWaveManager_IsPositionWalkable, "IsPositionWalkable" }, // 639896683
		{ &Z_Construct_UFunction_AMinionWaveManager_LogMinionStatistics, "LogMinionStatistics" }, // 3934136531
		{ &Z_Construct_UFunction_AMinionWaveManager_LogWaveStatistics, "LogWaveStatistics" }, // 631270523
		{ &Z_Construct_UFunction_AMinionWaveManager_OptimizePathfinding, "OptimizePathfinding" }, // 267967288
		{ &Z_Construct_UFunction_AMinionWaveManager_PauseWaveSystem, "PauseWaveSystem" }, // 3947724638
		{ &Z_Construct_UFunction_AMinionWaveManager_RemoveSpawnPoint, "RemoveSpawnPoint" }, // 3284634125
		{ &Z_Construct_UFunction_AMinionWaveManager_ResetWaveSystem, "ResetWaveSystem" }, // 2011689030
		{ &Z_Construct_UFunction_AMinionWaveManager_ResumeWaveSystem, "ResumeWaveSystem" }, // 1014646797
		{ &Z_Construct_UFunction_AMinionWaveManager_SetGridCellSize, "SetGridCellSize" }, // 58635210
		{ &Z_Construct_UFunction_AMinionWaveManager_SetPathfindingAlgorithm, "SetPathfindingAlgorithm" }, // 4223888489
		{ &Z_Construct_UFunction_AMinionWaveManager_SetupWaveFormation, "SetupWaveFormation" }, // 138138873
		{ &Z_Construct_UFunction_AMinionWaveManager_SetWaveInterval, "SetWaveInterval" }, // 663441837
		{ &Z_Construct_UFunction_AMinionWaveManager_SetWaveScalingFactor, "SetWaveScalingFactor" }, // 230177206
		{ &Z_Construct_UFunction_AMinionWaveManager_SpawnMinion, "SpawnMinion" }, // 4105350911
		{ &Z_Construct_UFunction_AMinionWaveManager_SpawnWaveMinions, "SpawnWaveMinions" }, // 1311833772
		{ &Z_Construct_UFunction_AMinionWaveManager_StartNextWave, "StartNextWave" }, // 2960494501
		{ &Z_Construct_UFunction_AMinionWaveManager_StopCurrentWave, "StopCurrentWave" }, // 2089694328
		{ &Z_Construct_UFunction_AMinionWaveManager_UpdateFormationPositions, "UpdateFormationPositions" }, // 3234088620
		{ &Z_Construct_UFunction_AMinionWaveManager_UpdateMinionBehavior, "UpdateMinionBehavior" }, // 60170283
		{ &Z_Construct_UFunction_AMinionWaveManager_UpdateMinionCombat, "UpdateMinionCombat" }, // 740978751
		{ &Z_Construct_UFunction_AMinionWaveManager_UpdateMinionLOD, "UpdateMinionLOD" }, // 4272741806
		{ &Z_Construct_UFunction_AMinionWaveManager_UpdateMinionMovement, "UpdateMinionMovement" }, // 3474112038
		{ &Z_Construct_UFunction_AMinionWaveManager_UpdateNavigationGrid, "UpdateNavigationGrid" }, // 337961054
		{ &Z_Construct_UFunction_AMinionWaveManager_UpdateSpawnPointCooldowns, "UpdateSpawnPointCooldowns" }, // 2814679035
		{ &Z_Construct_UFunction_AMinionWaveManager_ValidateMinionData, "ValidateMinionData" }, // 962267086
		{ &Z_Construct_UFunction_AMinionWaveManager_ValidatePathfindingGrid, "ValidatePathfindingGrid" }, // 2950967072
		{ &Z_Construct_UFunction_AMinionWaveManager_ValidateWaveSystem, "ValidateWaveSystem" }, // 64432857
		{ &Z_Construct_UFunction_AMinionWaveManager_WorldToGridCoordinate, "WorldToGridCoordinate" }, // 1218321520
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AMinionWaveManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_WaveInterval = { "WaveInterval", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, WaveInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveInterval_MetaData), NewProp_WaveInterval_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_CurrentWaveNumber = { "CurrentWaveNumber", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, CurrentWaveNumber), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentWaveNumber_MetaData), NewProp_CurrentWaveNumber_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MaxWaves = { "MaxWaves", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, MaxWaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxWaves_MetaData), NewProp_MaxWaves_MetaData) };
void Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bAutoStartWaves_SetBit(void* Obj)
{
	((AMinionWaveManager*)Obj)->bAutoStartWaves = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bAutoStartWaves = { "bAutoStartWaves", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMinionWaveManager), &Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bAutoStartWaves_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoStartWaves_MetaData), NewProp_bAutoStartWaves_MetaData) };
void Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bInfiniteWaves_SetBit(void* Obj)
{
	((AMinionWaveManager*)Obj)->bInfiniteWaves = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bInfiniteWaves = { "bInfiniteWaves", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMinionWaveManager), &Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bInfiniteWaves_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInfiniteWaves_MetaData), NewProp_bInfiniteWaves_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_WaveScalingFactor = { "WaveScalingFactor", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, WaveScalingFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveScalingFactor_MetaData), NewProp_WaveScalingFactor_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_PathfindingAlgorithm_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_PathfindingAlgorithm = { "PathfindingAlgorithm", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, PathfindingAlgorithm), Z_Construct_UEnum_Aura_EPathfindingAlgorithm, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathfindingAlgorithm_MetaData), NewProp_PathfindingAlgorithm_MetaData) }; // 3485225044
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_GridCellSize = { "GridCellSize", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, GridCellSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridCellSize_MetaData), NewProp_GridCellSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MaxPathfindingIterations = { "MaxPathfindingIterations", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, MaxPathfindingIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPathfindingIterations_MetaData), NewProp_MaxPathfindingIterations_MetaData) };
void Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bUseDynamicPathfinding_SetBit(void* Obj)
{
	((AMinionWaveManager*)Obj)->bUseDynamicPathfinding = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bUseDynamicPathfinding = { "bUseDynamicPathfinding", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMinionWaveManager), &Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bUseDynamicPathfinding_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDynamicPathfinding_MetaData), NewProp_bUseDynamicPathfinding_MetaData) };
void Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bAvoidOtherMinions_SetBit(void* Obj)
{
	((AMinionWaveManager*)Obj)->bAvoidOtherMinions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bAvoidOtherMinions = { "bAvoidOtherMinions", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMinionWaveManager), &Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bAvoidOtherMinions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAvoidOtherMinions_MetaData), NewProp_bAvoidOtherMinions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_PathUpdateInterval = { "PathUpdateInterval", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, PathUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathUpdateInterval_MetaData), NewProp_PathUpdateInterval_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_ActiveMinions_Inner = { "ActiveMinions", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FMinionData, METADATA_PARAMS(0, nullptr) }; // 65818637
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_ActiveMinions = { "ActiveMinions", nullptr, (EPropertyFlags)0x0020080000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, ActiveMinions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveMinions_MetaData), NewProp_ActiveMinions_MetaData) }; // 65818637
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_WaveQueue_Inner = { "WaveQueue", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FWaveData, METADATA_PARAMS(0, nullptr) }; // 416780774
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_WaveQueue = { "WaveQueue", nullptr, (EPropertyFlags)0x0020080000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, WaveQueue), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaveQueue_MetaData), NewProp_WaveQueue_MetaData) }; // 416780774
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_CurrentWave = { "CurrentWave", nullptr, (EPropertyFlags)0x0020080000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, CurrentWave), Z_Construct_UScriptStruct_FWaveData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentWave_MetaData), NewProp_CurrentWave_MetaData) }; // 416780774
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_SpawnPoints_Inner = { "SpawnPoints", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FMinionSpawnPoint, METADATA_PARAMS(0, nullptr) }; // 3125342975
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_SpawnPoints = { "SpawnPoints", nullptr, (EPropertyFlags)0x0020080000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, SpawnPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnPoints_MetaData), NewProp_SpawnPoints_MetaData) }; // 3125342975
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_NavigationGrid = { "NavigationGrid", nullptr, (EPropertyFlags)0x0020080000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, NavigationGrid), Z_Construct_UScriptStruct_FPathfindingGrid, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NavigationGrid_MetaData), NewProp_NavigationGrid_MetaData) }; // 1562893385
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_LaneManager = { "LaneManager", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, LaneManager), Z_Construct_UClass_ALaneManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneManager_MetaData), NewProp_LaneManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_BaronManager = { "BaronManager", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, BaronManager), Z_Construct_UClass_ABaronAuracronManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaronManager_MetaData), NewProp_BaronManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_DragonManager = { "DragonManager", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, DragonManager), Z_Construct_UClass_ADragonPrismalManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DragonManager_MetaData), NewProp_DragonManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_WallManager = { "WallManager", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, WallManager), Z_Construct_UClass_AWallCollisionManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WallManager_MetaData), NewProp_WallManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_RiverManager = { "RiverManager", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, RiverManager), Z_Construct_UClass_ARiverPrismalManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverManager_MetaData), NewProp_RiverManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMeshes_ValueProp = { "MinionMeshes", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMeshes_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMeshes_Key_KeyProp = { "MinionMeshes_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Aura_EMinionType, METADATA_PARAMS(0, nullptr) }; // 1491242637
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMeshes = { "MinionMeshes", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, MinionMeshes), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinionMeshes_MetaData), NewProp_MinionMeshes_MetaData) }; // 1491242637
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMaterials_ValueProp = { "MinionMaterials", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMaterials_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMaterials_Key_KeyProp = { "MinionMaterials_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Aura_EMinionType, METADATA_PARAMS(0, nullptr) }; // 1491242637
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMaterials = { "MinionMaterials", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, MinionMaterials), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinionMaterials_MetaData), NewProp_MinionMaterials_MetaData) }; // 1491242637
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_SpawnEffectMesh = { "SpawnEffectMesh", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, SpawnEffectMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnEffectMesh_MetaData), NewProp_SpawnEffectMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_SpawnEffectMaterial = { "SpawnEffectMaterial", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMinionWaveManager, SpawnEffectMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnEffectMaterial_MetaData), NewProp_SpawnEffectMaterial_MetaData) };
void Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowPathfindingGrid_SetBit(void* Obj)
{
	((AMinionWaveManager*)Obj)->bShowPathfindingGrid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowPathfindingGrid = { "bShowPathfindingGrid", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMinionWaveManager), &Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowPathfindingGrid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowPathfindingGrid_MetaData), NewProp_bShowPathfindingGrid_MetaData) };
void Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowMinionPaths_SetBit(void* Obj)
{
	((AMinionWaveManager*)Obj)->bShowMinionPaths = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowMinionPaths = { "bShowMinionPaths", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMinionWaveManager), &Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowMinionPaths_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowMinionPaths_MetaData), NewProp_bShowMinionPaths_MetaData) };
void Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowFormations_SetBit(void* Obj)
{
	((AMinionWaveManager*)Obj)->bShowFormations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowFormations = { "bShowFormations", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMinionWaveManager), &Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowFormations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowFormations_MetaData), NewProp_bShowFormations_MetaData) };
void Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowSpawnPoints_SetBit(void* Obj)
{
	((AMinionWaveManager*)Obj)->bShowSpawnPoints = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowSpawnPoints = { "bShowSpawnPoints", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMinionWaveManager), &Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowSpawnPoints_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowSpawnPoints_MetaData), NewProp_bShowSpawnPoints_MetaData) };
void Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bEnableDetailedLogging_SetBit(void* Obj)
{
	((AMinionWaveManager*)Obj)->bEnableDetailedLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bEnableDetailedLogging = { "bEnableDetailedLogging", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMinionWaveManager), &Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bEnableDetailedLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDetailedLogging_MetaData), NewProp_bEnableDetailedLogging_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AMinionWaveManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_WaveInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_CurrentWaveNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MaxWaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bAutoStartWaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bInfiniteWaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_WaveScalingFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_PathfindingAlgorithm_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_PathfindingAlgorithm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_GridCellSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MaxPathfindingIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bUseDynamicPathfinding,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bAvoidOtherMinions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_PathUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_ActiveMinions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_ActiveMinions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_WaveQueue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_WaveQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_CurrentWave,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_SpawnPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_SpawnPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_NavigationGrid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_LaneManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_BaronManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_DragonManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_WallManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_RiverManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMeshes_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMeshes_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMeshes_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMaterials_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMaterials_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMaterials_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_MinionMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_SpawnEffectMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_SpawnEffectMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowPathfindingGrid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowMinionPaths,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowFormations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bShowSpawnPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMinionWaveManager_Statics::NewProp_bEnableDetailedLogging,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AMinionWaveManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AMinionWaveManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AMinionWaveManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AMinionWaveManager_Statics::ClassParams = {
	&AMinionWaveManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AMinionWaveManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AMinionWaveManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AMinionWaveManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AMinionWaveManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AMinionWaveManager()
{
	if (!Z_Registration_Info_UClass_AMinionWaveManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AMinionWaveManager.OuterSingleton, Z_Construct_UClass_AMinionWaveManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AMinionWaveManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AMinionWaveManager);
AMinionWaveManager::~AMinionWaveManager() {}
// ********** End Class AMinionWaveManager *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMinionWaveManager_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EMinionType_StaticEnum, TEXT("EMinionType"), &Z_Registration_Info_UEnum_EMinionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1491242637U) },
		{ EWaveType_StaticEnum, TEXT("EWaveType"), &Z_Registration_Info_UEnum_EWaveType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1826622286U) },
		{ EMinionState_StaticEnum, TEXT("EMinionState"), &Z_Registration_Info_UEnum_EMinionState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, **********U) },
		{ ELaneType_StaticEnum, TEXT("ELaneType"), &Z_Registration_Info_UEnum_ELaneType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3945053688U) },
		{ EFormationType_StaticEnum, TEXT("EFormationType"), &Z_Registration_Info_UEnum_EFormationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, **********U) },
		{ EPathfindingAlgorithm_StaticEnum, TEXT("EPathfindingAlgorithm"), &Z_Registration_Info_UEnum_EPathfindingAlgorithm, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3485225044U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRewardFormula::StaticStruct, Z_Construct_UScriptStruct_FRewardFormula_Statics::NewStructOps, TEXT("RewardFormula"), &Z_Registration_Info_UScriptStruct_FRewardFormula, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRewardFormula), 1894923168U) },
		{ FWaveConfiguration::StaticStruct, Z_Construct_UScriptStruct_FWaveConfiguration_Statics::NewStructOps, TEXT("WaveConfiguration"), &Z_Registration_Info_UScriptStruct_FWaveConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FWaveConfiguration), 1064504452U) },
		{ FMinionData::StaticStruct, Z_Construct_UScriptStruct_FMinionData_Statics::NewStructOps, TEXT("MinionData"), &Z_Registration_Info_UScriptStruct_FMinionData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMinionData), 65818637U) },
		{ FWaveData::StaticStruct, Z_Construct_UScriptStruct_FWaveData_Statics::NewStructOps, TEXT("WaveData"), &Z_Registration_Info_UScriptStruct_FWaveData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FWaveData), 416780774U) },
		{ FPathfindingNode::StaticStruct, Z_Construct_UScriptStruct_FPathfindingNode_Statics::NewStructOps, TEXT("PathfindingNode"), &Z_Registration_Info_UScriptStruct_FPathfindingNode, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPathfindingNode), 3466120308U) },
		{ FPathfindingGrid::StaticStruct, Z_Construct_UScriptStruct_FPathfindingGrid_Statics::NewStructOps, TEXT("PathfindingGrid"), &Z_Registration_Info_UScriptStruct_FPathfindingGrid, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPathfindingGrid), 1562893385U) },
		{ FMinionFormation::StaticStruct, Z_Construct_UScriptStruct_FMinionFormation_Statics::NewStructOps, TEXT("MinionFormation"), &Z_Registration_Info_UScriptStruct_FMinionFormation, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMinionFormation), 2163295996U) },
		{ FMinionSpawnPoint::StaticStruct, Z_Construct_UScriptStruct_FMinionSpawnPoint_Statics::NewStructOps, TEXT("MinionSpawnPoint"), &Z_Registration_Info_UScriptStruct_FMinionSpawnPoint, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMinionSpawnPoint), 3125342975U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AMinionWaveManager, AMinionWaveManager::StaticClass, TEXT("AMinionWaveManager"), &Z_Registration_Info_UClass_AMinionWaveManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AMinionWaveManager), 1561694207U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMinionWaveManager_h__Script_Aura_2563220234(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMinionWaveManager_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMinionWaveManager_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMinionWaveManager_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMinionWaveManager_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMinionWaveManager_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_AMinionWaveManager_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
