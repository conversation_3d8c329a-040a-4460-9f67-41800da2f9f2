// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for Aura
#pragma once
#include "SharedDefinitions.UnrealEd.Project.RTTI.ValApi.ValExpApi.Cpp20.h"
#undef AURA_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define UE_PROJECT_NAME Aura
#define UE_TARGET_NAME AuraEditor
#define UE_MODULE_NAME "Aura"
#define UE_PLUGIN_NAME ""
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define PCG_API DLLIMPORT
#define COMPUTEFRAMEWORK_API DLLIMPORT
#define FOLIAGE_API DLLIMPORT
#define GEOMETRYFRAMEWORK_API DLLIMPORT
#define MESHCONVERSION_API DLLIMPORT
#define GEOMETRYSCRIPTINGCORE_API DLLIMPORT
#define DYNAMICMESH_API DLLIMPORT
#define GEOMETRYALGORITHMS_API DLLIMPORT
#define WITH_GAMEPLAY_DEBUGGER_CORE 1
#define WITH_GAMEPLAY_DEBUGGER 1
#define WITH_GAMEPLAY_DEBUGGER_MENU 1
#define AIMODULE_API DLLIMPORT
#define GAMEPLAYABILITIES_API DLLIMPORT
#define DATAREGISTRY_API DLLIMPORT
#define PROCEDURALMESHCOMPONENT_API DLLIMPORT
#define NIAGARA_API DLLIMPORT
#define NIAGARACORE_API DLLIMPORT
#define VECTORVM_API DLLIMPORT
#define NIAGARASHADER_API DLLIMPORT
#define NIAGARAVERTEXFACTORIES_API DLLIMPORT
#define STATICMESHEDITOR_API DLLIMPORT
#define NANITEBUILDER_API DLLIMPORT
#define MESHBUILDERCOMMON_API DLLIMPORT
#define GEOMETRYPROCESSINGINTERFACES_API DLLIMPORT
#define AURA_API DLLEXPORT
#define ENHANCEDINPUT_API DLLIMPORT
