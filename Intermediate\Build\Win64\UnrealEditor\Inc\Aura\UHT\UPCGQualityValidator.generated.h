// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "UPCGQualityValidator.h"

#ifdef AURA_UPCGQualityValidator_generated_h
#error "UPCGQualityValidator.generated.h already included, missing '#pragma once' in UPCGQualityValidator.h"
#endif
#define AURA_UPCGQualityValidator_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UPCGComponent;
class UPCGGraph;
enum class EPCGQualityLevel : uint8;
enum class EPCGValidationCategory : uint8;
enum class EPCGValidationSeverity : uint8;
struct FPCGQualityMetrics;
struct FPCGValidationConfig;
struct FPCGValidationIssue;
struct FPCGValidationReport;

// ********** Begin ScriptStruct FPCGValidationConfig **********************************************
#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_90_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGValidationConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGValidationConfig;
// ********** End ScriptStruct FPCGValidationConfig ************************************************

// ********** Begin ScriptStruct FPCGValidationIssue ***********************************************
#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_154_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGValidationIssue_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGValidationIssue;
// ********** End ScriptStruct FPCGValidationIssue *************************************************

// ********** Begin ScriptStruct FPCGValidationReport **********************************************
#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_199_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGValidationReport_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGValidationReport;
// ********** End ScriptStruct FPCGValidationReport ************************************************

// ********** Begin ScriptStruct FPCGQualityMetrics ************************************************
#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_241_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGQualityMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGQualityMetrics;
// ********** End ScriptStruct FPCGQualityMetrics **************************************************

// ********** Begin Delegate FOnValidationStarted **************************************************
#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_280_DELEGATE \
AURA_API void FOnValidationStarted_DelegateWrapper(const FMulticastScriptDelegate& OnValidationStarted, const FString& ValidationID);


// ********** End Delegate FOnValidationStarted ****************************************************

// ********** Begin Delegate FOnValidationCompleted ************************************************
#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_281_DELEGATE \
AURA_API void FOnValidationCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnValidationCompleted, const FString& ValidationID, FPCGValidationReport const& Report);


// ********** End Delegate FOnValidationCompleted **************************************************

// ********** Begin Delegate FOnIssueDetected ******************************************************
#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_282_DELEGATE \
AURA_API void FOnIssueDetected_DelegateWrapper(const FMulticastScriptDelegate& OnIssueDetected, const FString& ValidationID, FPCGValidationIssue const& Issue);


// ********** End Delegate FOnIssueDetected ********************************************************

// ********** Begin Delegate FOnIssueFixed *********************************************************
#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_283_DELEGATE \
AURA_API void FOnIssueFixed_DelegateWrapper(const FMulticastScriptDelegate& OnIssueFixed, const FString& ValidationID, FPCGValidationIssue const& Issue);


// ********** End Delegate FOnIssueFixed ***********************************************************

// ********** Begin Delegate FOnQualityChanged *****************************************************
#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_284_DELEGATE \
AURA_API void FOnQualityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnQualityChanged, EPCGQualityLevel OldLevel, EPCGQualityLevel NewLevel);


// ********** End Delegate FOnQualityChanged *******************************************************

// ********** Begin Class UPCGQualityValidator *****************************************************
#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_294_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCalculateLODCoverage); \
	DECLARE_FUNCTION(execCalculateLightingQuality); \
	DECLARE_FUNCTION(execCalculateGeometryComplexity); \
	DECLARE_FUNCTION(execCalculateTextureQuality); \
	DECLARE_FUNCTION(execValidateLumenSetup); \
	DECLARE_FUNCTION(execValidateNaniteContent); \
	DECLARE_FUNCTION(execValidateWorldPartition); \
	DECLARE_FUNCTION(execValidatePCGGraph); \
	DECLARE_FUNCTION(execValidatePCGComponent); \
	DECLARE_FUNCTION(execGenerateJSONReport); \
	DECLARE_FUNCTION(execGenerateHTMLReport); \
	DECLARE_FUNCTION(execExportReport); \
	DECLARE_FUNCTION(execIsAutomaticValidationRunning); \
	DECLARE_FUNCTION(execStopAutomaticValidation); \
	DECLARE_FUNCTION(execStartAutomaticValidation); \
	DECLARE_FUNCTION(execDisableCategory); \
	DECLARE_FUNCTION(execEnableCategory); \
	DECLARE_FUNCTION(execGetValidationConfig); \
	DECLARE_FUNCTION(execSetValidationConfig); \
	DECLARE_FUNCTION(execMeetsQualityStandards); \
	DECLARE_FUNCTION(execGetCurrentMetrics); \
	DECLARE_FUNCTION(execCalculateQualityScore); \
	DECLARE_FUNCTION(execAssessOverallQuality); \
	DECLARE_FUNCTION(execFixAllAutoFixableIssues); \
	DECLARE_FUNCTION(execFixIssue); \
	DECLARE_FUNCTION(execGetIssuesBySeverity); \
	DECLARE_FUNCTION(execGetIssuesByCategory); \
	DECLARE_FUNCTION(execGetAllReports); \
	DECLARE_FUNCTION(execGetValidationReport); \
	DECLARE_FUNCTION(execStopValidation); \
	DECLARE_FUNCTION(execStartValidation);


AURA_API UClass* Z_Construct_UClass_UPCGQualityValidator_NoRegister();

#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_294_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPCGQualityValidator(); \
	friend struct Z_Construct_UClass_UPCGQualityValidator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_UPCGQualityValidator_NoRegister(); \
public: \
	DECLARE_CLASS2(UPCGQualityValidator, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_UPCGQualityValidator_NoRegister) \
	DECLARE_SERIALIZER(UPCGQualityValidator)


#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_294_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPCGQualityValidator(UPCGQualityValidator&&) = delete; \
	UPCGQualityValidator(const UPCGQualityValidator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPCGQualityValidator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPCGQualityValidator); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UPCGQualityValidator) \
	NO_API virtual ~UPCGQualityValidator();


#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_291_PROLOG
#define FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_294_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_294_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_294_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_UPCGQualityValidator_h_294_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPCGQualityValidator;

// ********** End Class UPCGQualityValidator *******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_UPCGQualityValidator_h

// ********** Begin Enum EPCGValidationSeverity ****************************************************
#define FOREACH_ENUM_EPCGVALIDATIONSEVERITY(op) \
	op(EPCGValidationSeverity::Info) \
	op(EPCGValidationSeverity::Warning) \
	op(EPCGValidationSeverity::Error) \
	op(EPCGValidationSeverity::Critical) 

enum class EPCGValidationSeverity : uint8;
template<> struct TIsUEnumClass<EPCGValidationSeverity> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGValidationSeverity>();
// ********** End Enum EPCGValidationSeverity ******************************************************

// ********** Begin Enum EPCGValidationCategory ****************************************************
#define FOREACH_ENUM_EPCGVALIDATIONCATEGORY(op) \
	op(EPCGValidationCategory::Geometry) \
	op(EPCGValidationCategory::Performance) \
	op(EPCGValidationCategory::Memory) \
	op(EPCGValidationCategory::Rendering) \
	op(EPCGValidationCategory::Physics) \
	op(EPCGValidationCategory::Streaming) \
	op(EPCGValidationCategory::Cache) \
	op(EPCGValidationCategory::Quality) \
	op(EPCGValidationCategory::Compliance) 

enum class EPCGValidationCategory : uint8;
template<> struct TIsUEnumClass<EPCGValidationCategory> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGValidationCategory>();
// ********** End Enum EPCGValidationCategory ******************************************************

// ********** Begin Enum EPCGValidationMode ********************************************************
#define FOREACH_ENUM_EPCGVALIDATIONMODE(op) \
	op(EPCGValidationMode::Manual) \
	op(EPCGValidationMode::Automatic) \
	op(EPCGValidationMode::RealTime) \
	op(EPCGValidationMode::Scheduled) 

enum class EPCGValidationMode : uint8;
template<> struct TIsUEnumClass<EPCGValidationMode> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGValidationMode>();
// ********** End Enum EPCGValidationMode **********************************************************

// ********** Begin Enum EPCGValidationResult ******************************************************
#define FOREACH_ENUM_EPCGVALIDATIONRESULT(op) \
	op(EPCGValidationResult::Passed) \
	op(EPCGValidationResult::Failed) \
	op(EPCGValidationResult::Skipped) \
	op(EPCGValidationResult::Pending) 

enum class EPCGValidationResult : uint8;
template<> struct TIsUEnumClass<EPCGValidationResult> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGValidationResult>();
// ********** End Enum EPCGValidationResult ********************************************************

// ********** Begin Enum EPCGQualityLevel **********************************************************
#define FOREACH_ENUM_EPCGQUALITYLEVEL(op) \
	op(EPCGQualityLevel::Low) \
	op(EPCGQualityLevel::Medium) \
	op(EPCGQualityLevel::High) \
	op(EPCGQualityLevel::Ultra) \
	op(EPCGQualityLevel::Cinematic) 

enum class EPCGQualityLevel : uint8;
template<> struct TIsUEnumClass<EPCGQualityLevel> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGQualityLevel>();
// ********** End Enum EPCGQualityLevel ************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
