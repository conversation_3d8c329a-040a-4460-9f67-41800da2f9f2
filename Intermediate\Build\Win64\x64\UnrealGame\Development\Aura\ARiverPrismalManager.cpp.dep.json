{"Version": "1.2", "Data": {"Source": "c:\\aura\\source\\aura\\private\\ariverprismalmanager.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\intermediate\\build\\win64\\x64\\aura\\development\\engine\\sharedpch.engine.project.rtti.exceptions.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\intermediate\\build\\win64\\x64\\unrealgame\\development\\aura\\definitions.aura.h", "c:\\aura\\source\\aura\\public\\ariverprismalmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\splinecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\curves\\spline.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\spline.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\splinecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\boxcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\shapecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\boxcomponent.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\ariverprismalmanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\kismetmathlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\proceduralmeshcomponent\\source\\proceduralmeshcomponent\\public\\proceduralmeshcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\proceduralmeshcomponent\\intermediate\\build\\win64\\unrealgame\\inc\\proceduralmeshcomponent\\uht\\proceduralmeshcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\proceduralmeshcomponent\\source\\proceduralmeshcomponent\\public\\kismetproceduralmeshlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\proceduralmeshcomponent\\intermediate\\build\\win64\\unrealgame\\inc\\proceduralmeshcomponent\\uht\\kismetproceduralmeshlibrary.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}