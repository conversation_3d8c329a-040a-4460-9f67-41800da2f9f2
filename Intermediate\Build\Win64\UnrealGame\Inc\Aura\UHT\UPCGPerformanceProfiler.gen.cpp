// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "UPCGPerformanceProfiler.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUPCGPerformanceProfiler() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_APCGLumenIntegrator_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGNaniteOptimizer_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGStreamingManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_APCGWorldPartitionManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_UPCGPerformanceProfiler();
AURA_API UClass* Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGProfilerCategory();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGProfilerDisplayMode();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGProfilerSamplingRate();
AURA_API UEnum* Z_Construct_UEnum_Aura_EPCGProfilerSeverity();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGPerformanceMetric();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGProfilerAlert();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGProfilerConfig();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGProfilerReport();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPCGSystemPerformance();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTimespan();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UCanvas_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPCGProfilerCategory ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGProfilerCategory;
static UEnum* EPCGProfilerCategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGProfilerCategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGProfilerCategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGProfilerCategory, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGProfilerCategory"));
	}
	return Z_Registration_Info_UEnum_EPCGProfilerCategory.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGProfilerCategory>()
{
	return EPCGProfilerCategory_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGProfilerCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Animation.DisplayName", "Animation" },
		{ "Animation.Name", "EPCGProfilerCategory::Animation" },
		{ "Audio.DisplayName", "Audio" },
		{ "Audio.Name", "EPCGProfilerCategory::Audio" },
		{ "Blueprint.DisplayName", "Blueprint" },
		{ "Blueprint.Name", "EPCGProfilerCategory::Blueprint" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums\n" },
#endif
		{ "CPU.DisplayName", "CPU Performance" },
		{ "CPU.Name", "EPCGProfilerCategory::CPU" },
		{ "Custom.DisplayName", "Custom Metrics" },
		{ "Custom.Name", "EPCGProfilerCategory::Custom" },
		{ "GPU.DisplayName", "GPU Performance" },
		{ "GPU.Name", "EPCGProfilerCategory::GPU" },
		{ "Lumen.DisplayName", "Lumen" },
		{ "Lumen.Name", "EPCGProfilerCategory::Lumen" },
		{ "Memory.DisplayName", "Memory Usage" },
		{ "Memory.Name", "EPCGProfilerCategory::Memory" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
		{ "Nanite.DisplayName", "Nanite" },
		{ "Nanite.Name", "EPCGProfilerCategory::Nanite" },
		{ "Network.DisplayName", "Network" },
		{ "Network.Name", "EPCGProfilerCategory::Network" },
		{ "PCG.DisplayName", "PCG Generation" },
		{ "PCG.Name", "EPCGProfilerCategory::PCG" },
		{ "Physics.DisplayName", "Physics" },
		{ "Physics.Name", "EPCGProfilerCategory::Physics" },
		{ "Rendering.DisplayName", "Rendering" },
		{ "Rendering.Name", "EPCGProfilerCategory::Rendering" },
		{ "Streaming.DisplayName", "Asset Streaming" },
		{ "Streaming.Name", "EPCGProfilerCategory::Streaming" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums" },
#endif
		{ "WorldPartition.DisplayName", "World Partition" },
		{ "WorldPartition.Name", "EPCGProfilerCategory::WorldPartition" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGProfilerCategory::CPU", (int64)EPCGProfilerCategory::CPU },
		{ "EPCGProfilerCategory::Memory", (int64)EPCGProfilerCategory::Memory },
		{ "EPCGProfilerCategory::GPU", (int64)EPCGProfilerCategory::GPU },
		{ "EPCGProfilerCategory::Rendering", (int64)EPCGProfilerCategory::Rendering },
		{ "EPCGProfilerCategory::Streaming", (int64)EPCGProfilerCategory::Streaming },
		{ "EPCGProfilerCategory::PCG", (int64)EPCGProfilerCategory::PCG },
		{ "EPCGProfilerCategory::WorldPartition", (int64)EPCGProfilerCategory::WorldPartition },
		{ "EPCGProfilerCategory::Nanite", (int64)EPCGProfilerCategory::Nanite },
		{ "EPCGProfilerCategory::Lumen", (int64)EPCGProfilerCategory::Lumen },
		{ "EPCGProfilerCategory::Network", (int64)EPCGProfilerCategory::Network },
		{ "EPCGProfilerCategory::Audio", (int64)EPCGProfilerCategory::Audio },
		{ "EPCGProfilerCategory::Physics", (int64)EPCGProfilerCategory::Physics },
		{ "EPCGProfilerCategory::Animation", (int64)EPCGProfilerCategory::Animation },
		{ "EPCGProfilerCategory::Blueprint", (int64)EPCGProfilerCategory::Blueprint },
		{ "EPCGProfilerCategory::Custom", (int64)EPCGProfilerCategory::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGProfilerCategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGProfilerCategory",
	"EPCGProfilerCategory",
	Z_Construct_UEnum_Aura_EPCGProfilerCategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGProfilerCategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGProfilerCategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGProfilerCategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGProfilerCategory()
{
	if (!Z_Registration_Info_UEnum_EPCGProfilerCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGProfilerCategory.InnerSingleton, Z_Construct_UEnum_Aura_EPCGProfilerCategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGProfilerCategory.InnerSingleton;
}
// ********** End Enum EPCGProfilerCategory ********************************************************

// ********** Begin Enum EPCGProfilerSeverity ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGProfilerSeverity;
static UEnum* EPCGProfilerSeverity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGProfilerSeverity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGProfilerSeverity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGProfilerSeverity, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGProfilerSeverity"));
	}
	return Z_Registration_Info_UEnum_EPCGProfilerSeverity.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGProfilerSeverity>()
{
	return EPCGProfilerSeverity_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGProfilerSeverity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EPCGProfilerSeverity::Critical" },
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EPCGProfilerSeverity::Error" },
		{ "Info.DisplayName", "Information" },
		{ "Info.Name", "EPCGProfilerSeverity::Info" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
		{ "Warning.DisplayName", "Warning" },
		{ "Warning.Name", "EPCGProfilerSeverity::Warning" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGProfilerSeverity::Info", (int64)EPCGProfilerSeverity::Info },
		{ "EPCGProfilerSeverity::Warning", (int64)EPCGProfilerSeverity::Warning },
		{ "EPCGProfilerSeverity::Error", (int64)EPCGProfilerSeverity::Error },
		{ "EPCGProfilerSeverity::Critical", (int64)EPCGProfilerSeverity::Critical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGProfilerSeverity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGProfilerSeverity",
	"EPCGProfilerSeverity",
	Z_Construct_UEnum_Aura_EPCGProfilerSeverity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGProfilerSeverity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGProfilerSeverity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGProfilerSeverity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGProfilerSeverity()
{
	if (!Z_Registration_Info_UEnum_EPCGProfilerSeverity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGProfilerSeverity.InnerSingleton, Z_Construct_UEnum_Aura_EPCGProfilerSeverity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGProfilerSeverity.InnerSingleton;
}
// ********** End Enum EPCGProfilerSeverity ********************************************************

// ********** Begin Enum EPCGProfilerDisplayMode ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGProfilerDisplayMode;
static UEnum* EPCGProfilerDisplayMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGProfilerDisplayMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGProfilerDisplayMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGProfilerDisplayMode, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGProfilerDisplayMode"));
	}
	return Z_Registration_Info_UEnum_EPCGProfilerDisplayMode.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGProfilerDisplayMode>()
{
	return EPCGProfilerDisplayMode_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGProfilerDisplayMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "All.DisplayName", "All Methods" },
		{ "All.Name", "EPCGProfilerDisplayMode::All" },
		{ "BlueprintType", "true" },
		{ "Console.DisplayName", "Console Output" },
		{ "Console.Name", "EPCGProfilerDisplayMode::Console" },
		{ "File.DisplayName", "File Logging" },
		{ "File.Name", "EPCGProfilerDisplayMode::File" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
		{ "Network.DisplayName", "Network Streaming" },
		{ "Network.Name", "EPCGProfilerDisplayMode::Network" },
		{ "Overlay.DisplayName", "Screen Overlay" },
		{ "Overlay.Name", "EPCGProfilerDisplayMode::Overlay" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGProfilerDisplayMode::Overlay", (int64)EPCGProfilerDisplayMode::Overlay },
		{ "EPCGProfilerDisplayMode::Console", (int64)EPCGProfilerDisplayMode::Console },
		{ "EPCGProfilerDisplayMode::File", (int64)EPCGProfilerDisplayMode::File },
		{ "EPCGProfilerDisplayMode::Network", (int64)EPCGProfilerDisplayMode::Network },
		{ "EPCGProfilerDisplayMode::All", (int64)EPCGProfilerDisplayMode::All },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGProfilerDisplayMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGProfilerDisplayMode",
	"EPCGProfilerDisplayMode",
	Z_Construct_UEnum_Aura_EPCGProfilerDisplayMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGProfilerDisplayMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGProfilerDisplayMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGProfilerDisplayMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGProfilerDisplayMode()
{
	if (!Z_Registration_Info_UEnum_EPCGProfilerDisplayMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGProfilerDisplayMode.InnerSingleton, Z_Construct_UEnum_Aura_EPCGProfilerDisplayMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGProfilerDisplayMode.InnerSingleton;
}
// ********** End Enum EPCGProfilerDisplayMode *****************************************************

// ********** Begin Enum EPCGProfilerSamplingRate **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGProfilerSamplingRate;
static UEnum* EPCGProfilerSamplingRate_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGProfilerSamplingRate.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGProfilerSamplingRate.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_EPCGProfilerSamplingRate, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("EPCGProfilerSamplingRate"));
	}
	return Z_Registration_Info_UEnum_EPCGProfilerSamplingRate.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<EPCGProfilerSamplingRate>()
{
	return EPCGProfilerSamplingRate_StaticEnum();
}
struct Z_Construct_UEnum_Aura_EPCGProfilerSamplingRate_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "High.DisplayName", "High (30 Hz)" },
		{ "High.Name", "EPCGProfilerSamplingRate::High" },
		{ "Low.DisplayName", "Low (5 Hz)" },
		{ "Low.Name", "EPCGProfilerSamplingRate::Low" },
		{ "Medium.DisplayName", "Medium (10 Hz)" },
		{ "Medium.Name", "EPCGProfilerSamplingRate::Medium" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
		{ "Realtime.DisplayName", "Realtime (120 Hz)" },
		{ "Realtime.Name", "EPCGProfilerSamplingRate::Realtime" },
		{ "VeryHigh.DisplayName", "Very High (60 Hz)" },
		{ "VeryHigh.Name", "EPCGProfilerSamplingRate::VeryHigh" },
		{ "VeryLow.DisplayName", "Very Low (1 Hz)" },
		{ "VeryLow.Name", "EPCGProfilerSamplingRate::VeryLow" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGProfilerSamplingRate::VeryLow", (int64)EPCGProfilerSamplingRate::VeryLow },
		{ "EPCGProfilerSamplingRate::Low", (int64)EPCGProfilerSamplingRate::Low },
		{ "EPCGProfilerSamplingRate::Medium", (int64)EPCGProfilerSamplingRate::Medium },
		{ "EPCGProfilerSamplingRate::High", (int64)EPCGProfilerSamplingRate::High },
		{ "EPCGProfilerSamplingRate::VeryHigh", (int64)EPCGProfilerSamplingRate::VeryHigh },
		{ "EPCGProfilerSamplingRate::Realtime", (int64)EPCGProfilerSamplingRate::Realtime },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_EPCGProfilerSamplingRate_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"EPCGProfilerSamplingRate",
	"EPCGProfilerSamplingRate",
	Z_Construct_UEnum_Aura_EPCGProfilerSamplingRate_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGProfilerSamplingRate_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_EPCGProfilerSamplingRate_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_EPCGProfilerSamplingRate_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_EPCGProfilerSamplingRate()
{
	if (!Z_Registration_Info_UEnum_EPCGProfilerSamplingRate.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGProfilerSamplingRate.InnerSingleton, Z_Construct_UEnum_Aura_EPCGProfilerSamplingRate_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGProfilerSamplingRate.InnerSingleton;
}
// ********** End Enum EPCGProfilerSamplingRate ****************************************************

// ********** Begin ScriptStruct FPCGProfilerConfig ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGProfilerConfig;
class UScriptStruct* FPCGProfilerConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGProfilerConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGProfilerConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGProfilerConfig, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGProfilerConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGProfilerConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Structures\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structures" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableProfiling_MetaData[] = {
		{ "Category", "Profiling" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SamplingRate_MetaData[] = {
		{ "Category", "Profiling" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayMode_MetaData[] = {
		{ "Category", "Display" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDetailedStats_MetaData[] = {
		{ "Category", "Display" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowGraphs_MetaData[] = {
		{ "Category", "Display" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowWarnings_MetaData[] = {
		{ "Category", "Display" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnabledCategories_MetaData[] = {
		{ "Category", "Categories" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSampleHistory_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "1000" },
		{ "ClampMin", "10" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateInterval_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "60.0" },
		{ "ClampMin", "1.0" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CPUWarningThreshold_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ClampMax", "200.0" },
		{ "ClampMin", "10.0" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryWarningThresholdMB_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ClampMax", "32000" },
		{ "ClampMin", "100" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUWarningThreshold_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ClampMax", "200.0" },
		{ "ClampMin", "10.0" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFileLogging_MetaData[] = {
		{ "Category", "Logging" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LogFilePath_MetaData[] = {
		{ "Category", "Logging" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableNetworkStreaming_MetaData[] = {
		{ "Category", "Network" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkEndpoint_MetaData[] = {
		{ "Category", "Network" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableProfiling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableProfiling;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SamplingRate_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SamplingRate;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DisplayMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DisplayMode;
	static void NewProp_bShowDetailedStats_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDetailedStats;
	static void NewProp_bShowGraphs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowGraphs;
	static void NewProp_bShowWarnings_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowWarnings;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnabledCategories_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnabledCategories_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EnabledCategories;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSampleHistory;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UpdateInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CPUWarningThreshold;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MemoryWarningThresholdMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUWarningThreshold;
	static void NewProp_bEnableFileLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFileLogging;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LogFilePath;
	static void NewProp_bEnableNetworkStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableNetworkStreaming;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NetworkEndpoint;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGProfilerConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bEnableProfiling_SetBit(void* Obj)
{
	((FPCGProfilerConfig*)Obj)->bEnableProfiling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bEnableProfiling = { "bEnableProfiling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGProfilerConfig), &Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bEnableProfiling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableProfiling_MetaData), NewProp_bEnableProfiling_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_SamplingRate_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_SamplingRate = { "SamplingRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerConfig, SamplingRate), Z_Construct_UEnum_Aura_EPCGProfilerSamplingRate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SamplingRate_MetaData), NewProp_SamplingRate_MetaData) }; // 1242914351
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_DisplayMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_DisplayMode = { "DisplayMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerConfig, DisplayMode), Z_Construct_UEnum_Aura_EPCGProfilerDisplayMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayMode_MetaData), NewProp_DisplayMode_MetaData) }; // 1174834543
void Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bShowDetailedStats_SetBit(void* Obj)
{
	((FPCGProfilerConfig*)Obj)->bShowDetailedStats = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bShowDetailedStats = { "bShowDetailedStats", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGProfilerConfig), &Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bShowDetailedStats_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDetailedStats_MetaData), NewProp_bShowDetailedStats_MetaData) };
void Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bShowGraphs_SetBit(void* Obj)
{
	((FPCGProfilerConfig*)Obj)->bShowGraphs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bShowGraphs = { "bShowGraphs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGProfilerConfig), &Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bShowGraphs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowGraphs_MetaData), NewProp_bShowGraphs_MetaData) };
void Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bShowWarnings_SetBit(void* Obj)
{
	((FPCGProfilerConfig*)Obj)->bShowWarnings = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bShowWarnings = { "bShowWarnings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGProfilerConfig), &Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bShowWarnings_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowWarnings_MetaData), NewProp_bShowWarnings_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_EnabledCategories_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_EnabledCategories_Inner = { "EnabledCategories", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Aura_EPCGProfilerCategory, METADATA_PARAMS(0, nullptr) }; // 2695066029
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_EnabledCategories = { "EnabledCategories", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerConfig, EnabledCategories), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnabledCategories_MetaData), NewProp_EnabledCategories_MetaData) }; // 2695066029
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_MaxSampleHistory = { "MaxSampleHistory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerConfig, MaxSampleHistory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSampleHistory_MetaData), NewProp_MaxSampleHistory_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_UpdateInterval = { "UpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerConfig, UpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateInterval_MetaData), NewProp_UpdateInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_CPUWarningThreshold = { "CPUWarningThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerConfig, CPUWarningThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CPUWarningThreshold_MetaData), NewProp_CPUWarningThreshold_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_MemoryWarningThresholdMB = { "MemoryWarningThresholdMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerConfig, MemoryWarningThresholdMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryWarningThresholdMB_MetaData), NewProp_MemoryWarningThresholdMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_GPUWarningThreshold = { "GPUWarningThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerConfig, GPUWarningThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUWarningThreshold_MetaData), NewProp_GPUWarningThreshold_MetaData) };
void Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bEnableFileLogging_SetBit(void* Obj)
{
	((FPCGProfilerConfig*)Obj)->bEnableFileLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bEnableFileLogging = { "bEnableFileLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGProfilerConfig), &Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bEnableFileLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFileLogging_MetaData), NewProp_bEnableFileLogging_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_LogFilePath = { "LogFilePath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerConfig, LogFilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LogFilePath_MetaData), NewProp_LogFilePath_MetaData) };
void Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bEnableNetworkStreaming_SetBit(void* Obj)
{
	((FPCGProfilerConfig*)Obj)->bEnableNetworkStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bEnableNetworkStreaming = { "bEnableNetworkStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGProfilerConfig), &Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bEnableNetworkStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableNetworkStreaming_MetaData), NewProp_bEnableNetworkStreaming_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_NetworkEndpoint = { "NetworkEndpoint", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerConfig, NetworkEndpoint), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkEndpoint_MetaData), NewProp_NetworkEndpoint_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bEnableProfiling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_SamplingRate_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_SamplingRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_DisplayMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_DisplayMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bShowDetailedStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bShowGraphs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bShowWarnings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_EnabledCategories_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_EnabledCategories_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_EnabledCategories,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_MaxSampleHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_UpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_CPUWarningThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_MemoryWarningThresholdMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_GPUWarningThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bEnableFileLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_LogFilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_bEnableNetworkStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewProp_NetworkEndpoint,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGProfilerConfig",
	Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::PropPointers),
	sizeof(FPCGProfilerConfig),
	alignof(FPCGProfilerConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGProfilerConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGProfilerConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGProfilerConfig.InnerSingleton, Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGProfilerConfig.InnerSingleton;
}
// ********** End ScriptStruct FPCGProfilerConfig **************************************************

// ********** Begin ScriptStruct FPCGPerformanceMetric *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGPerformanceMetric;
class UScriptStruct* FPCGPerformanceMetric::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPerformanceMetric.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGPerformanceMetric.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGPerformanceMetric, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGPerformanceMetric"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGPerformanceMetric.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricName_MetaData[] = {
		{ "Category", "Metric" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "Metric" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentValue_MetaData[] = {
		{ "Category", "Value" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinValue_MetaData[] = {
		{ "Category", "Value" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxValue_MetaData[] = {
		{ "Category", "Value" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageValue_MetaData[] = {
		{ "Category", "Value" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Unit_MetaData[] = {
		{ "Category", "Value" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Timing" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValueHistory_MetaData[] = {
		{ "Category", "History" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Severity_MetaData[] = {
		{ "Category", "Status" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WarningThreshold_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorThreshold_MetaData[] = {
		{ "Category", "Thresholds" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MetricName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageValue;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Unit;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValueHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValueHistory;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Severity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WarningThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ErrorThreshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGPerformanceMetric>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_MetricName = { "MetricName", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceMetric, MetricName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricName_MetaData), NewProp_MetricName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceMetric, Category), Z_Construct_UEnum_Aura_EPCGProfilerCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) }; // 2695066029
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_CurrentValue = { "CurrentValue", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceMetric, CurrentValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentValue_MetaData), NewProp_CurrentValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_MinValue = { "MinValue", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceMetric, MinValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinValue_MetaData), NewProp_MinValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_MaxValue = { "MaxValue", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceMetric, MaxValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxValue_MetaData), NewProp_MaxValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_AverageValue = { "AverageValue", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceMetric, AverageValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageValue_MetaData), NewProp_AverageValue_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceMetric, Unit), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Unit_MetaData), NewProp_Unit_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceMetric, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_ValueHistory_Inner = { "ValueHistory", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_ValueHistory = { "ValueHistory", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceMetric, ValueHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValueHistory_MetaData), NewProp_ValueHistory_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_Severity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceMetric, Severity), Z_Construct_UEnum_Aura_EPCGProfilerSeverity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Severity_MetaData), NewProp_Severity_MetaData) }; // 813734881
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_WarningThreshold = { "WarningThreshold", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceMetric, WarningThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WarningThreshold_MetaData), NewProp_WarningThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_ErrorThreshold = { "ErrorThreshold", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGPerformanceMetric, ErrorThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorThreshold_MetaData), NewProp_ErrorThreshold_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_MetricName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_CurrentValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_MinValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_MaxValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_AverageValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_Unit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_LastUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_ValueHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_ValueHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_Severity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_Severity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_WarningThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewProp_ErrorThreshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGPerformanceMetric",
	Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::PropPointers),
	sizeof(FPCGPerformanceMetric),
	alignof(FPCGPerformanceMetric),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGPerformanceMetric()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGPerformanceMetric.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGPerformanceMetric.InnerSingleton, Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGPerformanceMetric.InnerSingleton;
}
// ********** End ScriptStruct FPCGPerformanceMetric ***********************************************

// ********** Begin ScriptStruct FPCGSystemPerformance *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGSystemPerformance;
class UScriptStruct* FPCGSystemPerformance::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGSystemPerformance.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGSystemPerformance.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGSystemPerformance, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGSystemPerformance"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGSystemPerformance.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CPUUsagePercent_MetaData[] = {
		{ "Category", "CPU" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// CPU Metrics\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "CPU Metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameThreadTime_MetaData[] = {
		{ "Category", "CPU" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderThreadTime_MetaData[] = {
		{ "Category", "CPU" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RHIThreadTime_MetaData[] = {
		{ "Category", "CPU" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UsedMemoryMB_MetaData[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory Metrics\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory Metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvailableMemoryMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUMemoryMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingPoolMB_MetaData[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUUsagePercent_MetaData[] = {
		{ "Category", "GPU" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// GPU Metrics\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU Metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameTime_MetaData[] = {
		{ "Category", "GPU" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FPS_MetaData[] = {
		{ "Category", "GPU" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DrawCalls_MetaData[] = {
		{ "Category", "GPU" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Triangles_MetaData[] = {
		{ "Category", "GPU" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivePCGComponents_MetaData[] = {
		{ "Category", "PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG Specific Metrics\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG Specific Metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGGenerationTime_MetaData[] = {
		{ "Category", "PCG" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedInstances_MetaData[] = {
		{ "Category", "PCG" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingEfficiency_MetaData[] = {
		{ "Category", "PCG" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteTriangles_MetaData[] = {
		{ "Category", "Nanite" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Nanite Metrics\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nanite Metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteClusters_MetaData[] = {
		{ "Category", "Nanite" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteMemoryMB_MetaData[] = {
		{ "Category", "Nanite" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LumenUpdateTime_MetaData[] = {
		{ "Category", "Lumen" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Lumen Metrics\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lumen Metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LumenProbes_MetaData[] = {
		{ "Category", "Lumen" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LumenMemoryMB_MetaData[] = {
		{ "Category", "Lumen" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CPUUsagePercent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GameThreadTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RenderThreadTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RHIThreadTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UsedMemoryMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AvailableMemoryMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GPUMemoryMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingPoolMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUUsagePercent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrameTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FPS;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DrawCalls;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Triangles;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActivePCGComponents;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PCGGenerationTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GeneratedInstances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingEfficiency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NaniteTriangles;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NaniteClusters;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NaniteMemoryMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LumenUpdateTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LumenProbes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LumenMemoryMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGSystemPerformance>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_CPUUsagePercent = { "CPUUsagePercent", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, CPUUsagePercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CPUUsagePercent_MetaData), NewProp_CPUUsagePercent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_GameThreadTime = { "GameThreadTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, GameThreadTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameThreadTime_MetaData), NewProp_GameThreadTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_RenderThreadTime = { "RenderThreadTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, RenderThreadTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderThreadTime_MetaData), NewProp_RenderThreadTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_RHIThreadTime = { "RHIThreadTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, RHIThreadTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RHIThreadTime_MetaData), NewProp_RHIThreadTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_UsedMemoryMB = { "UsedMemoryMB", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, UsedMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UsedMemoryMB_MetaData), NewProp_UsedMemoryMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_AvailableMemoryMB = { "AvailableMemoryMB", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, AvailableMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvailableMemoryMB_MetaData), NewProp_AvailableMemoryMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_GPUMemoryMB = { "GPUMemoryMB", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, GPUMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUMemoryMB_MetaData), NewProp_GPUMemoryMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_StreamingPoolMB = { "StreamingPoolMB", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, StreamingPoolMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingPoolMB_MetaData), NewProp_StreamingPoolMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_GPUUsagePercent = { "GPUUsagePercent", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, GPUUsagePercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUUsagePercent_MetaData), NewProp_GPUUsagePercent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_FrameTime = { "FrameTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, FrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameTime_MetaData), NewProp_FrameTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_FPS = { "FPS", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, FPS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FPS_MetaData), NewProp_FPS_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_DrawCalls = { "DrawCalls", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, DrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DrawCalls_MetaData), NewProp_DrawCalls_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_Triangles = { "Triangles", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, Triangles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Triangles_MetaData), NewProp_Triangles_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_ActivePCGComponents = { "ActivePCGComponents", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, ActivePCGComponents), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivePCGComponents_MetaData), NewProp_ActivePCGComponents_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_PCGGenerationTime = { "PCGGenerationTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, PCGGenerationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGGenerationTime_MetaData), NewProp_PCGGenerationTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_GeneratedInstances = { "GeneratedInstances", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, GeneratedInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedInstances_MetaData), NewProp_GeneratedInstances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_StreamingEfficiency = { "StreamingEfficiency", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, StreamingEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingEfficiency_MetaData), NewProp_StreamingEfficiency_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_NaniteTriangles = { "NaniteTriangles", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, NaniteTriangles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteTriangles_MetaData), NewProp_NaniteTriangles_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_NaniteClusters = { "NaniteClusters", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, NaniteClusters), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteClusters_MetaData), NewProp_NaniteClusters_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_NaniteMemoryMB = { "NaniteMemoryMB", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, NaniteMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteMemoryMB_MetaData), NewProp_NaniteMemoryMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_LumenUpdateTime = { "LumenUpdateTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, LumenUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LumenUpdateTime_MetaData), NewProp_LumenUpdateTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_LumenProbes = { "LumenProbes", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, LumenProbes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LumenProbes_MetaData), NewProp_LumenProbes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_LumenMemoryMB = { "LumenMemoryMB", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGSystemPerformance, LumenMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LumenMemoryMB_MetaData), NewProp_LumenMemoryMB_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_CPUUsagePercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_GameThreadTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_RenderThreadTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_RHIThreadTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_UsedMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_AvailableMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_GPUMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_StreamingPoolMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_GPUUsagePercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_FrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_FPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_DrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_Triangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_ActivePCGComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_PCGGenerationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_GeneratedInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_StreamingEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_NaniteTriangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_NaniteClusters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_NaniteMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_LumenUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_LumenProbes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewProp_LumenMemoryMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGSystemPerformance",
	Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::PropPointers),
	sizeof(FPCGSystemPerformance),
	alignof(FPCGSystemPerformance),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGSystemPerformance()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGSystemPerformance.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGSystemPerformance.InnerSingleton, Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGSystemPerformance.InnerSingleton;
}
// ********** End ScriptStruct FPCGSystemPerformance ***********************************************

// ********** Begin ScriptStruct FPCGProfilerAlert *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGProfilerAlert;
class UScriptStruct* FPCGProfilerAlert::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGProfilerAlert.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGProfilerAlert.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGProfilerAlert, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGProfilerAlert"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGProfilerAlert.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlertMessage_MetaData[] = {
		{ "Category", "Alert" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Severity_MetaData[] = {
		{ "Category", "Alert" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "Alert" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Alert" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricValue_MetaData[] = {
		{ "Category", "Alert" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThresholdValue_MetaData[] = {
		{ "Category", "Alert" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Alert" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AlertMessage;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Severity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MetricValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ThresholdValue;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGProfilerAlert>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_AlertMessage = { "AlertMessage", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerAlert, AlertMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlertMessage_MetaData), NewProp_AlertMessage_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_Severity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerAlert, Severity), Z_Construct_UEnum_Aura_EPCGProfilerSeverity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Severity_MetaData), NewProp_Severity_MetaData) }; // 813734881
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerAlert, Category), Z_Construct_UEnum_Aura_EPCGProfilerCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) }; // 2695066029
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerAlert, Timestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_MetricValue = { "MetricValue", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerAlert, MetricValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricValue_MetaData), NewProp_MetricValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_ThresholdValue = { "ThresholdValue", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerAlert, ThresholdValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThresholdValue_MetaData), NewProp_ThresholdValue_MetaData) };
void Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FPCGProfilerAlert*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGProfilerAlert), &Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_AlertMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_Severity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_Severity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_MetricValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_ThresholdValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewProp_bIsActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGProfilerAlert",
	Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::PropPointers),
	sizeof(FPCGProfilerAlert),
	alignof(FPCGProfilerAlert),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGProfilerAlert()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGProfilerAlert.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGProfilerAlert.InnerSingleton, Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGProfilerAlert.InnerSingleton;
}
// ********** End ScriptStruct FPCGProfilerAlert ***************************************************

// ********** Begin ScriptStruct FPCGProfilerReport ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGProfilerReport;
class UScriptStruct* FPCGProfilerReport::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGProfilerReport.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGProfilerReport.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGProfilerReport, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PCGProfilerReport"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGProfilerReport.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGProfilerReport_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationTime_MetaData[] = {
		{ "Category", "Report" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProfileDuration_MetaData[] = {
		{ "Category", "Report" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AveragePerformance_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeakPerformance_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetailedMetrics_MetaData[] = {
		{ "Category", "Metrics" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Alerts_MetaData[] = {
		{ "Category", "Alerts" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceSummary_MetaData[] = {
		{ "Category", "Summary" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationRecommendations_MetaData[] = {
		{ "Category", "Recommendations" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProfileDuration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AveragePerformance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PeakPerformance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DetailedMetrics_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DetailedMetrics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Alerts_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Alerts;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PerformanceSummary;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OptimizationRecommendations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OptimizationRecommendations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGProfilerReport>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_GenerationTime = { "GenerationTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerReport, GenerationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationTime_MetaData), NewProp_GenerationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_ProfileDuration = { "ProfileDuration", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerReport, ProfileDuration), Z_Construct_UScriptStruct_FTimespan, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProfileDuration_MetaData), NewProp_ProfileDuration_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_AveragePerformance = { "AveragePerformance", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerReport, AveragePerformance), Z_Construct_UScriptStruct_FPCGSystemPerformance, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AveragePerformance_MetaData), NewProp_AveragePerformance_MetaData) }; // 2597856837
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_PeakPerformance = { "PeakPerformance", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerReport, PeakPerformance), Z_Construct_UScriptStruct_FPCGSystemPerformance, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeakPerformance_MetaData), NewProp_PeakPerformance_MetaData) }; // 2597856837
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_DetailedMetrics_Inner = { "DetailedMetrics", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGPerformanceMetric, METADATA_PARAMS(0, nullptr) }; // 140184097
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_DetailedMetrics = { "DetailedMetrics", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerReport, DetailedMetrics), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetailedMetrics_MetaData), NewProp_DetailedMetrics_MetaData) }; // 140184097
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_Alerts_Inner = { "Alerts", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGProfilerAlert, METADATA_PARAMS(0, nullptr) }; // 2569321676
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_Alerts = { "Alerts", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerReport, Alerts), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Alerts_MetaData), NewProp_Alerts_MetaData) }; // 2569321676
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_PerformanceSummary = { "PerformanceSummary", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerReport, PerformanceSummary), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceSummary_MetaData), NewProp_PerformanceSummary_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_OptimizationRecommendations_Inner = { "OptimizationRecommendations", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_OptimizationRecommendations = { "OptimizationRecommendations", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGProfilerReport, OptimizationRecommendations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationRecommendations_MetaData), NewProp_OptimizationRecommendations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_GenerationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_ProfileDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_AveragePerformance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_PeakPerformance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_DetailedMetrics_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_DetailedMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_Alerts_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_Alerts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_PerformanceSummary,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_OptimizationRecommendations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewProp_OptimizationRecommendations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PCGProfilerReport",
	Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::PropPointers),
	sizeof(FPCGProfilerReport),
	alignof(FPCGProfilerReport),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGProfilerReport()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGProfilerReport.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGProfilerReport.InnerSingleton, Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGProfilerReport.InnerSingleton;
}
// ********** End ScriptStruct FPCGProfilerReport **************************************************

// ********** Begin Delegate FOnPerformanceAlert ***************************************************
struct Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPerformanceAlert_Parms
	{
		FPCGProfilerAlert Alert;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegates\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Alert_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Alert;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature_Statics::NewProp_Alert = { "Alert", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPerformanceAlert_Parms, Alert), Z_Construct_UScriptStruct_FPCGProfilerAlert, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Alert_MetaData), NewProp_Alert_MetaData) }; // 2569321676
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature_Statics::NewProp_Alert,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPerformanceAlert__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature_Statics::_Script_Aura_eventOnPerformanceAlert_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature_Statics::_Script_Aura_eventOnPerformanceAlert_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPerformanceAlert_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceAlert, FPCGProfilerAlert const& Alert)
{
	struct _Script_Aura_eventOnPerformanceAlert_Parms
	{
		FPCGProfilerAlert Alert;
	};
	_Script_Aura_eventOnPerformanceAlert_Parms Parms;
	Parms.Alert=Alert;
	OnPerformanceAlert.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPerformanceAlert *****************************************************

// ********** Begin Delegate FOnMetricUpdated ******************************************************
struct Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnMetricUpdated_Parms
	{
		FPCGPerformanceMetric Metric;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metric_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Metric;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature_Statics::NewProp_Metric = { "Metric", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnMetricUpdated_Parms, Metric), Z_Construct_UScriptStruct_FPCGPerformanceMetric, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metric_MetaData), NewProp_Metric_MetaData) }; // 140184097
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature_Statics::NewProp_Metric,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnMetricUpdated__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature_Statics::_Script_Aura_eventOnMetricUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature_Statics::_Script_Aura_eventOnMetricUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnMetricUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnMetricUpdated, FPCGPerformanceMetric const& Metric)
{
	struct _Script_Aura_eventOnMetricUpdated_Parms
	{
		FPCGPerformanceMetric Metric;
	};
	_Script_Aura_eventOnMetricUpdated_Parms Parms;
	Parms.Metric=Metric;
	OnMetricUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMetricUpdated ********************************************************

// ********** Begin Delegate FOnProfilerReportGenerated ********************************************
struct Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnProfilerReportGenerated_Parms
	{
		FPCGProfilerReport Report;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Report_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Report;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature_Statics::NewProp_Report = { "Report", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnProfilerReportGenerated_Parms, Report), Z_Construct_UScriptStruct_FPCGProfilerReport, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Report_MetaData), NewProp_Report_MetaData) }; // 2505381472
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature_Statics::NewProp_Report,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnProfilerReportGenerated__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature_Statics::_Script_Aura_eventOnProfilerReportGenerated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature_Statics::_Script_Aura_eventOnProfilerReportGenerated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnProfilerReportGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnProfilerReportGenerated, FPCGProfilerReport const& Report)
{
	struct _Script_Aura_eventOnProfilerReportGenerated_Parms
	{
		FPCGProfilerReport Report;
	};
	_Script_Aura_eventOnProfilerReportGenerated_Parms Parms;
	Parms.Report=Report;
	OnProfilerReportGenerated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnProfilerReportGenerated **********************************************

// ********** Begin Delegate FOnThresholdExceeded **************************************************
struct Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnThresholdExceeded_Parms
	{
		FString MetricName;
		float Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MetricName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::NewProp_MetricName = { "MetricName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnThresholdExceeded_Parms, MetricName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricName_MetaData), NewProp_MetricName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnThresholdExceeded_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::NewProp_MetricName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnThresholdExceeded__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::_Script_Aura_eventOnThresholdExceeded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::_Script_Aura_eventOnThresholdExceeded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnThresholdExceeded_DelegateWrapper(const FMulticastScriptDelegate& OnThresholdExceeded, const FString& MetricName, float Value)
{
	struct _Script_Aura_eventOnThresholdExceeded_Parms
	{
		FString MetricName;
		float Value;
	};
	_Script_Aura_eventOnThresholdExceeded_Parms Parms;
	Parms.MetricName=MetricName;
	Parms.Value=Value;
	OnThresholdExceeded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnThresholdExceeded ****************************************************

// ********** Begin Class UPCGPerformanceProfiler Function ClearAlert ******************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert_Statics
{
	struct PCGPerformanceProfiler_eventClearAlert_Parms
	{
		FString MetricName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Thresholds" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MetricName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert_Statics::NewProp_MetricName = { "MetricName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventClearAlert_Parms, MetricName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricName_MetaData), NewProp_MetricName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert_Statics::NewProp_MetricName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "ClearAlert", Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert_Statics::PCGPerformanceProfiler_eventClearAlert_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert_Statics::PCGPerformanceProfiler_eventClearAlert_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execClearAlert)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_MetricName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAlert(Z_Param_MetricName);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function ClearAlert ********************************

// ********** Begin Class UPCGPerformanceProfiler Function ClearAllAlerts **************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAllAlerts_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Thresholds" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAllAlerts_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "ClearAllAlerts", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAllAlerts_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAllAlerts_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAllAlerts()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAllAlerts_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execClearAllAlerts)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllAlerts();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function ClearAllAlerts ****************************

// ********** Begin Class UPCGPerformanceProfiler Function DrawProfilerOverlay *********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay_Statics
{
	struct PCGPerformanceProfiler_eventDrawProfilerOverlay_Parms
	{
		UCanvas* Canvas;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Visualization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visualization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Canvas;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay_Statics::NewProp_Canvas = { "Canvas", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventDrawProfilerOverlay_Parms, Canvas), Z_Construct_UClass_UCanvas_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay_Statics::NewProp_Canvas,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "DrawProfilerOverlay", Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay_Statics::PCGPerformanceProfiler_eventDrawProfilerOverlay_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay_Statics::PCGPerformanceProfiler_eventDrawProfilerOverlay_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execDrawProfilerOverlay)
{
	P_GET_OBJECT(UCanvas,Z_Param_Canvas);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawProfilerOverlay(Z_Param_Canvas);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function DrawProfilerOverlay ***********************

// ********** Begin Class UPCGPerformanceProfiler Function EnableCategory **************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics
{
	struct PCGPerformanceProfiler_eventEnableCategory_Parms
	{
		EPCGProfilerCategory Category;
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Control" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventEnableCategory_Parms, Category), Z_Construct_UEnum_Aura_EPCGProfilerCategory, METADATA_PARAMS(0, nullptr) }; // 2695066029
void Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((PCGPerformanceProfiler_eventEnableCategory_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGPerformanceProfiler_eventEnableCategory_Parms), &Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "EnableCategory", Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::PCGPerformanceProfiler_eventEnableCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::PCGPerformanceProfiler_eventEnableCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execEnableCategory)
{
	P_GET_ENUM(EPCGProfilerCategory,Z_Param_Category);
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableCategory(EPCGProfilerCategory(Z_Param_Category),Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function EnableCategory ****************************

// ********** Begin Class UPCGPerformanceProfiler Function EndBenchmark ****************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark_Statics
{
	struct PCGPerformanceProfiler_eventEndBenchmark_Parms
	{
		FString BenchmarkName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Benchmark" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BenchmarkName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BenchmarkName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark_Statics::NewProp_BenchmarkName = { "BenchmarkName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventEndBenchmark_Parms, BenchmarkName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BenchmarkName_MetaData), NewProp_BenchmarkName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark_Statics::NewProp_BenchmarkName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "EndBenchmark", Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark_Statics::PCGPerformanceProfiler_eventEndBenchmark_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark_Statics::PCGPerformanceProfiler_eventEndBenchmark_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execEndBenchmark)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BenchmarkName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EndBenchmark(Z_Param_BenchmarkName);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function EndBenchmark ******************************

// ********** Begin Class UPCGPerformanceProfiler Function ExportMetricsToCSV **********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV_Statics
{
	struct PCGPerformanceProfiler_eventExportMetricsToCSV_Parms
	{
		FString FilePath;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Reports" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventExportMetricsToCSV_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV_Statics::NewProp_FilePath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "ExportMetricsToCSV", Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV_Statics::PCGPerformanceProfiler_eventExportMetricsToCSV_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV_Statics::PCGPerformanceProfiler_eventExportMetricsToCSV_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execExportMetricsToCSV)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExportMetricsToCSV(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function ExportMetricsToCSV ************************

// ********** Begin Class UPCGPerformanceProfiler Function ExportReportToFile **********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile_Statics
{
	struct PCGPerformanceProfiler_eventExportReportToFile_Parms
	{
		FString FilePath;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Reports" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventExportReportToFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile_Statics::NewProp_FilePath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "ExportReportToFile", Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile_Statics::PCGPerformanceProfiler_eventExportReportToFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile_Statics::PCGPerformanceProfiler_eventExportReportToFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execExportReportToFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExportReportToFile(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function ExportReportToFile ************************

// ********** Begin Class UPCGPerformanceProfiler Function GenerateReport **************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport_Statics
{
	struct PCGPerformanceProfiler_eventGenerateReport_Parms
	{
		FPCGProfilerReport ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Reports" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Reporting\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reporting" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGenerateReport_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGProfilerReport, METADATA_PARAMS(0, nullptr) }; // 2505381472
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GenerateReport", Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport_Statics::PCGPerformanceProfiler_eventGenerateReport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport_Statics::PCGPerformanceProfiler_eventGenerateReport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGenerateReport)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGProfilerReport*)Z_Param__Result=P_THIS->GenerateReport();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GenerateReport ****************************

// ********** Begin Class UPCGPerformanceProfiler Function GetActiveAlerts *************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics
{
	struct PCGPerformanceProfiler_eventGetActiveAlerts_Parms
	{
		TArray<FPCGProfilerAlert> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Data" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGProfilerAlert, METADATA_PARAMS(0, nullptr) }; // 2569321676
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetActiveAlerts_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2569321676
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetActiveAlerts", Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::PCGPerformanceProfiler_eventGetActiveAlerts_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::PCGPerformanceProfiler_eventGetActiveAlerts_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetActiveAlerts)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGProfilerAlert>*)Z_Param__Result=P_THIS->GetActiveAlerts();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetActiveAlerts ***************************

// ********** Begin Class UPCGPerformanceProfiler Function GetAlertHistory *************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics
{
	struct PCGPerformanceProfiler_eventGetAlertHistory_Parms
	{
		TArray<FPCGProfilerAlert> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Data" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGProfilerAlert, METADATA_PARAMS(0, nullptr) }; // 2569321676
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetAlertHistory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2569321676
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetAlertHistory", Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::PCGPerformanceProfiler_eventGetAlertHistory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::PCGPerformanceProfiler_eventGetAlertHistory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetAlertHistory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGProfilerAlert>*)Z_Param__Result=P_THIS->GetAlertHistory();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetAlertHistory ***************************

// ********** Begin Class UPCGPerformanceProfiler Function GetAllBenchmarkResults ******************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics
{
	struct PCGPerformanceProfiler_eventGetAllBenchmarkResults_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Benchmark" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetAllBenchmarkResults_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetAllBenchmarkResults", Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::PCGPerformanceProfiler_eventGetAllBenchmarkResults_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::PCGPerformanceProfiler_eventGetAllBenchmarkResults_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetAllBenchmarkResults)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetAllBenchmarkResults();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetAllBenchmarkResults ********************

// ********** Begin Class UPCGPerformanceProfiler Function GetAllMetrics ***************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics
{
	struct PCGPerformanceProfiler_eventGetAllMetrics_Parms
	{
		TArray<FPCGPerformanceMetric> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Metrics" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGPerformanceMetric, METADATA_PARAMS(0, nullptr) }; // 140184097
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetAllMetrics_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 140184097
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetAllMetrics", Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::PCGPerformanceProfiler_eventGetAllMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::PCGPerformanceProfiler_eventGetAllMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetAllMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGPerformanceMetric>*)Z_Param__Result=P_THIS->GetAllMetrics();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetAllMetrics *****************************

// ********** Begin Class UPCGPerformanceProfiler Function GetAverageFrameTime *********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime_Statics
{
	struct PCGPerformanceProfiler_eventGetAverageFrameTime_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Performance" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetAverageFrameTime_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetAverageFrameTime", Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime_Statics::PCGPerformanceProfiler_eventGetAverageFrameTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime_Statics::PCGPerformanceProfiler_eventGetAverageFrameTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetAverageFrameTime)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAverageFrameTime();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetAverageFrameTime ***********************

// ********** Begin Class UPCGPerformanceProfiler Function GetAveragePerformance *******************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance_Statics
{
	struct PCGPerformanceProfiler_eventGetAveragePerformance_Parms
	{
		FPCGSystemPerformance ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Data" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetAveragePerformance_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGSystemPerformance, METADATA_PARAMS(0, nullptr) }; // 2597856837
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetAveragePerformance", Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance_Statics::PCGPerformanceProfiler_eventGetAveragePerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance_Statics::PCGPerformanceProfiler_eventGetAveragePerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetAveragePerformance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGSystemPerformance*)Z_Param__Result=P_THIS->GetAveragePerformance();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetAveragePerformance *********************

// ********** Begin Class UPCGPerformanceProfiler Function GetBenchmarkResult **********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics
{
	struct PCGPerformanceProfiler_eventGetBenchmarkResult_Parms
	{
		FString BenchmarkName;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Benchmark" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BenchmarkName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BenchmarkName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::NewProp_BenchmarkName = { "BenchmarkName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetBenchmarkResult_Parms, BenchmarkName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BenchmarkName_MetaData), NewProp_BenchmarkName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetBenchmarkResult_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::NewProp_BenchmarkName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetBenchmarkResult", Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::PCGPerformanceProfiler_eventGetBenchmarkResult_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::PCGPerformanceProfiler_eventGetBenchmarkResult_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetBenchmarkResult)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BenchmarkName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetBenchmarkResult(Z_Param_BenchmarkName);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetBenchmarkResult ************************

// ********** Begin Class UPCGPerformanceProfiler Function GetCurrentPerformance *******************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance_Statics
{
	struct PCGPerformanceProfiler_eventGetCurrentPerformance_Parms
	{
		FPCGSystemPerformance ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance Data\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetCurrentPerformance_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGSystemPerformance, METADATA_PARAMS(0, nullptr) }; // 2597856837
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetCurrentPerformance", Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance_Statics::PCGPerformanceProfiler_eventGetCurrentPerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance_Statics::PCGPerformanceProfiler_eventGetCurrentPerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetCurrentPerformance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGSystemPerformance*)Z_Param__Result=P_THIS->GetCurrentPerformance();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetCurrentPerformance *********************

// ********** Begin Class UPCGPerformanceProfiler Function GetDrawCallCount ************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount_Statics
{
	struct PCGPerformanceProfiler_eventGetDrawCallCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler GPU" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetDrawCallCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetDrawCallCount", Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount_Statics::PCGPerformanceProfiler_eventGetDrawCallCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount_Statics::PCGPerformanceProfiler_eventGetDrawCallCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetDrawCallCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetDrawCallCount();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetDrawCallCount **************************

// ********** Begin Class UPCGPerformanceProfiler Function GetGPUFrameTime *************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime_Statics
{
	struct PCGPerformanceProfiler_eventGetGPUFrameTime_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler GPU" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetGPUFrameTime_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetGPUFrameTime", Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime_Statics::PCGPerformanceProfiler_eventGetGPUFrameTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime_Statics::PCGPerformanceProfiler_eventGetGPUFrameTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetGPUFrameTime)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetGPUFrameTime();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetGPUFrameTime ***************************

// ********** Begin Class UPCGPerformanceProfiler Function GetMemoryUsage **************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics
{
	struct PCGPerformanceProfiler_eventGetMemoryUsage_Parms
	{
		FString Category;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Memory" },
		{ "CPP_Default_Category", "" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetMemoryUsage_Parms, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetMemoryUsage", Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::PCGPerformanceProfiler_eventGetMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::PCGPerformanceProfiler_eventGetMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetMemoryUsage)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetMemoryUsage(Z_Param_Category);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetMemoryUsage ****************************

// ********** Begin Class UPCGPerformanceProfiler Function GetMetric *******************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics
{
	struct PCGPerformanceProfiler_eventGetMetric_Parms
	{
		FString MetricName;
		FPCGPerformanceMetric ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Metrics" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MetricName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::NewProp_MetricName = { "MetricName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetMetric_Parms, MetricName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricName_MetaData), NewProp_MetricName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetMetric_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGPerformanceMetric, METADATA_PARAMS(0, nullptr) }; // 140184097
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::NewProp_MetricName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetMetric", Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::PCGPerformanceProfiler_eventGetMetric_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::PCGPerformanceProfiler_eventGetMetric_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetMetric)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_MetricName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGPerformanceMetric*)Z_Param__Result=P_THIS->GetMetric(Z_Param_MetricName);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetMetric *********************************

// ********** Begin Class UPCGPerformanceProfiler Function GetMetricsByCategory ********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics
{
	struct PCGPerformanceProfiler_eventGetMetricsByCategory_Parms
	{
		EPCGProfilerCategory Category;
		TArray<FPCGPerformanceMetric> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Metrics" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetMetricsByCategory_Parms, Category), Z_Construct_UEnum_Aura_EPCGProfilerCategory, METADATA_PARAMS(0, nullptr) }; // 2695066029
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGPerformanceMetric, METADATA_PARAMS(0, nullptr) }; // 140184097
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetMetricsByCategory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 140184097
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetMetricsByCategory", Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::PCGPerformanceProfiler_eventGetMetricsByCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::PCGPerformanceProfiler_eventGetMetricsByCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetMetricsByCategory)
{
	P_GET_ENUM(EPCGProfilerCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPCGPerformanceMetric>*)Z_Param__Result=P_THIS->GetMetricsByCategory(EPCGProfilerCategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetMetricsByCategory **********************

// ********** Begin Class UPCGPerformanceProfiler Function GetOptimizationRecommendations **********
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics
{
	struct PCGPerformanceProfiler_eventGetOptimizationRecommendations_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Reports" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetOptimizationRecommendations_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetOptimizationRecommendations", Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::PCGPerformanceProfiler_eventGetOptimizationRecommendations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::PCGPerformanceProfiler_eventGetOptimizationRecommendations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetOptimizationRecommendations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetOptimizationRecommendations();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetOptimizationRecommendations ************

// ********** Begin Class UPCGPerformanceProfiler Function GetPeakPerformance **********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance_Statics
{
	struct PCGPerformanceProfiler_eventGetPeakPerformance_Parms
	{
		FPCGSystemPerformance ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Data" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetPeakPerformance_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGSystemPerformance, METADATA_PARAMS(0, nullptr) }; // 2597856837
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetPeakPerformance", Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance_Statics::PCGPerformanceProfiler_eventGetPeakPerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance_Statics::PCGPerformanceProfiler_eventGetPeakPerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetPeakPerformance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGSystemPerformance*)Z_Param__Result=P_THIS->GetPeakPerformance();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetPeakPerformance ************************

// ********** Begin Class UPCGPerformanceProfiler Function GetPerformanceSummary *******************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary_Statics
{
	struct PCGPerformanceProfiler_eventGetPerformanceSummary_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Reports" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetPerformanceSummary_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetPerformanceSummary", Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary_Statics::PCGPerformanceProfiler_eventGetPerformanceSummary_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary_Statics::PCGPerformanceProfiler_eventGetPerformanceSummary_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetPerformanceSummary)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetPerformanceSummary();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetPerformanceSummary *********************

// ********** Begin Class UPCGPerformanceProfiler Function GetTriangleCount ************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount_Statics
{
	struct PCGPerformanceProfiler_eventGetTriangleCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Performance" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventGetTriangleCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "GetTriangleCount", Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount_Statics::PCGPerformanceProfiler_eventGetTriangleCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount_Statics::PCGPerformanceProfiler_eventGetTriangleCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execGetTriangleCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTriangleCount();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function GetTriangleCount **************************

// ********** Begin Class UPCGPerformanceProfiler Function HasActiveAlerts *************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics
{
	struct PCGPerformanceProfiler_eventHasActiveAlerts_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Thresholds" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGPerformanceProfiler_eventHasActiveAlerts_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGPerformanceProfiler_eventHasActiveAlerts_Parms), &Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "HasActiveAlerts", Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::PCGPerformanceProfiler_eventHasActiveAlerts_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::PCGPerformanceProfiler_eventHasActiveAlerts_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execHasActiveAlerts)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasActiveAlerts();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function HasActiveAlerts ***************************

// ********** Begin Class UPCGPerformanceProfiler Function InitializeProfiler **********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_InitializeProfiler_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Lifecycle" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Initialization and Cleanup\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialization and Cleanup" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_InitializeProfiler_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "InitializeProfiler", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_InitializeProfiler_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_InitializeProfiler_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_InitializeProfiler()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_InitializeProfiler_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execInitializeProfiler)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeProfiler();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function InitializeProfiler ************************

// ********** Begin Class UPCGPerformanceProfiler Function IntegrateWithLumen **********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen_Statics
{
	struct PCGPerformanceProfiler_eventIntegrateWithLumen_Parms
	{
		APCGLumenIntegrator* LumenIntegrator;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Integration" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LumenIntegrator;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen_Statics::NewProp_LumenIntegrator = { "LumenIntegrator", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventIntegrateWithLumen_Parms, LumenIntegrator), Z_Construct_UClass_APCGLumenIntegrator_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen_Statics::NewProp_LumenIntegrator,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "IntegrateWithLumen", Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen_Statics::PCGPerformanceProfiler_eventIntegrateWithLumen_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen_Statics::PCGPerformanceProfiler_eventIntegrateWithLumen_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execIntegrateWithLumen)
{
	P_GET_OBJECT(APCGLumenIntegrator,Z_Param_LumenIntegrator);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithLumen(Z_Param_LumenIntegrator);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function IntegrateWithLumen ************************

// ********** Begin Class UPCGPerformanceProfiler Function IntegrateWithNanite *********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite_Statics
{
	struct PCGPerformanceProfiler_eventIntegrateWithNanite_Parms
	{
		APCGNaniteOptimizer* NaniteOptimizer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Integration" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NaniteOptimizer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite_Statics::NewProp_NaniteOptimizer = { "NaniteOptimizer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventIntegrateWithNanite_Parms, NaniteOptimizer), Z_Construct_UClass_APCGNaniteOptimizer_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite_Statics::NewProp_NaniteOptimizer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "IntegrateWithNanite", Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite_Statics::PCGPerformanceProfiler_eventIntegrateWithNanite_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite_Statics::PCGPerformanceProfiler_eventIntegrateWithNanite_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execIntegrateWithNanite)
{
	P_GET_OBJECT(APCGNaniteOptimizer,Z_Param_NaniteOptimizer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithNanite(Z_Param_NaniteOptimizer);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function IntegrateWithNanite ***********************

// ********** Begin Class UPCGPerformanceProfiler Function IntegrateWithStreaming ******************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming_Statics
{
	struct PCGPerformanceProfiler_eventIntegrateWithStreaming_Parms
	{
		APCGStreamingManager* StreamingManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Integration" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StreamingManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming_Statics::NewProp_StreamingManager = { "StreamingManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventIntegrateWithStreaming_Parms, StreamingManager), Z_Construct_UClass_APCGStreamingManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming_Statics::NewProp_StreamingManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "IntegrateWithStreaming", Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming_Statics::PCGPerformanceProfiler_eventIntegrateWithStreaming_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming_Statics::PCGPerformanceProfiler_eventIntegrateWithStreaming_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execIntegrateWithStreaming)
{
	P_GET_OBJECT(APCGStreamingManager,Z_Param_StreamingManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithStreaming(Z_Param_StreamingManager);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function IntegrateWithStreaming ********************

// ********** Begin Class UPCGPerformanceProfiler Function IntegrateWithWorldPartition *************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition_Statics
{
	struct PCGPerformanceProfiler_eventIntegrateWithWorldPartition_Parms
	{
		APCGWorldPartitionManager* WorldPartitionManager;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with PCG Systems\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with PCG Systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartitionManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition_Statics::NewProp_WorldPartitionManager = { "WorldPartitionManager", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventIntegrateWithWorldPartition_Parms, WorldPartitionManager), Z_Construct_UClass_APCGWorldPartitionManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition_Statics::NewProp_WorldPartitionManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "IntegrateWithWorldPartition", Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition_Statics::PCGPerformanceProfiler_eventIntegrateWithWorldPartition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition_Statics::PCGPerformanceProfiler_eventIntegrateWithWorldPartition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execIntegrateWithWorldPartition)
{
	P_GET_OBJECT(APCGWorldPartitionManager,Z_Param_WorldPartitionManager);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithWorldPartition(Z_Param_WorldPartitionManager);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function IntegrateWithWorldPartition ***************

// ********** Begin Class UPCGPerformanceProfiler Function IsProfilerActive ************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics
{
	struct PCGPerformanceProfiler_eventIsProfilerActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Lifecycle" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGPerformanceProfiler_eventIsProfilerActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGPerformanceProfiler_eventIsProfilerActive_Parms), &Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "IsProfilerActive", Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::PCGPerformanceProfiler_eventIsProfilerActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::PCGPerformanceProfiler_eventIsProfilerActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execIsProfilerActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsProfilerActive();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function IsProfilerActive **************************

// ********** Begin Class UPCGPerformanceProfiler Function PauseProfiling **************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics
{
	struct PCGPerformanceProfiler_eventPauseProfiling_Parms
	{
		bool bPause;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Control" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bPause_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPause;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::NewProp_bPause_SetBit(void* Obj)
{
	((PCGPerformanceProfiler_eventPauseProfiling_Parms*)Obj)->bPause = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::NewProp_bPause = { "bPause", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGPerformanceProfiler_eventPauseProfiling_Parms), &Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::NewProp_bPause_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::NewProp_bPause,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "PauseProfiling", Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::PCGPerformanceProfiler_eventPauseProfiling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::PCGPerformanceProfiler_eventPauseProfiling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execPauseProfiling)
{
	P_GET_UBOOL(Z_Param_bPause);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PauseProfiling(Z_Param_bPause);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function PauseProfiling ****************************

// ********** Begin Class UPCGPerformanceProfiler Function RegisterCustomMetric ********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics
{
	struct PCGPerformanceProfiler_eventRegisterCustomMetric_Parms
	{
		FString MetricName;
		EPCGProfilerCategory Category;
		FString Unit;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Metrics Management\n" },
#endif
		{ "CPP_Default_Unit", "" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Metrics Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Unit_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MetricName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Unit;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::NewProp_MetricName = { "MetricName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventRegisterCustomMetric_Parms, MetricName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricName_MetaData), NewProp_MetricName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventRegisterCustomMetric_Parms, Category), Z_Construct_UEnum_Aura_EPCGProfilerCategory, METADATA_PARAMS(0, nullptr) }; // 2695066029
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventRegisterCustomMetric_Parms, Unit), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Unit_MetaData), NewProp_Unit_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::NewProp_MetricName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::NewProp_Unit,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "RegisterCustomMetric", Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::PCGPerformanceProfiler_eventRegisterCustomMetric_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::PCGPerformanceProfiler_eventRegisterCustomMetric_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execRegisterCustomMetric)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_MetricName);
	P_GET_ENUM(EPCGProfilerCategory,Z_Param_Category);
	P_GET_PROPERTY(FStrProperty,Z_Param_Unit);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterCustomMetric(Z_Param_MetricName,EPCGProfilerCategory(Z_Param_Category),Z_Param_Unit);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function RegisterCustomMetric **********************

// ********** Begin Class UPCGPerformanceProfiler Function RemoveCustomMetric **********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric_Statics
{
	struct PCGPerformanceProfiler_eventRemoveCustomMetric_Parms
	{
		FString MetricName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Metrics" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MetricName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric_Statics::NewProp_MetricName = { "MetricName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventRemoveCustomMetric_Parms, MetricName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricName_MetaData), NewProp_MetricName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric_Statics::NewProp_MetricName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "RemoveCustomMetric", Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric_Statics::PCGPerformanceProfiler_eventRemoveCustomMetric_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric_Statics::PCGPerformanceProfiler_eventRemoveCustomMetric_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execRemoveCustomMetric)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_MetricName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveCustomMetric(Z_Param_MetricName);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function RemoveCustomMetric ************************

// ********** Begin Class UPCGPerformanceProfiler Function ResetProfiler ***************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_ResetProfiler_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Control" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_ResetProfiler_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "ResetProfiler", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_ResetProfiler_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_ResetProfiler_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_ResetProfiler()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_ResetProfiler_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execResetProfiler)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetProfiler();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function ResetProfiler *****************************

// ********** Begin Class UPCGPerformanceProfiler Function SetMetricThreshold **********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics
{
	struct PCGPerformanceProfiler_eventSetMetricThreshold_Parms
	{
		FString MetricName;
		float WarningThreshold;
		float ErrorThreshold;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Thresholds" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Thresholds and Alerts\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Thresholds and Alerts" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MetricName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WarningThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ErrorThreshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::NewProp_MetricName = { "MetricName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventSetMetricThreshold_Parms, MetricName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricName_MetaData), NewProp_MetricName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::NewProp_WarningThreshold = { "WarningThreshold", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventSetMetricThreshold_Parms, WarningThreshold), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::NewProp_ErrorThreshold = { "ErrorThreshold", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventSetMetricThreshold_Parms, ErrorThreshold), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::NewProp_MetricName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::NewProp_WarningThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::NewProp_ErrorThreshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "SetMetricThreshold", Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::PCGPerformanceProfiler_eventSetMetricThreshold_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::PCGPerformanceProfiler_eventSetMetricThreshold_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execSetMetricThreshold)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_MetricName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_WarningThreshold);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ErrorThreshold);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMetricThreshold(Z_Param_MetricName,Z_Param_WarningThreshold,Z_Param_ErrorThreshold);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function SetMetricThreshold ************************

// ********** Begin Class UPCGPerformanceProfiler Function SetOverlayPosition **********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition_Statics
{
	struct PCGPerformanceProfiler_eventSetOverlayPosition_Parms
	{
		FVector2D Position;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Visualization" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventSetOverlayPosition_Parms, Position), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition_Statics::NewProp_Position,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "SetOverlayPosition", Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition_Statics::PCGPerformanceProfiler_eventSetOverlayPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition_Statics::PCGPerformanceProfiler_eventSetOverlayPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execSetOverlayPosition)
{
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetOverlayPosition(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function SetOverlayPosition ************************

// ********** Begin Class UPCGPerformanceProfiler Function SetOverlayScale *************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale_Statics
{
	struct PCGPerformanceProfiler_eventSetOverlayScale_Parms
	{
		float Scale;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Visualization" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventSetOverlayScale_Parms, Scale), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale_Statics::NewProp_Scale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "SetOverlayScale", Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale_Statics::PCGPerformanceProfiler_eventSetOverlayScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale_Statics::PCGPerformanceProfiler_eventSetOverlayScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execSetOverlayScale)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Scale);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetOverlayScale(Z_Param_Scale);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function SetOverlayScale ***************************

// ********** Begin Class UPCGPerformanceProfiler Function SetSamplingRate *************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics
{
	struct PCGPerformanceProfiler_eventSetSamplingRate_Parms
	{
		EPCGProfilerSamplingRate Rate;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Control" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Rate_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Rate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::NewProp_Rate_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::NewProp_Rate = { "Rate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventSetSamplingRate_Parms, Rate), Z_Construct_UEnum_Aura_EPCGProfilerSamplingRate, METADATA_PARAMS(0, nullptr) }; // 1242914351
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::NewProp_Rate_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::NewProp_Rate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "SetSamplingRate", Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::PCGPerformanceProfiler_eventSetSamplingRate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::PCGPerformanceProfiler_eventSetSamplingRate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execSetSamplingRate)
{
	P_GET_ENUM(EPCGProfilerSamplingRate,Z_Param_Rate);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSamplingRate(EPCGProfilerSamplingRate(Z_Param_Rate));
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function SetSamplingRate ***************************

// ********** Begin Class UPCGPerformanceProfiler Function ShutdownProfiler ************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_ShutdownProfiler_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Lifecycle" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_ShutdownProfiler_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "ShutdownProfiler", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_ShutdownProfiler_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_ShutdownProfiler_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_ShutdownProfiler()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_ShutdownProfiler_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execShutdownProfiler)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShutdownProfiler();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function ShutdownProfiler **************************

// ********** Begin Class UPCGPerformanceProfiler Function StartBenchmark **************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark_Statics
{
	struct PCGPerformanceProfiler_eventStartBenchmark_Parms
	{
		FString BenchmarkName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Benchmark" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Benchmarking\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Benchmarking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BenchmarkName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BenchmarkName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark_Statics::NewProp_BenchmarkName = { "BenchmarkName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventStartBenchmark_Parms, BenchmarkName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BenchmarkName_MetaData), NewProp_BenchmarkName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark_Statics::NewProp_BenchmarkName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "StartBenchmark", Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark_Statics::PCGPerformanceProfiler_eventStartBenchmark_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark_Statics::PCGPerformanceProfiler_eventStartBenchmark_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execStartBenchmark)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BenchmarkName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartBenchmark(Z_Param_BenchmarkName);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function StartBenchmark ****************************

// ********** Begin Class UPCGPerformanceProfiler Function StartGPUProfiling ***********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_StartGPUProfiling_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler GPU" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// GPU Profiling\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU Profiling" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_StartGPUProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "StartGPUProfiling", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StartGPUProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_StartGPUProfiling_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_StartGPUProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_StartGPUProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execStartGPUProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartGPUProfiling();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function StartGPUProfiling *************************

// ********** Begin Class UPCGPerformanceProfiler Function StartMemoryProfiling ********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_StartMemoryProfiling_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Memory Profiling\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory Profiling" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_StartMemoryProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "StartMemoryProfiling", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StartMemoryProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_StartMemoryProfiling_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_StartMemoryProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_StartMemoryProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execStartMemoryProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartMemoryProfiling();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function StartMemoryProfiling **********************

// ********** Begin Class UPCGPerformanceProfiler Function StartNetworkProfiling *******************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_StartNetworkProfiling_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Network" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Network Profiling\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Network Profiling" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_StartNetworkProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "StartNetworkProfiling", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StartNetworkProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_StartNetworkProfiling_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_StartNetworkProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_StartNetworkProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execStartNetworkProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartNetworkProfiling();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function StartNetworkProfiling *********************

// ********** Begin Class UPCGPerformanceProfiler Function StartProfiling **************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_StartProfiling_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Profiling Control\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Profiling Control" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_StartProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "StartProfiling", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StartProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_StartProfiling_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_StartProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_StartProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execStartProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartProfiling();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function StartProfiling ****************************

// ********** Begin Class UPCGPerformanceProfiler Function StopGPUProfiling ************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_StopGPUProfiling_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler GPU" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_StopGPUProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "StopGPUProfiling", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StopGPUProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_StopGPUProfiling_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_StopGPUProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_StopGPUProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execStopGPUProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopGPUProfiling();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function StopGPUProfiling **************************

// ********** Begin Class UPCGPerformanceProfiler Function StopMemoryProfiling *********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_StopMemoryProfiling_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Memory" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_StopMemoryProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "StopMemoryProfiling", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StopMemoryProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_StopMemoryProfiling_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_StopMemoryProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_StopMemoryProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execStopMemoryProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopMemoryProfiling();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function StopMemoryProfiling ***********************

// ********** Begin Class UPCGPerformanceProfiler Function StopNetworkProfiling ********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_StopNetworkProfiling_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Network" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_StopNetworkProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "StopNetworkProfiling", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StopNetworkProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_StopNetworkProfiling_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_StopNetworkProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_StopNetworkProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execStopNetworkProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopNetworkProfiling();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function StopNetworkProfiling **********************

// ********** Begin Class UPCGPerformanceProfiler Function StopProfiling ***************************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_StopProfiling_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Control" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_StopProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "StopProfiling", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StopProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_StopProfiling_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_StopProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_StopProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execStopProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopProfiling();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function StopProfiling *****************************

// ********** Begin Class UPCGPerformanceProfiler Function StreamDataToEndpoint ********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint_Statics
{
	struct PCGPerformanceProfiler_eventStreamDataToEndpoint_Parms
	{
		FString Endpoint;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Network" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Endpoint_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Endpoint;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint_Statics::NewProp_Endpoint = { "Endpoint", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventStreamDataToEndpoint_Parms, Endpoint), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Endpoint_MetaData), NewProp_Endpoint_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint_Statics::NewProp_Endpoint,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "StreamDataToEndpoint", Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint_Statics::PCGPerformanceProfiler_eventStreamDataToEndpoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint_Statics::PCGPerformanceProfiler_eventStreamDataToEndpoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execStreamDataToEndpoint)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Endpoint);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StreamDataToEndpoint(Z_Param_Endpoint);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function StreamDataToEndpoint **********************

// ********** Begin Class UPCGPerformanceProfiler Function TakeMemorySnapshot **********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot_Statics
{
	struct PCGPerformanceProfiler_eventTakeMemorySnapshot_Parms
	{
		FString SnapshotName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Memory" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SnapshotName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SnapshotName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot_Statics::NewProp_SnapshotName = { "SnapshotName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventTakeMemorySnapshot_Parms, SnapshotName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SnapshotName_MetaData), NewProp_SnapshotName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot_Statics::NewProp_SnapshotName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "TakeMemorySnapshot", Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot_Statics::PCGPerformanceProfiler_eventTakeMemorySnapshot_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot_Statics::PCGPerformanceProfiler_eventTakeMemorySnapshot_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execTakeMemorySnapshot)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SnapshotName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TakeMemorySnapshot(Z_Param_SnapshotName);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function TakeMemorySnapshot ************************

// ********** Begin Class UPCGPerformanceProfiler Function ToggleOverlayVisibility *****************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_ToggleOverlayVisibility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Visualization" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_ToggleOverlayVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "ToggleOverlayVisibility", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_ToggleOverlayVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_ToggleOverlayVisibility_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_ToggleOverlayVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_ToggleOverlayVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execToggleOverlayVisibility)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ToggleOverlayVisibility();
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function ToggleOverlayVisibility *******************

// ********** Begin Class UPCGPerformanceProfiler Function UpdateCustomMetric **********************
struct Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics
{
	struct PCGPerformanceProfiler_eventUpdateCustomMetric_Parms
	{
		FString MetricName;
		float Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Profiler Metrics" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MetricName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::NewProp_MetricName = { "MetricName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventUpdateCustomMetric_Parms, MetricName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricName_MetaData), NewProp_MetricName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGPerformanceProfiler_eventUpdateCustomMetric_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::NewProp_MetricName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGPerformanceProfiler, nullptr, "UpdateCustomMetric", Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::PCGPerformanceProfiler_eventUpdateCustomMetric_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::PCGPerformanceProfiler_eventUpdateCustomMetric_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGPerformanceProfiler::execUpdateCustomMetric)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_MetricName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateCustomMetric(Z_Param_MetricName,Z_Param_Value);
	P_NATIVE_END;
}
// ********** End Class UPCGPerformanceProfiler Function UpdateCustomMetric ************************

// ********** Begin Class UPCGPerformanceProfiler **************************************************
void UPCGPerformanceProfiler::StaticRegisterNativesUPCGPerformanceProfiler()
{
	UClass* Class = UPCGPerformanceProfiler::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearAlert", &UPCGPerformanceProfiler::execClearAlert },
		{ "ClearAllAlerts", &UPCGPerformanceProfiler::execClearAllAlerts },
		{ "DrawProfilerOverlay", &UPCGPerformanceProfiler::execDrawProfilerOverlay },
		{ "EnableCategory", &UPCGPerformanceProfiler::execEnableCategory },
		{ "EndBenchmark", &UPCGPerformanceProfiler::execEndBenchmark },
		{ "ExportMetricsToCSV", &UPCGPerformanceProfiler::execExportMetricsToCSV },
		{ "ExportReportToFile", &UPCGPerformanceProfiler::execExportReportToFile },
		{ "GenerateReport", &UPCGPerformanceProfiler::execGenerateReport },
		{ "GetActiveAlerts", &UPCGPerformanceProfiler::execGetActiveAlerts },
		{ "GetAlertHistory", &UPCGPerformanceProfiler::execGetAlertHistory },
		{ "GetAllBenchmarkResults", &UPCGPerformanceProfiler::execGetAllBenchmarkResults },
		{ "GetAllMetrics", &UPCGPerformanceProfiler::execGetAllMetrics },
		{ "GetAverageFrameTime", &UPCGPerformanceProfiler::execGetAverageFrameTime },
		{ "GetAveragePerformance", &UPCGPerformanceProfiler::execGetAveragePerformance },
		{ "GetBenchmarkResult", &UPCGPerformanceProfiler::execGetBenchmarkResult },
		{ "GetCurrentPerformance", &UPCGPerformanceProfiler::execGetCurrentPerformance },
		{ "GetDrawCallCount", &UPCGPerformanceProfiler::execGetDrawCallCount },
		{ "GetGPUFrameTime", &UPCGPerformanceProfiler::execGetGPUFrameTime },
		{ "GetMemoryUsage", &UPCGPerformanceProfiler::execGetMemoryUsage },
		{ "GetMetric", &UPCGPerformanceProfiler::execGetMetric },
		{ "GetMetricsByCategory", &UPCGPerformanceProfiler::execGetMetricsByCategory },
		{ "GetOptimizationRecommendations", &UPCGPerformanceProfiler::execGetOptimizationRecommendations },
		{ "GetPeakPerformance", &UPCGPerformanceProfiler::execGetPeakPerformance },
		{ "GetPerformanceSummary", &UPCGPerformanceProfiler::execGetPerformanceSummary },
		{ "GetTriangleCount", &UPCGPerformanceProfiler::execGetTriangleCount },
		{ "HasActiveAlerts", &UPCGPerformanceProfiler::execHasActiveAlerts },
		{ "InitializeProfiler", &UPCGPerformanceProfiler::execInitializeProfiler },
		{ "IntegrateWithLumen", &UPCGPerformanceProfiler::execIntegrateWithLumen },
		{ "IntegrateWithNanite", &UPCGPerformanceProfiler::execIntegrateWithNanite },
		{ "IntegrateWithStreaming", &UPCGPerformanceProfiler::execIntegrateWithStreaming },
		{ "IntegrateWithWorldPartition", &UPCGPerformanceProfiler::execIntegrateWithWorldPartition },
		{ "IsProfilerActive", &UPCGPerformanceProfiler::execIsProfilerActive },
		{ "PauseProfiling", &UPCGPerformanceProfiler::execPauseProfiling },
		{ "RegisterCustomMetric", &UPCGPerformanceProfiler::execRegisterCustomMetric },
		{ "RemoveCustomMetric", &UPCGPerformanceProfiler::execRemoveCustomMetric },
		{ "ResetProfiler", &UPCGPerformanceProfiler::execResetProfiler },
		{ "SetMetricThreshold", &UPCGPerformanceProfiler::execSetMetricThreshold },
		{ "SetOverlayPosition", &UPCGPerformanceProfiler::execSetOverlayPosition },
		{ "SetOverlayScale", &UPCGPerformanceProfiler::execSetOverlayScale },
		{ "SetSamplingRate", &UPCGPerformanceProfiler::execSetSamplingRate },
		{ "ShutdownProfiler", &UPCGPerformanceProfiler::execShutdownProfiler },
		{ "StartBenchmark", &UPCGPerformanceProfiler::execStartBenchmark },
		{ "StartGPUProfiling", &UPCGPerformanceProfiler::execStartGPUProfiling },
		{ "StartMemoryProfiling", &UPCGPerformanceProfiler::execStartMemoryProfiling },
		{ "StartNetworkProfiling", &UPCGPerformanceProfiler::execStartNetworkProfiling },
		{ "StartProfiling", &UPCGPerformanceProfiler::execStartProfiling },
		{ "StopGPUProfiling", &UPCGPerformanceProfiler::execStopGPUProfiling },
		{ "StopMemoryProfiling", &UPCGPerformanceProfiler::execStopMemoryProfiling },
		{ "StopNetworkProfiling", &UPCGPerformanceProfiler::execStopNetworkProfiling },
		{ "StopProfiling", &UPCGPerformanceProfiler::execStopProfiling },
		{ "StreamDataToEndpoint", &UPCGPerformanceProfiler::execStreamDataToEndpoint },
		{ "TakeMemorySnapshot", &UPCGPerformanceProfiler::execTakeMemorySnapshot },
		{ "ToggleOverlayVisibility", &UPCGPerformanceProfiler::execToggleOverlayVisibility },
		{ "UpdateCustomMetric", &UPCGPerformanceProfiler::execUpdateCustomMetric },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPCGPerformanceProfiler;
UClass* UPCGPerformanceProfiler::GetPrivateStaticClass()
{
	using TClass = UPCGPerformanceProfiler;
	if (!Z_Registration_Info_UClass_UPCGPerformanceProfiler.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PCGPerformanceProfiler"),
			Z_Registration_Info_UClass_UPCGPerformanceProfiler.InnerSingleton,
			StaticRegisterNativesUPCGPerformanceProfiler,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPCGPerformanceProfiler.InnerSingleton;
}
UClass* Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister()
{
	return UPCGPerformanceProfiler::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPCGPerformanceProfiler_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * UPCGPerformanceProfiler - Advanced performance profiling system for PCG\n * Provides real-time monitoring, analysis, and optimization recommendations\n * Optimized for UE5.6 with modern profiling APIs and integration capabilities\n */" },
#endif
		{ "IncludePath", "UPCGPerformanceProfiler.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UPCGPerformanceProfiler - Advanced performance profiling system for PCG\nProvides real-time monitoring, analysis, and optimization recommendations\nOptimized for UE5.6 with modern profiling APIs and integration capabilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProfilerConfig_MetaData[] = {
		{ "Category", "Profiler Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowOverlay_MetaData[] = {
		{ "Category", "Display" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowGraphs_MetaData[] = {
		{ "Category", "Display" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoOptimization_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPerformanceAlert_MetaData[] = {
		{ "Category", "Profiler Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMetricUpdated_MetaData[] = {
		{ "Category", "Profiler Events" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnProfilerReportGenerated_MetaData[] = {
		{ "Category", "Profiler Events" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnThresholdExceeded_MetaData[] = {
		{ "Category", "Profiler Events" },
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredMetrics_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal data\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveAlerts_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlertHistory_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPerformance_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AveragePerformance_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeakPerformance_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPartitionManagerRef_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration references\n" },
#endif
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration references" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NaniteOptimizerRef_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LumenIntegratorRef_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingManagerRef_MetaData[] = {
		{ "ModuleRelativePath", "Public/UPCGPerformanceProfiler.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProfilerConfig;
	static void NewProp_bShowOverlay_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowOverlay;
	static void NewProp_bShowGraphs_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowGraphs;
	static void NewProp_bAutoOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoOptimization;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPerformanceAlert;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMetricUpdated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnProfilerReportGenerated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnThresholdExceeded;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredMetrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegisteredMetrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RegisteredMetrics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveAlerts_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveAlerts;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AlertHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AlertHistory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentPerformance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AveragePerformance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PeakPerformance;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_WorldPartitionManagerRef;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_NaniteOptimizerRef;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_LumenIntegratorRef;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_StreamingManagerRef;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAlert, "ClearAlert" }, // 2196909522
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_ClearAllAlerts, "ClearAllAlerts" }, // 3567977687
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_DrawProfilerOverlay, "DrawProfilerOverlay" }, // 1365151856
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_EnableCategory, "EnableCategory" }, // 2399639712
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_EndBenchmark, "EndBenchmark" }, // 2420731471
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_ExportMetricsToCSV, "ExportMetricsToCSV" }, // 1327772240
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_ExportReportToFile, "ExportReportToFile" }, // 4161287803
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GenerateReport, "GenerateReport" }, // 1964046190
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetActiveAlerts, "GetActiveAlerts" }, // 1185332139
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetAlertHistory, "GetAlertHistory" }, // 671150069
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllBenchmarkResults, "GetAllBenchmarkResults" }, // 193935463
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetAllMetrics, "GetAllMetrics" }, // 2213199426
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetAverageFrameTime, "GetAverageFrameTime" }, // 2414189772
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetAveragePerformance, "GetAveragePerformance" }, // 3108305000
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetBenchmarkResult, "GetBenchmarkResult" }, // 1465295489
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetCurrentPerformance, "GetCurrentPerformance" }, // 4257165738
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetDrawCallCount, "GetDrawCallCount" }, // 2852752379
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetGPUFrameTime, "GetGPUFrameTime" }, // 1457878552
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetMemoryUsage, "GetMemoryUsage" }, // 3471071621
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetric, "GetMetric" }, // 864243936
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetMetricsByCategory, "GetMetricsByCategory" }, // 2914717774
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetOptimizationRecommendations, "GetOptimizationRecommendations" }, // 1818852406
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetPeakPerformance, "GetPeakPerformance" }, // 1228277804
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetPerformanceSummary, "GetPerformanceSummary" }, // 2713402585
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_GetTriangleCount, "GetTriangleCount" }, // 3743825841
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_HasActiveAlerts, "HasActiveAlerts" }, // 1647813983
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_InitializeProfiler, "InitializeProfiler" }, // 358849722
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithLumen, "IntegrateWithLumen" }, // 2695474539
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithNanite, "IntegrateWithNanite" }, // 3577902296
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithStreaming, "IntegrateWithStreaming" }, // 1317192466
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_IntegrateWithWorldPartition, "IntegrateWithWorldPartition" }, // 478052352
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_IsProfilerActive, "IsProfilerActive" }, // 1423566707
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_PauseProfiling, "PauseProfiling" }, // 3896457340
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_RegisterCustomMetric, "RegisterCustomMetric" }, // 2382748533
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_RemoveCustomMetric, "RemoveCustomMetric" }, // 3468217843
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_ResetProfiler, "ResetProfiler" }, // 75968636
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_SetMetricThreshold, "SetMetricThreshold" }, // 566651210
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayPosition, "SetOverlayPosition" }, // 2019706445
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_SetOverlayScale, "SetOverlayScale" }, // 1539381736
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_SetSamplingRate, "SetSamplingRate" }, // 789491924
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_ShutdownProfiler, "ShutdownProfiler" }, // 2526279884
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_StartBenchmark, "StartBenchmark" }, // 3878274018
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_StartGPUProfiling, "StartGPUProfiling" }, // 524277000
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_StartMemoryProfiling, "StartMemoryProfiling" }, // 4269686632
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_StartNetworkProfiling, "StartNetworkProfiling" }, // 534449587
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_StartProfiling, "StartProfiling" }, // 295299168
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_StopGPUProfiling, "StopGPUProfiling" }, // 1459697205
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_StopMemoryProfiling, "StopMemoryProfiling" }, // 3676383668
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_StopNetworkProfiling, "StopNetworkProfiling" }, // 2587344359
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_StopProfiling, "StopProfiling" }, // 194290012
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_StreamDataToEndpoint, "StreamDataToEndpoint" }, // 991836171
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_TakeMemorySnapshot, "TakeMemorySnapshot" }, // 3709117375
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_ToggleOverlayVisibility, "ToggleOverlayVisibility" }, // 1028507927
		{ &Z_Construct_UFunction_UPCGPerformanceProfiler_UpdateCustomMetric, "UpdateCustomMetric" }, // 4153003838
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPCGPerformanceProfiler>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_ProfilerConfig = { "ProfilerConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, ProfilerConfig), Z_Construct_UScriptStruct_FPCGProfilerConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProfilerConfig_MetaData), NewProp_ProfilerConfig_MetaData) }; // 4282767612
void Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_bShowOverlay_SetBit(void* Obj)
{
	((UPCGPerformanceProfiler*)Obj)->bShowOverlay = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_bShowOverlay = { "bShowOverlay", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPCGPerformanceProfiler), &Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_bShowOverlay_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowOverlay_MetaData), NewProp_bShowOverlay_MetaData) };
void Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_bShowGraphs_SetBit(void* Obj)
{
	((UPCGPerformanceProfiler*)Obj)->bShowGraphs = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_bShowGraphs = { "bShowGraphs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPCGPerformanceProfiler), &Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_bShowGraphs_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowGraphs_MetaData), NewProp_bShowGraphs_MetaData) };
void Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_bAutoOptimization_SetBit(void* Obj)
{
	((UPCGPerformanceProfiler*)Obj)->bAutoOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_bAutoOptimization = { "bAutoOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPCGPerformanceProfiler), &Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_bAutoOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoOptimization_MetaData), NewProp_bAutoOptimization_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_OnPerformanceAlert = { "OnPerformanceAlert", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, OnPerformanceAlert), Z_Construct_UDelegateFunction_Aura_OnPerformanceAlert__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPerformanceAlert_MetaData), NewProp_OnPerformanceAlert_MetaData) }; // 313731242
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_OnMetricUpdated = { "OnMetricUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, OnMetricUpdated), Z_Construct_UDelegateFunction_Aura_OnMetricUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMetricUpdated_MetaData), NewProp_OnMetricUpdated_MetaData) }; // 816247630
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_OnProfilerReportGenerated = { "OnProfilerReportGenerated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, OnProfilerReportGenerated), Z_Construct_UDelegateFunction_Aura_OnProfilerReportGenerated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnProfilerReportGenerated_MetaData), NewProp_OnProfilerReportGenerated_MetaData) }; // 3847259722
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_OnThresholdExceeded = { "OnThresholdExceeded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, OnThresholdExceeded), Z_Construct_UDelegateFunction_Aura_OnThresholdExceeded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnThresholdExceeded_MetaData), NewProp_OnThresholdExceeded_MetaData) }; // 330891502
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_RegisteredMetrics_ValueProp = { "RegisteredMetrics", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPCGPerformanceMetric, METADATA_PARAMS(0, nullptr) }; // 140184097
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_RegisteredMetrics_Key_KeyProp = { "RegisteredMetrics_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_RegisteredMetrics = { "RegisteredMetrics", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, RegisteredMetrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredMetrics_MetaData), NewProp_RegisteredMetrics_MetaData) }; // 140184097
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_ActiveAlerts_Inner = { "ActiveAlerts", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGProfilerAlert, METADATA_PARAMS(0, nullptr) }; // 2569321676
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_ActiveAlerts = { "ActiveAlerts", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, ActiveAlerts), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveAlerts_MetaData), NewProp_ActiveAlerts_MetaData) }; // 2569321676
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_AlertHistory_Inner = { "AlertHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPCGProfilerAlert, METADATA_PARAMS(0, nullptr) }; // 2569321676
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_AlertHistory = { "AlertHistory", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, AlertHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlertHistory_MetaData), NewProp_AlertHistory_MetaData) }; // 2569321676
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_CurrentPerformance = { "CurrentPerformance", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, CurrentPerformance), Z_Construct_UScriptStruct_FPCGSystemPerformance, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPerformance_MetaData), NewProp_CurrentPerformance_MetaData) }; // 2597856837
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_AveragePerformance = { "AveragePerformance", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, AveragePerformance), Z_Construct_UScriptStruct_FPCGSystemPerformance, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AveragePerformance_MetaData), NewProp_AveragePerformance_MetaData) }; // 2597856837
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_PeakPerformance = { "PeakPerformance", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, PeakPerformance), Z_Construct_UScriptStruct_FPCGSystemPerformance, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeakPerformance_MetaData), NewProp_PeakPerformance_MetaData) }; // 2597856837
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_WorldPartitionManagerRef = { "WorldPartitionManagerRef", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, WorldPartitionManagerRef), Z_Construct_UClass_APCGWorldPartitionManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPartitionManagerRef_MetaData), NewProp_WorldPartitionManagerRef_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_NaniteOptimizerRef = { "NaniteOptimizerRef", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, NaniteOptimizerRef), Z_Construct_UClass_APCGNaniteOptimizer_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NaniteOptimizerRef_MetaData), NewProp_NaniteOptimizerRef_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_LumenIntegratorRef = { "LumenIntegratorRef", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, LumenIntegratorRef), Z_Construct_UClass_APCGLumenIntegrator_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LumenIntegratorRef_MetaData), NewProp_LumenIntegratorRef_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_StreamingManagerRef = { "StreamingManagerRef", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGPerformanceProfiler, StreamingManagerRef), Z_Construct_UClass_APCGStreamingManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingManagerRef_MetaData), NewProp_StreamingManagerRef_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPCGPerformanceProfiler_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_ProfilerConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_bShowOverlay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_bShowGraphs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_bAutoOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_OnPerformanceAlert,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_OnMetricUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_OnProfilerReportGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_OnThresholdExceeded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_RegisteredMetrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_RegisteredMetrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_RegisteredMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_ActiveAlerts_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_ActiveAlerts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_AlertHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_AlertHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_CurrentPerformance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_AveragePerformance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_PeakPerformance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_WorldPartitionManagerRef,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_NaniteOptimizerRef,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_LumenIntegratorRef,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGPerformanceProfiler_Statics::NewProp_StreamingManagerRef,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPCGPerformanceProfiler_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPCGPerformanceProfiler_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPCGPerformanceProfiler_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPCGPerformanceProfiler_Statics::ClassParams = {
	&UPCGPerformanceProfiler::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UPCGPerformanceProfiler_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UPCGPerformanceProfiler_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPCGPerformanceProfiler_Statics::Class_MetaDataParams), Z_Construct_UClass_UPCGPerformanceProfiler_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPCGPerformanceProfiler()
{
	if (!Z_Registration_Info_UClass_UPCGPerformanceProfiler.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPCGPerformanceProfiler.OuterSingleton, Z_Construct_UClass_UPCGPerformanceProfiler_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPCGPerformanceProfiler.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPCGPerformanceProfiler);
UPCGPerformanceProfiler::~UPCGPerformanceProfiler() {}
// ********** End Class UPCGPerformanceProfiler ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPCGProfilerCategory_StaticEnum, TEXT("EPCGProfilerCategory"), &Z_Registration_Info_UEnum_EPCGProfilerCategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2695066029U) },
		{ EPCGProfilerSeverity_StaticEnum, TEXT("EPCGProfilerSeverity"), &Z_Registration_Info_UEnum_EPCGProfilerSeverity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 813734881U) },
		{ EPCGProfilerDisplayMode_StaticEnum, TEXT("EPCGProfilerDisplayMode"), &Z_Registration_Info_UEnum_EPCGProfilerDisplayMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1174834543U) },
		{ EPCGProfilerSamplingRate_StaticEnum, TEXT("EPCGProfilerSamplingRate"), &Z_Registration_Info_UEnum_EPCGProfilerSamplingRate, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1242914351U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPCGProfilerConfig::StaticStruct, Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics::NewStructOps, TEXT("PCGProfilerConfig"), &Z_Registration_Info_UScriptStruct_FPCGProfilerConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGProfilerConfig), 4282767612U) },
		{ FPCGPerformanceMetric::StaticStruct, Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics::NewStructOps, TEXT("PCGPerformanceMetric"), &Z_Registration_Info_UScriptStruct_FPCGPerformanceMetric, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGPerformanceMetric), 140184097U) },
		{ FPCGSystemPerformance::StaticStruct, Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics::NewStructOps, TEXT("PCGSystemPerformance"), &Z_Registration_Info_UScriptStruct_FPCGSystemPerformance, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGSystemPerformance), 2597856837U) },
		{ FPCGProfilerAlert::StaticStruct, Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics::NewStructOps, TEXT("PCGProfilerAlert"), &Z_Registration_Info_UScriptStruct_FPCGProfilerAlert, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGProfilerAlert), 2569321676U) },
		{ FPCGProfilerReport::StaticStruct, Z_Construct_UScriptStruct_FPCGProfilerReport_Statics::NewStructOps, TEXT("PCGProfilerReport"), &Z_Registration_Info_UScriptStruct_FPCGProfilerReport, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGProfilerReport), 2505381472U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPCGPerformanceProfiler, UPCGPerformanceProfiler::StaticClass, TEXT("UPCGPerformanceProfiler"), &Z_Registration_Info_UClass_UPCGPerformanceProfiler, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPCGPerformanceProfiler), 151354912U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h__Script_Aura_3391731443(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
