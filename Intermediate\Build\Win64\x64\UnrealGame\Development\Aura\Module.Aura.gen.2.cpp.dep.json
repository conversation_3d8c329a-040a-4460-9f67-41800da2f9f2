{"Version": "1.2", "Data": {"Source": "c:\\aura\\intermediate\\build\\win64\\x64\\unrealgame\\development\\aura\\module.aura.gen.2.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\intermediate\\build\\win64\\x64\\aura\\development\\engine\\sharedpch.engine.project.rtti.exceptions.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\intermediate\\build\\win64\\x64\\unrealgame\\development\\aura\\definitions.aura.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\ageometricvalidator.gen.cpp", "c:\\aura\\source\\aura\\public\\ageometricvalidator.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\ageometricvalidator.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\alanemanager.gen.cpp", "c:\\aura\\source\\aura\\public\\alanemanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\splinecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\curves\\spline.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\spline.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\splinecomponent.generated.h", "c:\\aura\\intermediate\\build\\win64\\unrealgame\\inc\\aura\\uht\\alanemanager.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}