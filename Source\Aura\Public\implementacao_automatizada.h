#pragma once

#include "CoreMinimal.h"
#include "Components/StaticMeshComponent.h"
#include "GameFramework/Actor.h"
#include "Math/UnrealMathUtility.h"
#include "implementacao_automatizada.generated.h"

// ========== ENUMS GLOBAIS ==========

UENUM(BlueprintType)
enum class ETeam : uint8
{
    Azul UMETA(DisplayName = "Azul"),
    Vermelho UMETA(DisplayName = "Vermelho"),
    Neutro UMETA(DisplayName = "Neutro")
};

UENUM(BlueprintType)
enum class ETowerType : uint8
{
    Externa UMETA(DisplayName = "Torre Externa"),
    Interna UMETA(DisplayName = "Torre Interna"),
    Inibidor UMETA(DisplayName = "Torre Inibidor"),
    Nexus UMETA(DisplayName = "Torre Nexus")
};

// ========== ESTRUTURAS DE DADOS PRECISAS (ESCOPO GLOBAL) ==========

USTRUCT(BlueprintType)
struct AURA_API FLaneEspecificacao
{
    GENERATED_BODY()
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float CoeficienteAngular;  // m da função y = mx + b
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float CoeficienteLinear;   // b da função y = mx + b
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Largura;             // Largura total da lane
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FVector PontoInicial;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FVector PontoFinal;
    
    FLaneEspecificacao()
    {
        CoeficienteAngular = 0.0f;
        CoeficienteLinear = 0.0f;
        Largura = 600.0f;
        PontoInicial = FVector::ZeroVector;
        PontoFinal = FVector::ZeroVector;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FCovilEspecificacao
{
    GENERATED_BODY()
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FVector Centro;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString TipoGeometria;  // "Hexagono", "Elipse", "Circulo"
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Parametro1;       // Raio para círculo/hexágono, semi-eixo A para elipse
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Parametro2;       // Não usado para círculo/hexágono, semi-eixo B para elipse
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float RotacaoGraus;
    
    FCovilEspecificacao()
    {
        Centro = FVector::ZeroVector;
        TipoGeometria = "Circulo";
        Parametro1 = 500.0f;
        Parametro2 = 500.0f;
        RotacaoGraus = 0.0f;
    }
};

/**
 * SISTEMA DE IMPLEMENTAÇÃO AUTOMATIZADA - GEOMETRIA MATEMÁTICA PRECISA
 * Garante 100% de precisão na criação do mapa Aura
 * Todas as coordenadas, ângulos e formas são calculadas matematicamente
 */

UCLASS(BlueprintType, Blueprintable)
class AURA_API AImplementacaoAutomatizada : public AActor
{
    GENERATED_BODY()

public:
    AImplementacaoAutomatizada();

    // ========== CONSTANTES MATEMÁTICAS PRECISAS ==========
    
    // Dimensões base do mapa (UE5.6: 1 UU = 1 cm)
    static constexpr float MAPA_LARGURA = 16000.0f;  // 160m
    static constexpr float MAPA_ALTURA = 16000.0f;   // 160m
    static constexpr float MAPA_Z_MAX = 800.0f;      // 8m altura
    static constexpr float MAPA_CENTRO_X = 0.0f;
    static constexpr float MAPA_CENTRO_Y = 0.0f;
    
    // Constantes trigonométricas precisas
    static constexpr float LANE_ANGULO_30_GRAUS = 0.57735026919f;  // tan(30°)
    // Constantes matemáticas de alta precisão
    static constexpr float PI_PRECISO = 3.14159265359f;
    static constexpr float SQRT_3 = 1.73205080757f;
    
    // ========== FUNÇÕES DE VALIDAÇÃO GEOMÉTRICA ==========
    
    UFUNCTION(BlueprintCallable, Category = "Validação")
    static bool ValidarPontoNaLane(float X, float Y, int32 LaneIndex);
    
    UFUNCTION(BlueprintCallable, Category = "Validação")
    static bool ValidarPontoNoRio(float X, float Y);
    
    UFUNCTION(BlueprintCallable, Category = "Validação")
    static bool ValidarPontoEmHexagono(float X, float Y, FVector2D Centro, float Raio);
    
    UFUNCTION(BlueprintCallable, Category = "Validação")
    static bool ValidarPontoEmElipse(float X, float Y, FVector2D Centro, float SemiEixoA, float SemiEixoB);
    
    // ========== FUNÇÕES DE CÁLCULO AUTOMÁTICO ==========
    
    UFUNCTION(BlueprintCallable, Category = "Cálculos")
    static FVector CalcularPosicaoTorre(int32 LaneIndex, int32 TorreIndex, bool IsTeamAzul);
    
    UFUNCTION(BlueprintCallable, Category = "Cálculos")
    static TArray<FVector> CalcularVerticesHexagono(FVector2D Centro, float Raio, float RotacaoGraus = 0.0f);
    
    UFUNCTION(BlueprintCallable, Category = "Cálculos")
    static TArray<FVector> CalcularPontosRioSenoidal(int32 NumPontos = 100);
    
    UFUNCTION(BlueprintCallable, Category = "Cálculos")
    static FVector CalcularPosicaoMinion(int32 LaneIndex, float DistanciaPercorrida, bool IsTeamAzul);
    
    // ========== SISTEMA DE CRIAÇÃO AUTOMÁTICA ==========
    
    UFUNCTION(BlueprintCallable, Category = "Criação Automática")
    void CriarMapaCompleto();
    
    UFUNCTION(BlueprintCallable, Category = "Criação Automática")
    void CriarLanes();
    
    UFUNCTION(BlueprintCallable, Category = "Criação Automática")
    void CriarRioPrismal();
    
    UFUNCTION(BlueprintCallable, Category = "Criação Automática")
    void CriarBases();
    
    UFUNCTION(BlueprintCallable, Category = "Criação Automática")
    void CriarCovils();
    
    UFUNCTION(BlueprintCallable, Category = "Criação Automática")
    void CriarTorres();
    
    UFUNCTION(BlueprintCallable, Category = "Criação Automática")
    void CriarParedes();
    
    UFUNCTION(BlueprintCallable, Category = "Criação Automática")
    void CriarIlhaCentral();
    
    // ========== ESTRUTURAS DE DADOS PRECISAS (movidas para escopo global) ==========
    
    // ========== ARRAYS DE ESPECIFICAÇÕES ==========
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Especificações")
    TArray<FLaneEspecificacao> LanesEspecificacoes;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Especificações")
    TArray<FCovilEspecificacao> CovilsEspecificacoes;
    
    // ========== SISTEMA DE VERIFICAÇÃO CONTÍNUA ==========
    
    UFUNCTION(BlueprintCallable, Category = "Verificação")
    bool VerificarPrecisaoCompleta();
    
    UFUNCTION(BlueprintCallable, Category = "Verificação")
    FString GerarRelatorioValidacao();
    
    UFUNCTION(BlueprintCallable, Category = "Verificação")
    void CorrigirDesviosPrecisao();
    
protected:
    virtual void BeginPlay() override;
    
private:
    // ========== FUNÇÕES AUXILIARES MATEMÁTICAS ==========
    
    static float CalcularDistanciaEuclidiana(FVector2D P1, FVector2D P2);
    static float CalcularAnguloEntrePontos(FVector2D P1, FVector2D P2);
    static FVector2D CalcularPontoNaLinha(FVector2D Inicio, FVector2D Fim, float Parametro);
    static bool PontoEstaEmPoligono(FVector2D Ponto, const TArray<FVector2D>& Vertices);
    
    // ========== TOLERÂNCIAS DE PRECISÃO ==========
    
    static constexpr float TOLERANCIA_COORDENADA = 1.0f;    // ±1 UU
    static constexpr float TOLERANCIA_ANGULO = 0.1f;        // ±0.1°
    static constexpr float TOLERANCIA_AREA = 0.001f;        // ±0.1%
    static constexpr float TOLERANCIA_DISTANCIA = 0.5f;     // ±0.5 UU
    
    // ========== COMPONENTES DE MESH ==========
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Componentes", meta = (AllowPrivateAccess = "true"))
    UStaticMeshComponent* MeshComponent;
    
    // ========== ARRAYS DE OBJETOS CRIADOS ==========
    
    UPROPERTY()
    TArray<AActor*> TorresCreated;
    
    UPROPERTY()
    TArray<AActor*> ParedesCreated;
    
    UPROPERTY()
    TArray<AActor*> CovilsCreated;
};

// ========== MACROS PARA CÁLCULOS PRECISOS ==========

// Constante global para uso em macros
static constexpr float PI_PRECISO_GLOBAL = 3.14159265359f;

#define CALCULAR_Y_LANE_SUPERIOR(x) (-0.57735026919f * (x) + 6928.0f)
#define CALCULAR_Y_LANE_INFERIOR(x) (0.57735026919f * (x) - 6928.0f)
#define CALCULAR_Y_RIO_SENOIDAL(x) (200.0f * FMath::Sin(PI_PRECISO_GLOBAL * (x) / 4800.0f))
#define CALCULAR_LARGURA_RIO(x) (1200.0f + 200.0f * FMath::Abs(FMath::Sin(PI_PRECISO_GLOBAL * (x) / 2400.0f)))

// ========== ENUMS PARA TIPOS ==========

UENUM(BlueprintType)
enum class EImplementacaoLaneType : uint8
{
    Superior    UMETA(DisplayName = "Lane Superior"),
    Central     UMETA(DisplayName = "Lane Central"),
    Inferior    UMETA(DisplayName = "Lane Inferior")
};

UENUM(BlueprintType)
enum class ECovilType : uint8
{
    DragaoPrismal       UMETA(DisplayName = "Dragão Prismal"),
    BaraoAuracron      UMETA(DisplayName = "Barão Auracron"),
    SentinelaCristalina UMETA(DisplayName = "Sentinela Cristalina"),
    GuardiaoPortal     UMETA(DisplayName = "Guardião do Portal")
};

// ETeam enum já definido em ALaneManager.h - removido para evitar conflito