/NOLOGO
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/DEF
/NAME:"UnrealEditor-Aura.dll"
/IGNORE:4221
/NODEFAULTLIB
"C:/Aura/Intermediate/Build/Win64/x64/AuraEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.RTTI.ValApi.ValExpApi.Cpp20.h.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.1.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.2.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.3.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.4.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.5.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.6.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.7.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.8.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.9.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.10.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.11.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Aura.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/ABaronAuracronManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/ADragonPrismalManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/AGeometricValidator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/ALaneManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/AMapManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/AMinionWaveManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/APCGCacheManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/APCGChaosIntegrator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/APCGLumenIntegrator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/APCGNaniteOptimizer.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/APCGStreamingManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/APCGWorldPartitionManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/AProceduralMapGenerator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/ARiverPrismalManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/AWallCollisionManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/implementacao_automatizada.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/testes_precisao_geometrica.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/UPCGPerformanceProfiler.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/UPCGQualityValidator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/UPCGVersionManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/PerModuleInline.gen.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Default.rc2.res"
/OUT:"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/UnrealEditor-Aura.lib"