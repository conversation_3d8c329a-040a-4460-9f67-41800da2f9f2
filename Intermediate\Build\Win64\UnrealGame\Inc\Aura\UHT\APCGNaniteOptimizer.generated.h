// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "APCGNaniteOptimizer.h"

#ifdef AURA_APCGNaniteOptimizer_generated_h
#error "APCGNaniteOptimizer.generated.h already included, missing '#pragma once' in APCGNaniteOptimizer.h"
#endif
#define AURA_APCGNaniteOptimizer_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class APCGWorldPartitionManager;
class AProceduralMapGenerator;
class UPCGComponent;
class UStaticMesh;
class UStaticMeshComponent;
enum class EPCGNaniteLODStrategy : uint8;
struct FPCGNaniteInstanceData;
struct FPCGNaniteMeshData;
struct FPCGNaniteOptimizationConfig;
struct FPCGNanitePerformanceStats;

// ********** Begin ScriptStruct FPCGNaniteOptimizationConfig **************************************
#define FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h_80_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGNaniteOptimizationConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGNaniteOptimizationConfig;
// ********** End ScriptStruct FPCGNaniteOptimizationConfig ****************************************

// ********** Begin ScriptStruct FPCGNaniteMeshData ************************************************
#define FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h_155_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGNaniteMeshData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGNaniteMeshData;
// ********** End ScriptStruct FPCGNaniteMeshData **************************************************

// ********** Begin ScriptStruct FPCGNaniteInstanceData ********************************************
#define FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h_212_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGNaniteInstanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGNaniteInstanceData;
// ********** End ScriptStruct FPCGNaniteInstanceData **********************************************

// ********** Begin ScriptStruct FPCGNanitePerformanceStats ****************************************
#define FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h_255_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGNanitePerformanceStats_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGNanitePerformanceStats;
// ********** End ScriptStruct FPCGNanitePerformanceStats ******************************************

// ********** Begin Class APCGNaniteOptimizer ******************************************************
#define FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h_332_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnPerformanceThresholdExceeded); \
	DECLARE_FUNCTION(execOnNaniteStreamingUpdate); \
	DECLARE_FUNCTION(execOnMeshConversionComplete); \
	DECLARE_FUNCTION(execExportNaniteOptimizationReport); \
	DECLARE_FUNCTION(execGetNaniteSystemInfo); \
	DECLARE_FUNCTION(execIsMeshNaniteEnabled); \
	DECLARE_FUNCTION(execIsNaniteSupported); \
	DECLARE_FUNCTION(execProcessPCGGeneratedMeshes); \
	DECLARE_FUNCTION(execIntegrateWithPCGComponent); \
	DECLARE_FUNCTION(execSetWorldPartitionManager); \
	DECLARE_FUNCTION(execSetProceduralMapGenerator); \
	DECLARE_FUNCTION(execUnloadDistantNaniteData); \
	DECLARE_FUNCTION(execStreamNaniteData); \
	DECLARE_FUNCTION(execOptimizeMemoryUsage); \
	DECLARE_FUNCTION(execSetMemoryBudget); \
	DECLARE_FUNCTION(execGetCompressionEfficiency); \
	DECLARE_FUNCTION(execGetGPUMemoryUsage); \
	DECLARE_FUNCTION(execEnablePerformanceMonitoring); \
	DECLARE_FUNCTION(execResetPerformanceStats); \
	DECLARE_FUNCTION(execGetPerformanceStats); \
	DECLARE_FUNCTION(execGetVisibleInstances); \
	DECLARE_FUNCTION(execUpdateVisibilityFromViewpoint); \
	DECLARE_FUNCTION(execUpdateInstanceVisibility); \
	DECLARE_FUNCTION(execSetCullingParameters); \
	DECLARE_FUNCTION(execCalculateOptimalLODBias); \
	DECLARE_FUNCTION(execSetInstanceLODBias); \
	DECLARE_FUNCTION(execUpdateLODParameters); \
	DECLARE_FUNCTION(execSetLODStrategy); \
	DECLARE_FUNCTION(execGetNaniteInstanceCount); \
	DECLARE_FUNCTION(execGetNaniteInstances); \
	DECLARE_FUNCTION(execClearAllNaniteInstances); \
	DECLARE_FUNCTION(execUnregisterNaniteInstance); \
	DECLARE_FUNCTION(execRegisterNaniteInstance); \
	DECLARE_FUNCTION(execOptimizeMeshForNanite); \
	DECLARE_FUNCTION(execValidateMeshForNanite); \
	DECLARE_FUNCTION(execBatchConvertMeshesToNanite); \
	DECLARE_FUNCTION(execConvertMeshToNanite); \
	DECLARE_FUNCTION(execGetOptimizationConfig); \
	DECLARE_FUNCTION(execSetOptimizationConfig); \
	DECLARE_FUNCTION(execShutdownNaniteOptimizer); \
	DECLARE_FUNCTION(execInitializeNaniteOptimizer);


AURA_API UClass* Z_Construct_UClass_APCGNaniteOptimizer_NoRegister();

#define FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h_332_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAPCGNaniteOptimizer(); \
	friend struct Z_Construct_UClass_APCGNaniteOptimizer_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_APCGNaniteOptimizer_NoRegister(); \
public: \
	DECLARE_CLASS2(APCGNaniteOptimizer, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_APCGNaniteOptimizer_NoRegister) \
	DECLARE_SERIALIZER(APCGNaniteOptimizer)


#define FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h_332_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	APCGNaniteOptimizer(APCGNaniteOptimizer&&) = delete; \
	APCGNaniteOptimizer(const APCGNaniteOptimizer&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, APCGNaniteOptimizer); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(APCGNaniteOptimizer); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(APCGNaniteOptimizer) \
	NO_API virtual ~APCGNaniteOptimizer();


#define FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h_329_PROLOG
#define FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h_332_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h_332_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h_332_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h_332_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class APCGNaniteOptimizer;

// ********** End Class APCGNaniteOptimizer ********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_APCGNaniteOptimizer_h

// ********** Begin Enum EPCGNaniteOptimizationLevel ***********************************************
#define FOREACH_ENUM_EPCGNANITEOPTIMIZATIONLEVEL(op) \
	op(EPCGNaniteOptimizationLevel::Disabled) \
	op(EPCGNaniteOptimizationLevel::Conservative) \
	op(EPCGNaniteOptimizationLevel::Balanced) \
	op(EPCGNaniteOptimizationLevel::Aggressive) \
	op(EPCGNaniteOptimizationLevel::Maximum) 

enum class EPCGNaniteOptimizationLevel : uint8;
template<> struct TIsUEnumClass<EPCGNaniteOptimizationLevel> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGNaniteOptimizationLevel>();
// ********** End Enum EPCGNaniteOptimizationLevel *************************************************

// ********** Begin Enum EPCGNaniteLODStrategy *****************************************************
#define FOREACH_ENUM_EPCGNANITELODSTRATEGY(op) \
	op(EPCGNaniteLODStrategy::Automatic) \
	op(EPCGNaniteLODStrategy::DistanceBased) \
	op(EPCGNaniteLODStrategy::ScreenSize) \
	op(EPCGNaniteLODStrategy::Hybrid) \
	op(EPCGNaniteLODStrategy::Custom) 

enum class EPCGNaniteLODStrategy : uint8;
template<> struct TIsUEnumClass<EPCGNaniteLODStrategy> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGNaniteLODStrategy>();
// ********** End Enum EPCGNaniteLODStrategy *******************************************************

// ********** Begin Enum EPCGNaniteClusteringMode **************************************************
#define FOREACH_ENUM_EPCGNANITECLUSTERINGMODE(op) \
	op(EPCGNaniteClusteringMode::Default) \
	op(EPCGNaniteClusteringMode::Spatial) \
	op(EPCGNaniteClusteringMode::Material) \
	op(EPCGNaniteClusteringMode::Performance) \
	op(EPCGNaniteClusteringMode::Quality) 

enum class EPCGNaniteClusteringMode : uint8;
template<> struct TIsUEnumClass<EPCGNaniteClusteringMode> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGNaniteClusteringMode>();
// ********** End Enum EPCGNaniteClusteringMode ****************************************************

// ********** Begin Enum EPCGNaniteCompressionLevel ************************************************
#define FOREACH_ENUM_EPCGNANITECOMPRESSIONLEVEL(op) \
	op(EPCGNaniteCompressionLevel::None) \
	op(EPCGNaniteCompressionLevel::Low) \
	op(EPCGNaniteCompressionLevel::Medium) \
	op(EPCGNaniteCompressionLevel::High) \
	op(EPCGNaniteCompressionLevel::Maximum) 

enum class EPCGNaniteCompressionLevel : uint8;
template<> struct TIsUEnumClass<EPCGNaniteCompressionLevel> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGNaniteCompressionLevel>();
// ********** End Enum EPCGNaniteCompressionLevel **************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
