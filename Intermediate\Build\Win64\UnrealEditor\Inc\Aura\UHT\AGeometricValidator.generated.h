// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AGeometricValidator.h"

#ifdef AURA_AGeometricValidator_generated_h
#error "AGeometricValidator.generated.h already included, missing '#pragma once' in AGeometricValidator.h"
#endif
#define AURA_AGeometricValidator_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
enum class EValidationSeverity : uint8;
enum class EValidationType : uint8;
struct FGeometricShape;
struct FValidationConfig;
struct FValidationResult;

// ********** Begin ScriptStruct FValidationResult *************************************************
#define FID_Aura_Source_Aura_Public_AGeometricValidator_h_80_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FValidationResult_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FValidationResult;
// ********** End ScriptStruct FValidationResult ***************************************************

// ********** Begin ScriptStruct FGeometricShape ***************************************************
#define FID_Aura_Source_Aura_Public_AGeometricValidator_h_130_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FGeometricShape_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FGeometricShape;
// ********** End ScriptStruct FGeometricShape *****************************************************

// ********** Begin ScriptStruct FValidationConfig *************************************************
#define FID_Aura_Source_Aura_Public_AGeometricValidator_h_172_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FValidationConfig_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FValidationConfig;
// ********** End ScriptStruct FValidationConfig ***************************************************

// ********** Begin Delegate FOnValidationResult ***************************************************
#define FID_Aura_Source_Aura_Public_AGeometricValidator_h_220_DELEGATE \
AURA_API void FOnValidationResult_DelegateWrapper(const FMulticastScriptDelegate& OnValidationResult, FValidationResult const& Result);


// ********** End Delegate FOnValidationResult *****************************************************

// ********** Begin Delegate FOnValidationError ****************************************************
#define FID_Aura_Source_Aura_Public_AGeometricValidator_h_221_DELEGATE \
AURA_API void FOnValidationError_DelegateWrapper(const FMulticastScriptDelegate& OnValidationError, const FString& ErrorMessage, EValidationSeverity Severity);


// ********** End Delegate FOnValidationError ******************************************************

// ********** Begin Delegate FOnValidationComplete *************************************************
#define FID_Aura_Source_Aura_Public_AGeometricValidator_h_222_DELEGATE \
AURA_API void FOnValidationComplete_DelegateWrapper(const FMulticastScriptDelegate& OnValidationComplete, TArray<FValidationResult> const& Results);


// ********** End Delegate FOnValidationComplete ***************************************************

// ********** Begin Class AGeometricValidator ******************************************************
#define FID_Aura_Source_Aura_Public_AGeometricValidator_h_242_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetValidationStatistics); \
	DECLARE_FUNCTION(execSetDebugVisualization); \
	DECLARE_FUNCTION(execResetValidator); \
	DECLARE_FUNCTION(execGetValidationSummary); \
	DECLARE_FUNCTION(execRunPerformanceBenchmark); \
	DECLARE_FUNCTION(execGetValidationProgress); \
	DECLARE_FUNCTION(execGetRegisteredShape); \
	DECLARE_FUNCTION(execGetValidationHistoryBySeverity); \
	DECLARE_FUNCTION(execGetValidationHistoryByType); \
	DECLARE_FUNCTION(execGetValidationConfig); \
	DECLARE_FUNCTION(execSetValidationConfig); \
	DECLARE_FUNCTION(execClearValidationHistory); \
	DECLARE_FUNCTION(execExportValidationReport); \
	DECLARE_FUNCTION(execGetWarningCount); \
	DECLARE_FUNCTION(execGetErrorCount); \
	DECLARE_FUNCTION(execGetValidationCount); \
	DECLARE_FUNCTION(execGetRegisteredShapes); \
	DECLARE_FUNCTION(execGetValidationHistory); \
	DECLARE_FUNCTION(execIsValidating); \
	DECLARE_FUNCTION(execValidateRiverIsland); \
	DECLARE_FUNCTION(execValidateRiverGeometry); \
	DECLARE_FUNCTION(execValidateDragonEllipticalArea); \
	DECLARE_FUNCTION(execValidateBaronHexagonalArea); \
	DECLARE_FUNCTION(execValidateTowerPositions); \
	DECLARE_FUNCTION(execValidateLaneWaypoints); \
	DECLARE_FUNCTION(execValidateLaneGeometry); \
	DECLARE_FUNCTION(execValidateSymmetry); \
	DECLARE_FUNCTION(execValidatePointInPolygon); \
	DECLARE_FUNCTION(execValidateLineIntersection); \
	DECLARE_FUNCTION(execValidatePerpendicularity); \
	DECLARE_FUNCTION(execValidateParallelism); \
	DECLARE_FUNCTION(execValidateSinusoidalCurve); \
	DECLARE_FUNCTION(execValidateEllipse); \
	DECLARE_FUNCTION(execValidateRegularHexagon); \
	DECLARE_FUNCTION(execValidatePolygonArea); \
	DECLARE_FUNCTION(execValidateAngle); \
	DECLARE_FUNCTION(execValidateDistance); \
	DECLARE_FUNCTION(execClearRegisteredShapes); \
	DECLARE_FUNCTION(execUnregisterShape); \
	DECLARE_FUNCTION(execRegisterShape); \
	DECLARE_FUNCTION(execPerformFullValidation); \
	DECLARE_FUNCTION(execStopRealTimeValidation); \
	DECLARE_FUNCTION(execStartRealTimeValidation);


AURA_API UClass* Z_Construct_UClass_AGeometricValidator_NoRegister();

#define FID_Aura_Source_Aura_Public_AGeometricValidator_h_242_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAGeometricValidator(); \
	friend struct Z_Construct_UClass_AGeometricValidator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_AGeometricValidator_NoRegister(); \
public: \
	DECLARE_CLASS2(AGeometricValidator, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_AGeometricValidator_NoRegister) \
	DECLARE_SERIALIZER(AGeometricValidator)


#define FID_Aura_Source_Aura_Public_AGeometricValidator_h_242_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AGeometricValidator(AGeometricValidator&&) = delete; \
	AGeometricValidator(const AGeometricValidator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AGeometricValidator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AGeometricValidator); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AGeometricValidator) \
	NO_API virtual ~AGeometricValidator();


#define FID_Aura_Source_Aura_Public_AGeometricValidator_h_239_PROLOG
#define FID_Aura_Source_Aura_Public_AGeometricValidator_h_242_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_AGeometricValidator_h_242_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_AGeometricValidator_h_242_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_AGeometricValidator_h_242_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AGeometricValidator;

// ********** End Class AGeometricValidator ********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_AGeometricValidator_h

// ********** Begin Enum EValidationType ***********************************************************
#define FOREACH_ENUM_EVALIDATIONTYPE(op) \
	op(EValidationType::Distance) \
	op(EValidationType::Angle) \
	op(EValidationType::Area) \
	op(EValidationType::Volume) \
	op(EValidationType::Intersection) \
	op(EValidationType::Containment) \
	op(EValidationType::Symmetry) \
	op(EValidationType::Parallelism) \
	op(EValidationType::Perpendicularity) \
	op(EValidationType::Collinearity) 

enum class EValidationType : uint8;
template<> struct TIsUEnumClass<EValidationType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EValidationType>();
// ********** End Enum EValidationType *************************************************************

// ********** Begin Enum EValidationSeverity *******************************************************
#define FOREACH_ENUM_EVALIDATIONSEVERITY(op) \
	op(EValidationSeverity::Info) \
	op(EValidationSeverity::Warning) \
	op(EValidationSeverity::Error) \
	op(EValidationSeverity::Critical) 

enum class EValidationSeverity : uint8;
template<> struct TIsUEnumClass<EValidationSeverity> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EValidationSeverity>();
// ********** End Enum EValidationSeverity *********************************************************

// ********** Begin Enum EGeometricShape ***********************************************************
#define FOREACH_ENUM_EGEOMETRICSHAPE(op) \
	op(EGeometricShape::Point) \
	op(EGeometricShape::Line) \
	op(EGeometricShape::Ray) \
	op(EGeometricShape::Segment) \
	op(EGeometricShape::Circle) \
	op(EGeometricShape::Ellipse) \
	op(EGeometricShape::Rectangle) \
	op(EGeometricShape::Polygon) \
	op(EGeometricShape::Hexagon) \
	op(EGeometricShape::Sphere) \
	op(EGeometricShape::Box) \
	op(EGeometricShape::Cylinder) \
	op(EGeometricShape::Plane) 

enum class EGeometricShape : uint8;
template<> struct TIsUEnumClass<EGeometricShape> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EGeometricShape>();
// ********** End Enum EGeometricShape *************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
