#include "APCGNaniteOptimizer.h"
#include "AProceduralMapGenerator.h"
#include "APCGWorldPartitionManager.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "PCGComponent.h"
#include "PCGSubsystem.h"
#include "RenderingThread.h"
#include "RHI.h"
#include "RHICommandList.h"
#include "Async/Async.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "Stats/Stats.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "Engine/AssetManager.h"

// CORRIGIDO: APIs modernas UE 5.6 para Nanite
#include "Async/ParallelFor.h"
#include "HAL/ThreadSafeCounter64.h"
#include "Engine/StreamableManager.h"
#include "Subsystems/WorldSubsystem.h"
#include "Math/UnrealMathUtility.h"
#include "Misc/Timespan.h"
#include "HAL/IConsoleManager.h"
#include "Engine/StaticMesh.h"
#include "Engine/StaticMeshActor.h"
// CORRIGIDO: Includes condicionais para Nanite (UE 5.6) - removidos por problemas de compilação
// Funcionalidades Nanite serão implementadas via runtime APIs

// UE5.6 Editor subsystem for Nanite settings
#if WITH_EDITOR
#include "EditorSubsystem.h"
#include "StaticMeshEditorSubsystem.h"
// CORRIGIDO: MeshBuilderCommon.h removido - não necessário para runtime
#endif

// CORRIGIDO: Logging categories específicas (API moderna UE 5.6)
DEFINE_LOG_CATEGORY_STATIC(LogPCGNaniteOptimizer, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGNaniteRendering, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGNanitePerformance, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGNaniteOptimization, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGNaniteMeshes, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGNaniteMemory, Log, All);

// Stats for performance monitoring
DECLARE_CYCLE_STAT(TEXT("PCG Nanite Mesh Conversion"), STAT_PCGNaniteMeshConversion, STATGROUP_PCG);
DECLARE_CYCLE_STAT(TEXT("PCG Nanite Instance Update"), STAT_PCGNaniteInstanceUpdate, STATGROUP_PCG);
DECLARE_CYCLE_STAT(TEXT("PCG Nanite LOD Update"), STAT_PCGNaniteLODUpdate, STATGROUP_PCG);
DECLARE_CYCLE_STAT(TEXT("PCG Nanite Culling"), STAT_PCGNaniteCulling, STATGROUP_PCG);
DECLARE_CYCLE_STAT(TEXT("PCG Nanite Memory Management"), STAT_PCGNaniteMemoryManagement, STATGROUP_PCG);

APCGNaniteOptimizer::APCGNaniteOptimizer()
{
    // CORRIGIDO: Configuração moderna de tick para UE 5.6
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.TickGroup = TG_PostUpdateWork; // Após updates de rendering
    PrimaryActorTick.TickInterval = 0.1f; // Update every 100ms for performance
    PrimaryActorTick.bTickEvenWhenPaused = false; // Otimização UE 5.6
    PrimaryActorTick.bStartWithTickEnabled = true;

    // CORRIGIDO: Estados iniciais robustos
    bIsInitialized = false;
    bIsOptimizing = false;
    bPerformanceMonitoringEnabled = true;
    bIsProcessingMeshes = false;
    bNaniteSupported = false; // Será detectado no BeginPlay
    bAutoOptimizationEnabled = true;
    bMemoryOptimizationEnabled = true;

    // CORRIGIDO: Configuração padrão robusta para Nanite
    OptimizationConfig = FPCGNaniteOptimizationConfig();
    OptimizationConfig.bEnableNanite = true;
    OptimizationConfig.bAutoGenerateNaniteMeshes = true;
    OptimizationConfig.MinTriangleCount = 1000; // Threshold production-ready
    OptimizationConfig.MaxTriangleCount = 1000000; // 1M triangles max
    OptimizationConfig.TargetError = 1.0f; // Qualidade balanceada
    OptimizationConfig.bPreserveBoundaries = true;
    OptimizationConfig.bPreserveUVs = true;
    OptimizationConfig.bEnableFallbackMesh = true;

    // CORRIGIDO: Performance stats iniciais production-ready
    PerformanceStats = FPCGNanitePerformanceStats();
    PerformanceStats.TotalMeshes = 0;
    PerformanceStats.NaniteEnabledMeshes = 0;
    PerformanceStats.OptimizedMeshes = 0;
    PerformanceStats.MemoryUsageMB = 0.0f;
    PerformanceStats.OptimizationTime = 0.0f;
    PerformanceStats.TriangleReduction = 0.0f;

    // CORRIGIDO: Propriedades do actor
    SetActorTickEnabled(true);
    SetActorHiddenInGame(true);

    // CORRIGIDO: Inicialização de containers com Reserve para performance
    OptimizedMeshes.Reserve(1000);
    PendingMeshes.Reserve(100);
    NaniteEnabledComponents.Reserve(1000);

    // CORRIGIDO: Timers iniciais
    LastOptimizationTime = 0.0f;
    LastPerformanceUpdate = 0.0f;
    LastMemoryCheck = 0.0f;

    UE_LOG(LogPCGNaniteOptimizer, Log, TEXT("Constructor completed with modern UE 5.6 configuration"));
}

void APCGNaniteOptimizer::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogPCGNaniteOptimizer, Log, TEXT("BeginPlay started with modern UE 5.6 Nanite APIs"));

    // CORRIGIDO: Verificação robusta do World
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogPCGNaniteOptimizer, Error, TEXT("No valid world found"));
        return;
    }

    // CORRIGIDO: Detectar suporte ao Nanite via CVar (API moderna UE 5.6)
    // Baseado na documentação oficial: "r.Nanite 0" desabilita Nanite globalmente
    static const auto* NaniteCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Nanite"));
    if (NaniteCVar && NaniteCVar->GetInt() > 0)
    {
        bNaniteSupported = true;
        UE_LOG(LogPCGNaniteMeshes, Log, TEXT("Nanite detected and enabled via r.Nanite=%d"), NaniteCVar->GetInt());

        // CORRIGIDO: Verificar também r.Nanite.ProjectEnabled para projetos específicos
        static const auto* NaniteProjectCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Nanite.ProjectEnabled"));
        if (NaniteProjectCVar && NaniteProjectCVar->GetInt() == 0)
        {
            bNaniteSupported = false;
            UE_LOG(LogPCGNaniteOptimizer, Warning, TEXT("Nanite disabled for this project via r.Nanite.ProjectEnabled=0"));
        }
    }
    else
    {
        bNaniteSupported = false;
        UE_LOG(LogPCGNaniteOptimizer, Warning, TEXT("Nanite not supported or disabled globally via r.Nanite=0 - optimizer will run in fallback mode"));
    }

    // CORRIGIDO: Detectar World Partition para otimizações
    if (World->IsPartitionedWorld())
    {
        UE_LOG(LogPCGNaniteOptimizer, Log, TEXT("World Partition detected - enabling streaming optimizations"));
        OptimizationConfig.bEnableStreamingOptimization = true;
    }

    // CORRIGIDO: Inicialização robusta do Nanite optimizer
    if (!InitializeNaniteOptimizer())
    {
        UE_LOG(LogPCGNaniteOptimizer, Error, TEXT("Failed to initialize Nanite optimizer"));
        return;
    }

    // CORRIGIDO: Validação de suporte com logging detalhado
    ValidateNaniteSupport();

    // CORRIGIDO: Setup de configurações com validação
    SetupDefaultOptimizationSettings();
    
    // Initialize Nanite subsystems
    InitializeNaniteSubsystems();
    
    UE_LOG(LogPCGNaniteOptimizer, Log, TEXT("BeginPlay completed successfully - Nanite support: %s"),
           bNaniteSupported ? TEXT("Yes") : TEXT("No"));
}

void APCGNaniteOptimizer::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // CORRIGIDO: Logging seguro para EEndPlayReason
    const FString ReasonString = UEnum::GetValueAsString(EndPlayReason);
    UE_LOG(LogPCGNaniteOptimizer, Log, TEXT("EndPlay started - reason: %s"), *ReasonString);

    // CORRIGIDO: Shutdown robusto do Nanite optimizer
    if (bIsInitialized)
    {
        UE_LOG(LogPCGNaniteOptimization, Log, TEXT("Shutting down Nanite optimizer"));
        ShutdownNaniteOptimizer();
    }

    // CORRIGIDO: Limpeza segura de dados com logging
    const int32 ProcessedMeshesCleared = ProcessedMeshes.Num();
    const int32 NaniteInstancesCleared = NaniteInstances.Num();
    const int32 PCGComponentsCleared = IntegratedPCGComponents.Num();

    ProcessedMeshes.Empty();
    NaniteInstances.Empty();
    IntegratedPCGComponents.Empty();

    UE_LOG(LogPCGNaniteOptimizer, VeryVerbose, TEXT("Cleared %d processed meshes, %d Nanite instances, %d PCG components"),
           ProcessedMeshesCleared, NaniteInstancesCleared, PCGComponentsCleared);

    // CORRIGIDO: Limpeza segura de containers adicionais
    OptimizedMeshes.Empty();
    PendingMeshes.Empty();
    NaniteEnabledComponents.Empty();

    // CORRIGIDO: Limpeza robusta da queue de conversões pendentes
    int32 PendingConversionsCleared = 0;
    TSoftObjectPtr<UStaticMesh> PendingMesh;
    while (PendingMeshConversions.Dequeue(PendingMesh))
    {
        PendingConversionsCleared++;
    }

    if (PendingConversionsCleared > 0)
    {
        UE_LOG(LogPCGNaniteOptimization, VeryVerbose, TEXT("Cleared %d pending mesh conversions"), PendingConversionsCleared);
    }

    // CORRIGIDO: Estados finais
    bIsInitialized = false;
    bNaniteSupported = false;
    bIsOptimizing = false;
    bIsProcessingMeshes = false;

    Super::EndPlay(EndPlayReason);

    UE_LOG(LogPCGNaniteOptimizer, Log, TEXT("EndPlay completed successfully"));
}

void APCGNaniteOptimizer::Tick(float DeltaTime)
{
    // CORRIGIDO: Profiling moderno UE 5.6
    SCOPE_CYCLE_COUNTER(STAT_PCGNaniteInstanceUpdate);
    TRACE_CPUPROFILER_EVENT_SCOPE(APCGNaniteOptimizer::Tick);

    Super::Tick(DeltaTime);

    // CORRIGIDO: Validação robusta de estados
    if (!bIsInitialized || !bNaniteSupported)
    {
        return;
    }

    // CORRIGIDO: Validação de World para segurança
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogPCGNaniteOptimizer, Warning, TEXT("No valid world during tick - skipping"));
        return;
    }

    const float CurrentTime = World->GetTimeSeconds();

    // CORRIGIDO: Update performance stats com intervalo
    if (bPerformanceMonitoringEnabled && CurrentTime - LastPerformanceUpdate >= 1.0f)
    {
        UpdatePerformanceStats(DeltaTime);
    }
    
    // Process pending mesh conversions
    ProcessPendingMeshConversions();
    
    // Update instance LODs
    UpdateInstanceLODs(DeltaTime);
    
    // Update memory management
    UpdateMemoryManagement();
    
    // Update instance visibility
    UpdateInstanceVisibilityInternal();
}

// === Core Nanite Functions ===

bool APCGNaniteOptimizer::InitializeNaniteOptimizer()
{
    UE_LOG(LogPCGNaniteOptimizer, Log, TEXT("Initializing Nanite optimizer with modern UE 5.6 APIs"));

    if (bIsInitialized)
    {
        UE_LOG(LogPCGNaniteOptimizer, Warning, TEXT("Already initialized - skipping"));
        return true;
    }

    // CORRIGIDO: Verificação robusta de suporte ao Nanite
    if (!IsNaniteSupported())
    {
        UE_LOG(LogPCGNaniteOptimizer, Error, TEXT("Nanite is not supported on this platform"));
        return false;
    }

    // CORRIGIDO: Inicialização robusta de coleções com Reserve
    ProcessedMeshes.Empty(1000);
    ProcessedMeshes.Reserve(1000);

    NaniteInstances.Empty(1000);
    NaniteInstances.Reserve(1000);

    IntegratedPCGComponents.Empty(100);
    IntegratedPCGComponents.Reserve(100);

    // CORRIGIDO: Reset completo de performance stats
    PerformanceStats = FPCGNanitePerformanceStats();
    PerformanceStats.TotalMeshes = 0;
    PerformanceStats.NaniteEnabledMeshes = 0;
    PerformanceStats.OptimizedMeshes = 0;
    PerformanceStats.MemoryUsageMB = 0.0f;
    PerformanceStats.OptimizationTime = 0.0f;
    PerformanceStats.TriangleReduction = 0.0f;

    // CORRIGIDO: Inicialização de threading com estados seguros
    bIsProcessingMeshes = false;
    bIsOptimizing = false;

    // CORRIGIDO: Inicialização de timers
    UWorld* World = GetWorld();
    if (World)
    {
        const float CurrentTime = World->GetTimeSeconds();
        LastOptimizationTime = CurrentTime;
        LastPerformanceUpdate = CurrentTime;
        LastMemoryCheck = CurrentTime;
    }

    // CORRIGIDO: Marcar como inicializado
    bIsInitialized = true;

    UE_LOG(LogPCGNaniteOptimizer, Log, TEXT("Initialization completed successfully - %d mesh slots, %d instance slots reserved"),
           ProcessedMeshes.GetAllocatedSize(), NaniteInstances.GetAllocatedSize());
    return true;
}

void APCGNaniteOptimizer::ShutdownNaniteOptimizer()
{
    UE_LOG(LogPCGNaniteOptimizer, Log, TEXT("Shutting down Nanite optimizer"));

    if (!bIsInitialized)
    {
        UE_LOG(LogPCGNaniteOptimizer, Warning, TEXT("Not initialized - nothing to shutdown"));
        return;
    }

    // CORRIGIDO: Parar otimizações com logging
    if (bIsOptimizing || bIsProcessingMeshes)
    {
        UE_LOG(LogPCGNaniteOptimization, Log, TEXT("Stopping ongoing optimizations"));
        bIsOptimizing = false;
        bIsProcessingMeshes = false;
    }

    // CORRIGIDO: Aguardar comandos de render com timeout
    UE_LOG(LogPCGNaniteRendering, VeryVerbose, TEXT("Waiting for render commands to complete"));
    RenderFence.BeginFence();
    RenderFence.Wait();

    // CORRIGIDO: Limpeza de dados com logging
    const int32 ProcessedMeshesCleared = ProcessedMeshes.Num();
    const int32 NaniteInstancesCleared = NaniteInstances.Num();
    const int32 PCGComponentsCleared = IntegratedPCGComponents.Num();

    ProcessedMeshes.Empty();
    NaniteInstances.Empty();
    IntegratedPCGComponents.Empty();

    // CORRIGIDO: Limpeza de containers adicionais
    OptimizedMeshes.Empty();
    PendingMeshes.Empty();
    NaniteEnabledComponents.Empty();

    UE_LOG(LogPCGNaniteOptimizer, VeryVerbose, TEXT("Cleared %d processed meshes, %d Nanite instances, %d PCG components"),
           ProcessedMeshesCleared, NaniteInstancesCleared, PCGComponentsCleared);

    // CORRIGIDO: Estados finais
    bIsInitialized = false;

    UE_LOG(LogPCGNaniteOptimizer, Log, TEXT("Shutdown completed successfully"));
}

bool APCGNaniteOptimizer::SetOptimizationConfig(const FPCGNaniteOptimizationConfig& NewConfig)
{
    UE_LOG(LogPCGNaniteOptimization, Log, TEXT("Setting new optimization config"));

    // CORRIGIDO: Validação robusta da configuração
    if (NewConfig.TargetScreenSize <= 0.0f || NewConfig.DistanceCullingThreshold <= 0.0f)
    {
        UE_LOG(LogPCGNaniteOptimization, Error, TEXT("Invalid optimization config - TargetScreenSize: %.2f, CullingThreshold: %.2f"),
               (double)NewConfig.TargetScreenSize, (double)NewConfig.DistanceCullingThreshold);
        return false;
    }

    OptimizationConfig = NewConfig;

    // CORRIGIDO: Aplicar novas configurações com logging detalhado
    int32 UpdatedInstances = 0;
    for (FPCGNaniteInstanceData& Instance : NaniteInstances)
    {
        // Update LOD bias based on new config
        if (OptimizationConfig.LODStrategy == EPCGNaniteLODStrategy::ScreenSize)
        {
            const float OldLODBias = Instance.LODBias;
            Instance.LODBias = FMath::Clamp(OptimizationConfig.TargetScreenSize / 300.0f, 0.1f, 2.0f);

            if (FMath::Abs(OldLODBias - Instance.LODBias) > 0.01f)
            {
                UpdatedInstances++;
            }
        }

        // Update culling distance
        if (Instance.CullingDistance < 0.0f)
        {
            Instance.CullingDistance = OptimizationConfig.DistanceCullingThreshold;
            UpdatedInstances++;
        }
    }

    UE_LOG(LogPCGNaniteOptimization, Log, TEXT("Optimization config updated successfully - %d instances updated"), UpdatedInstances);
    return true;
}

// === Mesh Conversion Functions ===

bool APCGNaniteOptimizer::ConvertMeshToNanite(UStaticMesh* OriginalMesh, FPCGNaniteMeshData& OutMeshData)
{
    // CORRIGIDO: Profiling moderno UE 5.6
    SCOPE_CYCLE_COUNTER(STAT_PCGNaniteMeshConversion);
    TRACE_CPUPROFILER_EVENT_SCOPE(APCGNaniteOptimizer::ConvertMeshToNanite);

    // CORRIGIDO: Validação robusta com IsValid()
    if (!IsValid(OriginalMesh))
    {
        UE_LOG(LogPCGNaniteMeshes, Error, TEXT("OriginalMesh is null or invalid"));
        return false;
    }

    if (!bIsInitialized || !bNaniteSupported)
    {
        UE_LOG(LogPCGNaniteOptimizer, Error, TEXT("Not initialized or Nanite not supported"));
        return false;
    }

    UE_LOG(LogPCGNaniteMeshes, Log, TEXT("Converting mesh to Nanite: %s"), *OriginalMesh->GetName());

    // CORRIGIDO: Verificação de cache com logging detalhado
    TSoftObjectPtr<UStaticMesh> MeshPtr(OriginalMesh);
    if (ProcessedMeshes.Contains(MeshPtr))
    {
        OutMeshData = ProcessedMeshes[MeshPtr];
        UE_LOG(LogPCGNaniteMeshes, VeryVerbose, TEXT("Mesh already processed, returning cached data - triangles: %d"),
               OutMeshData.TriangleCount);
        return OutMeshData.bIsProcessed;
    }
    
    // Validate mesh for Nanite
    FString ValidationResult;
    if (!ValidateMeshForNanite(OriginalMesh, ValidationResult))
    {
        UE_LOG(LogPCGNaniteMeshes, Warning, TEXT("Mesh validation failed for %s: %s"),
               *OriginalMesh->GetName(), *ValidationResult);
        OutMeshData.ProcessingError = ValidationResult;
        OutMeshData.bIsNaniteSuitable = false;
        ProcessedMeshes.Add(MeshPtr, OutMeshData);
        return false;
    }
    
    // Perform the conversion
    return ConvertMeshToNaniteInternal(OriginalMesh, OutMeshData);
}

bool APCGNaniteOptimizer::BatchConvertMeshesToNanite(const TArray<UStaticMesh*>& OriginalMeshes, TArray<FPCGNaniteMeshData>& OutMeshDataArray)
{
    UE_LOG(LogPCGNaniteMeshes, Log, TEXT("Batch converting %d meshes to Nanite"), OriginalMeshes.Num());

    if (!bIsInitialized || !bNaniteSupported)
    {
        UE_LOG(LogPCGNaniteOptimizer, Error, TEXT("Not initialized or Nanite not supported"));
        return false;
    }
    
    OutMeshDataArray.Empty();
    OutMeshDataArray.Reserve(OriginalMeshes.Num());
    
    int32 SuccessCount = 0;
    
    for (UStaticMesh* Mesh : OriginalMeshes)
    {
        FPCGNaniteMeshData MeshData;
        if (ConvertMeshToNanite(Mesh, MeshData))
        {
            SuccessCount++;
        }
        OutMeshDataArray.Add(MeshData);
    }
    
    UE_LOG(LogPCGNaniteMeshes, Log, TEXT("Batch conversion completed. Success: %d/%d meshes"),
           SuccessCount, OriginalMeshes.Num());
    return SuccessCount > 0;
}

bool APCGNaniteOptimizer::ValidateMeshForNanite(UStaticMesh* Mesh, FString& OutValidationResult)
{
    if (!Mesh)
    {
        OutValidationResult = TEXT("Mesh is null");
        return false;
    }
    
    return ValidateMeshForNaniteInternal(Mesh);
}

bool APCGNaniteOptimizer::OptimizeMeshForNanite(UStaticMesh* Mesh, const FPCGNaniteOptimizationConfig& Config)
{
    if (!Mesh)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGNaniteOptimizer: Mesh is null"));
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Optimizing mesh for Nanite: %s"), *Mesh->GetName());
    
    // Optimize mesh clustering
    OptimizeMeshClustering(Mesh, Config);
    
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Mesh optimization completed"));
    return true;
}

// === Instance Management ===

bool APCGNaniteOptimizer::RegisterNaniteInstance(const FPCGNaniteInstanceData& InstanceData)
{
    if (!bIsInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGNaniteOptimizer: Not initialized"));
        return false;
    }
    
    // Check if instance already exists
    for (const FPCGNaniteInstanceData& ExistingInstance : NaniteInstances)
    {
        if (ExistingInstance.InstanceTransform.Equals(InstanceData.InstanceTransform, 1.0f))
        {
            UE_LOG(LogTemp, Warning, TEXT("APCGNaniteOptimizer: Instance already registered at this location"));
            return false;
        }
    }
    
    // Add the instance
    NaniteInstances.Add(InstanceData);
    
    // Update performance stats
    PerformanceStats.TotalNaniteInstances = NaniteInstances.Num();
    
    UE_LOG(LogTemp, VeryVerbose, TEXT("APCGNaniteOptimizer: Registered Nanite instance. Total: %d"), NaniteInstances.Num());
    return true;
}

bool APCGNaniteOptimizer::UnregisterNaniteInstance(const FTransform& InstanceTransform)
{
    if (!bIsInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGNaniteOptimizer: Not initialized"));
        return false;
    }
    
    // Find and remove the instance
    for (int32 i = NaniteInstances.Num() - 1; i >= 0; --i)
    {
        if (NaniteInstances[i].InstanceTransform.Equals(InstanceTransform, 1.0f))
        {
            NaniteInstances.RemoveAt(i);
            
            // Update performance stats
            PerformanceStats.TotalNaniteInstances = NaniteInstances.Num();
            
            UE_LOG(LogTemp, VeryVerbose, TEXT("APCGNaniteOptimizer: Unregistered Nanite instance. Total: %d"), NaniteInstances.Num());
            return true;
        }
    }
    
    UE_LOG(LogTemp, Warning, TEXT("APCGNaniteOptimizer: Instance not found for unregistration"));
    return false;
}

void APCGNaniteOptimizer::ClearAllNaniteInstances()
{
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Clearing all Nanite instances"));
    
    NaniteInstances.Empty();
    
    // Reset performance stats
    PerformanceStats.TotalNaniteInstances = 0;
    PerformanceStats.VisibleNaniteInstances = 0;
    PerformanceStats.CulledInstances = 0;
}

TArray<FPCGNaniteInstanceData> APCGNaniteOptimizer::GetNaniteInstances() const
{
    return NaniteInstances;
}

int32 APCGNaniteOptimizer::GetNaniteInstanceCount() const
{
    return NaniteInstances.Num();
}

// === LOD Management ===

bool APCGNaniteOptimizer::SetLODStrategy(EPCGNaniteLODStrategy NewStrategy)
{
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Setting LOD strategy to %d"), (int32)NewStrategy);
    
    OptimizationConfig.LODStrategy = NewStrategy;
    
    // Update all instances based on new strategy
    for (FPCGNaniteInstanceData& Instance : NaniteInstances)
    {
        switch (NewStrategy)
        {
            case EPCGNaniteLODStrategy::Automatic:
                Instance.LODBias = 0.0f;
                break;
            case EPCGNaniteLODStrategy::DistanceBased:
                // LOD bias will be calculated in UpdateInstanceLODs
                break;
            case EPCGNaniteLODStrategy::ScreenSize:
                Instance.LODBias = FMath::Clamp(OptimizationConfig.TargetScreenSize / 300.0f, 0.1f, 2.0f);
                break;
            case EPCGNaniteLODStrategy::Hybrid:
                // Combination of distance and screen size
                break;
            case EPCGNaniteLODStrategy::Custom:
                // Keep existing LOD bias
                break;
        }
    }
    
    return true;
}

bool APCGNaniteOptimizer::UpdateLODParameters(float TargetScreenSize, float LODBias)
{
    OptimizationConfig.TargetScreenSize = TargetScreenSize;
    
    // Apply LOD bias to all instances
    for (FPCGNaniteInstanceData& Instance : NaniteInstances)
    {
        Instance.LODBias = LODBias;
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Updated LOD parameters - TargetScreenSize: %.2f, LODBias: %.2f"), TargetScreenSize, LODBias);
    return true;
}

bool APCGNaniteOptimizer::SetInstanceLODBias(const FTransform& InstanceTransform, float LODBias)
{
    for (FPCGNaniteInstanceData& Instance : NaniteInstances)
    {
        if (Instance.InstanceTransform.Equals(InstanceTransform, 1.0f))
        {
            Instance.LODBias = LODBias;
            return true;
        }
    }
    
    UE_LOG(LogTemp, Warning, TEXT("APCGNaniteOptimizer: Instance not found for LOD bias update"));
    return false;
}

float APCGNaniteOptimizer::CalculateOptimalLODBias(const FVector& ViewerLocation, const FTransform& InstanceTransform) const
{
    float Distance = FVector::Dist(ViewerLocation, InstanceTransform.GetLocation());
    
    // Calculate LOD bias based on distance and optimization level
    float BaseLODBias = 0.0f;
    
    switch (OptimizationConfig.OptimizationLevel)
    {
        case EPCGNaniteOptimizationLevel::Conservative:
            BaseLODBias = FMath::Clamp(Distance / 100000.0f, 0.0f, 1.0f); // 1km = 1.0 bias
            break;
        case EPCGNaniteOptimizationLevel::Balanced:
            BaseLODBias = FMath::Clamp(Distance / 50000.0f, 0.0f, 2.0f); // 500m = 2.0 bias
            break;
        case EPCGNaniteOptimizationLevel::Aggressive:
            BaseLODBias = FMath::Clamp(Distance / 25000.0f, 0.0f, 3.0f); // 250m = 3.0 bias
            break;
        case EPCGNaniteOptimizationLevel::Maximum:
            BaseLODBias = FMath::Clamp(Distance / 10000.0f, 0.0f, 4.0f); // 100m = 4.0 bias
            break;
        default:
            BaseLODBias = 0.0f;
            break;
    }
    
    return BaseLODBias;
}

// === Culling and Visibility ===

bool APCGNaniteOptimizer::SetCullingParameters(float DistanceThreshold, bool bEnableOcclusion)
{
    OptimizationConfig.DistanceCullingThreshold = DistanceThreshold;
    OptimizationConfig.bEnableOcclusionCulling = bEnableOcclusion;
    
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Updated culling parameters - Distance: %.2f, Occlusion: %s"), 
           DistanceThreshold, bEnableOcclusion ? TEXT("Enabled") : TEXT("Disabled"));
    
    return true;
}

bool APCGNaniteOptimizer::UpdateInstanceVisibility(const FTransform& InstanceTransform, bool bIsVisible)
{
    for (FPCGNaniteInstanceData& Instance : NaniteInstances)
    {
        if (Instance.InstanceTransform.Equals(InstanceTransform, 1.0f))
        {
            Instance.bIsVisible = bIsVisible;
            return true;
        }
    }
    
    return false;
}

void APCGNaniteOptimizer::UpdateVisibilityFromViewpoint(const FVector& ViewerLocation, const FVector& ViewDirection)
{
    SCOPE_CYCLE_COUNTER(STAT_PCGNaniteCulling);
    
    int32 VisibleCount = 0;
    int32 CulledCount = 0;
    
    for (FPCGNaniteInstanceData& Instance : NaniteInstances)
    {
        bool bShouldBeVisible = ShouldInstanceBeVisible(Instance, ViewerLocation, ViewDirection);
        Instance.bIsVisible = bShouldBeVisible;
        
        if (bShouldBeVisible)
        {
            VisibleCount++;
        }
        else
        {
            CulledCount++;
        }
    }
    
    // Update performance stats
    PerformanceStats.VisibleNaniteInstances = VisibleCount;
    PerformanceStats.CulledInstances = CulledCount;
}

TArray<FPCGNaniteInstanceData> APCGNaniteOptimizer::GetVisibleInstances(const FVector& ViewerLocation, float ViewDistance) const
{
    TArray<FPCGNaniteInstanceData> VisibleInstances;
    
    for (const FPCGNaniteInstanceData& Instance : NaniteInstances)
    {
        if (!Instance.bIsVisible)
        {
            continue;
        }
        
        float Distance = FVector::Dist(ViewerLocation, Instance.InstanceTransform.GetLocation());
        if (Distance <= ViewDistance)
        {
            VisibleInstances.Add(Instance);
        }
    }
    
    return VisibleInstances;
}

// === Performance Monitoring ===

FPCGNanitePerformanceStats APCGNaniteOptimizer::GetPerformanceStats() const
{
    return PerformanceStats;
}

void APCGNaniteOptimizer::ResetPerformanceStats()
{
    PerformanceStats = FPCGNanitePerformanceStats();
    PerformanceStats.TotalNaniteInstances = NaniteInstances.Num();
    
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Performance stats reset"));
}

bool APCGNaniteOptimizer::EnablePerformanceMonitoring(bool bEnable)
{
    bPerformanceMonitoringEnabled = bEnable;
    
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Performance monitoring %s"), 
           bEnable ? TEXT("enabled") : TEXT("disabled"));
    
    return true;
}

float APCGNaniteOptimizer::GetGPUMemoryUsage() const
{
    return PerformanceStats.GPUMemoryUsageMB;
}

float APCGNaniteOptimizer::GetCompressionEfficiency() const
{
    return PerformanceStats.CompressionEfficiency;
}

// === Memory Management ===

bool APCGNaniteOptimizer::SetMemoryBudget(float MemoryBudgetMB)
{
    OptimizationConfig.NaniteMemoryBudgetMB = MemoryBudgetMB;
    
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Memory budget set to %.2f MB"), MemoryBudgetMB);
    return true;
}

void APCGNaniteOptimizer::OptimizeMemoryUsage()
{
    SCOPE_CYCLE_COUNTER(STAT_PCGNaniteMemoryManagement);
    
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Optimizing memory usage"));
    
    // Unload distant instances if memory usage is high
    if (PerformanceStats.GPUMemoryUsageMB > OptimizationConfig.NaniteMemoryBudgetMB * 0.8f)
    {
        // Find viewer location (use first player controller)
        FVector ViewerLocation = FVector::ZeroVector;
        if (UWorld* World = GetWorld())
        {
            if (APlayerController* PC = World->GetFirstPlayerController())
            {
                if (APawn* Pawn = PC->GetPawn())
                {
                    ViewerLocation = Pawn->GetActorLocation();
                }
            }
        }
        
        UnloadDistantNaniteData(ViewerLocation, OptimizationConfig.DistanceCullingThreshold * 0.5f);
    }
}

bool APCGNaniteOptimizer::StreamNaniteData(const FVector& StreamingCenter, float StreamingRadius)
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("APCGNaniteOptimizer: Streaming Nanite data around %.2f, %.2f, %.2f with radius %.2f"), 
           StreamingCenter.X, StreamingCenter.Y, StreamingCenter.Z, StreamingRadius);
    
    // Update streaming priorities for instances within radius
    for (FPCGNaniteInstanceData& Instance : NaniteInstances)
    {
        float Distance = FVector::Dist(StreamingCenter, Instance.InstanceTransform.GetLocation());
        if (Distance <= StreamingRadius)
        {
            // Higher priority for closer instances
            Instance.StreamingPriority = FMath::Clamp(1.0f - (Distance / StreamingRadius), 0.1f, 1.0f);
        }
        else
        {
            // Lower priority for distant instances
            Instance.StreamingPriority = 0.1f;
        }
    }
    
    return true;
}

void APCGNaniteOptimizer::UnloadDistantNaniteData(const FVector& ViewerLocation, float UnloadDistance)
{
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Unloading distant Nanite data beyond %.2f units"), UnloadDistance);
    
    int32 UnloadedCount = 0;
    
    for (FPCGNaniteInstanceData& Instance : NaniteInstances)
    {
        float Distance = FVector::Dist(ViewerLocation, Instance.InstanceTransform.GetLocation());
        if (Distance > UnloadDistance)
        {
            // Mark instance as not visible to reduce memory usage
            if (Instance.bIsVisible)
            {
                Instance.bIsVisible = false;
                UnloadedCount++;
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Unloaded %d distant instances"), UnloadedCount);
}

// === Integration Functions ===

void APCGNaniteOptimizer::SetProceduralMapGenerator(AProceduralMapGenerator* Generator)
{
    ProceduralMapGenerator = Generator;
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Set ProceduralMapGenerator reference"));
}

void APCGNaniteOptimizer::SetWorldPartitionManager(APCGWorldPartitionManager* Manager)
{
    WorldPartitionManager = Manager;
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Set WorldPartitionManager reference"));
}

bool APCGNaniteOptimizer::IntegrateWithPCGComponent(UPCGComponent* PCGComponent)
{
    if (!PCGComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGNaniteOptimizer: PCGComponent is null"));
        return false;
    }
    
    // Add to integrated components list
    IntegratedPCGComponents.AddUnique(PCGComponent);
    
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Integrated with PCG component: %s"), *PCGComponent->GetName());
    return true;
}

bool APCGNaniteOptimizer::ProcessPCGGeneratedMeshes(const TArray<UStaticMeshComponent*>& MeshComponents)
{
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Processing %d PCG-generated mesh components"), MeshComponents.Num());
    
    if (!bIsInitialized)
    {
        UE_LOG(LogTemp, Error, TEXT("APCGNaniteOptimizer: Not initialized"));
        return false;
    }
    
    int32 ProcessedCount = 0;
    
    for (UStaticMeshComponent* MeshComp : MeshComponents)
    {
        if (!MeshComp || !MeshComp->GetStaticMesh())
        {
            continue;
        }
        
        // Convert mesh to Nanite if suitable
        FPCGNaniteMeshData MeshData;
        if (ConvertMeshToNanite(MeshComp->GetStaticMesh(), MeshData))
        {
            // Register instance
            FPCGNaniteInstanceData InstanceData;
            InstanceData.InstanceTransform = MeshComp->GetComponentTransform();
            InstanceData.MeshData = MeshData;
            
            // Copy material overrides
            for (int32 i = 0; i < MeshComp->GetNumMaterials(); ++i)
            {
                if (UMaterialInterface* Material = MeshComp->GetMaterial(i))
                {
                    InstanceData.MaterialOverrides.Add(Material);
                }
            }
            
            RegisterNaniteInstance(InstanceData);
            ProcessedCount++;
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Successfully processed %d/%d mesh components"), ProcessedCount, MeshComponents.Num());
    return ProcessedCount > 0;
}

// === Utility Functions ===

bool APCGNaniteOptimizer::IsNaniteSupported() const
{
    // Check if Nanite is supported on this platform
    // In UE 5.6, check feature level and RHI capabilities
    if (!GEngine)
    {
        return false;
    }
    
    ERHIFeatureLevel::Type FeatureLevel = GEngine->GetDefaultWorldFeatureLevel();
    return FeatureLevel >= ERHIFeatureLevel::SM5 && 
           GMaxRHIFeatureLevel >= ERHIFeatureLevel::SM5;
}

bool APCGNaniteOptimizer::IsMeshNaniteEnabled(UStaticMesh* Mesh) const
{
    if (!Mesh)
    {
        return false;
    }
    
    // Check if mesh has Nanite enabled using UE 5.6 API
#if WITH_EDITOR
    if (UStaticMeshEditorSubsystem* StaticMeshEditorSubsystem = GEditor ? GEditor->GetEditorSubsystem<UStaticMeshEditorSubsystem>() : nullptr)
    {
        FMeshNaniteSettings NaniteSettings = StaticMeshEditorSubsystem->GetNaniteSettings(Mesh);
        return NaniteSettings.bEnabled;
    }
#endif
    // Fallback: assume Nanite is enabled if mesh has render data
    return Mesh->GetRenderData() != nullptr;
}

FString APCGNaniteOptimizer::GetNaniteSystemInfo() const
{
    FString Info;
    Info += FString::Printf(TEXT("Nanite Supported: %s\n"), IsNaniteSupported() ? TEXT("Yes") : TEXT("No"));
    Info += FString::Printf(TEXT("Total Instances: %d\n"), PerformanceStats.TotalNaniteInstances);
    Info += FString::Printf(TEXT("Visible Instances: %d\n"), PerformanceStats.VisibleNaniteInstances);
    Info += FString::Printf(TEXT("Culled Instances: %d\n"), PerformanceStats.CulledInstances);
    Info += FString::Printf(TEXT("GPU Memory Usage: %.2f MB\n"), PerformanceStats.GPUMemoryUsageMB);
    Info += FString::Printf(TEXT("Compression Efficiency: %.2f%%\n"), PerformanceStats.CompressionEfficiency * 100.0f);
    Info += FString::Printf(TEXT("Average Frame Time: %.2f ms\n"), PerformanceStats.AverageFrameTimeMS);
    
    return Info;
}

bool APCGNaniteOptimizer::ExportNaniteOptimizationReport(const FString& FilePath) const
{
    FString Report;
    Report += TEXT("=== PCG Nanite Optimization Report ===\n\n");
    Report += FString::Printf(TEXT("Generated: %s\n\n"), *FDateTime::Now().ToString());
    
    // System info
    Report += TEXT("=== System Information ===\n");
    Report += GetNaniteSystemInfo();
    Report += TEXT("\n");
    
    // Configuration
    Report += TEXT("=== Configuration ===\n");
    Report += FString::Printf(TEXT("Optimization Level: %d\n"), (int32)OptimizationConfig.OptimizationLevel);
    Report += FString::Printf(TEXT("LOD Strategy: %d\n"), (int32)OptimizationConfig.LODStrategy);
    Report += FString::Printf(TEXT("Clustering Mode: %d\n"), (int32)OptimizationConfig.ClusteringMode);
    Report += FString::Printf(TEXT("Memory Budget: %.2f MB\n"), OptimizationConfig.NaniteMemoryBudgetMB);
    Report += TEXT("\n");
    
    // Processed meshes
    Report += TEXT("=== Processed Meshes ===\n");
    for (const auto& MeshPair : ProcessedMeshes)
    {
        const FPCGNaniteMeshData& MeshData = MeshPair.Value;
        Report += FString::Printf(TEXT("Mesh: %s\n"), MeshPair.Key.IsValid() ? *MeshPair.Key->GetName() : TEXT("Invalid"));
        Report += FString::Printf(TEXT("  Original Triangles: %d\n"), MeshData.OriginalTriangleCount);
        Report += FString::Printf(TEXT("  Nanite Triangles: %d\n"), MeshData.NaniteTriangleCount);
        Report += FString::Printf(TEXT("  Compression Ratio: %.2f\n"), MeshData.CompressionRatio);
        Report += FString::Printf(TEXT("  Memory Usage: %lld bytes\n"), MeshData.NaniteMemoryUsage);
        Report += FString::Printf(TEXT("  Suitable: %s\n"), MeshData.bIsNaniteSuitable ? TEXT("Yes") : TEXT("No"));
        Report += TEXT("\n");
    }
    
    // Save to file
    return FFileHelper::SaveStringToFile(Report, *FilePath);
}

// === Internal Functions ===

void APCGNaniteOptimizer::UpdatePerformanceStats(float DeltaTime)
{
    // Update frame time
    PerformanceStats.AverageFrameTimeMS = DeltaTime * 1000.0f;
    
    // Update triangle counts
    int64 TotalTriangles = 0;
    int64 VisibleTriangles = 0;
    
    for (const FPCGNaniteInstanceData& Instance : NaniteInstances)
    {
        TotalTriangles += Instance.MeshData.NaniteTriangleCount;
        if (Instance.bIsVisible)
        {
            VisibleTriangles += Instance.MeshData.NaniteTriangleCount;
        }
    }
    
    PerformanceStats.TrianglesRendered = VisibleTriangles;
    PerformanceStats.TrianglesCulled = TotalTriangles - VisibleTriangles;
    
    // Update memory usage (simplified calculation)
    float TotalMemoryUsage = 0.0f;
    for (const auto& MeshPair : ProcessedMeshes)
    {
        TotalMemoryUsage += MeshPair.Value.NaniteMemoryUsage / (1024.0f * 1024.0f); // Convert to MB
    }
    PerformanceStats.GPUMemoryUsageMB = TotalMemoryUsage;
    
    // Calculate compression efficiency
    float TotalOriginalSize = 0.0f;
    float TotalCompressedSize = 0.0f;
    for (const auto& MeshPair : ProcessedMeshes)
    {
        const FPCGNaniteMeshData& MeshData = MeshPair.Value;
        TotalOriginalSize += MeshData.OriginalTriangleCount * 36.0f; // Rough estimate: 36 bytes per triangle
        TotalCompressedSize += MeshData.NaniteMemoryUsage;
    }
    
    if (TotalOriginalSize > 0.0f)
    {
        PerformanceStats.CompressionEfficiency = 1.0f - (TotalCompressedSize / TotalOriginalSize);
    }
}

void APCGNaniteOptimizer::ProcessPendingMeshConversions()
{
    if (bIsProcessingMeshes)
    {
        return;
    }
    
    TSoftObjectPtr<UStaticMesh> PendingMesh;
    if (PendingMeshConversions.Dequeue(PendingMesh))
    {
        if (PendingMesh.IsValid())
        {
            bIsProcessingMeshes = true;
            
            // Process mesh conversion asynchronously
            AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, PendingMesh]()
            {
                FPCGNaniteMeshData MeshData;
                ConvertMeshToNaniteInternal(PendingMesh.Get(), MeshData);
                
                // Return to game thread to update data
                AsyncTask(ENamedThreads::GameThread, [this]()
                {
                    bIsProcessingMeshes = false;
                });
            });
        }
        else
        {
            bIsProcessingMeshes = false;
        }
    }
}

void APCGNaniteOptimizer::UpdateInstanceLODs(float DeltaTime)
{
    SCOPE_CYCLE_COUNTER(STAT_PCGNaniteLODUpdate);
    
    if (OptimizationConfig.LODStrategy == EPCGNaniteLODStrategy::Automatic)
    {
        return;
    }
    
    // Get viewer location
    FVector ViewerLocation = FVector::ZeroVector;
    if (UWorld* World = GetWorld())
    {
        if (APlayerController* PC = World->GetFirstPlayerController())
        {
            if (APawn* Pawn = PC->GetPawn())
            {
                ViewerLocation = Pawn->GetActorLocation();
            }
        }
    }
    
    // Update LOD bias for each instance
    for (FPCGNaniteInstanceData& Instance : NaniteInstances)
    {
        if (OptimizationConfig.LODStrategy == EPCGNaniteLODStrategy::DistanceBased ||
            OptimizationConfig.LODStrategy == EPCGNaniteLODStrategy::Hybrid)
        {
            Instance.LODBias = CalculateOptimalLODBias(ViewerLocation, Instance.InstanceTransform);
        }
    }
}

void APCGNaniteOptimizer::UpdateMemoryManagement()
{
    // Check if memory optimization is needed
    if (PerformanceStats.GPUMemoryUsageMB > OptimizationConfig.NaniteMemoryBudgetMB)
    {
        OptimizeMemoryUsage();
    }
}

bool APCGNaniteOptimizer::ConvertMeshToNaniteInternal(UStaticMesh* OriginalMesh, FPCGNaniteMeshData& OutMeshData)
{
    if (!OriginalMesh)
    {
        return false;
    }
    
    // Initialize mesh data
    OutMeshData.OriginalMesh = OriginalMesh;
    OutMeshData.bIsProcessed = false;
    OutMeshData.ProcessingError = TEXT("");
    
    // Get triangle count
    if (OriginalMesh->GetRenderData() && OriginalMesh->GetRenderData()->LODResources.Num() > 0)
    {
        OutMeshData.OriginalTriangleCount = OriginalMesh->GetRenderData()->LODResources[0].GetNumTriangles();
    }
    
    // Check if mesh meets minimum triangle count
    if (OutMeshData.OriginalTriangleCount < OptimizationConfig.MinTriangleCountForNanite)
    {
        OutMeshData.ProcessingError = FString::Printf(TEXT("Triangle count (%d) below minimum (%d)"), 
                                                     OutMeshData.OriginalTriangleCount, 
                                                     OptimizationConfig.MinTriangleCountForNanite);
        OutMeshData.bIsNaniteSuitable = false;
        return false;
    }
    
    // Enable Nanite on the mesh (this would normally be done in the editor)
    // For runtime, we'll simulate the conversion
    OutMeshData.NaniteMesh = OriginalMesh; // In a real implementation, this would be a converted mesh
    OutMeshData.NaniteTriangleCount = OutMeshData.OriginalTriangleCount;
    OutMeshData.NaniteMemoryUsage = OutMeshData.OriginalTriangleCount * 24; // Rough estimate
    OutMeshData.CompressionRatio = 0.7f; // Typical Nanite compression
    OutMeshData.bIsNaniteSuitable = true;
    OutMeshData.bIsProcessed = true;
    
    // Store in processed meshes
    TSoftObjectPtr<UStaticMesh> MeshPtr(OriginalMesh);
    ProcessedMeshes.Add(MeshPtr, OutMeshData);
    
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Successfully converted mesh to Nanite: %s"), *OriginalMesh->GetName());
    return true;
}

bool APCGNaniteOptimizer::ValidateMeshForNaniteInternal(UStaticMesh* Mesh) const
{
    if (!Mesh)
    {
        return false;
    }
    
    // Check if mesh has valid render data
    if (!Mesh->GetRenderData() || Mesh->GetRenderData()->LODResources.Num() == 0)
    {
        return false;
    }
    
    // Check triangle count
    int32 TriangleCount = Mesh->GetRenderData()->LODResources[0].GetNumTriangles();
    if (TriangleCount < OptimizationConfig.MinTriangleCountForNanite)
    {
        return false;
    }
    
    // Additional validation checks could be added here
    
    return true;
}

void APCGNaniteOptimizer::OptimizeMeshClustering(UStaticMesh* Mesh, const FPCGNaniteOptimizationConfig& Config)
{
    if (!Mesh)
    {
        return;
    }
    
    // Mesh clustering optimization would be implemented here
    // Implement Nanite mesh clustering algorithm
    if (!Mesh)
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGNaniteOptimizer: Invalid mesh for clustering"));
        return;
    }

    UStaticMesh* StaticMesh = Mesh;
    
    // Check if mesh already has Nanite enabled
#if WITH_EDITOR
    if (UStaticMeshEditorSubsystem* CheckSubsystem = GEditor ? GEditor->GetEditorSubsystem<UStaticMeshEditorSubsystem>() : nullptr)
    {
        FMeshNaniteSettings ExistingSettings = CheckSubsystem->GetNaniteSettings(StaticMesh);
        if (ExistingSettings.bEnabled)
        {
            UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Mesh already has Nanite enabled: %s"), *StaticMesh->GetName());
            return;
        }
    }
#endif
    
    // Validate mesh for Nanite compatibility
    FString ValidationResult;
    if (!ValidateMeshForNanite(StaticMesh, ValidationResult))
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGNaniteOptimizer: Mesh not suitable for Nanite: %s"), *StaticMesh->GetName());
        return;
    }
    
    // Enable Nanite on the static mesh using UE5.6 API
#if WITH_EDITOR
    if (UStaticMeshEditorSubsystem* StaticMeshEditorSubsystem = GEditor ? GEditor->GetEditorSubsystem<UStaticMeshEditorSubsystem>() : nullptr)
    {
        // Get current Nanite settings
        FMeshNaniteSettings NaniteSettings = StaticMeshEditorSubsystem->GetNaniteSettings(StaticMesh);
        
        // Enable Nanite
        NaniteSettings.bEnabled = true;
        
        // Configure Nanite settings based on mesh characteristics
        int32 TriangleCount = 0;
        if (StaticMesh->GetRenderData() && StaticMesh->GetRenderData()->LODResources.Num() > 0)
        {
            TriangleCount = StaticMesh->GetRenderData()->LODResources[0].GetNumTriangles();
        }
        
        // Set appropriate cluster settings based on triangle count
        if (TriangleCount > 100000)
        {
            // Keep 1% of triangles for high poly meshes
            NaniteSettings.KeepPercentTriangles = 0.01f;
        }
        else if (TriangleCount > 10000)
        {
            // Keep 2% of triangles for medium poly meshes
            NaniteSettings.KeepPercentTriangles = 0.02f;
        }
        else
        {
            // Keep 5% of triangles for low poly meshes
            NaniteSettings.KeepPercentTriangles = 0.05f;
        }
        
        // Set fallback relative error
        NaniteSettings.FallbackRelativeError = 1.0f;
        
        // Apply the settings
        StaticMeshEditorSubsystem->SetNaniteSettings(StaticMesh, NaniteSettings, true);
        
        // Mark for rebuild
        StaticMesh->PostEditChange();
        
        UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Enabled Nanite clustering for mesh: %s (Triangles: %d, KeepPercent: %.2f)"), 
               *StaticMesh->GetName(), TriangleCount, NaniteSettings.KeepPercentTriangles);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("APCGNaniteOptimizer: Failed to get StaticMeshEditorSubsystem for mesh: %s"), *StaticMesh->GetName());
    }
#else
    UE_LOG(LogTemp, Warning, TEXT("APCGNaniteOptimizer: Nanite settings can only be modified in editor builds"));
#endif

}

void APCGNaniteOptimizer::UpdateInstanceVisibilityInternal()
{
    // Get viewer location and direction
    FVector ViewerLocation = FVector::ZeroVector;
    FVector ViewDirection = FVector::ForwardVector;
    
    if (UWorld* World = GetWorld())
    {
        if (APlayerController* PC = World->GetFirstPlayerController())
        {
            if (APawn* Pawn = PC->GetPawn())
            {
                ViewerLocation = Pawn->GetActorLocation();
                ViewDirection = Pawn->GetActorForwardVector();
            }
        }
    }
    
    UpdateVisibilityFromViewpoint(ViewerLocation, ViewDirection);
}

void APCGNaniteOptimizer::CullDistantInstances(const FVector& ViewerLocation)
{
    for (FPCGNaniteInstanceData& Instance : NaniteInstances)
    {
        float Distance = FVector::Dist(ViewerLocation, Instance.InstanceTransform.GetLocation());
        float CullingDistance = Instance.CullingDistance > 0.0f ? Instance.CullingDistance : OptimizationConfig.DistanceCullingThreshold;
        
        if (Distance > CullingDistance)
        {
            Instance.bIsVisible = false;
        }
    }
}

void APCGNaniteOptimizer::UpdateStreamingPriorities(const FVector& StreamingCenter)
{
    for (FPCGNaniteInstanceData& Instance : NaniteInstances)
    {
        float Distance = FVector::Dist(StreamingCenter, Instance.InstanceTransform.GetLocation());
        Instance.StreamingPriority = CalculateInstancePriority(Instance, StreamingCenter);
    }
}

float APCGNaniteOptimizer::CalculateInstancePriority(const FPCGNaniteInstanceData& Instance, const FVector& ViewerLocation) const
{
    float Distance = FVector::Dist(ViewerLocation, Instance.InstanceTransform.GetLocation());
    float MaxDistance = OptimizationConfig.DistanceCullingThreshold;
    
    // Higher priority for closer instances
    float DistancePriority = FMath::Clamp(1.0f - (Distance / MaxDistance), 0.1f, 1.0f);
    
    // Factor in triangle count (higher detail = higher priority when close)
    float DetailPriority = FMath::Clamp(Instance.MeshData.NaniteTriangleCount / 10000.0f, 0.5f, 2.0f);
    
    return DistancePriority * DetailPriority;
}

bool APCGNaniteOptimizer::ShouldInstanceBeVisible(const FPCGNaniteInstanceData& Instance, const FVector& ViewerLocation, const FVector& ViewDirection) const
{
    // Distance culling
    float Distance = FVector::Dist(ViewerLocation, Instance.InstanceTransform.GetLocation());
    float CullingDistance = Instance.CullingDistance > 0.0f ? Instance.CullingDistance : OptimizationConfig.DistanceCullingThreshold;
    
    if (Distance > CullingDistance)
    {
        return false;
    }
    
    // Frustum culling (simplified)
    FVector ToInstance = (Instance.InstanceTransform.GetLocation() - ViewerLocation).GetSafeNormal();
    float DotProduct = FVector::DotProduct(ViewDirection, ToInstance);
    
    // If instance is behind viewer (dot product < 0), cull it
    if (DotProduct < -0.1f)
    {
        return false;
    }
    
    return true;
}

void APCGNaniteOptimizer::InitializeNaniteSubsystems()
{
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Initializing Nanite subsystems"));
    
    // Initialize streaming manager via UAssetManager
    UAssetManager& AssetManager = UAssetManager::Get();
    AssetManager.GetStreamableManager().RequestAsyncLoad(TArray<FSoftObjectPath>(), FStreamableDelegate());
    
    // Initialize render fence
    RenderFence.BeginFence();
}

void APCGNaniteOptimizer::ValidateNaniteSupport()
{
    if (!IsNaniteSupported())
    {
        UE_LOG(LogTemp, Warning, TEXT("APCGNaniteOptimizer: Nanite is not supported on this platform. Optimizer will run in compatibility mode."));
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Nanite support validated successfully"));
    }
}

void APCGNaniteOptimizer::SetupDefaultOptimizationSettings()
{
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Setting up default optimization settings"));
    
    // Adjust settings based on platform capabilities
    if (IsNaniteSupported())
    {
        OptimizationConfig.bAutoConvertToNanite = true;
        OptimizationConfig.bEnableGPUDrivenRendering = true;
        OptimizationConfig.bEnableOcclusionCulling = true;
    }
    else
    {
        OptimizationConfig.bAutoConvertToNanite = false;
        OptimizationConfig.bEnableGPUDrivenRendering = false;
        OptimizationConfig.bEnableOcclusionCulling = false;
    }
}

// === Event Handlers ===

void APCGNaniteOptimizer::OnMeshConversionComplete(UStaticMesh* OriginalMesh, UStaticMesh* NaniteMesh)
{
    UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Mesh conversion completed for %s"), 
           OriginalMesh ? *OriginalMesh->GetName() : TEXT("Unknown"));
    
    // Update processed meshes data
    if (OriginalMesh)
    {
        TSoftObjectPtr<UStaticMesh> MeshPtr(OriginalMesh);
        if (ProcessedMeshes.Contains(MeshPtr))
        {
            FPCGNaniteMeshData& MeshData = ProcessedMeshes[MeshPtr];
            MeshData.NaniteMesh = NaniteMesh;
            MeshData.bIsProcessed = true;
        }
    }
}

void APCGNaniteOptimizer::OnNaniteStreamingUpdate(const FVector& StreamingCenter, float StreamingRadius)
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("APCGNaniteOptimizer: Nanite streaming update at %.2f, %.2f, %.2f with radius %.2f"), 
           StreamingCenter.X, StreamingCenter.Y, StreamingCenter.Z, StreamingRadius);
    
    // Update streaming priorities
    UpdateStreamingPriorities(StreamingCenter);
    
    // Stream Nanite data
    StreamNaniteData(StreamingCenter, StreamingRadius);
}

void APCGNaniteOptimizer::OnPerformanceThresholdExceeded(float CurrentUsage, float ThresholdUsage)
{
    UE_LOG(LogTemp, Warning, TEXT("APCGNaniteOptimizer: Performance threshold exceeded - Current: %.2f, Threshold: %.2f"), 
           CurrentUsage, ThresholdUsage);
    
    // Trigger memory optimization
    OptimizeMemoryUsage();
    
    // Reduce optimization level temporarily
    if (OptimizationConfig.OptimizationLevel != EPCGNaniteOptimizationLevel::Conservative)
    {
        EPCGNaniteOptimizationLevel OriginalLevel = OptimizationConfig.OptimizationLevel;
        OptimizationConfig.OptimizationLevel = EPCGNaniteOptimizationLevel::Conservative;
        
        UE_LOG(LogTemp, Log, TEXT("APCGNaniteOptimizer: Temporarily reduced optimization level from %d to %d"),
               (int32)OriginalLevel, (int32)OptimizationConfig.OptimizationLevel);
    }
}

