// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AWallCollisionManager.h"

#ifdef AURA_AWallCollisionManager_generated_h
#error "AWallCollisionManager.generated.h already included, missing '#pragma once' in AWallCollisionManager.h"
#endif
#define AURA_AWallCollisionManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class UPrimitiveComponent;
class UStaticMeshComponent;
enum class EWallOrientation : uint8;
struct FGateData;
struct FHitResult;
struct FWallCollisionData;
struct FWallMaterialConfig;
struct FWallSection;

// ********** Begin ScriptStruct FWallSection ******************************************************
#define FID_Aura_Source_Aura_Public_AWallCollisionManager_h_60_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWallSection_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FWallSection;
// ********** End ScriptStruct FWallSection ********************************************************

// ********** Begin ScriptStruct FWallCollisionData ************************************************
#define FID_Aura_Source_Aura_Public_AWallCollisionManager_h_123_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWallCollisionData_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FWallCollisionData;
// ********** End ScriptStruct FWallCollisionData **************************************************

// ********** Begin ScriptStruct FWallMaterialConfig ***********************************************
#define FID_Aura_Source_Aura_Public_AWallCollisionManager_h_158_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWallMaterialConfig_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FWallMaterialConfig;
// ********** End ScriptStruct FWallMaterialConfig *************************************************

// ********** Begin ScriptStruct FGateData *********************************************************
#define FID_Aura_Source_Aura_Public_AWallCollisionManager_h_193_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FGateData_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FGateData;
// ********** End ScriptStruct FGateData ***********************************************************

// ********** Begin ScriptStruct FPatrolArea *******************************************************
#define FID_Aura_Source_Aura_Public_AWallCollisionManager_h_232_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPatrolArea_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FPatrolArea;
// ********** End ScriptStruct FPatrolArea *********************************************************

// ********** Begin Class AWallCollisionManager ****************************************************
#define FID_Aura_Source_Aura_Public_AWallCollisionManager_h_258_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetWallThickness); \
	DECLARE_FUNCTION(execSetWallHeight); \
	DECLARE_FUNCTION(execSetWallMaterial); \
	DECLARE_FUNCTION(execGetMapArea); \
	DECLARE_FUNCTION(execGetMapCenter); \
	DECLARE_FUNCTION(execGetWallPerimeter); \
	DECLARE_FUNCTION(execGetTotalWallLength); \
	DECLARE_FUNCTION(execGetMaterialConfig); \
	DECLARE_FUNCTION(execGetGates); \
	DECLARE_FUNCTION(execGetWallSections); \
	DECLARE_FUNCTION(execDistancePointToLine); \
	DECLARE_FUNCTION(execProjectPointOntoLine); \
	DECLARE_FUNCTION(execIsPointInsidePolygon); \
	DECLARE_FUNCTION(execCalculateAngleBetweenVectors); \
	DECLARE_FUNCTION(execRotatePointAroundCenter); \
	DECLARE_FUNCTION(execOnActorEndOverlap); \
	DECLARE_FUNCTION(execOnActorBeginOverlap); \
	DECLARE_FUNCTION(execOnWallHit); \
	DECLARE_FUNCTION(execDrawDebugPatrolPath); \
	DECLARE_FUNCTION(execDrawDebugGates); \
	DECLARE_FUNCTION(execDrawDebugCollisions); \
	DECLARE_FUNCTION(execDrawDebugWalls); \
	DECLARE_FUNCTION(execValidateCollisionSetup); \
	DECLARE_FUNCTION(execValidateWallGeometry); \
	DECLARE_FUNCTION(execGeneratePatrolPath); \
	DECLARE_FUNCTION(execGetSafePositionNearWalls); \
	DECLARE_FUNCTION(execIsPathClearOfWalls); \
	DECLARE_FUNCTION(execFindPathAroundWalls); \
	DECLARE_FUNCTION(execGetNearestGatePosition); \
	DECLARE_FUNCTION(execAuthorizeActorForGate); \
	DECLARE_FUNCTION(execCanActorPassThroughGate); \
	DECLARE_FUNCTION(execIsGateOpen); \
	DECLARE_FUNCTION(execCloseGate); \
	DECLARE_FUNCTION(execOpenGate); \
	DECLARE_FUNCTION(execCreateGates); \
	DECLARE_FUNCTION(execLineIntersectsWall); \
	DECLARE_FUNCTION(execGetWallIntersectionPoints); \
	DECLARE_FUNCTION(execGetDistanceToNearestWall); \
	DECLARE_FUNCTION(execGetClosestPointOnWalls); \
	DECLARE_FUNCTION(execIsPositionInsideWalls); \
	DECLARE_FUNCTION(execCheckCollisionWithWalls); \
	DECLARE_FUNCTION(execUpdateWallMeshes); \
	DECLARE_FUNCTION(execApplyWallMaterial); \
	DECLARE_FUNCTION(execCreateWallSegment); \
	DECLARE_FUNCTION(execCreateWallMeshes); \
	DECLARE_FUNCTION(execGetWallCenter); \
	DECLARE_FUNCTION(execCalculateWallLength); \
	DECLARE_FUNCTION(execCalculateWallNormal); \
	DECLARE_FUNCTION(execGenerateCurvedWallPoints); \
	DECLARE_FUNCTION(execGenerateCornerWallPoints); \
	DECLARE_FUNCTION(execGenerateStraightWallPoints); \
	DECLARE_FUNCTION(execCalculateWallBoundaries); \
	DECLARE_FUNCTION(execSetupCollisionSystem); \
	DECLARE_FUNCTION(execGenerateWallGeometry); \
	DECLARE_FUNCTION(execInitializeWallSystem);


AURA_API UClass* Z_Construct_UClass_AWallCollisionManager_NoRegister();

#define FID_Aura_Source_Aura_Public_AWallCollisionManager_h_258_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAWallCollisionManager(); \
	friend struct Z_Construct_UClass_AWallCollisionManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_AWallCollisionManager_NoRegister(); \
public: \
	DECLARE_CLASS2(AWallCollisionManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_AWallCollisionManager_NoRegister) \
	DECLARE_SERIALIZER(AWallCollisionManager)


#define FID_Aura_Source_Aura_Public_AWallCollisionManager_h_258_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AWallCollisionManager(AWallCollisionManager&&) = delete; \
	AWallCollisionManager(const AWallCollisionManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AWallCollisionManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AWallCollisionManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AWallCollisionManager) \
	NO_API virtual ~AWallCollisionManager();


#define FID_Aura_Source_Aura_Public_AWallCollisionManager_h_255_PROLOG
#define FID_Aura_Source_Aura_Public_AWallCollisionManager_h_258_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_AWallCollisionManager_h_258_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_AWallCollisionManager_h_258_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_AWallCollisionManager_h_258_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AWallCollisionManager;

// ********** End Class AWallCollisionManager ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_AWallCollisionManager_h

// ********** Begin Enum EWallType *****************************************************************
#define FOREACH_ENUM_EWALLTYPE(op) \
	op(EWallType::Straight) \
	op(EWallType::Corner) \
	op(EWallType::Gate) \
	op(EWallType::Curved) 

enum class EWallType : uint8;
template<> struct TIsUEnumClass<EWallType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EWallType>();
// ********** End Enum EWallType *******************************************************************

// ********** Begin Enum EWallOrientation **********************************************************
#define FOREACH_ENUM_EWALLORIENTATION(op) \
	op(EWallOrientation::North) \
	op(EWallOrientation::South) \
	op(EWallOrientation::East) \
	op(EWallOrientation::West) \
	op(EWallOrientation::NorthEast) \
	op(EWallOrientation::NorthWest) \
	op(EWallOrientation::SouthEast) \
	op(EWallOrientation::SouthWest) 

enum class EWallOrientation : uint8;
template<> struct TIsUEnumClass<EWallOrientation> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EWallOrientation>();
// ********** End Enum EWallOrientation ************************************************************

// ********** Begin Enum EWallMaterial *************************************************************
#define FOREACH_ENUM_EWALLMATERIAL(op) \
	op(EWallMaterial::Stone) \
	op(EWallMaterial::Metal) \
	op(EWallMaterial::Wood) \
	op(EWallMaterial::Crystal) \
	op(EWallMaterial::Magical) 

enum class EWallMaterial : uint8;
template<> struct TIsUEnumClass<EWallMaterial> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EWallMaterial>();
// ********** End Enum EWallMaterial ***************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
