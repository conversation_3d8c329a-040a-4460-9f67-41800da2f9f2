/NOLOGO
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/DEF
/NAME:"UnrealEditor-Aura-Win64-DebugGame.dll"
/IGNORE:4221
/NODEFAULTLIB
"C:/Aura/Intermediate/Build/Win64/x64/AuraEditor/DebugGame/UnrealEd/SharedPCH.UnrealEd.Project.NonOptimized.RTTI.ValApi.ValExpApi.Cpp20.h.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/Module.Aura.gen.1.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/Module.Aura.gen.2.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/Module.Aura.gen.3.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/Module.Aura.gen.4.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/Module.Aura.gen.5.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/Module.Aura.gen.6.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/Module.Aura.gen.7.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/Module.Aura.gen.8.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/Module.Aura.gen.9.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/Module.Aura.gen.10.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/Aura.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/ABaronAuracronManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/ADragonPrismalManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/AGeometricValidator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/ALaneManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/AMapManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/AMinionWaveManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/APCGCacheManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/APCGChaosIntegrator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/APCGLumenIntegrator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/APCGNaniteOptimizer.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/APCGStreamingManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/APCGWorldPartitionManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/AProceduralMapGenerator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/ARiverPrismalManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/AWallCollisionManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/implementacao_automatizada.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/testes_precisao_geometrica.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/UPCGPerformanceProfiler.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/UPCGQualityValidator.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/UPCGVersionManager.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/PerModuleInline.gen.cpp.obj"
"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/Default.rc2.res"
/OUT:"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/DebugGame/Aura/UnrealEditor-Aura-Win64-DebugGame.lib"