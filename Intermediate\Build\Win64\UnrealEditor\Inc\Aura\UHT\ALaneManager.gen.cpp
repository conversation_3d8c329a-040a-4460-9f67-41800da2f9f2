// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "ALaneManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeALaneManager() {}

// ********** Begin Cross Module References ********************************************************
AURA_API UClass* Z_Construct_UClass_ALaneManager();
AURA_API UClass* Z_Construct_UClass_ALaneManager_NoRegister();
AURA_API UClass* Z_Construct_UClass_ARiverPrismalManager_NoRegister();
AURA_API UEnum* Z_Construct_UEnum_Aura_ELaneManagerType();
AURA_API UEnum* Z_Construct_UEnum_Aura_ETeam();
AURA_API UEnum* Z_Construct_UEnum_Aura_ETowerType();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature();
AURA_API UFunction* Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FCachedPath();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FLaneData();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FPathNode();
AURA_API UScriptStruct* Z_Construct_UScriptStruct_FTowerData();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_Aura();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ELaneManagerType **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ELaneManagerType;
static UEnum* ELaneManagerType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ELaneManagerType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ELaneManagerType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Aura_ELaneManagerType, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("ELaneManagerType"));
	}
	return Z_Registration_Info_UEnum_ELaneManagerType.OuterSingleton;
}
template<> AURA_API UEnum* StaticEnum<ELaneManagerType>()
{
	return ELaneManagerType_StaticEnum();
}
struct Z_Construct_UEnum_Aura_ELaneManagerType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Central.DisplayName", "Lane Central" },
		{ "Central.Name", "ELaneManagerType::Central" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums para tipos de lanes e torres\n" },
#endif
		{ "Inferior.DisplayName", "Lane Inferior" },
		{ "Inferior.Name", "ELaneManagerType::Inferior" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
		{ "Superior.DisplayName", "Lane Superior" },
		{ "Superior.Name", "ELaneManagerType::Superior" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums para tipos de lanes e torres" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ELaneManagerType::Superior", (int64)ELaneManagerType::Superior },
		{ "ELaneManagerType::Central", (int64)ELaneManagerType::Central },
		{ "ELaneManagerType::Inferior", (int64)ELaneManagerType::Inferior },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Aura_ELaneManagerType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	"ELaneManagerType",
	"ELaneManagerType",
	Z_Construct_UEnum_Aura_ELaneManagerType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ELaneManagerType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Aura_ELaneManagerType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Aura_ELaneManagerType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Aura_ELaneManagerType()
{
	if (!Z_Registration_Info_UEnum_ELaneManagerType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ELaneManagerType.InnerSingleton, Z_Construct_UEnum_Aura_ELaneManagerType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ELaneManagerType.InnerSingleton;
}
// ********** End Enum ELaneManagerType ************************************************************

// ********** Begin ScriptStruct FLaneData *********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FLaneData;
class UScriptStruct* FLaneData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FLaneData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FLaneData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLaneData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("LaneData"));
	}
	return Z_Registration_Info_UScriptStruct_FLaneData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FLaneData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estruturas de dados para lanes e torres\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estruturas de dados para lanes e torres" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneType_MetaData[] = {
		{ "Category", "Lane" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneWidth_MetaData[] = {
		{ "Category", "Lane" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Waypoints_MetaData[] = {
		{ "Category", "Lane" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPosition_MetaData[] = {
		{ "Category", "Lane" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPosition_MetaData[] = {
		{ "Category", "Lane" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LaneWidth;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Waypoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Waypoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPosition;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLaneData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneData, LaneType), Z_Construct_UEnum_Aura_ELaneManagerType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneType_MetaData), NewProp_LaneType_MetaData) }; // 281285961
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_LaneWidth = { "LaneWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneData, LaneWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneWidth_MetaData), NewProp_LaneWidth_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_Waypoints_Inner = { "Waypoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_Waypoints = { "Waypoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneData, Waypoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Waypoints_MetaData), NewProp_Waypoints_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_StartPosition = { "StartPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneData, StartPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPosition_MetaData), NewProp_StartPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_EndPosition = { "EndPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneData, EndPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPosition_MetaData), NewProp_EndPosition_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLaneData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_LaneType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_LaneWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_Waypoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_Waypoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_StartPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneData_Statics::NewProp_EndPosition,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLaneData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLaneData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"LaneData",
	Z_Construct_UScriptStruct_FLaneData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLaneData_Statics::PropPointers),
	sizeof(FLaneData),
	alignof(FLaneData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLaneData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLaneData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLaneData()
{
	if (!Z_Registration_Info_UScriptStruct_FLaneData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FLaneData.InnerSingleton, Z_Construct_UScriptStruct_FLaneData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FLaneData.InnerSingleton;
}
// ********** End ScriptStruct FLaneData ***********************************************************

// ********** Begin ScriptStruct FTowerData ********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FTowerData;
class UScriptStruct* FTowerData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FTowerData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FTowerData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTowerData, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("TowerData"));
	}
	return Z_Registration_Info_UScriptStruct_FTowerData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FTowerData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerType_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseRadius_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TopRadius_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Height_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackRange_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "Tower" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Campos adicionais necess\xc3\xa1rios para compila\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Campos adicionais necess\xc3\xa1rios para compila\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HP_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHP_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackDamage_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackSpeed_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDestroyed_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Team_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "Category", "Tower" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TowerType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TowerType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TopRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackRange;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Health;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HP;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxHP;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackSpeed;
	static void NewProp_bIsDestroyed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDestroyed;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Team_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Team;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTowerData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_TowerType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_TowerType = { "TowerType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, TowerType), Z_Construct_UEnum_Aura_ETowerType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerType_MetaData), NewProp_TowerType_MetaData) }; // 4294645771
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_BaseRadius = { "BaseRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, BaseRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseRadius_MetaData), NewProp_BaseRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_TopRadius = { "TopRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, TopRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TopRadius_MetaData), NewProp_TopRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, Height), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Height_MetaData), NewProp_Height_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_AttackRange = { "AttackRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, AttackRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackRange_MetaData), NewProp_AttackRange_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_HP = { "HP", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, HP), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HP_MetaData), NewProp_HP_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_MaxHP = { "MaxHP", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, MaxHP), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHP_MetaData), NewProp_MaxHP_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_AttackDamage = { "AttackDamage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, AttackDamage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackDamage_MetaData), NewProp_AttackDamage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_AttackSpeed = { "AttackSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, AttackSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackSpeed_MetaData), NewProp_AttackSpeed_MetaData) };
void Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_bIsDestroyed_SetBit(void* Obj)
{
	((FTowerData*)Obj)->bIsDestroyed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_bIsDestroyed = { "bIsDestroyed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTowerData), &Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_bIsDestroyed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDestroyed_MetaData), NewProp_bIsDestroyed_MetaData) };
void Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FTowerData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTowerData), &Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_Team_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_Team = { "Team", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, Team), Z_Construct_UEnum_Aura_ETeam, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Team_MetaData), NewProp_Team_MetaData) }; // 1024800466
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTowerData, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTowerData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_TowerType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_TowerType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_BaseRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_TopRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_AttackRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_Health,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_HP,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_MaxHP,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_AttackDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_AttackSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_bIsDestroyed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_Team_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_Team,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTowerData_Statics::NewProp_Radius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTowerData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTowerData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"TowerData",
	Z_Construct_UScriptStruct_FTowerData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTowerData_Statics::PropPointers),
	sizeof(FTowerData),
	alignof(FTowerData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTowerData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTowerData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTowerData()
{
	if (!Z_Registration_Info_UScriptStruct_FTowerData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FTowerData.InnerSingleton, Z_Construct_UScriptStruct_FTowerData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FTowerData.InnerSingleton;
}
// ********** End ScriptStruct FTowerData **********************************************************

// ********** Begin ScriptStruct FPathNode *********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPathNode;
class UScriptStruct* FPathNode::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPathNode.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPathNode.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPathNode, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("PathNode"));
	}
	return Z_Registration_Info_UScriptStruct_FPathNode.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPathNode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para pathfinding A*\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para pathfinding A*" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GCost_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HCost_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FCost_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParentIndex_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parent_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GCost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HCost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FCost;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ParentIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Parent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPathNode>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPathNode_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathNode, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPathNode_Statics::NewProp_GCost = { "GCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathNode, GCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GCost_MetaData), NewProp_GCost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPathNode_Statics::NewProp_HCost = { "HCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathNode, HCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HCost_MetaData), NewProp_HCost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPathNode_Statics::NewProp_FCost = { "FCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathNode, FCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FCost_MetaData), NewProp_FCost_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPathNode_Statics::NewProp_ParentIndex = { "ParentIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathNode, ParentIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParentIndex_MetaData), NewProp_ParentIndex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPathNode_Statics::NewProp_Parent = { "Parent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPathNode, Parent), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parent_MetaData), NewProp_Parent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPathNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathNode_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathNode_Statics::NewProp_GCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathNode_Statics::NewProp_HCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathNode_Statics::NewProp_FCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathNode_Statics::NewProp_ParentIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPathNode_Statics::NewProp_Parent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPathNode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPathNode_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"PathNode",
	Z_Construct_UScriptStruct_FPathNode_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPathNode_Statics::PropPointers),
	sizeof(FPathNode),
	alignof(FPathNode),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPathNode_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPathNode_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPathNode()
{
	if (!Z_Registration_Info_UScriptStruct_FPathNode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPathNode.InnerSingleton, Z_Construct_UScriptStruct_FPathNode_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPathNode.InnerSingleton;
}
// ********** End ScriptStruct FPathNode ***********************************************************

// ********** Begin ScriptStruct FCachedPath *******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FCachedPath;
class UScriptStruct* FCachedPath::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FCachedPath.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FCachedPath.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FCachedPath, (UObject*)Z_Construct_UPackage__Script_Aura(), TEXT("CachedPath"));
	}
	return Z_Registration_Info_UScriptStruct_FCachedPath.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FCachedPath_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Estrutura para cache de pathfinding\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para cache de pathfinding" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Path_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathLength_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValid_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Path_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Path;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PathLength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CreationTime;
	static void NewProp_bIsValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValid;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FCachedPath>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_Path_Inner = { "Path", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_Path = { "Path", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCachedPath, Path), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Path_MetaData), NewProp_Path_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCachedPath, Timestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_PathLength = { "PathLength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCachedPath, PathLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathLength_MetaData), NewProp_PathLength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCachedPath, CreationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
void Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_bIsValid_SetBit(void* Obj)
{
	((FCachedPath*)Obj)->bIsValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_bIsValid = { "bIsValid", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FCachedPath), &Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_bIsValid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValid_MetaData), NewProp_bIsValid_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FCachedPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_Path_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_Path,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_PathLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCachedPath_Statics::NewProp_bIsValid,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCachedPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FCachedPath_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
	nullptr,
	&NewStructOps,
	"CachedPath",
	Z_Construct_UScriptStruct_FCachedPath_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCachedPath_Statics::PropPointers),
	sizeof(FCachedPath),
	alignof(FCachedPath),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCachedPath_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FCachedPath_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FCachedPath()
{
	if (!Z_Registration_Info_UScriptStruct_FCachedPath.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FCachedPath.InnerSingleton, Z_Construct_UScriptStruct_FCachedPath_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FCachedPath.InnerSingleton;
}
// ********** End ScriptStruct FCachedPath *********************************************************

// ********** Begin Delegate FOnTowerDestroyed *****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnTowerDestroyed_Parms
	{
		FTowerData DestroyedTower;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegates para sistema de eventos\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates para sistema de eventos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestroyedTower_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestroyedTower;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature_Statics::NewProp_DestroyedTower = { "DestroyedTower", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnTowerDestroyed_Parms, DestroyedTower), Z_Construct_UScriptStruct_FTowerData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestroyedTower_MetaData), NewProp_DestroyedTower_MetaData) }; // 1218475868
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature_Statics::NewProp_DestroyedTower,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnTowerDestroyed__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature_Statics::_Script_Aura_eventOnTowerDestroyed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature_Statics::_Script_Aura_eventOnTowerDestroyed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnTowerDestroyed_DelegateWrapper(const FMulticastScriptDelegate& OnTowerDestroyed, FTowerData const& DestroyedTower)
{
	struct _Script_Aura_eventOnTowerDestroyed_Parms
	{
		FTowerData DestroyedTower;
	};
	_Script_Aura_eventOnTowerDestroyed_Parms Parms;
	Parms.DestroyedTower=DestroyedTower;
	OnTowerDestroyed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTowerDestroyed *******************************************************

// ********** Begin Delegate FOnTowerSpawned *******************************************************
struct Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnTowerSpawned_Parms
	{
		FTowerData SpawnedTower;
		AActor* TowerActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnedTower_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnedTower;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TowerActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::NewProp_SpawnedTower = { "SpawnedTower", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnTowerSpawned_Parms, SpawnedTower), Z_Construct_UScriptStruct_FTowerData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnedTower_MetaData), NewProp_SpawnedTower_MetaData) }; // 1218475868
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::NewProp_TowerActor = { "TowerActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnTowerSpawned_Parms, TowerActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::NewProp_SpawnedTower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::NewProp_TowerActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnTowerSpawned__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::_Script_Aura_eventOnTowerSpawned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::_Script_Aura_eventOnTowerSpawned_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnTowerSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnTowerSpawned, FTowerData const& SpawnedTower, AActor* TowerActor)
{
	struct _Script_Aura_eventOnTowerSpawned_Parms
	{
		FTowerData SpawnedTower;
		AActor* TowerActor;
	};
	_Script_Aura_eventOnTowerSpawned_Parms Parms;
	Parms.SpawnedTower=SpawnedTower;
	Parms.TowerActor=TowerActor;
	OnTowerSpawned.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTowerSpawned *********************************************************

// ********** Begin Delegate FOnPathfindingCacheUpdated ********************************************
struct Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnPathfindingCacheUpdated_Parms
	{
		int32 CacheSize;
		float UpdateTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CacheSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::NewProp_CacheSize = { "CacheSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPathfindingCacheUpdated_Parms, CacheSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::NewProp_UpdateTime = { "UpdateTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnPathfindingCacheUpdated_Parms, UpdateTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::NewProp_CacheSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::NewProp_UpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnPathfindingCacheUpdated__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::_Script_Aura_eventOnPathfindingCacheUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::_Script_Aura_eventOnPathfindingCacheUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPathfindingCacheUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnPathfindingCacheUpdated, int32 CacheSize, float UpdateTime)
{
	struct _Script_Aura_eventOnPathfindingCacheUpdated_Parms
	{
		int32 CacheSize;
		float UpdateTime;
	};
	_Script_Aura_eventOnPathfindingCacheUpdated_Parms Parms;
	Parms.CacheSize=CacheSize;
	Parms.UpdateTime=UpdateTime;
	OnPathfindingCacheUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPathfindingCacheUpdated **********************************************

// ********** Begin Delegate FOnLaneInitialized ****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnLaneInitialized_Parms
	{
		ELaneManagerType LaneType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnLaneInitialized_Parms, LaneType), Z_Construct_UEnum_Aura_ELaneManagerType, METADATA_PARAMS(0, nullptr) }; // 281285961
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::NewProp_LaneType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnLaneInitialized__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::_Script_Aura_eventOnLaneInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::_Script_Aura_eventOnLaneInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLaneInitialized_DelegateWrapper(const FMulticastScriptDelegate& OnLaneInitialized, ELaneManagerType LaneType)
{
	struct _Script_Aura_eventOnLaneInitialized_Parms
	{
		ELaneManagerType LaneType;
	};
	_Script_Aura_eventOnLaneInitialized_Parms Parms;
	Parms.LaneType=LaneType;
	OnLaneInitialized.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLaneInitialized ******************************************************

// ********** Begin Delegate FOnWaypointReached ****************************************************
struct Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnWaypointReached_Parms
	{
		FVector WaypointPosition;
		ELaneManagerType LaneType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaypointPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WaypointPosition;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::NewProp_WaypointPosition = { "WaypointPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnWaypointReached_Parms, WaypointPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaypointPosition_MetaData), NewProp_WaypointPosition_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_Aura_eventOnWaypointReached_Parms, LaneType), Z_Construct_UEnum_Aura_ELaneManagerType, METADATA_PARAMS(0, nullptr) }; // 281285961
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::NewProp_WaypointPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::NewProp_LaneType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnWaypointReached__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::_Script_Aura_eventOnWaypointReached_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::_Script_Aura_eventOnWaypointReached_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnWaypointReached_DelegateWrapper(const FMulticastScriptDelegate& OnWaypointReached, FVector const& WaypointPosition, ELaneManagerType LaneType)
{
	struct _Script_Aura_eventOnWaypointReached_Parms
	{
		FVector WaypointPosition;
		ELaneManagerType LaneType;
	};
	_Script_Aura_eventOnWaypointReached_Parms Parms;
	Parms.WaypointPosition=WaypointPosition;
	Parms.LaneType=LaneType;
	OnWaypointReached.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnWaypointReached ******************************************************

// ********** Begin Delegate FOnRiverIntegrationStatusChanged **************************************
struct Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics
{
	struct _Script_Aura_eventOnRiverIntegrationStatusChanged_Parms
	{
		bool bIsConnected;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsConnected_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsConnected;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::NewProp_bIsConnected_SetBit(void* Obj)
{
	((_Script_Aura_eventOnRiverIntegrationStatusChanged_Parms*)Obj)->bIsConnected = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::NewProp_bIsConnected = { "bIsConnected", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_Aura_eventOnRiverIntegrationStatusChanged_Parms), &Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::NewProp_bIsConnected_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::NewProp_bIsConnected,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_Aura, nullptr, "OnRiverIntegrationStatusChanged__DelegateSignature", Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::_Script_Aura_eventOnRiverIntegrationStatusChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::_Script_Aura_eventOnRiverIntegrationStatusChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRiverIntegrationStatusChanged_DelegateWrapper(const FMulticastScriptDelegate& OnRiverIntegrationStatusChanged, bool bIsConnected)
{
	struct _Script_Aura_eventOnRiverIntegrationStatusChanged_Parms
	{
		bool bIsConnected;
	};
	_Script_Aura_eventOnRiverIntegrationStatusChanged_Parms Parms;
	Parms.bIsConnected=bIsConnected ? true : false;
	OnRiverIntegrationStatusChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRiverIntegrationStatusChanged ****************************************

// ********** Begin Class ALaneManager Function BroadcastLaneInitialized ***************************
struct Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics
{
	struct LaneManager_eventBroadcastLaneInitialized_Parms
	{
		ELaneManagerType LaneType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventBroadcastLaneInitialized_Parms, LaneType), Z_Construct_UEnum_Aura_ELaneManagerType, METADATA_PARAMS(0, nullptr) }; // 281285961
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::NewProp_LaneType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "BroadcastLaneInitialized", Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::LaneManager_eventBroadcastLaneInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::LaneManager_eventBroadcastLaneInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execBroadcastLaneInitialized)
{
	P_GET_ENUM(ELaneManagerType,Z_Param_LaneType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BroadcastLaneInitialized(ELaneManagerType(Z_Param_LaneType));
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function BroadcastLaneInitialized *****************************

// ********** Begin Class ALaneManager Function BroadcastPathfindingCacheUpdated *******************
struct Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics
{
	struct LaneManager_eventBroadcastPathfindingCacheUpdated_Parms
	{
		int32 CacheSize;
		float UpdateTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CacheSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::NewProp_CacheSize = { "CacheSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventBroadcastPathfindingCacheUpdated_Parms, CacheSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::NewProp_UpdateTime = { "UpdateTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventBroadcastPathfindingCacheUpdated_Parms, UpdateTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::NewProp_CacheSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::NewProp_UpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "BroadcastPathfindingCacheUpdated", Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::LaneManager_eventBroadcastPathfindingCacheUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::LaneManager_eventBroadcastPathfindingCacheUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execBroadcastPathfindingCacheUpdated)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_CacheSize);
	P_GET_PROPERTY(FFloatProperty,Z_Param_UpdateTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BroadcastPathfindingCacheUpdated(Z_Param_CacheSize,Z_Param_UpdateTime);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function BroadcastPathfindingCacheUpdated *********************

// ********** Begin Class ALaneManager Function BroadcastRiverIntegrationStatusChanged *************
struct Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics
{
	struct LaneManager_eventBroadcastRiverIntegrationStatusChanged_Parms
	{
		bool bIsConnected;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsConnected_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsConnected;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::NewProp_bIsConnected_SetBit(void* Obj)
{
	((LaneManager_eventBroadcastRiverIntegrationStatusChanged_Parms*)Obj)->bIsConnected = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::NewProp_bIsConnected = { "bIsConnected", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneManager_eventBroadcastRiverIntegrationStatusChanged_Parms), &Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::NewProp_bIsConnected_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::NewProp_bIsConnected,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "BroadcastRiverIntegrationStatusChanged", Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::LaneManager_eventBroadcastRiverIntegrationStatusChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::LaneManager_eventBroadcastRiverIntegrationStatusChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execBroadcastRiverIntegrationStatusChanged)
{
	P_GET_UBOOL(Z_Param_bIsConnected);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BroadcastRiverIntegrationStatusChanged(Z_Param_bIsConnected);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function BroadcastRiverIntegrationStatusChanged ***************

// ********** Begin Class ALaneManager Function BroadcastTowerDestroyed ****************************
struct Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed_Statics
{
	struct LaneManager_eventBroadcastTowerDestroyed_Parms
	{
		FTowerData DestroyedTower;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de eventos\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de eventos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestroyedTower_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestroyedTower;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed_Statics::NewProp_DestroyedTower = { "DestroyedTower", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventBroadcastTowerDestroyed_Parms, DestroyedTower), Z_Construct_UScriptStruct_FTowerData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestroyedTower_MetaData), NewProp_DestroyedTower_MetaData) }; // 1218475868
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed_Statics::NewProp_DestroyedTower,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "BroadcastTowerDestroyed", Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed_Statics::LaneManager_eventBroadcastTowerDestroyed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed_Statics::LaneManager_eventBroadcastTowerDestroyed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execBroadcastTowerDestroyed)
{
	P_GET_STRUCT_REF(FTowerData,Z_Param_Out_DestroyedTower);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BroadcastTowerDestroyed(Z_Param_Out_DestroyedTower);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function BroadcastTowerDestroyed ******************************

// ********** Begin Class ALaneManager Function BroadcastTowerSpawned ******************************
struct Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics
{
	struct LaneManager_eventBroadcastTowerSpawned_Parms
	{
		FTowerData SpawnedTower;
		AActor* TowerActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnedTower_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnedTower;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TowerActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::NewProp_SpawnedTower = { "SpawnedTower", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventBroadcastTowerSpawned_Parms, SpawnedTower), Z_Construct_UScriptStruct_FTowerData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnedTower_MetaData), NewProp_SpawnedTower_MetaData) }; // 1218475868
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::NewProp_TowerActor = { "TowerActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventBroadcastTowerSpawned_Parms, TowerActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::NewProp_SpawnedTower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::NewProp_TowerActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "BroadcastTowerSpawned", Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::LaneManager_eventBroadcastTowerSpawned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::LaneManager_eventBroadcastTowerSpawned_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execBroadcastTowerSpawned)
{
	P_GET_STRUCT_REF(FTowerData,Z_Param_Out_SpawnedTower);
	P_GET_OBJECT(AActor,Z_Param_TowerActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BroadcastTowerSpawned(Z_Param_Out_SpawnedTower,Z_Param_TowerActor);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function BroadcastTowerSpawned ********************************

// ********** Begin Class ALaneManager Function BroadcastWaypointReached ***************************
struct Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics
{
	struct LaneManager_eventBroadcastWaypointReached_Parms
	{
		FVector WaypointPosition;
		ELaneManagerType LaneType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaypointPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WaypointPosition;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::NewProp_WaypointPosition = { "WaypointPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventBroadcastWaypointReached_Parms, WaypointPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaypointPosition_MetaData), NewProp_WaypointPosition_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventBroadcastWaypointReached_Parms, LaneType), Z_Construct_UEnum_Aura_ELaneManagerType, METADATA_PARAMS(0, nullptr) }; // 281285961
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::NewProp_WaypointPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::NewProp_LaneType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "BroadcastWaypointReached", Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::LaneManager_eventBroadcastWaypointReached_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::LaneManager_eventBroadcastWaypointReached_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execBroadcastWaypointReached)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WaypointPosition);
	P_GET_ENUM(ELaneManagerType,Z_Param_LaneType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BroadcastWaypointReached(Z_Param_Out_WaypointPosition,ELaneManagerType(Z_Param_LaneType));
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function BroadcastWaypointReached *****************************

// ********** Begin Class ALaneManager Function CalculateCentralLanePosition ***********************
struct Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics
{
	struct LaneManager_eventCalculateCentralLanePosition_Parms
	{
		float X;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane Mathematics" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_X;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventCalculateCentralLanePosition_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventCalculateCentralLanePosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "CalculateCentralLanePosition", Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::LaneManager_eventCalculateCentralLanePosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::LaneManager_eventCalculateCentralLanePosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execCalculateCentralLanePosition)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_X);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateCentralLanePosition(Z_Param_X);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function CalculateCentralLanePosition *************************

// ********** Begin Class ALaneManager Function CalculateHeuristic *********************************
struct Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics
{
	struct LaneManager_eventCalculateHeuristic_Parms
	{
		FVector Start;
		FVector End;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_End_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_End;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::NewProp_Start = { "Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventCalculateHeuristic_Parms, Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Start_MetaData), NewProp_Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::NewProp_End = { "End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventCalculateHeuristic_Parms, End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_End_MetaData), NewProp_End_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventCalculateHeuristic_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::NewProp_Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::NewProp_End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "CalculateHeuristic", Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::LaneManager_eventCalculateHeuristic_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::LaneManager_eventCalculateHeuristic_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_CalculateHeuristic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_CalculateHeuristic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execCalculateHeuristic)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_End);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateHeuristic(Z_Param_Out_Start,Z_Param_Out_End);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function CalculateHeuristic ***********************************

// ********** Begin Class ALaneManager Function CalculateInferiorLanePosition **********************
struct Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics
{
	struct LaneManager_eventCalculateInferiorLanePosition_Parms
	{
		float X;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane Mathematics" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_X;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventCalculateInferiorLanePosition_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventCalculateInferiorLanePosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "CalculateInferiorLanePosition", Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::LaneManager_eventCalculateInferiorLanePosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::LaneManager_eventCalculateInferiorLanePosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execCalculateInferiorLanePosition)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_X);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateInferiorLanePosition(Z_Param_X);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function CalculateInferiorLanePosition ************************

// ********** Begin Class ALaneManager Function CalculateSuperiorLanePosition **********************
struct Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics
{
	struct LaneManager_eventCalculateSuperiorLanePosition_Parms
	{
		float X;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane Mathematics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es p\xc3\xba""blicas para geometria das lanes\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es p\xc3\xba""blicas para geometria das lanes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_X;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventCalculateSuperiorLanePosition_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventCalculateSuperiorLanePosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "CalculateSuperiorLanePosition", Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::LaneManager_eventCalculateSuperiorLanePosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::LaneManager_eventCalculateSuperiorLanePosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execCalculateSuperiorLanePosition)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_X);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateSuperiorLanePosition(Z_Param_X);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function CalculateSuperiorLanePosition ************************

// ********** Begin Class ALaneManager Function FindPath *******************************************
struct Z_Construct_UFunction_ALaneManager_FindPath_Statics
{
	struct LaneManager_eventFindPath_Parms
	{
		FVector StartPos;
		FVector EndPos;
		ELaneManagerType LaneType;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de pathfinding A*\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de pathfinding A*" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_FindPath_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventFindPath_Parms, StartPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_FindPath_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventFindPath_Parms, EndPos), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ALaneManager_FindPath_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ALaneManager_FindPath_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventFindPath_Parms, LaneType), Z_Construct_UEnum_Aura_ELaneManagerType, METADATA_PARAMS(0, nullptr) }; // 281285961
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_FindPath_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ALaneManager_FindPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventFindPath_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_FindPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindPath_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindPath_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindPath_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindPath_Statics::NewProp_LaneType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindPath_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_FindPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_FindPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "FindPath", Z_Construct_UFunction_ALaneManager_FindPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_FindPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_FindPath_Statics::LaneManager_eventFindPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_FindPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_FindPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_FindPath_Statics::LaneManager_eventFindPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_FindPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_FindPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execFindPath)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPos);
	P_GET_ENUM(ELaneManagerType,Z_Param_LaneType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->FindPath(Z_Param_Out_StartPos,Z_Param_Out_EndPos,ELaneManagerType(Z_Param_LaneType));
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function FindPath *********************************************

// ********** Begin Class ALaneManager Function FindTargetsInRange *********************************
struct Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics
{
	struct LaneManager_eventFindTargetsInRange_Parms
	{
		FVector TowerPosition;
		float Range;
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tower Management" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TowerPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Range;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::NewProp_TowerPosition = { "TowerPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventFindTargetsInRange_Parms, TowerPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerPosition_MetaData), NewProp_TowerPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::NewProp_Range = { "Range", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventFindTargetsInRange_Parms, Range), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventFindTargetsInRange_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::NewProp_TowerPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::NewProp_Range,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "FindTargetsInRange", Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::LaneManager_eventFindTargetsInRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::LaneManager_eventFindTargetsInRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_FindTargetsInRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_FindTargetsInRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execFindTargetsInRange)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_TowerPosition);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Range);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->FindTargetsInRange(Z_Param_Out_TowerPosition,Z_Param_Range);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function FindTargetsInRange ***********************************

// ********** Begin Class ALaneManager Function FindWaterPath **************************************
struct Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics
{
	struct LaneManager_eventFindWaterPath_Parms
	{
		FVector Start;
		FVector End;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Integration" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Start_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_End_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Start;
	static const UECodeGen_Private::FStructPropertyParams NewProp_End;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::NewProp_Start = { "Start", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventFindWaterPath_Parms, Start), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Start_MetaData), NewProp_Start_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::NewProp_End = { "End", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventFindWaterPath_Parms, End), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_End_MetaData), NewProp_End_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventFindWaterPath_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::NewProp_Start,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::NewProp_End,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "FindWaterPath", Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::LaneManager_eventFindWaterPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::LaneManager_eventFindWaterPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_FindWaterPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_FindWaterPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execFindWaterPath)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Start);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_End);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->FindWaterPath(Z_Param_Out_Start,Z_Param_Out_End);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function FindWaterPath ****************************************

// ********** Begin Class ALaneManager Function GenerateWaypoints **********************************
struct Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics
{
	struct LaneManager_eventGenerateWaypoints_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane Setup" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneManager_eventGenerateWaypoints_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneManager_eventGenerateWaypoints_Parms), &Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "GenerateWaypoints", Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::LaneManager_eventGenerateWaypoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::LaneManager_eventGenerateWaypoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_GenerateWaypoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_GenerateWaypoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execGenerateWaypoints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateWaypoints();
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function GenerateWaypoints ************************************

// ********** Begin Class ALaneManager Function GetBestTarget **************************************
struct Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics
{
	struct LaneManager_eventGetBestTarget_Parms
	{
		FVector TowerPosition;
		TArray<AActor*> PotentialTargets;
		AActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tower Management" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PotentialTargets_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TowerPosition;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PotentialTargets_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PotentialTargets;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::NewProp_TowerPosition = { "TowerPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetBestTarget_Parms, TowerPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerPosition_MetaData), NewProp_TowerPosition_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::NewProp_PotentialTargets_Inner = { "PotentialTargets", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::NewProp_PotentialTargets = { "PotentialTargets", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetBestTarget_Parms, PotentialTargets), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PotentialTargets_MetaData), NewProp_PotentialTargets_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetBestTarget_Parms, ReturnValue), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::NewProp_TowerPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::NewProp_PotentialTargets_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::NewProp_PotentialTargets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "GetBestTarget", Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::LaneManager_eventGetBestTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::LaneManager_eventGetBestTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_GetBestTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_GetBestTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execGetBestTarget)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_TowerPosition);
	P_GET_TARRAY_REF(AActor*,Z_Param_Out_PotentialTargets);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AActor**)Z_Param__Result=P_THIS->GetBestTarget(Z_Param_Out_TowerPosition,Z_Param_Out_PotentialTargets);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function GetBestTarget ****************************************

// ********** Begin Class ALaneManager Function GetBridgePosition **********************************
struct Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics
{
	struct LaneManager_eventGetBridgePosition_Parms
	{
		ELaneManagerType LaneType;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Integration" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetBridgePosition_Parms, LaneType), Z_Construct_UEnum_Aura_ELaneManagerType, METADATA_PARAMS(0, nullptr) }; // 281285961
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetBridgePosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::NewProp_LaneType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "GetBridgePosition", Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::LaneManager_eventGetBridgePosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::LaneManager_eventGetBridgePosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_GetBridgePosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_GetBridgePosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execGetBridgePosition)
{
	P_GET_ENUM(ELaneManagerType,Z_Param_LaneType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetBridgePosition(ELaneManagerType(Z_Param_LaneType));
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function GetBridgePosition ************************************

// ********** Begin Class ALaneManager Function GetClosestLane *************************************
struct Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics
{
	struct LaneManager_eventGetClosestLane_Parms
	{
		FVector Position;
		ELaneManagerType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetClosestLane_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetClosestLane_Parms, ReturnValue), Z_Construct_UEnum_Aura_ELaneManagerType, METADATA_PARAMS(0, nullptr) }; // 281285961
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "GetClosestLane", Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::LaneManager_eventGetClosestLane_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::LaneManager_eventGetClosestLane_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_GetClosestLane()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_GetClosestLane_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execGetClosestLane)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ELaneManagerType*)Z_Param__Result=P_THIS->GetClosestLane(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function GetClosestLane ***************************************

// ********** Begin Class ALaneManager Function GetMovementSpeedModifier ***************************
struct Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics
{
	struct LaneManager_eventGetMovementSpeedModifier_Parms
	{
		FVector Position;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Integration" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetMovementSpeedModifier_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetMovementSpeedModifier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "GetMovementSpeedModifier", Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::LaneManager_eventGetMovementSpeedModifier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::LaneManager_eventGetMovementSpeedModifier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execGetMovementSpeedModifier)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMovementSpeedModifier(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function GetMovementSpeedModifier *****************************

// ********** Begin Class ALaneManager Function GetNearbyWaypoints *********************************
struct Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics
{
	struct LaneManager_eventGetNearbyWaypoints_Parms
	{
		FVector Position;
		ELaneManagerType LaneType;
		float Radius;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Pathfinding" },
		{ "CPP_Default_Radius", "1200.000000" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetNearbyWaypoints_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetNearbyWaypoints_Parms, LaneType), Z_Construct_UEnum_Aura_ELaneManagerType, METADATA_PARAMS(0, nullptr) }; // 281285961
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetNearbyWaypoints_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetNearbyWaypoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::NewProp_LaneType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "GetNearbyWaypoints", Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::LaneManager_eventGetNearbyWaypoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::LaneManager_eventGetNearbyWaypoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execGetNearbyWaypoints)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_ENUM(ELaneManagerType,Z_Param_LaneType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GetNearbyWaypoints(Z_Param_Out_Position,ELaneManagerType(Z_Param_LaneType),Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function GetNearbyWaypoints ***********************************

// ********** Begin Class ALaneManager Function GetNearestBridgePosition ***************************
struct Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics
{
	struct LaneManager_eventGetNearestBridgePosition_Parms
	{
		FVector Position;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Integration" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetNearestBridgePosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetNearestBridgePosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "GetNearestBridgePosition", Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::LaneManager_eventGetNearestBridgePosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::LaneManager_eventGetNearestBridgePosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execGetNearestBridgePosition)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetNearestBridgePosition(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function GetNearestBridgePosition *****************************

// ********** Begin Class ALaneManager Function GetWaterSpeedMultiplier ****************************
struct Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics
{
	struct LaneManager_eventGetWaterSpeedMultiplier_Parms
	{
		FVector Position;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Integration" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetWaterSpeedMultiplier_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventGetWaterSpeedMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "GetWaterSpeedMultiplier", Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::LaneManager_eventGetWaterSpeedMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::LaneManager_eventGetWaterSpeedMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execGetWaterSpeedMultiplier)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetWaterSpeedMultiplier(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function GetWaterSpeedMultiplier ******************************

// ********** Begin Class ALaneManager Function InitializeLanes ************************************
struct Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics
{
	struct LaneManager_eventInitializeLanes_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane Setup" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de inicializa\xc3\xa7\xc3\xa3o\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de inicializa\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneManager_eventInitializeLanes_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneManager_eventInitializeLanes_Parms), &Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "InitializeLanes", Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::LaneManager_eventInitializeLanes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::LaneManager_eventInitializeLanes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_InitializeLanes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_InitializeLanes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execInitializeLanes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeLanes();
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function InitializeLanes **************************************

// ********** Begin Class ALaneManager Function InitializeTowers ***********************************
struct Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics
{
	struct LaneManager_eventInitializeTowers_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lane Setup" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneManager_eventInitializeTowers_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneManager_eventInitializeTowers_Parms), &Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "InitializeTowers", Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::LaneManager_eventInitializeTowers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::LaneManager_eventInitializeTowers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_InitializeTowers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_InitializeTowers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execInitializeTowers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeTowers();
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function InitializeTowers *************************************

// ********** Begin Class ALaneManager Function IsPositionInLane ***********************************
struct Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics
{
	struct LaneManager_eventIsPositionInLane_Parms
	{
		FVector Position;
		ELaneManagerType LaneType;
		float Tolerance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "CPP_Default_Tolerance", "1.000000" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LaneType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LaneType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventIsPositionInLane_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::NewProp_LaneType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::NewProp_LaneType = { "LaneType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventIsPositionInLane_Parms, LaneType), Z_Construct_UEnum_Aura_ELaneManagerType, METADATA_PARAMS(0, nullptr) }; // 281285961
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventIsPositionInLane_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneManager_eventIsPositionInLane_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneManager_eventIsPositionInLane_Parms), &Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::NewProp_LaneType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::NewProp_LaneType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "IsPositionInLane", Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::LaneManager_eventIsPositionInLane_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::LaneManager_eventIsPositionInLane_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_IsPositionInLane()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_IsPositionInLane_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execIsPositionInLane)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_ENUM(ELaneManagerType,Z_Param_LaneType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPositionInLane(Z_Param_Out_Position,ELaneManagerType(Z_Param_LaneType),Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function IsPositionInLane *************************************

// ********** Begin Class ALaneManager Function IsPositionInRiver **********************************
struct Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics
{
	struct LaneManager_eventIsPositionInRiver_Parms
	{
		FVector Position;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de integra\xc3\xa7\xc3\xa3o com rio\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de integra\xc3\xa7\xc3\xa3o com rio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventIsPositionInRiver_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
void Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneManager_eventIsPositionInRiver_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneManager_eventIsPositionInRiver_Parms), &Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "IsPositionInRiver", Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::LaneManager_eventIsPositionInRiver_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::LaneManager_eventIsPositionInRiver_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_IsPositionInRiver()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_IsPositionInRiver_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execIsPositionInRiver)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPositionInRiver(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function IsPositionInRiver ************************************

// ********** Begin Class ALaneManager Function IsPositionInWater **********************************
struct Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics
{
	struct LaneManager_eventIsPositionInWater_Parms
	{
		FVector Position;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "River Integration" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventIsPositionInWater_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
void Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneManager_eventIsPositionInWater_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneManager_eventIsPositionInWater_Parms), &Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "IsPositionInWater", Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::LaneManager_eventIsPositionInWater_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::LaneManager_eventIsPositionInWater_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_IsPositionInWater()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_IsPositionInWater_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execIsPositionInWater)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPositionInWater(Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function IsPositionInWater ************************************

// ********** Begin Class ALaneManager Function SpawnTower *****************************************
struct Z_Construct_UFunction_ALaneManager_SpawnTower_Statics
{
	struct LaneManager_eventSpawnTower_Parms
	{
		FTowerData TowerData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tower Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de torres\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de torres" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TowerData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_SpawnTower_Statics::NewProp_TowerData = { "TowerData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventSpawnTower_Parms, TowerData), Z_Construct_UScriptStruct_FTowerData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerData_MetaData), NewProp_TowerData_MetaData) }; // 1218475868
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_SpawnTower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_SpawnTower_Statics::NewProp_TowerData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_SpawnTower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_SpawnTower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "SpawnTower", Z_Construct_UFunction_ALaneManager_SpawnTower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_SpawnTower_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_SpawnTower_Statics::LaneManager_eventSpawnTower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_SpawnTower_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_SpawnTower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_SpawnTower_Statics::LaneManager_eventSpawnTower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_SpawnTower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_SpawnTower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execSpawnTower)
{
	P_GET_STRUCT_REF(FTowerData,Z_Param_Out_TowerData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnTower(Z_Param_Out_TowerData);
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function SpawnTower *******************************************

// ********** Begin Class ALaneManager Function SpawnTowerAtPosition *******************************
struct Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics
{
	struct LaneManager_eventSpawnTowerAtPosition_Parms
	{
		FVector Position;
		ETowerType TowerType;
		ETeam Team;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tower Management" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TowerType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TowerType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Team_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Team;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventSpawnTowerAtPosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_TowerType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_TowerType = { "TowerType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventSpawnTowerAtPosition_Parms, TowerType), Z_Construct_UEnum_Aura_ETowerType, METADATA_PARAMS(0, nullptr) }; // 4294645771
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_Team_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_Team = { "Team", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LaneManager_eventSpawnTowerAtPosition_Parms, Team), Z_Construct_UEnum_Aura_ETeam, METADATA_PARAMS(0, nullptr) }; // 1024800466
void Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneManager_eventSpawnTowerAtPosition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneManager_eventSpawnTowerAtPosition_Parms), &Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_TowerType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_TowerType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_Team_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_Team,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "SpawnTowerAtPosition", Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::LaneManager_eventSpawnTowerAtPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::LaneManager_eventSpawnTowerAtPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execSpawnTowerAtPosition)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_ENUM(ETowerType,Z_Param_TowerType);
	P_GET_ENUM(ETeam,Z_Param_Team);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SpawnTowerAtPosition(Z_Param_Out_Position,ETowerType(Z_Param_TowerType),ETeam(Z_Param_Team));
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function SpawnTowerAtPosition *********************************

// ********** Begin Class ALaneManager Function ValidateLaneGeometry *******************************
struct Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics
{
	struct LaneManager_eventValidateLaneGeometry_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o geom\xc3\xa9trica\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es de valida\xc3\xa7\xc3\xa3o geom\xc3\xa9trica" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LaneManager_eventValidateLaneGeometry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LaneManager_eventValidateLaneGeometry_Parms), &Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ALaneManager, nullptr, "ValidateLaneGeometry", Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::LaneManager_eventValidateLaneGeometry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::LaneManager_eventValidateLaneGeometry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALaneManager::execValidateLaneGeometry)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateLaneGeometry();
	P_NATIVE_END;
}
// ********** End Class ALaneManager Function ValidateLaneGeometry *********************************

// ********** Begin Class ALaneManager *************************************************************
void ALaneManager::StaticRegisterNativesALaneManager()
{
	UClass* Class = ALaneManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "BroadcastLaneInitialized", &ALaneManager::execBroadcastLaneInitialized },
		{ "BroadcastPathfindingCacheUpdated", &ALaneManager::execBroadcastPathfindingCacheUpdated },
		{ "BroadcastRiverIntegrationStatusChanged", &ALaneManager::execBroadcastRiverIntegrationStatusChanged },
		{ "BroadcastTowerDestroyed", &ALaneManager::execBroadcastTowerDestroyed },
		{ "BroadcastTowerSpawned", &ALaneManager::execBroadcastTowerSpawned },
		{ "BroadcastWaypointReached", &ALaneManager::execBroadcastWaypointReached },
		{ "CalculateCentralLanePosition", &ALaneManager::execCalculateCentralLanePosition },
		{ "CalculateHeuristic", &ALaneManager::execCalculateHeuristic },
		{ "CalculateInferiorLanePosition", &ALaneManager::execCalculateInferiorLanePosition },
		{ "CalculateSuperiorLanePosition", &ALaneManager::execCalculateSuperiorLanePosition },
		{ "FindPath", &ALaneManager::execFindPath },
		{ "FindTargetsInRange", &ALaneManager::execFindTargetsInRange },
		{ "FindWaterPath", &ALaneManager::execFindWaterPath },
		{ "GenerateWaypoints", &ALaneManager::execGenerateWaypoints },
		{ "GetBestTarget", &ALaneManager::execGetBestTarget },
		{ "GetBridgePosition", &ALaneManager::execGetBridgePosition },
		{ "GetClosestLane", &ALaneManager::execGetClosestLane },
		{ "GetMovementSpeedModifier", &ALaneManager::execGetMovementSpeedModifier },
		{ "GetNearbyWaypoints", &ALaneManager::execGetNearbyWaypoints },
		{ "GetNearestBridgePosition", &ALaneManager::execGetNearestBridgePosition },
		{ "GetWaterSpeedMultiplier", &ALaneManager::execGetWaterSpeedMultiplier },
		{ "InitializeLanes", &ALaneManager::execInitializeLanes },
		{ "InitializeTowers", &ALaneManager::execInitializeTowers },
		{ "IsPositionInLane", &ALaneManager::execIsPositionInLane },
		{ "IsPositionInRiver", &ALaneManager::execIsPositionInRiver },
		{ "IsPositionInWater", &ALaneManager::execIsPositionInWater },
		{ "SpawnTower", &ALaneManager::execSpawnTower },
		{ "SpawnTowerAtPosition", &ALaneManager::execSpawnTowerAtPosition },
		{ "ValidateLaneGeometry", &ALaneManager::execValidateLaneGeometry },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ALaneManager;
UClass* ALaneManager::GetPrivateStaticClass()
{
	using TClass = ALaneManager;
	if (!Z_Registration_Info_UClass_ALaneManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("LaneManager"),
			Z_Registration_Info_UClass_ALaneManager.InnerSingleton,
			StaticRegisterNativesALaneManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ALaneManager.InnerSingleton;
}
UClass* Z_Construct_UClass_ALaneManager_NoRegister()
{
	return ALaneManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ALaneManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "ALaneManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes principais\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes principais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuperiorLaneSpline_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CentralLaneSpline_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InferiorLaneSpline_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LanesData_MetaData[] = {
		{ "Category", "Lane Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados das lanes\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados das lanes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowersData_MetaData[] = {
		{ "Category", "Tower Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados das torres\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados das torres" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapSize_MetaData[] = {
		{ "Category", "Lane Mathematics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es matem\xc3\xa1ticas das lanes\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es matem\xc3\xa1ticas das lanes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayableArea_MetaData[] = {
		{ "Category", "Lane Mathematics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 160m x 160m em UU (1 UU = 1 cm)\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "160m x 160m em UU (1 UU = 1 cm)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTowerDestroyed_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sistema de eventos\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de eventos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTowerSpawned_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPathfindingCacheUpdated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLaneInitialized_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnWaypointReached_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRiverIntegrationStatusChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaterSpeedMultiplier_MetaData[] = {
		{ "Category", "River Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Propriedades de integra\xc3\xa7\xc3\xa3o com rio (movidas para p\xc3\xba""blico para BlueprintReadWrite)\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedades de integra\xc3\xa7\xc3\xa3o com rio (movidas para p\xc3\xba""blico para BlueprintReadWrite)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableWaterDetection_MetaData[] = {
		{ "Category", "River Integration" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TurretClass_MetaData[] = {
		{ "Category", "Tower Classes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Classes de torres para spawning\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classes de torres para spawning" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InternalTowerClass_MetaData[] = {
		{ "Category", "Tower Classes" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InhibitorClass_MetaData[] = {
		{ "Category", "Tower Classes" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultTowerClass_MetaData[] = {
		{ "Category", "Tower Classes" },
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastCacheUpdate_MetaData[] = {
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveTowers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Array de torres ativas\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Array de torres ativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiverManager_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integra\xc3\xa7\xc3\xa3o com sistema de rio\n" },
#endif
		{ "ModuleRelativePath", "Public/ALaneManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integra\xc3\xa7\xc3\xa3o com sistema de rio" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SuperiorLaneSpline;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CentralLaneSpline;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InferiorLaneSpline;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LanesData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LanesData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TowersData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TowersData;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MapSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayableArea;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTowerDestroyed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTowerSpawned;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPathfindingCacheUpdated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLaneInitialized;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnWaypointReached;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRiverIntegrationStatusChanged;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WaterSpeedMultiplier;
	static void NewProp_bEnableWaterDetection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableWaterDetection;
	static const UECodeGen_Private::FClassPropertyParams NewProp_TurretClass;
	static const UECodeGen_Private::FClassPropertyParams NewProp_InternalTowerClass;
	static const UECodeGen_Private::FClassPropertyParams NewProp_InhibitorClass;
	static const UECodeGen_Private::FClassPropertyParams NewProp_DefaultTowerClass;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastCacheUpdate;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveTowers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveTowers;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RiverManager;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ALaneManager_BroadcastLaneInitialized, "BroadcastLaneInitialized" }, // 1405230952
		{ &Z_Construct_UFunction_ALaneManager_BroadcastPathfindingCacheUpdated, "BroadcastPathfindingCacheUpdated" }, // 4191217366
		{ &Z_Construct_UFunction_ALaneManager_BroadcastRiverIntegrationStatusChanged, "BroadcastRiverIntegrationStatusChanged" }, // 3370034503
		{ &Z_Construct_UFunction_ALaneManager_BroadcastTowerDestroyed, "BroadcastTowerDestroyed" }, // 772329142
		{ &Z_Construct_UFunction_ALaneManager_BroadcastTowerSpawned, "BroadcastTowerSpawned" }, // 564927390
		{ &Z_Construct_UFunction_ALaneManager_BroadcastWaypointReached, "BroadcastWaypointReached" }, // 29464371
		{ &Z_Construct_UFunction_ALaneManager_CalculateCentralLanePosition, "CalculateCentralLanePosition" }, // 2595301941
		{ &Z_Construct_UFunction_ALaneManager_CalculateHeuristic, "CalculateHeuristic" }, // 3829700893
		{ &Z_Construct_UFunction_ALaneManager_CalculateInferiorLanePosition, "CalculateInferiorLanePosition" }, // 3961648002
		{ &Z_Construct_UFunction_ALaneManager_CalculateSuperiorLanePosition, "CalculateSuperiorLanePosition" }, // 1158828706
		{ &Z_Construct_UFunction_ALaneManager_FindPath, "FindPath" }, // 1946801589
		{ &Z_Construct_UFunction_ALaneManager_FindTargetsInRange, "FindTargetsInRange" }, // 3279279316
		{ &Z_Construct_UFunction_ALaneManager_FindWaterPath, "FindWaterPath" }, // 1055759850
		{ &Z_Construct_UFunction_ALaneManager_GenerateWaypoints, "GenerateWaypoints" }, // 3360587034
		{ &Z_Construct_UFunction_ALaneManager_GetBestTarget, "GetBestTarget" }, // 2165602404
		{ &Z_Construct_UFunction_ALaneManager_GetBridgePosition, "GetBridgePosition" }, // 3478098811
		{ &Z_Construct_UFunction_ALaneManager_GetClosestLane, "GetClosestLane" }, // 3724099518
		{ &Z_Construct_UFunction_ALaneManager_GetMovementSpeedModifier, "GetMovementSpeedModifier" }, // 1286726860
		{ &Z_Construct_UFunction_ALaneManager_GetNearbyWaypoints, "GetNearbyWaypoints" }, // 372631257
		{ &Z_Construct_UFunction_ALaneManager_GetNearestBridgePosition, "GetNearestBridgePosition" }, // 1139965652
		{ &Z_Construct_UFunction_ALaneManager_GetWaterSpeedMultiplier, "GetWaterSpeedMultiplier" }, // 110121854
		{ &Z_Construct_UFunction_ALaneManager_InitializeLanes, "InitializeLanes" }, // 1429624666
		{ &Z_Construct_UFunction_ALaneManager_InitializeTowers, "InitializeTowers" }, // 2733934933
		{ &Z_Construct_UFunction_ALaneManager_IsPositionInLane, "IsPositionInLane" }, // 829907930
		{ &Z_Construct_UFunction_ALaneManager_IsPositionInRiver, "IsPositionInRiver" }, // 851802181
		{ &Z_Construct_UFunction_ALaneManager_IsPositionInWater, "IsPositionInWater" }, // 1124291966
		{ &Z_Construct_UFunction_ALaneManager_SpawnTower, "SpawnTower" }, // 1961620677
		{ &Z_Construct_UFunction_ALaneManager_SpawnTowerAtPosition, "SpawnTowerAtPosition" }, // 3742767263
		{ &Z_Construct_UFunction_ALaneManager_ValidateLaneGeometry, "ValidateLaneGeometry" }, // 1488657901
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ALaneManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_SuperiorLaneSpline = { "SuperiorLaneSpline", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, SuperiorLaneSpline), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuperiorLaneSpline_MetaData), NewProp_SuperiorLaneSpline_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_CentralLaneSpline = { "CentralLaneSpline", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, CentralLaneSpline), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CentralLaneSpline_MetaData), NewProp_CentralLaneSpline_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_InferiorLaneSpline = { "InferiorLaneSpline", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, InferiorLaneSpline), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InferiorLaneSpline_MetaData), NewProp_InferiorLaneSpline_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_LanesData_Inner = { "LanesData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FLaneData, METADATA_PARAMS(0, nullptr) }; // 4141907221
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_LanesData = { "LanesData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, LanesData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LanesData_MetaData), NewProp_LanesData_MetaData) }; // 4141907221
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_TowersData_Inner = { "TowersData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTowerData, METADATA_PARAMS(0, nullptr) }; // 1218475868
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_TowersData = { "TowersData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, TowersData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowersData_MetaData), NewProp_TowersData_MetaData) }; // 1218475868
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_MapSize = { "MapSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, MapSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapSize_MetaData), NewProp_MapSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_PlayableArea = { "PlayableArea", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, PlayableArea), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayableArea_MetaData), NewProp_PlayableArea_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_OnTowerDestroyed = { "OnTowerDestroyed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, OnTowerDestroyed), Z_Construct_UDelegateFunction_Aura_OnTowerDestroyed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTowerDestroyed_MetaData), NewProp_OnTowerDestroyed_MetaData) }; // 4115910141
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_OnTowerSpawned = { "OnTowerSpawned", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, OnTowerSpawned), Z_Construct_UDelegateFunction_Aura_OnTowerSpawned__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTowerSpawned_MetaData), NewProp_OnTowerSpawned_MetaData) }; // 1147588837
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_OnPathfindingCacheUpdated = { "OnPathfindingCacheUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, OnPathfindingCacheUpdated), Z_Construct_UDelegateFunction_Aura_OnPathfindingCacheUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPathfindingCacheUpdated_MetaData), NewProp_OnPathfindingCacheUpdated_MetaData) }; // 2633504251
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_OnLaneInitialized = { "OnLaneInitialized", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, OnLaneInitialized), Z_Construct_UDelegateFunction_Aura_OnLaneInitialized__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLaneInitialized_MetaData), NewProp_OnLaneInitialized_MetaData) }; // 3405472363
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_OnWaypointReached = { "OnWaypointReached", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, OnWaypointReached), Z_Construct_UDelegateFunction_Aura_OnWaypointReached__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnWaypointReached_MetaData), NewProp_OnWaypointReached_MetaData) }; // 4272186508
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_OnRiverIntegrationStatusChanged = { "OnRiverIntegrationStatusChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, OnRiverIntegrationStatusChanged), Z_Construct_UDelegateFunction_Aura_OnRiverIntegrationStatusChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRiverIntegrationStatusChanged_MetaData), NewProp_OnRiverIntegrationStatusChanged_MetaData) }; // 1333794169
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_WaterSpeedMultiplier = { "WaterSpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, WaterSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaterSpeedMultiplier_MetaData), NewProp_WaterSpeedMultiplier_MetaData) };
void Z_Construct_UClass_ALaneManager_Statics::NewProp_bEnableWaterDetection_SetBit(void* Obj)
{
	((ALaneManager*)Obj)->bEnableWaterDetection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_bEnableWaterDetection = { "bEnableWaterDetection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ALaneManager), &Z_Construct_UClass_ALaneManager_Statics::NewProp_bEnableWaterDetection_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableWaterDetection_MetaData), NewProp_bEnableWaterDetection_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_TurretClass = { "TurretClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, TurretClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TurretClass_MetaData), NewProp_TurretClass_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_InternalTowerClass = { "InternalTowerClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, InternalTowerClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InternalTowerClass_MetaData), NewProp_InternalTowerClass_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_InhibitorClass = { "InhibitorClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, InhibitorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InhibitorClass_MetaData), NewProp_InhibitorClass_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_DefaultTowerClass = { "DefaultTowerClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, DefaultTowerClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultTowerClass_MetaData), NewProp_DefaultTowerClass_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_LastCacheUpdate = { "LastCacheUpdate", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, LastCacheUpdate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastCacheUpdate_MetaData), NewProp_LastCacheUpdate_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_ActiveTowers_Inner = { "ActiveTowers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_ActiveTowers = { "ActiveTowers", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, ActiveTowers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveTowers_MetaData), NewProp_ActiveTowers_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ALaneManager_Statics::NewProp_RiverManager = { "RiverManager", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALaneManager, RiverManager), Z_Construct_UClass_ARiverPrismalManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiverManager_MetaData), NewProp_RiverManager_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ALaneManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_SuperiorLaneSpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_CentralLaneSpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_InferiorLaneSpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_LanesData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_LanesData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_TowersData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_TowersData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_MapSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_PlayableArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_OnTowerDestroyed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_OnTowerSpawned,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_OnPathfindingCacheUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_OnLaneInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_OnWaypointReached,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_OnRiverIntegrationStatusChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_WaterSpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_bEnableWaterDetection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_TurretClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_InternalTowerClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_InhibitorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_DefaultTowerClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_LastCacheUpdate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_ActiveTowers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_ActiveTowers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALaneManager_Statics::NewProp_RiverManager,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ALaneManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ALaneManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Aura,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ALaneManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ALaneManager_Statics::ClassParams = {
	&ALaneManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ALaneManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ALaneManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ALaneManager_Statics::Class_MetaDataParams), Z_Construct_UClass_ALaneManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ALaneManager()
{
	if (!Z_Registration_Info_UClass_ALaneManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ALaneManager.OuterSingleton, Z_Construct_UClass_ALaneManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ALaneManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ALaneManager);
ALaneManager::~ALaneManager() {}
// ********** End Class ALaneManager ***************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ALaneManager_h__Script_Aura_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ELaneManagerType_StaticEnum, TEXT("ELaneManagerType"), &Z_Registration_Info_UEnum_ELaneManagerType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 281285961U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FLaneData::StaticStruct, Z_Construct_UScriptStruct_FLaneData_Statics::NewStructOps, TEXT("LaneData"), &Z_Registration_Info_UScriptStruct_FLaneData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLaneData), 4141907221U) },
		{ FTowerData::StaticStruct, Z_Construct_UScriptStruct_FTowerData_Statics::NewStructOps, TEXT("TowerData"), &Z_Registration_Info_UScriptStruct_FTowerData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTowerData), 1218475868U) },
		{ FPathNode::StaticStruct, Z_Construct_UScriptStruct_FPathNode_Statics::NewStructOps, TEXT("PathNode"), &Z_Registration_Info_UScriptStruct_FPathNode, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPathNode), 585598187U) },
		{ FCachedPath::StaticStruct, Z_Construct_UScriptStruct_FCachedPath_Statics::NewStructOps, TEXT("CachedPath"), &Z_Registration_Info_UScriptStruct_FCachedPath, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FCachedPath), 3319499528U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ALaneManager, ALaneManager::StaticClass, TEXT("ALaneManager"), &Z_Registration_Info_UClass_ALaneManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ALaneManager), 2444644939U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ALaneManager_h__Script_Aura_1687247209(TEXT("/Script/Aura"),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ALaneManager_h__Script_Aura_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ALaneManager_h__Script_Aura_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ALaneManager_h__Script_Aura_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ALaneManager_h__Script_Aura_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ALaneManager_h__Script_Aura_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_Source_Aura_Public_ALaneManager_h__Script_Aura_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
