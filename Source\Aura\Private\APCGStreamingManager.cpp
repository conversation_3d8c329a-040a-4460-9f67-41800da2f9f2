#include "APCGStreamingManager.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Pawn.h"
#include "Camera/CameraComponent.h"
#include "Components/SceneComponent.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Async/Async.h"
#include "DrawDebugHelpers.h"
#include "APCGWorldPartitionManager.h"
#include "APCGNaniteOptimizer.h"
#include "UPCGPerformanceProfiler.h"

// CORRIGIDO: Includes modernos UE 5.6 para streaming
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "HAL/IConsoleManager.h"
#include "Stats/Stats.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "Misc/ScopeLock.h"

#if WITH_EDITOR
#include "Editor.h"
#include "EditorSubsystem.h"
#endif

// CORRIGIDO: World Partition streaming APIs modernas UE 5.6
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"
#include "Components/WorldPartitionStreamingSourceComponent.h"

// CORRIGIDO: Categorias de logging específicas UE 5.6
DEFINE_LOG_CATEGORY_STATIC(LogPCGStreamingManager, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGStreamingPerformance, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGStreamingCache, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGStreamingAssets, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGStreamingRegions, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGStreamingViewers, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogPCGStreamingOptimization, Log, All);

// CORRIGIDO: Stats system moderno UE 5.6
DECLARE_CYCLE_STAT(TEXT("PCG Streaming Manager Tick"), STAT_PCGStreamingManagerTick, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("PCG Streaming Asset Loading"), STAT_PCGStreamingAssetLoading, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("PCG Streaming Asset Unloading"), STAT_PCGStreamingAssetUnloading, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("PCG Streaming Region Update"), STAT_PCGStreamingRegionUpdate, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("PCG Streaming Viewer Update"), STAT_PCGStreamingViewerUpdate, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("PCG Streaming Cache Management"), STAT_PCGStreamingCacheManagement, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("PCG Streaming Performance Monitor"), STAT_PCGStreamingPerformanceMonitor, STATGROUP_Game);

// CORRIGIDO: CVars modernos UE 5.6 para controle de streaming
static TAutoConsoleVariable<int32> CVarPCGStreamingEnabled(
    TEXT("pcg.Streaming.Enabled"),
    1,
    TEXT("Enable/disable PCG streaming system (0=disabled, 1=enabled)"),
    ECVF_Default
);

static TAutoConsoleVariable<float> CVarPCGStreamingUpdateInterval(
    TEXT("pcg.Streaming.UpdateInterval"),
    0.1f,
    TEXT("Update interval for PCG streaming system in seconds"),
    ECVF_Default
);

static TAutoConsoleVariable<int32> CVarPCGStreamingMaxConcurrentLoads(
    TEXT("pcg.Streaming.MaxConcurrentLoads"),
    8,
    TEXT("Maximum number of concurrent asset loads"),
    ECVF_Default
);

static TAutoConsoleVariable<int32> CVarPCGStreamingCacheSize(
    TEXT("pcg.Streaming.CacheSize"),
    1024,
    TEXT("Maximum cache size in MB"),
    ECVF_Default
);

static TAutoConsoleVariable<int32> CVarPCGStreamingDebugVisualization(
    TEXT("pcg.Streaming.DebugVisualization"),
    0,
    TEXT("Enable debug visualization for streaming regions (0=disabled, 1=enabled)"),
    ECVF_Default
);

APCGStreamingManager::APCGStreamingManager()
{
    // CORRIGIDO: Configuração moderna de tick UE 5.6
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.bTickEvenWhenPaused = false;
    PrimaryActorTick.bAllowTickOnDedicatedServer = true;
    PrimaryActorTick.TickInterval = CVarPCGStreamingUpdateInterval.GetValueOnGameThread();

    // CORRIGIDO: Configuração production-ready com valores otimizados
    StreamingConfig = FPCGStreamingConfig();
    StreamingConfig.LoadDistance = 5000.0f;
    StreamingConfig.UnloadDistance = 7500.0f;
    StreamingConfig.MaxConcurrentLoads = CVarPCGStreamingMaxConcurrentLoads.GetValueOnGameThread();
    StreamingConfig.CacheSizeMB = CVarPCGStreamingCacheSize.GetValueOnGameThread();
    StreamingConfig.bEnableAsyncLoading = true;
    StreamingConfig.bEnablePrioritySystem = true;
    StreamingConfig.bEnableMemoryOptimization = true;

    // CORRIGIDO: Estados com inicialização robusta
    bEnablePerformanceMonitoring = true;
    bAutoOptimization = true;
    bShowDebugVisualization = CVarPCGStreamingDebugVisualization.GetValueOnGameThread() > 0;
    bIsInitialized = false;
    bIsPaused = false;
    bNeedsUpdate = true;
    bWorldPartitionEnabled = false;
    bFastGeometryStreamingEnabled = false;

    // CORRIGIDO: Timers e contadores com valores iniciais seguros
    LastUpdateTime = 0.0f;
    AccumulatedDeltaTime = 0.0f;
    CurrentCacheSize = 0;
    LastPerformanceUpdate = 0.0f;
    LastMemoryCheck = 0.0f;
    TotalAssetsLoaded = 0;
    TotalAssetsUnloaded = 0;

    // CORRIGIDO: Inicialização de containers com Reserve() para performance
    RegisteredAssets.Reserve(1024);
    StreamingRegions.Reserve(256);
    StreamingViewers.Reserve(16);
    AssetCache.Reserve(512);
    LoadQueue.Reserve(128);
    UnloadQueue.Reserve(128);

    // CORRIGIDO: Stats com inicialização completa
    CurrentStats = FPCGStreamingStats();
    CurrentStats.TotalMemoryUsed = 0;
    CurrentStats.CachedAssets = 0;
    CurrentStats.ActiveLoads = 0;
    CurrentStats.QueuedLoads = 0;
    CurrentStats.FrameTime = 0.0f;

    UE_LOG(LogPCGStreamingManager, Log, TEXT("Constructor completed - Tick interval: %.3fs, Cache size: %dMB"),
           (double)PrimaryActorTick.TickInterval, StreamingConfig.CacheSizeMB);
}

void APCGStreamingManager::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogPCGStreamingManager, Log, TEXT("BeginPlay started"));

    // CORRIGIDO: Verificar se streaming está habilitado via CVar
    if (CVarPCGStreamingEnabled.GetValueOnGameThread() == 0)
    {
        UE_LOG(LogPCGStreamingManager, Warning, TEXT("PCG Streaming disabled via cvar pcg.Streaming.Enabled"));
        return;
    }

    // CORRIGIDO: Detectar recursos modernos UE 5.6
    if (UWorld* World = GetWorld())
    {
        // Detectar World Partition
        if (UWorldPartition* WorldPartition = World->GetWorldPartition())
        {
            bWorldPartitionEnabled = true;
            UE_LOG(LogPCGStreamingManager, Log, TEXT("World Partition detected and enabled"));

            // Integrar com World Partition Streaming Source
            if (UWorldPartitionSubsystem* WPSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>())
            {
                UE_LOG(LogPCGStreamingManager, Log, TEXT("World Partition Subsystem integration enabled"));
            }
        }

        // CORRIGIDO: Detectar Fast Geometry Streaming Plugin
        static const auto* FastGeoEnabledCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("FastGeo.AsyncRenderStateTask.TimeBudgetMS"));
        if (FastGeoEnabledCVar && FastGeoEnabledCVar->GetFloat() > 0.0f)
        {
            bFastGeometryStreamingEnabled = true;
            UE_LOG(LogPCGStreamingManager, Log, TEXT("Fast Geometry Streaming detected and enabled"));
        }

        // CORRIGIDO: Verificar streaming performance optimizations UE 5.6
        static const auto* UnifiedStreamingCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("s.UseUnifiedTimeBudgetForStreaming"));
        if (UnifiedStreamingCVar && UnifiedStreamingCVar->GetInt() > 0)
        {
            UE_LOG(LogPCGStreamingManager, Log, TEXT("Unified streaming time budget enabled"));
        }
    }

    // CORRIGIDO: Inicializar sistema com profiling
    {
        SCOPE_CYCLE_COUNTER(STAT_PCGStreamingManagerTick);
        TRACE_CPUPROFILER_EVENT_SCOPE(APCGStreamingManager::InitializeStreamingSystem);

        InitializeStreamingSystem();
    }

    // CORRIGIDO: Adicionar viewer padrão com validação robusta
    if (UWorld* World = GetWorld())
    {
        if (APlayerController* PC = World->GetFirstPlayerController())
        {
            if (IsValid(PC))
            {
                if (APawn* PlayerPawn = PC->GetPawn())
                {
                    if (IsValid(PlayerPawn))
                    {
                        AddStreamingViewer(PlayerPawn, StreamingConfig.LoadDistance);
                        UE_LOG(LogPCGStreamingViewers, Log, TEXT("Default streaming viewer added: %s"),
                               *PlayerPawn->GetName());
                    }
                }
            }
        }
    }

    UE_LOG(LogPCGStreamingManager, Log, TEXT("BeginPlay completed - WorldPartition: %s, FastGeo: %s"),
           bWorldPartitionEnabled ? TEXT("Enabled") : TEXT("Disabled"),
           bFastGeometryStreamingEnabled ? TEXT("Enabled") : TEXT("Disabled"));
}

void APCGStreamingManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // CORRIGIDO: Logging seguro com enum string
    const FString EndPlayReasonStr = UEnum::GetValueAsString(EndPlayReason);
    UE_LOG(LogPCGStreamingManager, Log, TEXT("EndPlay started - Reason: %s"), *EndPlayReasonStr);

    // CORRIGIDO: Profiling da operação de shutdown
    SCOPE_CYCLE_COUNTER(STAT_PCGStreamingManagerTick);
    TRACE_CPUPROFILER_EVENT_SCOPE(APCGStreamingManager::EndPlay);

    // CORRIGIDO: Cancelar operações ativas com timeout e logging detalhado
    int32 CancelledHandles = 0;
    const double StartTime = FPlatformTime::Seconds();

    for (auto& HandlePair : ActiveLoadHandles)
    {
        if (HandlePair.Value.IsValid())
        {
            HandlePair.Value->CancelHandle();
            CancelledHandles++;
        }
    }

    const double CancelTime = FPlatformTime::Seconds() - StartTime;
    UE_LOG(LogPCGStreamingAssets, Log, TEXT("Cancelled %d active load handles in %.3fs"),
           CancelledHandles, CancelTime);

    ActiveLoadHandles.Empty();

    // CORRIGIDO: Limpeza com contagem e logging
    const int32 QueuedLoads = LoadQueue.Num();
    const int32 QueuedUnloads = UnloadQueue.Num();
    const int32 CurrentLoading = CurrentlyLoading.Num();
    const int32 CurrentUnloading = CurrentlyUnloading.Num();
    const int32 CachedAssets = AssetCache.Num();
    const int32 RegisteredAssetsCount = RegisteredAssets.Num();
    const int32 StreamingRegionsCount = StreamingRegions.Num();
    const int32 StreamingViewersCount = StreamingViewers.Num();

    // Clear all queues and sets
    LoadQueue.Empty();
    UnloadQueue.Empty();
    CurrentlyLoading.Empty();
    CurrentlyUnloading.Empty();

    // Clear cache
    AssetCache.Empty();
    CacheAccessOrder.Empty();

    // Clear registered data
    RegisteredAssets.Empty();
    StreamingRegions.Empty();
    StreamingViewers.Empty();

    // CORRIGIDO: Reset de estados com logging
    bIsInitialized = false;
    bWorldPartitionEnabled = false;
    bFastGeometryStreamingEnabled = false;
    CurrentCacheSize = 0;

    Super::EndPlay(EndPlayReason);

    UE_LOG(LogPCGStreamingManager, Log, TEXT("EndPlay completed - Cleaned: %d queued loads, %d queued unloads, %d active loads, %d active unloads, %d cached assets, %d registered assets, %d regions, %d viewers"),
           QueuedLoads, QueuedUnloads, CurrentLoading, CurrentUnloading, CachedAssets,
           RegisteredAssetsCount, StreamingRegionsCount, StreamingViewersCount);
}

void APCGStreamingManager::Tick(float DeltaTime)
{
    // CORRIGIDO: Profiling completo da função Tick
    SCOPE_CYCLE_COUNTER(STAT_PCGStreamingManagerTick);
    TRACE_CPUPROFILER_EVENT_SCOPE(APCGStreamingManager::Tick);

    Super::Tick(DeltaTime);

    // CORRIGIDO: Verificação robusta de estados e CVars
    if (!bIsInitialized || bIsPaused || CVarPCGStreamingEnabled.GetValueOnGameThread() == 0)
    {
        return;
    }

    // CORRIGIDO: Bounds checking para DeltaTime
    const float ClampedDeltaTime = FMath::Clamp(DeltaTime, 0.0f, 1.0f);
    AccumulatedDeltaTime += ClampedDeltaTime;

    // CORRIGIDO: Update com intervalo dinâmico baseado em CVar
    const float UpdateInterval = CVarPCGStreamingUpdateInterval.GetValueOnGameThread();
    if (AccumulatedDeltaTime < UpdateInterval)
    {
        return;
    }

    // CORRIGIDO: Update streaming system com profiling
    {
        SCOPE_CYCLE_COUNTER(STAT_PCGStreamingRegionUpdate);
        UpdateStreamingSystem(ClampedDeltaTime);
    }

    // CORRIGIDO: Process queues com profiling separado
    {
        SCOPE_CYCLE_COUNTER(STAT_PCGStreamingAssetLoading);
        ProcessLoadQueue();
    }

    {
        SCOPE_CYCLE_COUNTER(STAT_PCGStreamingAssetUnloading);
        ProcessUnloadQueue();
    }

    // CORRIGIDO: Update states com profiling
    {
        SCOPE_CYCLE_COUNTER(STAT_PCGStreamingRegionUpdate);
        UpdateAssetStates();
        UpdateRegionStates();
    }

    // CORRIGIDO: Performance monitoring com intervalo otimizado
    if (bEnablePerformanceMonitoring)
    {
        const float CurrentTime = GetWorld()->GetTimeSeconds();
        if (CurrentTime - LastPerformanceUpdate >= 1.0f)
        {
            SCOPE_CYCLE_COUNTER(STAT_PCGStreamingPerformanceMonitor);
            UpdatePerformanceStats();
            LastPerformanceUpdate = CurrentTime;
        }
    }

    // CORRIGIDO: Auto optimization com intervalo configurável
    if (bAutoOptimization && AccumulatedDeltaTime >= 5.0f)
    {
        SCOPE_CYCLE_COUNTER(STAT_PCGStreamingCacheManagement);
        OptimizeMemoryUsage();
        AccumulatedDeltaTime = 0.0f;
    }

    // CORRIGIDO: Debug visualization com CVar check
    const bool bDebugEnabled = CVarPCGStreamingDebugVisualization.GetValueOnGameThread() > 0;
    if (bDebugEnabled || bShowDebugVisualization)
    {
        DrawDebugInfo();
    }
    
    LastUpdateTime = GetWorld()->GetTimeSeconds();
}

void APCGStreamingManager::InitializeStreamingSystem()
{
    UE_LOG(LogPCGStreamingManager, Log, TEXT("Initializing streaming system"));

    // CORRIGIDO: Validação robusta da configuração
    if (StreamingConfig.LoadDistance <= 0.0f)
    {
        StreamingConfig.LoadDistance = 5000.0f;
        UE_LOG(LogPCGStreamingManager, Warning, TEXT("Invalid LoadDistance, reset to default: %.1f"),
               (double)StreamingConfig.LoadDistance);
    }

    if (StreamingConfig.UnloadDistance <= StreamingConfig.LoadDistance)
    {
        StreamingConfig.UnloadDistance = StreamingConfig.LoadDistance * 1.5f;
        UE_LOG(LogPCGStreamingManager, Warning, TEXT("Invalid UnloadDistance, adjusted to %.1f"),
               (double)StreamingConfig.UnloadDistance);
    }

    // CORRIGIDO: Validação adicional de configurações críticas
    if (StreamingConfig.MaxConcurrentLoads <= 0)
    {
        StreamingConfig.MaxConcurrentLoads = 8;
        UE_LOG(LogPCGStreamingManager, Warning, TEXT("Invalid MaxConcurrentLoads, reset to %d"),
               StreamingConfig.MaxConcurrentLoads);
    }

    if (StreamingConfig.CacheSizeMB <= 0)
    {
        StreamingConfig.CacheSizeMB = 1024;
        UE_LOG(LogPCGStreamingManager, Warning, TEXT("Invalid CacheSizeMB, reset to %dMB"),
               StreamingConfig.CacheSizeMB);
    }

    // CORRIGIDO: Integração moderna com Asset Manager UE 5.6
    if (UAssetManager::IsInitialized())
    {
        UAssetManager& AssetManager = UAssetManager::Get();
        UE_LOG(LogPCGStreamingAssets, Log, TEXT("Asset Manager integration initialized"));

        // Configurar streaming settings modernos
        // UE 5.6: SetIsLoadingFromPakFiles não existe mais, usar método moderno
        // AssetManager.SetIsLoadingFromPakFiles(true); // Removido - não existe no UE 5.6
    }
    else
    {
        UE_LOG(LogPCGStreamingAssets, Warning, TEXT("Asset Manager not available - some features may be limited"));
    }

    // CORRIGIDO: Reset stats com inicialização completa
    CurrentStats = FPCGStreamingStats();
    CurrentStats.InitializationTime = FPlatformTime::Seconds();
    CurrentStats.TotalMemoryUsed = 0;
    CurrentStats.CachedAssets = 0;
    CurrentStats.ActiveLoads = 0;
    CurrentStats.QueuedLoads = 0;

    // CORRIGIDO: Inicializar contadores
    TotalAssetsLoaded = 0;
    TotalAssetsUnloaded = 0;
    CurrentCacheSize = 0;

    bIsInitialized = true;
    UE_LOG(LogPCGStreamingManager, Log, TEXT("Streaming system initialized successfully - Load distance: %.1f, Unload distance: %.1f, Max concurrent: %d, Cache: %dMB"),
           (double)StreamingConfig.LoadDistance, (double)StreamingConfig.UnloadDistance,
           StreamingConfig.MaxConcurrentLoads, StreamingConfig.CacheSizeMB);
}

void APCGStreamingManager::UpdateStreamingSystem(float DeltaTime)
{
    // Process different streaming modes
    switch (StreamingConfig.StreamingMode)
    {
        case EPCGStreamingMode::Distance:
            ProcessDistanceBasedStreaming();
            break;
            
        case EPCGStreamingMode::Frustum:
            ProcessFrustumBasedStreaming();
            break;
            
        case EPCGStreamingMode::Hybrid:
            ProcessDistanceBasedStreaming();
            ProcessFrustumBasedStreaming();
            break;
            
        case EPCGStreamingMode::Adaptive:
            ProcessAdaptiveStreaming();
            break;
            
        case EPCGStreamingMode::Manual:
            // Manual mode - no automatic processing
            break;
    }
    
    // Process viewer-based streaming
    ProcessViewerBasedStreaming();
    
    // Check memory usage
    if (bAutoOptimization)
    {
        CheckMemoryUsage();
    }
}

void APCGStreamingManager::ProcessLoadQueue()
{
    int32 ProcessedLoads = 0;
    
    while (!LoadQueue.IsEmpty() && ProcessedLoads < StreamingConfig.MaxConcurrentLoads)
    {
        TSoftObjectPtr<UObject> AssetToLoad;
        if (LoadQueue.Dequeue(AssetToLoad))
        {
            if (!CurrentlyLoading.Contains(AssetToLoad) && !IsAssetLoaded(AssetToLoad))
            {
                StartAssetLoad(AssetToLoad);
                ProcessedLoads++;
            }
        }
    }
}

void APCGStreamingManager::ProcessUnloadQueue()
{
    int32 ProcessedUnloads = 0;
    
    while (!UnloadQueue.IsEmpty() && ProcessedUnloads < StreamingConfig.MaxConcurrentUnloads)
    {
        TSoftObjectPtr<UObject> AssetToUnload;
        if (UnloadQueue.Dequeue(AssetToUnload))
        {
            if (!CurrentlyUnloading.Contains(AssetToUnload) && IsAssetLoaded(AssetToUnload))
            {
                StartAssetUnload(AssetToUnload);
                ProcessedUnloads++;
            }
        }
    }
}

void APCGStreamingManager::UpdateAssetStates()
{
    for (auto& AssetPair : RegisteredAssets)
    {
        FPCGStreamingAsset& Asset = AssetPair.Value;
        
        // Update asset state based on current conditions
        if (Asset.State == EPCGStreamingState::Loaded)
        {
            // Check if asset should be unloaded
            if (!IsAssetInViewerRange(Asset) && Asset.bCanBeUnloaded && !Asset.bIsEssential)
            {
                UnloadQueue.Enqueue(AssetPair.Key);
            }
        }
        else if (Asset.State == EPCGStreamingState::Unloaded)
        {
            // Check if asset should be loaded
            if (IsAssetInViewerRange(Asset) || Asset.bPreloadOnStart)
            {
                LoadQueue.Enqueue(AssetPair.Key);
            }
        }
    }
}

void APCGStreamingManager::UpdateRegionStates()
{
    for (auto& RegionPair : StreamingRegions)
    {
        FPCGStreamingRegion& Region = RegionPair.Value;
        
        if (!Region.bIsActive || !Region.bAutoManaged)
        {
            continue;
        }
        
        bool bShouldBeLoaded = IsRegionInViewerRange(Region);
        
        if (bShouldBeLoaded && !Region.bIsLoaded)
        {
            LoadRegion(RegionPair.Key);
        }
        else if (!bShouldBeLoaded && Region.bIsLoaded)
        {
            UnloadRegion(RegionPair.Key);
        }
        
        Region.LastUpdateTime = GetWorld()->GetTimeSeconds();
    }
}

void APCGStreamingManager::UpdatePerformanceStats()
{
    CurrentStats.TotalAssets = RegisteredAssets.Num();
    CurrentStats.LoadedAssets = 0;
    CurrentStats.LoadingAssets = CurrentlyLoading.Num();
    CurrentStats.CachedAssets = AssetCache.Num();
    CurrentStats.FailedAssets = 0;
    CurrentStats.ActiveRegions = 0;
    
    // Count loaded assets and failed assets
    for (const auto& AssetPair : RegisteredAssets)
    {
        const FPCGStreamingAsset& Asset = AssetPair.Value;
        
        if (Asset.State == EPCGStreamingState::Loaded)
        {
            CurrentStats.LoadedAssets++;
        }
        else if (Asset.State == EPCGStreamingState::Failed)
        {
            CurrentStats.FailedAssets++;
        }
    }
    
    // Count active regions
    for (const auto& RegionPair : StreamingRegions)
    {
        if (RegionPair.Value.bIsActive)
        {
            CurrentStats.ActiveRegions++;
        }
    }
    
    // Calculate streaming efficiency
    if (CurrentStats.TotalAssets > 0)
    {
        CurrentStats.StreamingEfficiency = (float)CurrentStats.LoadedAssets / (float)CurrentStats.TotalAssets * 100.0f;
    }
    
    // Update memory usage (simplified calculation)
    CurrentStats.MemoryUsageMB = CurrentStats.LoadedAssets * 10; // Rough estimate
    CurrentStats.CacheMemoryMB = CurrentStats.CachedAssets * 5; // Rough estimate
}

void APCGStreamingManager::ProcessViewerBasedStreaming()
{
    for (const auto& ViewerPair : StreamingViewers)
    {
        if (ViewerPair.Key.IsValid())
        {
            AActor* Viewer = ViewerPair.Key.Get();
            float ViewDistance = ViewerPair.Value;
            
            FVector ViewerLocation = Viewer->GetActorLocation();
            
            // Process assets based on viewer position
            for (auto& AssetPair : RegisteredAssets)
            {
                FPCGStreamingAsset& Asset = AssetPair.Value;
                float Distance = FVector::Dist(ViewerLocation, Asset.WorldPosition);
                
                float LoadDist = Asset.CustomLoadDistance > 0.0f ? Asset.CustomLoadDistance : StreamingConfig.LoadDistance;
                float UnloadDist = StreamingConfig.UnloadDistance;
                
                if (Distance <= LoadDist && Asset.State == EPCGStreamingState::Unloaded)
                {
                    LoadQueue.Enqueue(AssetPair.Key);
                }
                else if (Distance > UnloadDist && Asset.State == EPCGStreamingState::Loaded && Asset.bCanBeUnloaded)
                {
                    UnloadQueue.Enqueue(AssetPair.Key);
                }
            }
        }
    }
}

void APCGStreamingManager::ProcessDistanceBasedStreaming()
{
    // Already handled in ProcessViewerBasedStreaming
}

void APCGStreamingManager::ProcessFrustumBasedStreaming()
{
    // Get camera frustum from first player controller
    if (UWorld* World = GetWorld())
    {
        if (APlayerController* PC = World->GetFirstPlayerController())
        {
            FVector CameraLocation;
            FRotator CameraRotation;
            PC->GetPlayerViewPoint(CameraLocation, CameraRotation);
            
            // Simple frustum check - can be enhanced with proper frustum culling
            FVector ForwardVector = CameraRotation.Vector();
            
            for (auto& AssetPair : RegisteredAssets)
            {
                FPCGStreamingAsset& Asset = AssetPair.Value;
                FVector ToAsset = Asset.WorldPosition - CameraLocation;
                
                // Check if asset is in front of camera
                float DotProduct = FVector::DotProduct(ForwardVector, ToAsset.GetSafeNormal());
                
                if (DotProduct > 0.0f) // In front of camera
                {
                    float Distance = ToAsset.Size();
                    if (Distance <= StreamingConfig.LoadDistance && Asset.State == EPCGStreamingState::Unloaded)
                    {
                        LoadQueue.Enqueue(AssetPair.Key);
                    }
                }
            }
        }
    }
}

void APCGStreamingManager::ProcessAdaptiveStreaming()
{
    // Adaptive streaming based on performance metrics
    float CurrentFPS = 1.0f / GetWorld()->GetDeltaSeconds();
    float TargetFPS = 60.0f;
    
    if (CurrentFPS < TargetFPS * 0.8f) // Performance is low
    {
        // Reduce streaming aggressiveness
        StreamingConfig.LoadDistance *= 0.9f;
        StreamingConfig.MaxConcurrentLoads = FMath::Max(1, StreamingConfig.MaxConcurrentLoads - 1);
    }
    else if (CurrentFPS > TargetFPS * 1.1f) // Performance is good
    {
        // Increase streaming aggressiveness
        StreamingConfig.LoadDistance *= 1.05f;
        StreamingConfig.MaxConcurrentLoads = FMath::Min(20, StreamingConfig.MaxConcurrentLoads + 1);
    }
    
    // Process normal distance-based streaming with adjusted parameters
    ProcessDistanceBasedStreaming();
}

void APCGStreamingManager::StartAssetLoad(const TSoftObjectPtr<UObject>& AssetReference)
{
    if (CurrentlyLoading.Contains(AssetReference))
    {
        return;
    }
    
    CurrentlyLoading.Add(AssetReference);
    
    if (FPCGStreamingAsset* Asset = RegisteredAssets.Find(AssetReference))
    {
        Asset->State = EPCGStreamingState::Loading;
    }
    
    // Check cache first
    if (UObject* CachedAsset = GetFromCache(AssetReference))
    {
        CompleteAssetLoad(AssetReference, CachedAsset);
        return;
    }
    
    // Start async loading
    if (UAssetManager::IsInitialized())
    {
        UAssetManager& AssetManager = UAssetManager::Get();
        FStreamableManager& StreamableManager = AssetManager.GetStreamableManager();
        
        TSharedPtr<FStreamableHandle> Handle = StreamableManager.RequestAsyncLoad(
            AssetReference.ToSoftObjectPath(),
            FStreamableDelegate::CreateLambda([this, AssetReference]()
            {
                if (UObject* LoadedObject = AssetReference.Get())
                {
                    CompleteAssetLoad(AssetReference, LoadedObject);
                }
                else
                {
                    HandleLoadFailure(AssetReference, TEXT("Failed to load asset"));
                }
            }),
            FStreamableManager::AsyncLoadHighPriority
        );
        
        if (Handle.IsValid())
        {
            ActiveLoadHandles.Add(AssetReference, Handle);
        }
        else
        {
            HandleLoadFailure(AssetReference, TEXT("Failed to create streamable handle"));
        }
    }
    else
    {
        HandleLoadFailure(AssetReference, TEXT("Asset Manager not available"));
    }
}

void APCGStreamingManager::CompleteAssetLoad(const TSoftObjectPtr<UObject>& AssetReference, UObject* LoadedObject)
{
    CurrentlyLoading.Remove(AssetReference);
    ActiveLoadHandles.Remove(AssetReference);
    
    if (FPCGStreamingAsset* Asset = RegisteredAssets.Find(AssetReference))
    {
        Asset->State = EPCGStreamingState::Loaded;
        Asset->LoadedAsset = LoadedObject;
        Asset->LastAccessTime = GetWorld()->GetTimeSeconds();
        Asset->AccessCount++;
        
        // Add to cache
        AddToCache(AssetReference, LoadedObject);
        
        // Broadcast event
        OnAssetLoaded.Broadcast(*Asset, LoadedObject);
        
        UE_LOG(LogPCGStreamingAssets, Log, TEXT("Asset loaded successfully: %s"), *AssetReference.ToString());
        TotalAssetsLoaded++;
    }
}

void APCGStreamingManager::StartAssetUnload(const TSoftObjectPtr<UObject>& AssetReference)
{
    if (CurrentlyUnloading.Contains(AssetReference))
    {
        return;
    }
    
    CurrentlyUnloading.Add(AssetReference);
    
    if (FPCGStreamingAsset* Asset = RegisteredAssets.Find(AssetReference))
    {
        Asset->State = EPCGStreamingState::Unloading;
        
        // Broadcast event
        OnAssetUnloaded.Broadcast(*Asset);
        
        // Complete unload immediately (can be made async if needed)
        CompleteAssetUnload(AssetReference);
    }
}

void APCGStreamingManager::CompleteAssetUnload(const TSoftObjectPtr<UObject>& AssetReference)
{
    CurrentlyUnloading.Remove(AssetReference);
    
    if (FPCGStreamingAsset* Asset = RegisteredAssets.Find(AssetReference))
    {
        Asset->State = EPCGStreamingState::Unloaded;
        Asset->LoadedAsset = nullptr;
        
        // Keep in cache for potential reuse
        // RemoveFromCache(AssetReference);
        
        UE_LOG(LogPCGStreamingAssets, Log, TEXT("Asset unloaded: %s"), *AssetReference.ToString());
        TotalAssetsUnloaded++;
    }
}

void APCGStreamingManager::HandleLoadFailure(const TSoftObjectPtr<UObject>& AssetReference, const FString& ErrorMessage)
{
    CurrentlyLoading.Remove(AssetReference);
    ActiveLoadHandles.Remove(AssetReference);
    
    if (FPCGStreamingAsset* Asset = RegisteredAssets.Find(AssetReference))
    {
        Asset->State = EPCGStreamingState::Failed;
        
        // Broadcast event
        OnAssetLoadFailed.Broadcast(*Asset, ErrorMessage);
        
        UE_LOG(LogPCGStreamingAssets, Error, TEXT("Asset load failed: %s - %s"), *AssetReference.ToString(), *ErrorMessage);
    }
}

void APCGStreamingManager::AddToCache(const TSoftObjectPtr<UObject>& AssetReference, UObject* Asset)
{
    if (!Asset)
    {
        return;
    }
    
    // Remove from cache if already exists
    RemoveFromCache(AssetReference);
    
    // Add to cache
    AssetCache.Add(AssetReference, Asset);
    CacheAccessOrder.Add(AssetReference);
    CurrentCacheSize++;
    
    // Check cache size limit
    if (CurrentCacheSize > StreamingConfig.MaxCachedAssets)
    {
        OptimizeCache();
    }
}

void APCGStreamingManager::RemoveFromCache(const TSoftObjectPtr<UObject>& AssetReference)
{
    if (AssetCache.Remove(AssetReference) > 0)
    {
        CacheAccessOrder.Remove(AssetReference);
        CurrentCacheSize--;
    }
}

void APCGStreamingManager::OptimizeCache()
{
    // Remove oldest entries until we're under the limit
    while (CurrentCacheSize > StreamingConfig.MaxCachedAssets && CacheAccessOrder.Num() > 0)
    {
        TSoftObjectPtr<UObject> OldestAsset = CacheAccessOrder[0];
        RemoveFromCache(OldestAsset);
    }
}

UObject* APCGStreamingManager::GetFromCache(const TSoftObjectPtr<UObject>& AssetReference)
{
    if (TWeakObjectPtr<UObject>* CachedAsset = AssetCache.Find(AssetReference))
    {
        if (CachedAsset->IsValid())
        {
            // Move to end of access order (most recently used)
            CacheAccessOrder.Remove(AssetReference);
            CacheAccessOrder.Add(AssetReference);
            
            return CachedAsset->Get();
        }
        else
        {
            // Remove invalid reference
            RemoveFromCache(AssetReference);
        }
    }
    
    return nullptr;
}

float APCGStreamingManager::CalculateDistanceToAsset(const FPCGStreamingAsset& Asset) const
{
    float MinDistance = FLT_MAX;
    
    for (const auto& ViewerPair : StreamingViewers)
    {
        if (ViewerPair.Key.IsValid())
        {
            FVector ViewerLocation = ViewerPair.Key->GetActorLocation();
            float Distance = FVector::Dist(ViewerLocation, Asset.WorldPosition);
            MinDistance = FMath::Min(MinDistance, Distance);
        }
    }
    
    return MinDistance;
}

float APCGStreamingManager::CalculateDistanceToRegion(const FPCGStreamingRegion& Region) const
{
    float MinDistance = FLT_MAX;
    
    for (const auto& ViewerPair : StreamingViewers)
    {
        if (ViewerPair.Key.IsValid())
        {
            FVector ViewerLocation = ViewerPair.Key->GetActorLocation();
            float Distance = Region.RegionBounds.ComputeSquaredDistanceToPoint(ViewerLocation);
            MinDistance = FMath::Min(MinDistance, FMath::Sqrt(Distance));
        }
    }
    
    return MinDistance;
}

float APCGStreamingManager::GetStreamingEfficiency() const
{
    if (CurrentStats.TotalAssets == 0)
    {
        return 100.0f;
    }
    
    return (float)CurrentStats.LoadedAssets / (float)CurrentStats.TotalAssets * 100.0f;
}

bool APCGStreamingManager::IsAssetInViewerRange(const FPCGStreamingAsset& Asset) const
{
    float Distance = CalculateDistanceToAsset(Asset);
    float LoadDist = Asset.CustomLoadDistance > 0.0f ? Asset.CustomLoadDistance : StreamingConfig.LoadDistance;
    return Distance <= LoadDist;
}

bool APCGStreamingManager::IsRegionInViewerRange(const FPCGStreamingRegion& Region) const
{
    float Distance = CalculateDistanceToRegion(Region);
    return Distance <= Region.LoadDistance;
}

void APCGStreamingManager::CheckMemoryUsage()
{
    // Simplified memory check - in real implementation, use proper memory tracking
    if (CurrentStats.MemoryUsageMB > StreamingConfig.MaxMemoryUsageMB)
    {
        FreeMemoryIfNeeded();
    }
}

void APCGStreamingManager::FreeMemoryIfNeeded()
{
    int32 TargetMemoryMB = StreamingConfig.MaxMemoryUsageMB * 0.8f; // Target 80% of max
    UnloadLeastRecentlyUsedAssets(TargetMemoryMB);
}

void APCGStreamingManager::UnloadLeastRecentlyUsedAssets(int32 TargetMemoryMB)
{
    // Create list of assets sorted by last access time
    TArray<TPair<float, TSoftObjectPtr<UObject>>> AssetsByAccessTime;
    
    for (const auto& AssetPair : RegisteredAssets)
    {
        const FPCGStreamingAsset& Asset = AssetPair.Value;
        if (Asset.State == EPCGStreamingState::Loaded && Asset.bCanBeUnloaded && !Asset.bIsEssential)
        {
            AssetsByAccessTime.Add(TPair<float, TSoftObjectPtr<UObject>>(Asset.LastAccessTime, AssetPair.Key));
        }
    }
    
    // Sort by access time (oldest first)
    AssetsByAccessTime.Sort([](const TPair<float, TSoftObjectPtr<UObject>>& A, const TPair<float, TSoftObjectPtr<UObject>>& B)
    {
        return A.Key < B.Key;
    });
    
    // Unload assets until we reach target memory usage
    for (const auto& AssetPair : AssetsByAccessTime)
    {
        if (CurrentStats.MemoryUsageMB <= TargetMemoryMB)
        {
            break;
        }
        
        UnloadQueue.Enqueue(AssetPair.Value);
    }
}

void APCGStreamingManager::DrawDebugInfo()
{
    if (!GetWorld())
    {
        return;
    }
    
    // Draw asset positions and states
    for (const auto& AssetPair : RegisteredAssets)
    {
        const FPCGStreamingAsset& Asset = AssetPair.Value;
        
        FColor DebugColor = FColor::White;
        switch (Asset.State)
        {
            case EPCGStreamingState::Loaded:
                DebugColor = FColor::Green;
                break;
            case EPCGStreamingState::Loading:
                DebugColor = FColor::Yellow;
                break;
            case EPCGStreamingState::Unloaded:
                DebugColor = FColor::Red;
                break;
            case EPCGStreamingState::Failed:
                DebugColor = FColor::Purple;
                break;
            case EPCGStreamingState::Cached:
                DebugColor = FColor::Blue;
                break;
        }
        
        DrawDebugSphere(GetWorld(), Asset.WorldPosition, 100.0f, 8, DebugColor, false, -1.0f, 0, 5.0f);
    }
    
    // Draw streaming regions
    for (const auto& RegionPair : StreamingRegions)
    {
        const FPCGStreamingRegion& Region = RegionPair.Value;
        FColor RegionColor = Region.bIsLoaded ? FColor::Green : FColor::Red;
        
        DrawDebugBox(GetWorld(), Region.RegionBounds.GetCenter(), Region.RegionBounds.GetExtent(), RegionColor, false, -1.0f, 0, 10.0f);
    }
}

void APCGStreamingManager::OnAssetLoadCompleted()
{
    // Note: In UE 5.6, we need to track loaded assets differently
    // This callback doesn't provide the asset reference directly
    // We'll need to check all currently loading assets
    
    TArray<TSoftObjectPtr<UObject>> CompletedAssets;
    
    for (const TSoftObjectPtr<UObject>& AssetRef : CurrentlyLoading)
    {
        if (UObject* LoadedObject = AssetRef.Get())
        {
            CompletedAssets.Add(AssetRef);
            CompleteAssetLoad(AssetRef, LoadedObject);
        }
    }
    
    // Remove completed assets from loading set
    for (const TSoftObjectPtr<UObject>& CompletedAsset : CompletedAssets)
    {
        CurrentlyLoading.Remove(CompletedAsset);
    }
}

void APCGStreamingManager::HandleAssetLoadFailure(const TSoftObjectPtr<UObject>& AssetReference)
{
    HandleLoadFailure(AssetReference, TEXT("Async load operation failed"));
}

// Public API Implementation
void APCGStreamingManager::RegisterAsset(const FPCGStreamingAsset& Asset)
{
    RegisteredAssets.Add(Asset.AssetReference, Asset);
    UE_LOG(LogPCGStreamingAssets, Log, TEXT("Asset registered: %s (Priority: %s)"),
           *Asset.AssetReference.ToString(),
           *UEnum::GetValueAsString(Asset.Priority));
}

void APCGStreamingManager::UnregisterAsset(const TSoftObjectPtr<UObject>& AssetReference)
{
    if (RegisteredAssets.Remove(AssetReference) > 0)
    {
        // Cancel any active operations
        // Note: TQueue doesn't have Remove method, so we clear the queues if needed
        // LoadQueue and UnloadQueue will be processed normally
        CurrentlyLoading.Remove(AssetReference);
        CurrentlyUnloading.Remove(AssetReference);
        
        if (TSharedPtr<FStreamableHandle>* Handle = ActiveLoadHandles.Find(AssetReference))
        {
            if (Handle->IsValid())
            {
                (*Handle)->CancelHandle();
            }
            ActiveLoadHandles.Remove(AssetReference);
        }
        
        RemoveFromCache(AssetReference);
        
        UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Asset unregistered: %s"), *AssetReference.ToString());
    }
}

void APCGStreamingManager::UpdateAssetPosition(const TSoftObjectPtr<UObject>& AssetReference, const FVector& NewPosition)
{
    if (FPCGStreamingAsset* Asset = RegisteredAssets.Find(AssetReference))
    {
        Asset->WorldPosition = NewPosition;
        bNeedsUpdate = true;
    }
}

void APCGStreamingManager::SetAssetPriority(const TSoftObjectPtr<UObject>& AssetReference, EPCGStreamingManagerPriority Priority)
{
    if (FPCGStreamingAsset* Asset = RegisteredAssets.Find(AssetReference))
    {
        Asset->Priority = Priority;
    }
}

bool APCGStreamingManager::IsAssetLoaded(const TSoftObjectPtr<UObject>& AssetReference) const
{
    if (const FPCGStreamingAsset* Asset = RegisteredAssets.Find(AssetReference))
    {
        return Asset->State == EPCGStreamingState::Loaded;
    }
    return false;
}

UObject* APCGStreamingManager::GetLoadedAsset(const TSoftObjectPtr<UObject>& AssetReference) const
{
    if (const FPCGStreamingAsset* Asset = RegisteredAssets.Find(AssetReference))
    {
        if (Asset->State == EPCGStreamingState::Loaded && Asset->LoadedAsset.IsValid())
        {
            return Asset->LoadedAsset.Get();
        }
    }
    return nullptr;
}

TArray<FPCGStreamingAsset> APCGStreamingManager::GetAllAssets() const
{
    TArray<FPCGStreamingAsset> Assets;
    RegisteredAssets.GenerateValueArray(Assets);
    return Assets;
}

void APCGStreamingManager::CreateRegion(const FPCGStreamingRegion& Region)
{
    StreamingRegions.Add(Region.RegionName, Region);
    UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Region created: %s"), *Region.RegionName);
}

void APCGStreamingManager::RemoveRegion(const FString& RegionName)
{
    if (StreamingRegions.Remove(RegionName) > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Region removed: %s"), *RegionName);
    }
}

void APCGStreamingManager::UpdateRegion(const FString& RegionName, const FPCGStreamingRegion& UpdatedRegion)
{
    if (FPCGStreamingRegion* Region = StreamingRegions.Find(RegionName))
    {
        *Region = UpdatedRegion;
        Region->RegionName = RegionName; // Ensure name consistency
    }
}

void APCGStreamingManager::LoadRegion(const FString& RegionName)
{
    if (FPCGStreamingRegion* Region = StreamingRegions.Find(RegionName))
    {
        for (const FPCGStreamingAsset& Asset : Region->Assets)
        {
            LoadQueue.Enqueue(Asset.AssetReference);
        }
        
        Region->bIsLoaded = true;
        OnRegionLoaded.Broadcast(RegionName, true);
        
        UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Region loading started: %s"), *RegionName);
    }
}

void APCGStreamingManager::UnloadRegion(const FString& RegionName)
{
    if (FPCGStreamingRegion* Region = StreamingRegions.Find(RegionName))
    {
        for (const FPCGStreamingAsset& Asset : Region->Assets)
        {
            if (Asset.bCanBeUnloaded)
            {
                UnloadQueue.Enqueue(Asset.AssetReference);
            }
        }
        
        Region->bIsLoaded = false;
        OnRegionUnloaded.Broadcast(RegionName);
        
        UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Region unloading started: %s"), *RegionName);
    }
}

bool APCGStreamingManager::IsRegionLoaded(const FString& RegionName) const
{
    if (const FPCGStreamingRegion* Region = StreamingRegions.Find(RegionName))
    {
        return Region->bIsLoaded;
    }
    return false;
}

TArray<FPCGStreamingRegion> APCGStreamingManager::GetAllRegions() const
{
    TArray<FPCGStreamingRegion> Regions;
    StreamingRegions.GenerateValueArray(Regions);
    return Regions;
}

void APCGStreamingManager::ForceLoadAsset(const TSoftObjectPtr<UObject>& AssetReference)
{
    LoadQueue.Enqueue(AssetReference);
}

void APCGStreamingManager::ForceUnloadAsset(const TSoftObjectPtr<UObject>& AssetReference)
{
    UnloadQueue.Enqueue(AssetReference);
}

void APCGStreamingManager::PreloadAssetsInRadius(const FVector& Center, float Radius)
{
    for (const auto& AssetPair : RegisteredAssets)
    {
        const FPCGStreamingAsset& Asset = AssetPair.Value;
        float Distance = FVector::Dist(Center, Asset.WorldPosition);
        
        if (Distance <= Radius && Asset.State == EPCGStreamingState::Unloaded)
        {
            LoadQueue.Enqueue(AssetPair.Key);
        }
    }
}

void APCGStreamingManager::UnloadAssetsOutsideRadius(const FVector& Center, float Radius)
{
    for (const auto& AssetPair : RegisteredAssets)
    {
        const FPCGStreamingAsset& Asset = AssetPair.Value;
        float Distance = FVector::Dist(Center, Asset.WorldPosition);
        
        if (Distance > Radius && Asset.State == EPCGStreamingState::Loaded && Asset.bCanBeUnloaded)
        {
            UnloadQueue.Enqueue(AssetPair.Key);
        }
    }
}

void APCGStreamingManager::SetStreamingMode(EPCGStreamingMode Mode)
{
    StreamingConfig.StreamingMode = Mode;
    UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Streaming mode changed to: %d"), (int32)Mode);
}

void APCGStreamingManager::PauseStreaming(bool bPause)
{
    bIsPaused = bPause;
    UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Streaming %s"), bPause ? TEXT("paused") : TEXT("resumed"));
}

FPCGStreamingStats APCGStreamingManager::GetStreamingStats() const
{
    return CurrentStats;
}

void APCGStreamingManager::OptimizeMemoryUsage()
{
    OptimizeCache();
    FreeMemoryIfNeeded();
    
    // Force garbage collection if needed
    if (CurrentStats.MemoryUsageMB > StreamingConfig.MaxMemoryUsageMB * 0.9f)
    {
        GEngine->ForceGarbageCollection(true);
    }
}

void APCGStreamingManager::ClearCache()
{
    AssetCache.Empty();
    CacheAccessOrder.Empty();
    CurrentCacheSize = 0;
    
    UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Cache cleared"));
}

void APCGStreamingManager::SetMaxMemoryUsage(int32 MaxMemoryMB)
{
    StreamingConfig.MaxMemoryUsageMB = FMath::Max(100, MaxMemoryMB);
}

void APCGStreamingManager::SetMaxConcurrentLoads(int32 MaxLoads)
{
    StreamingConfig.MaxConcurrentLoads = FMath::Clamp(MaxLoads, 1, 50);
}

void APCGStreamingManager::IntegrateWithWorldPartition(APCGWorldPartitionManager* WorldPartitionManager)
{
    WorldPartitionManagerRef = WorldPartitionManager;
    UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Integrated with World Partition Manager"));
}

void APCGStreamingManager::IntegrateWithNanite(APCGNaniteOptimizer* NaniteOptimizer)
{
    NaniteOptimizerRef = NaniteOptimizer;
    UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Integrated with Nanite Optimizer"));
}

void APCGStreamingManager::IntegrateWithPerformanceProfiler(UPCGPerformanceProfiler* PerformanceProfiler)
{
    PerformanceProfilerRef = PerformanceProfiler;
    UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Integrated with Performance Profiler"));
}

void APCGStreamingManager::SynchronizeWithPCGSystem()
{
    // Synchronize with PCG system - implementation depends on specific PCG integration
    UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Synchronized with PCG system"));
}

void APCGStreamingManager::AddStreamingViewer(AActor* Viewer, float ViewDistance)
{
    if (Viewer)
    {
        StreamingViewers.Add(Viewer, ViewDistance);
        UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Streaming viewer added: %s"), *Viewer->GetName());
    }
}

void APCGStreamingManager::RemoveStreamingViewer(AActor* Viewer)
{
    if (StreamingViewers.Remove(Viewer) > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("APCGStreamingManager: Streaming viewer removed: %s"), Viewer ? *Viewer->GetName() : TEXT("NULL"));
    }
}

void APCGStreamingManager::UpdateViewerPosition(AActor* Viewer, const FVector& Position)
{
    if (Viewer && StreamingViewers.Contains(Viewer))
    {
        // Position is automatically updated through Actor's location
        bNeedsUpdate = true;
    }
}

TArray<AActor*> APCGStreamingManager::GetActiveViewers() const
{
    TArray<AActor*> Viewers;
    for (const auto& ViewerPair : StreamingViewers)
    {
        if (ViewerPair.Key.IsValid())
        {
            Viewers.Add(ViewerPair.Key.Get());
        }
    }
    return Viewers;
}