// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AMapManager.h"

#ifdef AURA_AMapManager_generated_h
#error "AMapManager.generated.h already included, missing '#pragma once' in AMapManager.h"
#endif
#define AURA_AMapManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class ABaronAuracronManager;
class ADragonPrismalManager;
class ALaneManager;
class AMinionWaveManager;
class ARiverPrismalManager;
class AWallCollisionManager;
enum class EMapGenerationPhase : uint8;
enum class EMapValidationLevel : uint8;
struct FMapConfiguration;
struct FMapGenerationProgress;
struct FMapValidationResult;

// ********** Begin ScriptStruct FMapConfiguration *************************************************
#define FID_Aura_Source_Aura_Public_AMapManager_h_68_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMapConfiguration_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FMapConfiguration;
// ********** End ScriptStruct FMapConfiguration ***************************************************

// ********** Begin ScriptStruct FMapGenerationProgress ********************************************
#define FID_Aura_Source_Aura_Public_AMapManager_h_106_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMapGenerationProgress_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FMapGenerationProgress;
// ********** End ScriptStruct FMapGenerationProgress **********************************************

// ********** Begin ScriptStruct FMapValidationResult **********************************************
#define FID_Aura_Source_Aura_Public_AMapManager_h_144_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMapValidationResult_Statics; \
	AURA_API static class UScriptStruct* StaticStruct();


struct FMapValidationResult;
// ********** End ScriptStruct FMapValidationResult ************************************************

// ********** Begin Delegate FOnMapGenerationPhaseChanged ******************************************
#define FID_Aura_Source_Aura_Public_AMapManager_h_176_DELEGATE \
AURA_API void FOnMapGenerationPhaseChanged_DelegateWrapper(const FMulticastScriptDelegate& OnMapGenerationPhaseChanged, EMapGenerationPhase NewPhase);


// ********** End Delegate FOnMapGenerationPhaseChanged ********************************************

// ********** Begin Delegate FOnMapGenerationProgress **********************************************
#define FID_Aura_Source_Aura_Public_AMapManager_h_177_DELEGATE \
AURA_API void FOnMapGenerationProgress_DelegateWrapper(const FMulticastScriptDelegate& OnMapGenerationProgress, FMapGenerationProgress const& Progress);


// ********** End Delegate FOnMapGenerationProgress ************************************************

// ********** Begin Delegate FOnMapGenerationComplete **********************************************
#define FID_Aura_Source_Aura_Public_AMapManager_h_178_DELEGATE \
AURA_API void FOnMapGenerationComplete_DelegateWrapper(const FMulticastScriptDelegate& OnMapGenerationComplete, bool bSuccess);


// ********** End Delegate FOnMapGenerationComplete ************************************************

// ********** Begin Delegate FOnMapValidationComplete **********************************************
#define FID_Aura_Source_Aura_Public_AMapManager_h_179_DELEGATE \
AURA_API void FOnMapValidationComplete_DelegateWrapper(const FMulticastScriptDelegate& OnMapValidationComplete, FMapValidationResult const& ValidationResult);


// ********** End Delegate FOnMapValidationComplete ************************************************

// ********** Begin Delegate FOnMapGenerationError *************************************************
#define FID_Aura_Source_Aura_Public_AMapManager_h_180_DELEGATE \
AURA_API void FOnMapGenerationError_DelegateWrapper(const FMulticastScriptDelegate& OnMapGenerationError, const FString& ErrorMessage, EMapGenerationPhase Phase);


// ********** End Delegate FOnMapGenerationError ***************************************************

// ********** Begin Class AMapManager **************************************************************
#define FID_Aura_Source_Aura_Public_AMapManager_h_198_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetMapStatistics); \
	DECLARE_FUNCTION(execSetDebugMode); \
	DECLARE_FUNCTION(execGetMinionManager); \
	DECLARE_FUNCTION(execGetRiverManager); \
	DECLARE_FUNCTION(execGetWallManager); \
	DECLARE_FUNCTION(execGetDragonManager); \
	DECLARE_FUNCTION(execGetBaronManager); \
	DECLARE_FUNCTION(execGetLaneManager); \
	DECLARE_FUNCTION(execGetCurrentPhase); \
	DECLARE_FUNCTION(execGetLastValidationResult); \
	DECLARE_FUNCTION(execGetGenerationProgress); \
	DECLARE_FUNCTION(execIsValidating); \
	DECLARE_FUNCTION(execIsGenerating); \
	DECLARE_FUNCTION(execClearMap); \
	DECLARE_FUNCTION(execValidateMap); \
	DECLARE_FUNCTION(execRestartMapGeneration); \
	DECLARE_FUNCTION(execStopMapGeneration); \
	DECLARE_FUNCTION(execStartMapGeneration);


AURA_API UClass* Z_Construct_UClass_AMapManager_NoRegister();

#define FID_Aura_Source_Aura_Public_AMapManager_h_198_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAMapManager(); \
	friend struct Z_Construct_UClass_AMapManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_AMapManager_NoRegister(); \
public: \
	DECLARE_CLASS2(AMapManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_AMapManager_NoRegister) \
	DECLARE_SERIALIZER(AMapManager)


#define FID_Aura_Source_Aura_Public_AMapManager_h_198_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AMapManager(AMapManager&&) = delete; \
	AMapManager(const AMapManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AMapManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AMapManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AMapManager) \
	NO_API virtual ~AMapManager();


#define FID_Aura_Source_Aura_Public_AMapManager_h_195_PROLOG
#define FID_Aura_Source_Aura_Public_AMapManager_h_198_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_AMapManager_h_198_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_AMapManager_h_198_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_AMapManager_h_198_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AMapManager;

// ********** End Class AMapManager ****************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_AMapManager_h

// ********** Begin Enum EMapGenerationPhase *******************************************************
#define FOREACH_ENUM_EMAPGENERATIONPHASE(op) \
	op(EMapGenerationPhase::None) \
	op(EMapGenerationPhase::Initializing) \
	op(EMapGenerationPhase::GeneratingTerrain) \
	op(EMapGenerationPhase::CreatingLanes) \
	op(EMapGenerationPhase::PlacingObjectives) \
	op(EMapGenerationPhase::BuildingWalls) \
	op(EMapGenerationPhase::GeneratingRiver) \
	op(EMapGenerationPhase::SpawningMinions) \
	op(EMapGenerationPhase::Validating) \
	op(EMapGenerationPhase::Complete) \
	op(EMapGenerationPhase::Error) 

enum class EMapGenerationPhase : uint8;
template<> struct TIsUEnumClass<EMapGenerationPhase> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EMapGenerationPhase>();
// ********** End Enum EMapGenerationPhase *********************************************************

// ********** Begin Enum EMapSize ******************************************************************
#define FOREACH_ENUM_EMAPSIZE(op) \
	op(EMapSize::Small) \
	op(EMapSize::Medium) \
	op(EMapSize::Large) \
	op(EMapSize::Custom) 

enum class EMapSize : uint8;
template<> struct TIsUEnumClass<EMapSize> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EMapSize>();
// ********** End Enum EMapSize ********************************************************************

// ********** Begin Enum EMapValidationLevel *******************************************************
#define FOREACH_ENUM_EMAPVALIDATIONLEVEL(op) \
	op(EMapValidationLevel::Basic) \
	op(EMapValidationLevel::Standard) \
	op(EMapValidationLevel::Comprehensive) \
	op(EMapValidationLevel::Debug) 

enum class EMapValidationLevel : uint8;
template<> struct TIsUEnumClass<EMapValidationLevel> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EMapValidationLevel>();
// ********** End Enum EMapValidationLevel *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
