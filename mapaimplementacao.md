# 🛠️ PLANO DE IMPLEMENTAÇÃO - MAPA AURACRON (ATUALIZADO)

## **VISÃO GERAL**

Este documento define o plano de implementação para criar todos os arquivos .cpp e .h necessários para o sistema de mapa do AURACRON com **dimensões corretas UE5.6 (1 UU = 1 cm)** e elementos detalhados baseados na análise de concorrentes. O foco está em **tarefas robustas** para cada arquivo, sem código, especificando responsabilidades e entregáveis.

### **Dimensões de Referência (UE5.6)**
- **Mapa Total**: 16000x16000 UU (160m x 160m)
- **<PERSON><PERSON>**: 14400x14400 UU (144m x 144m)
- **Personagem Padrão**: 180 UU (1.8m de altura)
- **Paredes Externas**: 800 UU (8m de altura)

## **ESTRUTURA DE TAREFAS PARA DESENVOLVIMENTO**

---

---

## **NOVAS TAREFAS ROBUSTAS - SISTEMA MATEMÁTICO COMPLETO**

### **FASE 1A: SISTEMA DE LANES E GEOMETRIA PRECISA (Semanas 1-3)**

#### **ALaneManager.h/.cpp**
**Responsabilidades:**
- Implementar funções lineares exatas para as 3 lanes com geometria matemática precisa
- Controlar spawns de minions em coordenadas UE5.6 exatas
- Implementar sistema de torres com geometria cilíndrica e octogonal
- Gerenciar pontes do Rio Prismal com detecção matemática

**Tarefas Detalhadas:**
1. **Estrutura Matemática de Lanes**:
   - Lane Superior: Y = -0.577X + 6928 (±300 UU largura)
   - Lane Central: Y = 0 (±400 UU largura)
   - Lane Inferior: Y = 0.577X - 6928 (±300 UU largura)
   - Sistema de coordenadas UE5.6: 1 UU = 1 cm, mapa 16000×16000 UU

2. **Sistema de Torres com Geometria Precisa**:
   - Torres Externas: Cilindros cônicos (raio base 120 UU, topo 80 UU, altura 600 UU)
   - Torres Internas: Prismas octogonais (raio 100 UU, altura 800 UU)
   - Torres de Inibidor: Torres duplas (raio 150+100 UU, altura 1000 UU)
   - Algoritmo de targeting com busca radial em incrementos de 45°

3. **Waypoints Matemáticos**:
   - 12 pontos por lane com distâncias 800-1200 UU
   - Algoritmo A* pathfinding com recálculo a cada 2 segundos
   - Função de distância: sqrt((x2-x1)² + (y2-y1)²)

4. **Integração com Rio Prismal**:
   - 2 pontes de 400 UU de largura na lane central
   - Detecção matemática de entrada/saída do rio
   - Sistema de redução de velocidade (50% velocidade base)

**Entregáveis:**
- Header com enums ELaneType, structs FLaneData, FTowerData
- Implementação de funções matemáticas para geometria
- Sistema de pathfinding A* funcional
- Testes de precisão geométrica (±1 UU tolerância)

#### **ABaronAuracronManager.h/.cpp**
**Responsabilidades:**
- Gerenciar Barão Auracron com geometria hexagonal precisa
- Controlar mecânicas de combate com cálculos matemáticos
- Implementar sistema de buffs de equipe
- Gerenciar área hexagonal com entradas controladas

**Tarefas Detalhadas:**
1. **Geometria Hexagonal do Covil**:
   - Hexágono regular: Centro (0, -4800), raio 700 UU
   - Vértices calculados: 6 pontos com ângulos múltiplos de 60°
   - Área: (3√3/2) × 700² = 1.272.792 UU²
   - 3 entradas de 200 UU nos vértices pares

2. **Barão Auracron com Timing Preciso**:
   - Spawn timing: t = 1200s (20min), respawn = 360s (6min)
   - HP escalável: 5000 + (300 × minutos_de_jogo)
   - Sistema de rotação hexagonal a cada 60s
   - Mecânicas de área com cálculos geométricos

3. **Sistema de Buffs Matemático**:
   - Buff de equipe: +20% dano, +15% velocidade por 180s
   - Área de efeito: Círculo raio 1200 UU
   - Distribuição de recompensas com fórmulas
   - Controle territorial hexagonal

4. **Sentinelas e Guardiões Secundários**:
   - Sentinelas Cristalinas: 4 círculos raio 400 UU em (±3000, ±2400)
   - Guardiões dos Portais: 2 hexágonos raio 300 UU em (±4500, 0)
   - Sistema de rotação: 90° a cada 120s (Sentinelas), 60° a cada 30s (Guardiões)
   - Respawn automático: 180s (Sentinelas), 240s (Guardiões)

**Entregáveis:**
- Classes de objetivos com geometrias matemáticas precisas
- Algoritmos de spawn e respawn temporais
- Sistema de recompensas escaláveis
- Funções trigonométricas para rotações e movimentos

### **FASE 1B: SISTEMA DE OBJETIVOS COM GEOMETRIA MATEMÁTICA (Semanas 4-6)**

#### **ADragonPrismalManager.h/.cpp**
**Responsabilidades:**
- Gerenciar Covil do Dragão Prismal com geometria elíptica precisa
- Controlar mecânicas de combate com cálculos matemáticos
- Implementar sistema de recompensas escaláveis
- Gerenciar área de combate com equações geométricas

**Tarefas Detalhadas:**
1. **Geometria Elíptica do Covil**:
   - Equação da elipse: (x-0)²/800² + (y-4800)²/600² = 1
   - Centro: (0, +4800) UU
   - Semi-eixos: a = 800 UU, b = 600 UU
   - Área: π × 800 × 600 = 1.507.964 UU²
   - 3 entradas de 300 UU nos ângulos 0°, 120°, 240°

2. **Dragão Prismal com Escalabilidade Matemática**:
   - HP escalável: 3000 + (200 × minutos_de_jogo)
   - Sistema de spawn: t = 300s (5min), respawn = 360s (6min)
   - Resistências: 40% mágico, 20% físico
   - Área de combate calculada matematicamente

3. **Mecânicas com Cálculos Precisos**:
   - Sopro Prismal: Cone 60°, alcance 1000 UU
   - Cristalização: -50% velocidade por 3s
   - Fúria Crescente: +10% dano por 25% HP perdido
   - Funções trigonométricas para ataques direcionais

4. **Sistema de Recompensas Escaláveis**:
   - Buff matemático: +20% dano, +15% velocidade por 180s
   - Gold e experiência com fórmulas de distribuição
   - Controle territorial com áreas calculadas

**Entregáveis:**
- Struct FDragonData com geometrias matemáticas precisas
- Algoritmos de spawn e respawn temporais
- Sistema de IA com cálculos geométricos
- Funções trigonométricas para mecânicas de combate

### **FASE 1C: SISTEMA DE PAREDES E COLISÕES MATEMÁTICO (Semanas 7-9)**

#### **AWallCollisionManager.h/.cpp**
**Responsabilidades:**
- Gerenciar paredes externas com geometria retangular precisa
- Controlar colisões com funções matemáticas otimizadas
- Implementar sistema de bases hexagonais com cálculos exatos
- Gerenciar teleporte e áreas protegidas com coordenadas precisas

**Tarefas Detalhadas:**
1. **Paredes Externas - Geometria Retangular**:
   - Coordenadas: Retângulo X(±8000), Y(±8000) UU
   - Altura: 800 UU (Z = 0 a +800)
   - Espessura: 200 UU (interna ao perímetro)
   - Geometria dos cantos: Raio de curvatura 100 UU
   - Força de repulsão: 2000 UU/s² (empurra para dentro)

2. **Paredes das Lanes - Funções Lineares**:
   - Lane Superior: Y = -0.577X + 6928 ± 300 UU
   - Lane Central: Y = ±400 UU (X de -4800 a +4800)
   - Lane Inferior: Y = 0.577X - 6928 ± 300 UU
   - Material: Pedra Prismal (HP: 2000 por seção 100 UU)
   - Resistências: 50% físico, 30% mágico

3. **Sistema de Bases Hexagonais**:
   - Base Azul: Centro (-6000, -6000) UU, raio 1200 UU
   - Base Vermelha: Centro (+6000, +6000) UU, raio 1200 UU
   - Vértices calculados: 6 pontos com ângulos múltiplos de 60°
   - Área protegida: (3√3/2) × 1200² = 3.742.320 UU²

4. **Estruturas Geométricas das Bases**:
   - Nexus: Cilindro raio 150 UU, altura 400 UU, HP 5000
   - Inibidores: 3 prismas hexagonais 80×80×300 UU, HP 4000
   - Fonte de cura: Círculo raio 300 UU, taxa 50 HP/s + 5% HP máximo
   - Teleporte: 2 círculos raio 100 UU, cooldown 8s

**Entregáveis:**
- Algoritmos de detecção de colisão com precisão ±1 UU
- Funções matemáticas para áreas hexagonais e circulares
- Sistema de força de repulsão com vetores calculados
- Integração com sistema de coordenadas UE5.6

#### **ARiverPrismalManager.h/.cpp**
**Responsabilidades:**
- Gerenciar Rio Prismal com geometria senoidal precisa
- Controlar Ilha Central com hexágono matemático exato
- Implementar sistema de velocidade com cálculos físicos
- Gerenciar pontes e travessias com coordenadas precisas

**Tarefas Detalhadas:**
1. **Geometria Senoidal do Rio**:
   - Função matemática: Y = 200 × sin(πX/4800)
   - Largura variável: 1200 UU ± 200 × |sin(πX/2400)|
   - Comprimento total: 9600 UU (X de -4800 a +4800)
   - Velocidade de corrente: 150 + 50 × cos(πX/2400) UU/s

2. **Ilha Central Hexagonal**:
   - Centro: (0, 0) UU
   - Hexágono regular: raio 600 UU
   - Vértices: 6 pontos calculados com cos(nπ/3), sin(nπ/3)
   - Área: (3√3/2) × 600² = 936.307 UU²
   - Rotação: 15° a cada 60 segundos

3. **Sistema de Velocidade Matemático**:
   - Redução na água: velocidade × 0.5
   - Função de detecção: |Y| ≤ |200 × sin(πX/4800)| + 600
   - Corrente adicional: vetor (150, 0) UU/s no rio
   - Efeitos de turbulência: ±50 UU/s aleatório

4. **Pontes e Acessos Calculados**:
   - Pontes da lane central: X = ±600 UU, largura 400 UU
   - 6 acessos à ilha: coordenadas dos vértices hexagonais
   - Pathfinding aquático: algoritmo A* modificado para água
   - Tempo de travessia: distância / (velocidade × 0.5)

**Entregáveis:**
- Funções senoidais para geometria fluida do rio
- Algoritmos de detecção de área aquática com precisão
- Sistema de modificação de velocidade baseado em posição
- Cálculos de pathfinding aquático otimizado

#### **AMinionWaveManager.h/.cpp**
**Responsabilidades:**
- Gerenciar sistema de ondas com progressão matemática
- Controlar spawning de minions com timing preciso
- Implementar pathfinding A* para 12 waypoints por lane
- Gerenciar recompensas escaláveis com fórmulas

**Tarefas Detalhadas:**
1. **Sistema de Ondas Matemático**:
   - Função temporal: Onda_n = 90 + (n-1) × 30 segundos
   - Primeira onda: t = 90s, intervalo: 30s
   - Progressão de minions: Melee = 3 + floor(n/20), Ranged = 3 + floor(n/25)
   - Minions de cerco: 1 unidade a cada 2-3 ondas (dependendo do tempo)

2. **Especificações Matemáticas dos Minions**:
   - HP Melee: 450 + (20 × número_da_onda)
   - HP Ranged: 300 + (15 × número_da_onda)
   - HP Cerco: 800 + (50 × número_da_onda)
   - Velocidades: 300, 280, 200 UU/s respectivamente

3. **Pathfinding A* com Waypoints**:
   - 12 waypoints por lane com distâncias 800-1200 UU
   - Função de custo: g(n) + h(n) onde h = distância euclidiana
   - Recálculo automático a cada 2 segundos
   - Prioridade de alvo: Torre > Minion > Campeão

4. **Sistema de Recompensas Escaláveis**:
   - Ouro Melee: 20 + (1 × número_da_onda)
   - Ouro Ranged: 25 + (1.5 × número_da_onda)
   - XP: 30-500 + (2-25 × número_da_onda)
   - Raio de compartilhamento: 1200 UU

**Entregáveis:**
- Sistema de timing com funções matemáticas precisas
- Algoritmo A* otimizado para pathfinding de minions
- Fórmulas de progressão para HP, dano e recompensas
- Sistema de detecção de alvos com prioridades numéricas

## **FASE 1: FUNDAÇÃO DO SISTEMA (Semana 7-8)**

### **Tarefa 1.1: Criar Enums e Estruturas Base**
**Arquivo:** `MapEnums.h`
- Definir enum EEnvironmentType (PlanicieRadiante, FirmamentoZephyr, ReinoPurgatorio, Hybrid)
- Definir enum EGamePhase (Despertar, Convergencia, Intensificacao, Resolucao)
- Definir enum ETrilhoType (Solar, Axis, Lunar)
- Definir enum EPortalType (Geotermal, Vento, Umbral)
- Definir enum EObjectiveType (CristalEnergetico, GuardiaoEspectral, NucleoTempestade, TorreLamentacao)
- Adicionar metadados BlueprintType para todos os enums

**Arquivo:** `MapStructures.h`
- Criar struct FPhaseConfiguration com campos: Phase, PhaseDuration, ActiveEnvironments, TrilhoPowerLevel, bAllowEnvironmentBlending
- Criar struct FEnvironmentBlendData com campos: EnvironmentType, BlendWeight, BlendCenter, BlendRadius
- Adicionar GENERATED_BODY() e metadados BlueprintType

### **Tarefa 1.2: Implementar Controlador Principal**
**Arquivo:** `AMapManager.h`
- Herdar de AActor
- Declarar ponteiros para: EnvironmentController, FluxoPrismalManager, TrilhoSystemManager, PortalSystemManager, PhaseController, PCGTerrainGenerator
- Declarar funções: BeginPlay(), Tick(), InitializeMap(), UpdateMapState()
- Adicionar UPROPERTY() para todos os componentes

**Arquivo:** `AMapManager.cpp`
- Implementar construtor com inicialização de componentes
- Implementar BeginPlay() para inicializar todos os sistemas
- Implementar Tick() para atualizar estado do mapa
- Implementar InitializeMap() para configuração inicial
- Implementar UpdateMapState() para sincronização entre sistemas
- Adicionar logs de debug para monitoramento

### **Tarefa 1.3: Criar Sistema de Fases**
**Arquivo:** `APhaseController.h`
- Herdar de AActor
- Declarar variáveis: CurrentPhase, PhaseTimer, PhaseConfigurations
- Declarar ponteiro para PhaseTransitionComponent
- Declarar funções: AdvanceToNextPhase(), ApplyPhaseConfiguration(), BroadcastPhaseChange()
- Adicionar delegates para notificação de mudança de fase

**Arquivo:** `APhaseController.cpp`
- Implementar lógica de timer para progressão de fases
- Implementar AdvanceToNextPhase() com validações
- Implementar ApplyPhaseConfiguration() para aplicar configurações específicas
- Implementar BroadcastPhaseChange() para notificar outros sistemas
- Adicionar sistema de eventos para mudanças de fase

**Arquivo:** `UPhaseTransitionComponent.h`
- Herdar de UActorComponent
- Declarar variáveis: TransitionDuration, TransitionCurve, bIsTransitioning
- Declarar funções: StartTransition(), UpdateTransition(), CompleteTransition()

**Arquivo:** `UPhaseTransitionComponent.cpp`
- Implementar StartTransition() com interpolação suave
- Implementar UpdateTransition() usando curvas de animação
- Implementar CompleteTransition() com cleanup
- Adicionar validações para evitar transições simultâneas

---

## **FASE 2: SISTEMA DE AMBIENTES (Semana 3-4)**

### **Tarefa 2.1: Criar Controlador de Ambientes**
**Arquivo:** `AEnvironmentController.h`
- Herdar de AActor
- Declarar array de AEnvironmentZone* para zonas ativas
- Declarar ponteiro para UEnvironmentTransitionComponent
- Declarar funções: SwitchEnvironment(), BlendEnvironments(), GetActiveEnvironments()
- Adicionar sistema de eventos para mudanças de ambiente

**Arquivo:** `AEnvironmentController.cpp`
- Implementar SwitchEnvironment() com validações de tipo
- Implementar BlendEnvironments() para mistura de múltiplos ambientes
- Implementar GetActiveEnvironments() para consulta de estado
- Adicionar lógica de cleanup para ambientes inativos

### **Tarefa 2.2: Criar Classe Base de Ambiente**
**Arquivo:** `AEnvironmentZone.h`
- Herdar de AActor
- Declarar como classe abstrata com funções virtuais puras
- Declarar variáveis protegidas: EnvironmentType, EnvironmentData, ZoneObjectives, ZoneTrilhos, EnvironmentMPC
- Declarar funções virtuais puras: ActivateEnvironment(), DeactivateEnvironment(), UpdateEnvironmentState()
- Adicionar funções auxiliares: GetZoneBounds(), IsPlayerInZone()

**Arquivo:** `AEnvironmentZone.cpp`
- Implementar construtor base com inicializações comuns
- Implementar funções auxiliares compartilhadas
- Implementar sistema de bounds para detecção de zona
- Adicionar validações base para todas as implementações

### **Tarefa 2.3: Implementar Planície Radiante**
**Arquivo:** `APlanicieRadiante.h`
- Herdar de AEnvironmentZone
- Declarar ponteiros específicos: ACristalEnergetico, ACanionDinamico, ATrilhoSolar, APortalGeotermal
- Sobrescrever funções virtuais da classe base
- Declarar funções específicas: UpdateCrystalPositions(), ManageDynamicCanyons()

**Arquivo:** `APlanicieRadiante.cpp`
- Implementar ActivateEnvironment() com spawn de elementos específicos
- Implementar UpdateEnvironmentState() com lógica de cristais e cânions
- Implementar UpdateCrystalPositions() para migração de cristais
- Implementar ManageDynamicCanyons() para expansão/contração
- Adicionar efeitos visuais específicos do ambiente

### **Tarefa 2.4: Implementar Firmamento Zephyr**
**Arquivo:** `AFirmamentoZephyr.h`
- Herdar de AEnvironmentZone
- Declarar ponteiros específicos: APlataformaFlutuante, ATrilhaAurora, AFortalezaNuvem, ANucleoTempestade, APortalVento
- Sobrescrever funções virtuais da classe base
- Declarar funções específicas: UpdateFloatingPlatforms(), ManageStormCore()

**Arquivo:** `AFirmamentoZephyr.cpp`
- Implementar ActivateEnvironment() com elementos aéreos
- Implementar UpdateEnvironmentState() com lógica de plataformas flutuantes
- Implementar UpdateFloatingPlatforms() para movimento orbital
- Implementar ManageStormCore() para núcleo de tempestade
- Adicionar sistema de vento e efeitos atmosféricos

### **Tarefa 2.5: Implementar Reino Purgatório**
**Arquivo:** `AReinoPurgatorio.h`
- Herdar de AEnvironmentZone
- Declarar ponteiros específicos: AEstruturaFragmentada, ANexoSombrio, AGuardiaoEspectral, ATorreLamentacao, APortalUmbral
- Sobrescrever funções virtuais da classe base
- Declarar funções específicas: UpdateFragmentation(), ManageSpectralGuardian()

**Arquivo:** `AReinoPurgatorio.cpp`
- Implementar ActivateEnvironment() com elementos espectrais
- Implementar UpdateEnvironmentState() com lógica de fragmentação
- Implementar UpdateFragmentation() para estruturas quebradas
- Implementar ManageSpectralGuardian() para guardião espectral
- Adicionar efeitos sombrios e distorção visual

### **Tarefa 2.6: Criar Componente de Transição**
**Arquivo:** `UEnvironmentTransitionComponent.h`
- Herdar de UActorComponent
- Declarar variáveis: BlendWeights, TransitionSpeed, CurrentBlendState
- Declarar funções: StartBlending(), UpdateBlending(), CompleteBlending()
- Adicionar suporte para múltiplos ambientes simultâneos

**Arquivo:** `UEnvironmentTransitionComponent.cpp`
- Implementar StartBlending() com validação de tipos
- Implementar UpdateBlending() com interpolação suave
- Implementar CompleteBlending() com finalização de estado
- Adicionar sistema de callbacks para notificação

---

## **FASE 3: SISTEMA FLUXO PRISMAL (Semana 5)**

### **Tarefa 3.1: Criar Gerenciador do Fluxo**
**Arquivo:** `AFluxoPrismalManager.h`
- Herdar de AActor
- Declarar ponteiros: USplineComponent, UFluxoDynamicsComponent, AIlhaAuracron
- Declarar array de AFluxoControlPoint
- Declarar funções: UpdateFluxoPath(), ChangeFluxoColor(), GetFluxoDirection()
- Adicionar sistema de controle de fluxo

**Arquivo:** `AFluxoPrismalManager.cpp`
- Implementar UpdateFluxoPath() com algoritmo Perlin noise
- Implementar ChangeFluxoColor() baseado em controle de equipes
- Implementar GetFluxoDirection() para movimento de jogadores
- Adicionar sistema de morphing de 30 segundos
- Implementar lógica de velocidade variável

### **Tarefa 3.2: Criar Ilha Central**
**Arquivo:** `AIlhaAuracron.h`
- Herdar de AActor
- Declarar componentes: UStaticMeshComponent, UCristalAuracronComponent, UControlZoneComponent
- Declarar array de ADefenseTower
- Declarar funções: InitializeIsland(), UpdateControlState(), SpawnDefenses()

**Arquivo:** `AIlhaAuracron.cpp`
- Implementar InitializeIsland() com configuração de mesh e cristal
- Implementar UpdateControlState() para controle de equipes
- Implementar SpawnDefenses() para torres defensivas
- Adicionar sistema de captura progressiva

### **Tarefa 3.3: Criar Pontos de Controle**
**Arquivo:** `AFluxoControlPoint.h`
- Herdar de AActor
- Declarar variáveis: BasePosition, CurrentOffset, NoiseScale, UpdateFrequency
- Declarar funções: UpdatePosition(), CalculateNoise(), ValidatePosition()

**Arquivo:** `AFluxoControlPoint.cpp`
- Implementar UpdatePosition() com movimento suave
- Implementar CalculateNoise() usando Perlin noise
- Implementar ValidatePosition() para limites do mapa
- Adicionar sistema de interpolação para transições

---

## **FASE 4: SISTEMA DE TRILHOS (Semana 6-7)**

### **Tarefa 4.1: Criar Gerenciador de Trilhos**
**Arquivo:** `ATrilhoSystemManager.h`
- Herdar de AActor
- Declarar array de ATrilhoBase para trilhos ativos
- Declarar ponteiro para UTrilhoIntersectionManager
- Declarar funções: UpdateTrilhoStates(), CreateTrilhoIntersections(), GetTrilhosByType()

**Arquivo:** `ATrilhoSystemManager.cpp`
- Implementar UpdateTrilhoStates() baseado na fase do jogo
- Implementar CreateTrilhoIntersections() para pontos de cruzamento
- Implementar GetTrilhosByType() para consultas específicas
- Adicionar sistema de ativação/desativação dinâmica

### **Tarefa 4.2: Criar Classe Base de Trilho**
**Arquivo:** `ATrilhoBase.h`
- Herdar de AActor
- Declarar como classe abstrata
- Declarar componentes protegidos: USplineComponent, UStaticMeshComponent, UTrilhoEffectComponent
- Declarar variáveis: ETrilhoType, PowerLevel, bIsActive
- Declarar funções virtuais puras: ActivateTrilho(), DeactivateTrilho(), ApplyTrilhoEffect()

**Arquivo:** `ATrilhoBase.cpp`
- Implementar construtor base com componentes comuns
- Implementar funções auxiliares compartilhadas
- Implementar sistema de detecção de jogadores
- Adicionar validações base para todas as implementações

### **Tarefa 4.3: Implementar Trilho Solar**
**Arquivo:** `ATrilhoSolar.h`
- Herdar de ATrilhoBase
- Declarar ponteiro para UDayNightCycleComponent
- Declarar variáveis: SpeedBoostMultiplier, HealthRegenRate
- Sobrescrever funções virtuais e adicionar UpdateCycleState()

**Arquivo:** `ATrilhoSolar.cpp`
- Implementar ActivateTrilho() com efeitos dourados
- Implementar ApplyTrilhoEffect() com boost de velocidade e regeneração
- Implementar UpdateCycleState() para ciclo dia/noite
- Adicionar sistema de partículas solares

### **Tarefa 4.4: Implementar Trilho Axis**
**Arquivo:** `ATrilhoAxis.h`
- Herdar de ATrilhoBase
- Declarar array de ANexusPoint conectados
- Declarar ponteiro para UTeleportComponent
- Declarar variáveis: TeleportCooldown, RequiredNexusControl
- Adicionar função TeleportPlayer()

**Arquivo:** `ATrilhoAxis.cpp`
- Implementar ActivateTrilho() baseado em controle de nexus
- Implementar ApplyTrilhoEffect() com teletransporte instantâneo
- Implementar TeleportPlayer() com validações de destino
- Adicionar sistema de cooldown global

### **Tarefa 4.5: Implementar Trilho Lunar**
**Arquivo:** `ATrilhoLunar.h`
- Herdar de ATrilhoBase
- Declarar componentes: UStealthComponent, UVisionEnhancementComponent
- Declarar variável: bVisibleToEnemies
- Adicionar função ToggleVisibility()

**Arquivo:** `ATrilhoLunar.cpp`
- Implementar ActivateTrilho() apenas durante ciclo noturno
- Implementar ApplyTrilhoEffect() com furtividade e visão aprimorada
- Implementar ToggleVisibility() para invisibilidade de inimigos
- Adicionar efeitos de névoa etérea

---

## **FASE 5: SISTEMA DE PORTAIS (Semana 8)**

### **Tarefa 5.1: Criar Gerenciador de Portais**
**Arquivo:** `APortalSystemManager.h`
- Herdar de AActor
- Declarar array de APortalBase para portais ativos
- Declarar ponteiro para UPortalNetworkComponent
- Declarar funções: UpdatePortalNetwork(), GetAvailableDestinations(), ValidatePortalConnection()

**Arquivo:** `APortalSystemManager.cpp`
- Implementar UpdatePortalNetwork() para conectividade dinâmica
- Implementar GetAvailableDestinations() baseado em alcance e tipo
- Implementar ValidatePortalConnection() para regras de teletransporte
- Adicionar sistema de cooldown global

### **Tarefa 5.2: Criar Classe Base de Portal**
**Arquivo:** `APortalBase.h`
- Herdar de AActor
- Declarar como classe abstrata
- Declarar componentes: UStaticMeshComponent, USphereComponent, UPortalEffectComponent
- Declarar variáveis: EPortalType, TeleportRange, ActivationTime, GlobalCooldown
- Declarar funções virtuais puras: InitializePortal(), TeleportPlayer(), GetValidDestinations()

**Arquivo:** `APortalBase.cpp`
- Implementar construtor base com componentes comuns
- Implementar sistema de detecção de ativação
- Implementar validações base para teletransporte
- Adicionar sistema de UI para seleção de destino

### **Tarefa 5.3: Implementar Portal Geotermal**
**Arquivo:** `APortalGeotermal.h`
- Herdar de APortalBase
- Declarar ponteiro para UHeatEffectComponent
- Sobrescrever funções virtuais da classe base
- Adicionar configurações específicas de alcance

**Arquivo:** `APortalGeotermal.cpp`
- Implementar InitializePortal() com efeitos de calor
- Implementar TeleportPlayer() com validação de destinos geotermais
- Implementar GetValidDestinations() para portais da Planície
- Adicionar efeitos visuais de energia dourada

### **Tarefa 5.4: Implementar Portal do Vento**
**Arquivo:** `APortalVento.h`
- Herdar de APortalBase
- Declarar ponteiro para UWindEffectComponent
- Sobrescrever funções virtuais da classe base
- Adicionar configurações de plataformas flutuantes

**Arquivo:** `APortalVento.cpp`
- Implementar InitializePortal() com efeitos de vórtice
- Implementar TeleportPlayer() com validação de destinos aéreos
- Implementar GetValidDestinations() para portais do Firmamento
- Adicionar efeitos de vento e partículas prateadas

### **Tarefa 5.5: Implementar Portal Umbral**
**Arquivo:** `APortalUmbral.h`
- Herdar de APortalBase
- Declarar ponteiro para UShadowEffectComponent
- Sobrescrever funções virtuais da classe base
- Adicionar configurações de estruturas fragmentadas

**Arquivo:** `APortalUmbral.cpp`
- Implementar InitializePortal() com efeitos sombrios
- Implementar TeleportPlayer() com validação de destinos espectrais
- Implementar GetValidDestinations() para portais do Purgatório
- Adicionar efeitos de distorção violeta

---

## **FASE 6: SISTEMA DE OBJETIVOS (Semana 9-10)**

### **Tarefa 6.1: Criar Gerenciador de Objetivos**
**Arquivo:** `AObjectiveManager.h`
- Herdar de AActor
- Declarar array de AObjectiveBase para objetivos ativos
- Declarar ponteiro para UObjectiveSpawner
- Declarar funções: UpdateObjectiveStates(), SpawnPhaseObjectives(), DestroyCompletedObjectives()

**Arquivo:** `AObjectiveManager.cpp`
- Implementar UpdateObjectiveStates() baseado na fase atual
- Implementar SpawnPhaseObjectives() para objetivos específicos de cada fase
- Implementar DestroyCompletedObjectives() com cleanup
- Adicionar sistema de recompensas por captura

### **Tarefa 6.2: Criar Classe Base de Objetivo**
**Arquivo:** `AObjectiveBase.h`
- Herdar de AActor
- Declarar como classe abstrata
- Declarar componentes: UStaticMeshComponent, UHealthComponent, UCaptureComponent
- Declarar variáveis: EObjectiveType, EEnvironmentType, RewardValue
- Declarar funções virtuais puras: InitializeObjective(), OnObjectiveCaptured(), OnObjectiveDestroyed()

**Arquivo:** `AObjectiveBase.cpp`
- Implementar construtor base com componentes comuns
- Implementar sistema de captura progressiva
- Implementar validações base para todas as implementações
- Adicionar sistema de notificação para captura

### **Tarefa 6.3: Implementar Cristal Energético**
**Arquivo:** `ACristalEnergetico.h`
- Herdar de AObjectiveBase
- Declarar componentes: UCrystalMigrationComponent, ULightEmissionComponent
- Sobrescrever funções virtuais da classe base
- Adicionar função StartMigration()

**Arquivo:** `ACristalEnergetico.cpp`
- Implementar InitializeObjective() com posicionamento inicial
- Implementar OnObjectiveCaptured() com recompensas de energia
- Implementar StartMigration() para reposicionamento a cada 8 minutos
- Adicionar efeitos de luz dinâmica

### **Tarefa 6.4: Implementar Guardião Espectral**
**Arquivo:** `AGuardiaoEspectral.h`
- Herdar de AObjectiveBase
- Declarar componentes: UInvertedAbilitiesComponent, UPurificationComponent
- Sobrescrever funções virtuais da classe base
- Adicionar função ApplyInvertedEffect()

**Arquivo:** `AGuardiaoEspectral.cpp`
- Implementar InitializeObjective() com habilidades invertidas
- Implementar OnObjectiveCaptured() através de purificação
- Implementar ApplyInvertedEffect() para cura de inimigos
- Adicionar sistema de regeneração de 50 HP/segundo

### **Tarefa 6.5: Implementar Núcleo de Tempestade**
**Arquivo:** `ANucleoTempestade.h`
- Herdar de AObjectiveBase
- Declarar componentes: UStormEffectComponent, UFloatingMovementComponent
- Sobrescrever funções virtuais da classe base
- Adicionar função ActivateStormEffect()

**Arquivo:** `ANucleoTempestade.cpp`
- Implementar InitializeObjective() com movimento flutuante
- Implementar OnObjectiveCaptured() com controle de tempestade
- Implementar ActivateStormEffect() para efeitos climáticos
- Adicionar sistema de captura de 8 segundos

---

## **FASE 7: SISTEMA PCG (Semana 11-12)**

### **Tarefa 7.1: Criar Gerador de Terreno**
**Arquivo:** `APCGTerrainGenerator.h`
- Herdar de AActor
- Declarar componentes: ULandscapeComponent, UPCGNoiseComponent, UPCGFoliageComponent, UPCGStructureComponent
- Declarar funções: GenerateBaseTerrain(), UpdateTerrainForPhase(), ApplyEnvironmentBlending()

**Arquivo:** `APCGTerrainGenerator.cpp`
- Implementar GenerateBaseTerrain() com configuração inicial 2048x2048
- Implementar UpdateTerrainForPhase() para mudanças por fase
- Implementar ApplyEnvironmentBlending() para mistura de biomas
- Adicionar sistema de LOD para otimização

### **Tarefa 7.2: Criar Componente de Ruído**
**Arquivo:** `UPCGNoiseComponent.h`
- Herdar de UActorComponent
- Declarar variáveis: NoiseScale, NoiseAmplitude, NoiseOctaves
- Declarar funções: GeneratePerlinNoise(), GenerateVoronoiNoise(), CombineNoises()

**Arquivo:** `UPCGNoiseComponent.cpp`
- Implementar GeneratePerlinNoise() para terreno suave
- Implementar GenerateVoronoiNoise() para distribuição de cristais
- Implementar CombineNoises() para padrões complexos
- Adicionar cache para otimização de performance

### **Tarefa 7.3: Criar Componente de Vegetação**
**Arquivo:** `UPCGFoliageComponent.h`
- Herdar de UActorComponent
- Declarar arrays: FoliageMeshes por tipo de ambiente
- Declarar variáveis: FoliageDensity, GrowthRate, MigrationSpeed
- Declarar funções: SpawnFoliage(), UpdateFoliageGrowth(), MigrateFoliage()

**Arquivo:** `UPCGFoliageComponent.cpp`
- Implementar SpawnFoliage() baseado no tipo de ambiente
- Implementar UpdateFoliageGrowth() com crescimento de 1-5% por minuto
- Implementar MigrateFoliage() com velocidade de 2-5 unidades/minuto
- Adicionar sistema de culling para performance

### **Tarefa 7.4: Criar Componente de Estruturas**
**Arquivo:** `UPCGStructureComponent.h`
- Herdar de UActorComponent
- Declarar arrays: StructureMeshes por ambiente
- Declarar ponteiro para UStructureSpawnRules
- Declarar funções: GenerateStructures(), UpdateStructureStates(), ValidateStructurePlacement()

**Arquivo:** `UPCGStructureComponent.cpp`
- Implementar GenerateStructures() baseado em regras de spawn
- Implementar UpdateStructureStates() para mudanças por fase
- Implementar ValidateStructurePlacement() para evitar sobreposições
- Adicionar sistema de pooling para reutilização

---

## **FASE 8: COMPONENTES DE GAMEPLAY (Semana 13)**

### **Tarefa 8.1: Criar Componente de Zona de Controle**
**Arquivo:** `UControlZoneComponent.h`
- Herdar de UActorComponent
- Declarar variáveis: CaptureRadius, CaptureTime, ControllingPlayer, CaptureProgress
- Declarar funções: StartCapture(), UpdateCapture(), CompleteCapture(), ResetCapture()

**Arquivo:** `UControlZoneComponent.cpp`
- Implementar StartCapture() com validação de jogador
- Implementar UpdateCapture() com progresso baseado em tempo
- Implementar CompleteCapture() com notificação de mudança
- Implementar ResetCapture() para interrupções

### **Tarefa 8.2: Criar Componente de Vida**
**Arquivo:** `UHealthComponent.h`
- Herdar de UActorComponent
- Declarar variáveis: MaxHealth, CurrentHealth, RegenerationRate, bCanRegenerate
- Declarar funções: TakeDamage(), Heal(), UpdateRegeneration(), GetHealthPercentage()

**Arquivo:** `UHealthComponent.cpp`
- Implementar TakeDamage() com validações de dano
- Implementar Heal() com limite máximo
- Implementar UpdateRegeneration() baseado em taxa configurada
- Implementar GetHealthPercentage() para UI

### **Tarefa 8.3: Criar Componente de Aprimoramento de Movimento**
**Arquivo:** `UMovementEnhancementComponent.h`
- Herdar de UActorComponent
- Declarar variáveis: SpeedMultiplier, EffectDuration, bIsActive, OriginalSpeed
- Declarar funções: ApplySpeedBoost(), ApplySlowEffect(), UpdateEffect(), RemoveEffect()

**Arquivo:** `UMovementEnhancementComponent.cpp`
- Implementar ApplySpeedBoost() com multiplicador temporário
- Implementar ApplySlowEffect() com redução de velocidade
- Implementar UpdateEffect() com countdown de duração
- Implementar RemoveEffect() com restauração de velocidade original

---

## **FASE 9: COMPONENTES DE EFEITOS (Semana 14)**

### **Tarefa 9.1: Criar Componente de Efeitos de Ambiente**
**Arquivo:** `UEnvironmentEffectComponent.h`
- Herdar de UActorComponent
- Declarar componentes: UParticleSystemComponent, UAudioComponent
- Declarar ponteiro para UMaterialParameterCollectionInstance
- Declarar funções: PlayEnvironmentEffect(), UpdateEffectIntensity(), StopAllEffects()

**Arquivo:** `UEnvironmentEffectComponent.cpp`
- Implementar PlayEnvironmentEffect() baseado no tipo de ambiente
- Implementar UpdateEffectIntensity() para transições suaves
- Implementar StopAllEffects() para cleanup
- Adicionar sistema de pooling para partículas

### **Tarefa 9.2: Criar Componente de Efeitos de Trilho**
**Arquivo:** `UTrilhoEffectComponent.h`
- Herdar de UActorComponent
- Declarar componentes: UNiagaraComponent para partículas
- Declarar ponteiro para UMaterialInstanceDynamic
- Declarar funções: ActivateEffect(), UpdateEffectPower(), DeactivateEffect()

**Arquivo:** `UTrilhoEffectComponent.cpp`
- Implementar ActivateEffect() baseado no tipo de trilho
- Implementar UpdateEffectPower() com intensidade variável
- Implementar DeactivateEffect() com fade out suave
- Adicionar efeitos específicos por tipo (solar, axis, lunar)

### **Tarefa 9.3: Criar Componente de Efeitos de Portal**
**Arquivo:** `UPortalEffectComponent.h`
- Herdar de UActorComponent
- Declarar componentes: UNiagaraComponent, UPostProcessComponent
- Declarar funções: PlayTeleportEffect(), UpdatePortalVisuals(), CreateDistortionEffect()

**Arquivo:** `UPortalEffectComponent.cpp`
- Implementar PlayTeleportEffect() com animação de entrada/saída
- Implementar UpdatePortalVisuals() baseado no tipo de portal
- Implementar CreateDistortionEffect() para efeito de vórtice
- Adicionar sincronização de efeitos entre portais conectados

---

## **FASE 10: DATA ASSETS E CONFIGURAÇÃO (Semana 15)**

### **Tarefa 10.1: Criar Data Asset de Ambiente**
**Arquivo:** `UEnvironmentDataAsset.h`
- Herdar de UDataAsset
- Declarar propriedades: EnvironmentType, EnvironmentMaterial, AmbientParticles, AmbientSound, EnvironmentTint
- Declarar array: EnvironmentMeshes
- Adicionar metadados EditAnywhere e BlueprintReadOnly

**Arquivo:** `UEnvironmentDataAsset.cpp`
- Implementar construtor com valores padrão
- Implementar validações para propriedades obrigatórias
- Adicionar sistema de carregamento assíncrono para assets

### **Tarefa 10.2: Criar Data Asset de Configuração do Mapa**
**Arquivo:** `UMapConfigurationAsset.h`
- Herdar de UDataAsset
- Declarar propriedades: MapSize, PhaseConfigurations, EnvironmentAssets
- Declarar ponteiro para UMaterialParameterCollection
- Adicionar validações de configuração

**Arquivo:** `UMapConfigurationAsset.cpp`
- Implementar construtor com configurações padrão
- Implementar validações para consistência de dados
- Adicionar sistema de backup para configurações

---

## **FASE 11: OTIMIZAÇÃO E PERFORMANCE (Semana 16)**

### **Tarefa 11.1: Implementar Sistema de LOD**
- Adicionar LODComponent a todos os objetos visuais
- Configurar níveis de detalhe baseados em distância
- Implementar culling automático para objetos fora da tela
- Adicionar sistema de pooling para objetos reutilizáveis

### **Tarefa 11.2: Otimizar Sistemas PCG**
- Implementar threading para geração de terreno
- Adicionar cache para cálculos de ruído
- Otimizar spawning de vegetação com batching
- Implementar sistema de streaming para grandes áreas

### **Tarefa 11.3: Criar Sistema de Monitoramento**
**Arquivo:** `UMapPerformanceMonitor.h`
- Herdar de UActorComponent
- Declarar variáveis: FrameTime, ActiveObjects, MemoryUsage
- Declarar funções: LogPerformanceMetrics(), GetSystemStats(), OptimizeIfNeeded()

**Arquivo:** `UMapPerformanceMonitor.cpp`
- Implementar LogPerformanceMetrics() com dados detalhados
- Implementar GetSystemStats() para monitoramento em tempo real
- Implementar OptimizeIfNeeded() com ajustes automáticos
- Adicionar alertas para problemas de performance

---

## **FASE 12: TESTES E INTEGRAÇÃO (Semana 17)**

### **Tarefa 12.1: Criar Testes Unitários**
- Implementar testes para cada classe base
- Criar testes de integração entre sistemas
- Adicionar testes de performance para PCG
- Implementar testes de stress para múltiplos jogadores

### **Tarefa 12.2: Validação Final**
- Verificar todas as funcionalidades do mapa.md
- Testar transições entre fases
- Validar comportamento de todos os ambientes
- Confirmar funcionamento de trilhos e portais

### **Tarefa 12.3: Documentação de Código**
- Adicionar comentários detalhados em todas as classes
- Criar documentação de API para desenvolvedores
- Documentar configurações e parâmetros ajustáveis
- Criar guia de troubleshooting

---

## **RESUMO DE ENTREGÁVEIS**

**Total de Arquivos:** 89 arquivos (.h/.cpp)
**Tempo Estimado:** 17 semanas
**Sistemas Implementados:** 12 sistemas principais
**Componentes:** 25+ componentes especializados
**Classes Base:** 6 classes abstratas reutilizáveis
**Data Assets:** 2 assets de configuração
**Performance:** Sistema completo de otimização e monitoramento

**Todos os elementos do mapa.md estão cobertos neste plano de implementação.**