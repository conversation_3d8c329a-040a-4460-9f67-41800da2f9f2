// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "UPCGVersionManager.h"

#ifdef AURA_UPCGVersionManager_generated_h
#error "UPCGVersionManager.generated.h already included, missing '#pragma once' in UPCGVersionManager.h"
#endif
#define AURA_UPCGVersionManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UObject;
enum class EPCGRestoreMode : uint8;
enum class EPCGVersionType : uint8;
struct FDateTime;
struct FPCGVersionConfig;
struct FPCGVersionInfo;

// ********** Begin ScriptStruct FPCGVersionInfo ***************************************************
#define FID_Aura_Source_Aura_Public_UPCGVersionManager_h_57_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGVersionInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGVersionInfo;
// ********** End ScriptStruct FPCGVersionInfo *****************************************************

// ********** Begin ScriptStruct FPCGBackupData ****************************************************
#define FID_Aura_Source_Aura_Public_UPCGVersionManager_h_101_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGBackupData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGBackupData;
// ********** End ScriptStruct FPCGBackupData ******************************************************

// ********** Begin ScriptStruct FPCGVersionConfig *************************************************
#define FID_Aura_Source_Aura_Public_UPCGVersionManager_h_134_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGVersionConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGVersionConfig;
// ********** End ScriptStruct FPCGVersionConfig ***************************************************

// ********** Begin Delegate FOnBackupCompleted ****************************************************
#define FID_Aura_Source_Aura_Public_UPCGVersionManager_h_168_DELEGATE \
AURA_API void FOnBackupCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnBackupCompleted, bool bSuccess, FPCGVersionInfo const& VersionInfo);


// ********** End Delegate FOnBackupCompleted ******************************************************

// ********** Begin Delegate FOnRestoreCompleted ***************************************************
#define FID_Aura_Source_Aura_Public_UPCGVersionManager_h_169_DELEGATE \
AURA_API void FOnRestoreCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnRestoreCompleted, bool bSuccess, FPCGVersionInfo const& VersionInfo);


// ********** End Delegate FOnRestoreCompleted *****************************************************

// ********** Begin Delegate FOnVersionCreated *****************************************************
#define FID_Aura_Source_Aura_Public_UPCGVersionManager_h_170_DELEGATE \
AURA_API void FOnVersionCreated_DelegateWrapper(const FMulticastScriptDelegate& OnVersionCreated, FPCGVersionInfo const& VersionInfo);


// ********** End Delegate FOnVersionCreated *******************************************************

// ********** Begin Delegate FOnBackupProgress *****************************************************
#define FID_Aura_Source_Aura_Public_UPCGVersionManager_h_171_DELEGATE \
AURA_API void FOnBackupProgress_DelegateWrapper(const FMulticastScriptDelegate& OnBackupProgress, float Progress, const FString& CurrentFile);


// ********** End Delegate FOnBackupProgress *******************************************************

// ********** Begin Class UPCGVersionManager *******************************************************
#define FID_Aura_Source_Aura_Public_UPCGVersionManager_h_179_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execRestoreMOBAComponent); \
	DECLARE_FUNCTION(execBackupMOBAComponent); \
	DECLARE_FUNCTION(execUnregisterMOBAComponent); \
	DECLARE_FUNCTION(execRegisterMOBAComponent); \
	DECLARE_FUNCTION(execImportVersion); \
	DECLARE_FUNCTION(execExportVersion); \
	DECLARE_FUNCTION(execGenerateVersionDiff); \
	DECLARE_FUNCTION(execCompareVersions); \
	DECLARE_FUNCTION(execCleanupOldBackups); \
	DECLARE_FUNCTION(execRepairCorruptedBackup); \
	DECLARE_FUNCTION(execValidateBackup); \
	DECLARE_FUNCTION(execForceBackup); \
	DECLARE_FUNCTION(execIsAutoBackupRunning); \
	DECLARE_FUNCTION(execStopAutoBackup); \
	DECLARE_FUNCTION(execStartAutoBackup); \
	DECLARE_FUNCTION(execFindVersionsByDateRange); \
	DECLARE_FUNCTION(execFindVersionsByAuthor); \
	DECLARE_FUNCTION(execGetLatestVersion); \
	DECLARE_FUNCTION(execGetCurrentVersion); \
	DECLARE_FUNCTION(execGetAllVersions); \
	DECLARE_FUNCTION(execDeleteVersion); \
	DECLARE_FUNCTION(execRestoreVersion); \
	DECLARE_FUNCTION(execCreateBackup); \
	DECLARE_FUNCTION(execCreateVersion); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize);


AURA_API UClass* Z_Construct_UClass_UPCGVersionManager_NoRegister();

#define FID_Aura_Source_Aura_Public_UPCGVersionManager_h_179_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPCGVersionManager(); \
	friend struct Z_Construct_UClass_UPCGVersionManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_UPCGVersionManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UPCGVersionManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_UPCGVersionManager_NoRegister) \
	DECLARE_SERIALIZER(UPCGVersionManager)


#define FID_Aura_Source_Aura_Public_UPCGVersionManager_h_179_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPCGVersionManager(UPCGVersionManager&&) = delete; \
	UPCGVersionManager(const UPCGVersionManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPCGVersionManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPCGVersionManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UPCGVersionManager) \
	NO_API virtual ~UPCGVersionManager();


#define FID_Aura_Source_Aura_Public_UPCGVersionManager_h_176_PROLOG
#define FID_Aura_Source_Aura_Public_UPCGVersionManager_h_179_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_UPCGVersionManager_h_179_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_UPCGVersionManager_h_179_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_UPCGVersionManager_h_179_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPCGVersionManager;

// ********** End Class UPCGVersionManager *********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_UPCGVersionManager_h

// ********** Begin Enum EPCGVersionType ***********************************************************
#define FOREACH_ENUM_EPCGVERSIONTYPE(op) \
	op(EPCGVersionType::None) \
	op(EPCGVersionType::Minor) \
	op(EPCGVersionType::Major) \
	op(EPCGVersionType::Hotfix) \
	op(EPCGVersionType::Snapshot) 

enum class EPCGVersionType : uint8;
template<> struct TIsUEnumClass<EPCGVersionType> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGVersionType>();
// ********** End Enum EPCGVersionType *************************************************************

// ********** Begin Enum EPCGBackupStatus **********************************************************
#define FOREACH_ENUM_EPCGBACKUPSTATUS(op) \
	op(EPCGBackupStatus::Pending) \
	op(EPCGBackupStatus::InProgress) \
	op(EPCGBackupStatus::Completed) \
	op(EPCGBackupStatus::Failed) \
	op(EPCGBackupStatus::Corrupted) 

enum class EPCGBackupStatus : uint8;
template<> struct TIsUEnumClass<EPCGBackupStatus> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGBackupStatus>();
// ********** End Enum EPCGBackupStatus ************************************************************

// ********** Begin Enum EPCGRestoreMode ***********************************************************
#define FOREACH_ENUM_EPCGRESTOREMODE(op) \
	op(EPCGRestoreMode::Full) \
	op(EPCGRestoreMode::Partial) \
	op(EPCGRestoreMode::GeometryOnly) \
	op(EPCGRestoreMode::ConfigOnly) 

enum class EPCGRestoreMode : uint8;
template<> struct TIsUEnumClass<EPCGRestoreMode> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGRestoreMode>();
// ********** End Enum EPCGRestoreMode *************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
