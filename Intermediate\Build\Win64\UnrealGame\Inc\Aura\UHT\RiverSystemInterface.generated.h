// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Interfaces/RiverSystemInterface.h"

#ifdef AURA_RiverSystemInterface_generated_h
#error "RiverSystemInterface.generated.h already included, missing '#pragma once' in RiverSystemInterface.h"
#endif
#define AURA_RiverSystemInterface_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

struct FRiverGenerationParams;

// ********** Begin ScriptStruct FRiverGenerationParams ********************************************
#define FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h_12_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRiverGenerationParams_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRiverGenerationParams;
// ********** End ScriptStruct FRiverGenerationParams **********************************************

// ********** Begin Interface URiverSystemInterface ************************************************
#define FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h_50_CALLBACK_WRAPPERS
AURA_API UClass* Z_Construct_UClass_URiverSystemInterface_NoRegister();

#define FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h_50_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	AURA_API URiverSystemInterface(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	URiverSystemInterface(URiverSystemInterface&&) = delete; \
	URiverSystemInterface(const URiverSystemInterface&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(AURA_API, URiverSystemInterface); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URiverSystemInterface); \
	DEFINE_ABSTRACT_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(URiverSystemInterface) \
	virtual ~URiverSystemInterface() = default;


#define FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h_50_GENERATED_UINTERFACE_BODY() \
private: \
	static void StaticRegisterNativesURiverSystemInterface(); \
	friend struct Z_Construct_UClass_URiverSystemInterface_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_URiverSystemInterface_NoRegister(); \
public: \
	DECLARE_CLASS2(URiverSystemInterface, UInterface, COMPILED_IN_FLAGS(CLASS_Abstract | CLASS_Interface), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_URiverSystemInterface_NoRegister) \
	DECLARE_SERIALIZER(URiverSystemInterface)


#define FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h_50_GENERATED_BODY \
	PRAGMA_DISABLE_DEPRECATION_WARNINGS \
	FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h_50_GENERATED_UINTERFACE_BODY() \
	FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h_50_ENHANCED_CONSTRUCTORS \
private: \
	PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h_50_INCLASS_IINTERFACE_NO_PURE_DECLS \
protected: \
	virtual ~IRiverSystemInterface() {} \
public: \
	typedef URiverSystemInterface UClassType; \
	typedef IRiverSystemInterface ThisClass; \
	static bool Execute_ClearRiverData(UObject* O); \
	static bool Execute_ConfigureRiver(UObject* O, FRiverGenerationParams const& Params); \
	static bool Execute_CreateBridge(UObject* O, FVector const& BridgePosition, float BridgeLength, float BridgeWidth); \
	static bool Execute_GenerateRiverMesh(UObject* O); \
	static TArray<FVector> Execute_GetAllBridgePositions(UObject* O); \
	static FVector Execute_GetFlowVelocityAtPosition(UObject* O, FVector const& Position); \
	static float Execute_GetWaterDepthAtPosition(UObject* O, FVector const& Position); \
	static FVector Execute_GetWaterSurfacePosition(UObject* O, FVector const& Position); \
	static bool Execute_IsPositionInWater(UObject* O, FVector const& Position); \
	static bool Execute_RemoveBridge(UObject* O, int32 BridgeIndex); \
	static bool Execute_SetSplinePoints(UObject* O, TArray<FVector> const& SplinePoints); \
	static bool Execute_ValidateRiverConfiguration(UObject* O); \
	virtual UObject* _getUObject() const { return nullptr; }


#define FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h_47_PROLOG
#define FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h_59_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h_50_CALLBACK_WRAPPERS \
	FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h_50_INCLASS_IINTERFACE_NO_PURE_DECLS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URiverSystemInterface;

// ********** End Interface URiverSystemInterface **************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_Interfaces_RiverSystemInterface_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
