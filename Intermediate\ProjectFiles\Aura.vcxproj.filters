<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <None Include="..\..\Aura.uproject" />
    <Filter Include="Source">
      <UniqueIdentifier>{F31BBDD1-B3E8-3BCC-9652-680E16935819}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\Aura.Target.cs">
      <Filter>Source</Filter>
    </None>
    <None Include="..\..\Source\AuraEditor.Target.cs">
      <Filter>Source</Filter>
    </None>
    <None Include="..\..\.vsconfig" />
    <None Include="..\..\AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md" />
    <None Include="..\..\EXPLICACAO_IMPLEMENTACOES.md" />
    <None Include="..\..\GARANTIA_PRECISAO_100_PORCENTO.md" />
    <None Include="..\..\mapa.md" />
    <None Include="..\..\mapaimplementacao.md" />
    <None Include="..\..\validacao_geometrica.md" />
    <Filter Include="Config">
      <UniqueIdentifier>{FA535FFB-25E1-3D20-B416-52F9BE21E06E}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Config\DefaultEditor.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultEngine.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultGame.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultInput.ini">
      <Filter>Config</Filter>
    </None>
    <Filter Include="Source\Aura">
      <UniqueIdentifier>{171ED4C2-8F97-3FFE-B0B6-01D55C378E24}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\Aura\Aura.Build.cs">
      <Filter>Source\Aura</Filter>
    </None>
    <ClCompile Include="..\..\Source\Aura\Aura.cpp">
      <Filter>Source\Aura</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Aura\Aura.h">
      <Filter>Source\Aura</Filter>
    </ClInclude>
    <Filter Include="Source\Aura\Private">
      <UniqueIdentifier>{614644D7-6FA7-303B-9CB5-F9579003D315}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\Aura\Private\ABaronAuracronManager.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\ADragonPrismalManager.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\AGeometricValidator.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\ALaneManager.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\AMapManager.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\AMinionWaveManager.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\APCGCacheManager.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\APCGChaosIntegrator.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\APCGLumenIntegrator.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\APCGNaniteOptimizer.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\APCGStreamingManager.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\APCGWorldPartitionManager.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\AProceduralMapGenerator.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\ARiverPrismalManager.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\AWallCollisionManager.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\implementacao_automatizada.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\testes_precisao_geometrica.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\UPCGPerformanceProfiler.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\UPCGQualityValidator.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Aura\Private\UPCGVersionManager.cpp">
      <Filter>Source\Aura\Private</Filter>
    </ClCompile>
    <Filter Include="Source\Aura\Public">
      <UniqueIdentifier>{812D085E-218E-32A1-B6D9-A817B3D0072A}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\Aura\Public\ABaronAuracronManager.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\ADragonPrismalManager.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\AGeometricValidator.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\ALaneManager.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\AMapManager.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\AMinionWaveManager.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\APCGCacheManager.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\APCGChaosIntegrator.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\APCGLumenIntegrator.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\APCGNaniteOptimizer.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\APCGStreamingManager.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\APCGWorldPartitionManager.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\AProceduralMapGenerator.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\ARiverPrismalManager.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\AWallCollisionManager.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\implementacao_automatizada.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\testes_precisao_geometrica.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\UPCGPerformanceProfiler.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\UPCGQualityValidator.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\UPCGVersionManager.h">
      <Filter>Source\Aura\Public</Filter>
    </ClInclude>
    <Filter Include="Source\Aura\Public\Interfaces">
      <UniqueIdentifier>{3D7C911E-EF2A-33D6-A0B5-8635DD090A5B}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\Aura\Public\Interfaces\LaneSystemInterface.h">
      <Filter>Source\Aura\Public\Interfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Aura\Public\Interfaces\RiverSystemInterface.h">
      <Filter>Source\Aura\Public\Interfaces</Filter>
    </ClInclude>
  </ItemGroup>
</Project>
