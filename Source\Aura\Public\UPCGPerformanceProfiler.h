#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "HAL/Platform.h"
#include "HAL/PlatformProcess.h"
#include "HAL/PlatformMemory.h"
#include "HAL/ThreadSafeBool.h"
#include "Stats/Stats.h"
#include "Stats/StatsData.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "ProfilingDebugging/MemoryTrace.h"
#include "RHI.h"
#include "RenderCore.h"
#include "Containers/Array.h"
#include "Containers/Map.h"
#include "Containers/Queue.h"
#include "Containers/CircularBuffer.h"
#include "Math/Vector.h"
#include "Math/Color.h"
#include "Templates/SharedPointer.h"
#include "Async/AsyncWork.h"
#include "Async/TaskGraphInterfaces.h"
#include "Misc/DateTime.h"
#include "Misc/Timespan.h"
#include "UPCGPerformanceProfiler.generated.h"

// Forward Declarations
class APCGWorldPartitionManager;
class APCGNaniteOptimizer;
class APCGLumenIntegrator;
class APCGStreamingManager;
class UCanvas;
class UFont;

// Enums
UENUM(BlueprintType)
enum class EPCGProfilerCategory : uint8
{
    CPU             UMETA(DisplayName = "CPU Performance"),
    Memory          UMETA(DisplayName = "Memory Usage"),
    GPU             UMETA(DisplayName = "GPU Performance"),
    Rendering       UMETA(DisplayName = "Rendering"),
    Streaming       UMETA(DisplayName = "Asset Streaming"),
    PCG             UMETA(DisplayName = "PCG Generation"),
    WorldPartition  UMETA(DisplayName = "World Partition"),
    Nanite          UMETA(DisplayName = "Nanite"),
    Lumen           UMETA(DisplayName = "Lumen"),
    Network         UMETA(DisplayName = "Network"),
    Audio           UMETA(DisplayName = "Audio"),
    Physics         UMETA(DisplayName = "Physics"),
    Animation       UMETA(DisplayName = "Animation"),
    Blueprint       UMETA(DisplayName = "Blueprint"),
    Custom          UMETA(DisplayName = "Custom Metrics")
};

UENUM(BlueprintType)
enum class EPCGProfilerSeverity : uint8
{
    Info        UMETA(DisplayName = "Information"),
    Warning     UMETA(DisplayName = "Warning"),
    Error       UMETA(DisplayName = "Error"),
    Critical    UMETA(DisplayName = "Critical")
};

UENUM(BlueprintType)
enum class EPCGProfilerDisplayMode : uint8
{
    Overlay     UMETA(DisplayName = "Screen Overlay"),
    Console     UMETA(DisplayName = "Console Output"),
    File        UMETA(DisplayName = "File Logging"),
    Network     UMETA(DisplayName = "Network Streaming"),
    All         UMETA(DisplayName = "All Methods")
};

UENUM(BlueprintType)
enum class EPCGProfilerSamplingRate : uint8
{
    VeryLow     UMETA(DisplayName = "Very Low (1 Hz)"),
    Low         UMETA(DisplayName = "Low (5 Hz)"),
    Medium      UMETA(DisplayName = "Medium (10 Hz)"),
    High        UMETA(DisplayName = "High (30 Hz)"),
    VeryHigh    UMETA(DisplayName = "Very High (60 Hz)"),
    Realtime    UMETA(DisplayName = "Realtime (120 Hz)")
};

// Structures
USTRUCT(BlueprintType)
struct AURA_API FPCGProfilerConfig
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiling")
    bool bEnableProfiling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiling")
    EPCGProfilerSamplingRate SamplingRate = EPCGProfilerSamplingRate::Medium;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Display")
    EPCGProfilerDisplayMode DisplayMode = EPCGProfilerDisplayMode::Overlay;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Display")
    bool bShowDetailedStats = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Display")
    bool bShowGraphs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Display")
    bool bShowWarnings = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Categories")
    TArray<EPCGProfilerCategory> EnabledCategories;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "10", ClampMax = "1000"))
    int32 MaxSampleHistory = 300;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1.0", ClampMax = "60.0"))
    float UpdateInterval = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds", meta = (ClampMin = "10.0", ClampMax = "200.0"))
    float CPUWarningThreshold = 80.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds", meta = (ClampMin = "100", ClampMax = "32000"))
    int32 MemoryWarningThresholdMB = 4096;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds", meta = (ClampMin = "10.0", ClampMax = "200.0"))
    float GPUWarningThreshold = 90.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging")
    bool bEnableFileLogging = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging")
    FString LogFilePath = TEXT("Logs/PCGProfiler.log");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Network")
    bool bEnableNetworkStreaming = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Network")
    FString NetworkEndpoint = TEXT("localhost:8080");

    FPCGProfilerConfig()
    {
        bEnableProfiling = true;
        SamplingRate = EPCGProfilerSamplingRate::Medium;
        DisplayMode = EPCGProfilerDisplayMode::Overlay;
        bShowDetailedStats = true;
        bShowGraphs = true;
        bShowWarnings = true;
        MaxSampleHistory = 300;
        UpdateInterval = 0.1f;
        CPUWarningThreshold = 80.0f;
        MemoryWarningThresholdMB = 4096;
        GPUWarningThreshold = 90.0f;
        bEnableFileLogging = false;
        LogFilePath = TEXT("Logs/PCGProfiler.log");
        bEnableNetworkStreaming = false;
        NetworkEndpoint = TEXT("localhost:8080");
        
        // Enable all categories by default
        EnabledCategories.Add(EPCGProfilerCategory::CPU);
        EnabledCategories.Add(EPCGProfilerCategory::Memory);
        EnabledCategories.Add(EPCGProfilerCategory::GPU);
        EnabledCategories.Add(EPCGProfilerCategory::Rendering);
        EnabledCategories.Add(EPCGProfilerCategory::PCG);
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGPerformanceMetric
{
    GENERATED_BODY()

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Metric")
    FString MetricName = TEXT("Unknown");

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Metric")
    EPCGProfilerCategory Category = EPCGProfilerCategory::Custom;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Value")
    float CurrentValue = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Value")
    float MinValue = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Value")
    float MaxValue = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Value")
    float AverageValue = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Value")
    FString Unit = TEXT("");

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Timing")
    FDateTime LastUpdateTime;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "History")
    TArray<float> ValueHistory;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Status")
    EPCGProfilerSeverity Severity = EPCGProfilerSeverity::Info;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Thresholds")
    float WarningThreshold = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Thresholds")
    float ErrorThreshold = 0.0f;

    FPCGPerformanceMetric()
    {
        MetricName = TEXT("Unknown");
        Category = EPCGProfilerCategory::Custom;
        CurrentValue = 0.0f;
        MinValue = 0.0f;
        MaxValue = 0.0f;
        AverageValue = 0.0f;
        Unit = TEXT("");
        LastUpdateTime = FDateTime::Now();
        Severity = EPCGProfilerSeverity::Info;
        WarningThreshold = 0.0f;
        ErrorThreshold = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGSystemPerformance
{
    GENERATED_BODY()

    // CPU Metrics
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "CPU")
    float CPUUsagePercent = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "CPU")
    float GameThreadTime = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "CPU")
    float RenderThreadTime = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "CPU")
    float RHIThreadTime = 0.0f;

    // Memory Metrics
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Memory")
    int32 UsedMemoryMB = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Memory")
    int32 AvailableMemoryMB = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Memory")
    int32 GPUMemoryMB = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Memory")
    int32 StreamingPoolMB = 0;

    // GPU Metrics
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "GPU")
    float GPUUsagePercent = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "GPU")
    float FrameTime = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "GPU")
    float FPS = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "GPU")
    int32 DrawCalls = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "GPU")
    int32 Triangles = 0;

    // PCG Specific Metrics
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "PCG")
    int32 ActivePCGComponents = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "PCG")
    float PCGGenerationTime = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "PCG")
    int32 GeneratedInstances = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "PCG")
    float StreamingEfficiency = 0.0f;

    // Nanite Metrics
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Nanite")
    int32 NaniteTriangles = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Nanite")
    int32 NaniteClusters = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Nanite")
    float NaniteMemoryMB = 0.0f;

    // Lumen Metrics
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Lumen")
    float LumenUpdateTime = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Lumen")
    int32 LumenProbes = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Lumen")
    float LumenMemoryMB = 0.0f;

    FPCGSystemPerformance()
    {
        CPUUsagePercent = 0.0f;
        GameThreadTime = 0.0f;
        RenderThreadTime = 0.0f;
        RHIThreadTime = 0.0f;
        UsedMemoryMB = 0;
        AvailableMemoryMB = 0;
        GPUMemoryMB = 0;
        StreamingPoolMB = 0;
        GPUUsagePercent = 0.0f;
        FrameTime = 0.0f;
        FPS = 0.0f;
        DrawCalls = 0;
        Triangles = 0;
        ActivePCGComponents = 0;
        PCGGenerationTime = 0.0f;
        GeneratedInstances = 0;
        StreamingEfficiency = 0.0f;
        NaniteTriangles = 0;
        NaniteClusters = 0;
        NaniteMemoryMB = 0.0f;
        LumenUpdateTime = 0.0f;
        LumenProbes = 0;
        LumenMemoryMB = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGProfilerAlert
{
    GENERATED_BODY()

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Alert")
    FString AlertMessage = TEXT("");

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Alert")
    EPCGProfilerSeverity Severity = EPCGProfilerSeverity::Info;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Alert")
    EPCGProfilerCategory Category = EPCGProfilerCategory::Custom;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Alert")
    FDateTime Timestamp;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Alert")
    float MetricValue = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Alert")
    float ThresholdValue = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Alert")
    bool bIsActive = true;

    FPCGProfilerAlert()
    {
        AlertMessage = TEXT("");
        Severity = EPCGProfilerSeverity::Info;
        Category = EPCGProfilerCategory::Custom;
        Timestamp = FDateTime::Now();
        MetricValue = 0.0f;
        ThresholdValue = 0.0f;
        bIsActive = true;
    }
};

USTRUCT(BlueprintType)
struct AURA_API FPCGProfilerReport
{
    GENERATED_BODY()

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Report")
    FDateTime GenerationTime;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Report")
    FTimespan ProfileDuration;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    FPCGSystemPerformance AveragePerformance;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Performance")
    FPCGSystemPerformance PeakPerformance;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Metrics")
    TArray<FPCGPerformanceMetric> DetailedMetrics;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Alerts")
    TArray<FPCGProfilerAlert> Alerts;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Summary")
    FString PerformanceSummary = TEXT("");

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Recommendations")
    TArray<FString> OptimizationRecommendations;

    FPCGProfilerReport()
    {
        GenerationTime = FDateTime::Now();
        ProfileDuration = FTimespan::Zero();
        AveragePerformance = FPCGSystemPerformance();
        PeakPerformance = FPCGSystemPerformance();
        PerformanceSummary = TEXT("");
    }
};

// Delegates
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPerformanceAlert, const FPCGProfilerAlert&, Alert);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMetricUpdated, const FPCGPerformanceMetric&, Metric);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnProfilerReportGenerated, const FPCGProfilerReport&, Report);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnThresholdExceeded, const FString&, MetricName, float, Value);

/**
 * UPCGPerformanceProfiler - Advanced performance profiling system for PCG
 * Provides real-time monitoring, analysis, and optimization recommendations
 * Optimized for UE5.6 with modern profiling APIs and integration capabilities
 */
UCLASS(BlueprintType, Blueprintable)
class AURA_API UPCGPerformanceProfiler : public UObject
{
    GENERATED_BODY()

public:
    UPCGPerformanceProfiler();

    // Initialization and Cleanup
    UFUNCTION(BlueprintCallable, Category = "Profiler Lifecycle")
    void InitializeProfiler();

    UFUNCTION(BlueprintCallable, Category = "Profiler Lifecycle")
    void ShutdownProfiler();

    UFUNCTION(BlueprintCallable, Category = "Profiler Lifecycle")
    bool IsProfilerActive() const;

public:
    // Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiler Config")
    FPCGProfilerConfig ProfilerConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Display")
    bool bShowOverlay = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Display")
    bool bShowGraphs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bAutoOptimization = false;

    // Events
    UPROPERTY(BlueprintAssignable, Category = "Profiler Events")
    FOnPerformanceAlert OnPerformanceAlert;

    UPROPERTY(BlueprintAssignable, Category = "Profiler Events")
    FOnMetricUpdated OnMetricUpdated;

    UPROPERTY(BlueprintAssignable, Category = "Profiler Events")
    FOnProfilerReportGenerated OnProfilerReportGenerated;

    UPROPERTY(BlueprintAssignable, Category = "Profiler Events")
    FOnThresholdExceeded OnThresholdExceeded;

    // Profiling Control
    UFUNCTION(BlueprintCallable, Category = "Profiler Control")
    void StartProfiling();

    UFUNCTION(BlueprintCallable, Category = "Profiler Control")
    void StopProfiling();

    UFUNCTION(BlueprintCallable, Category = "Profiler Control")
    void PauseProfiling(bool bPause);

    UFUNCTION(BlueprintCallable, Category = "Profiler Control")
    void ResetProfiler();

    UFUNCTION(BlueprintCallable, Category = "Profiler Control")
    void SetSamplingRate(EPCGProfilerSamplingRate Rate);

    UFUNCTION(BlueprintCallable, Category = "Profiler Control")
    void EnableCategory(EPCGProfilerCategory Category, bool bEnable);

    // Metrics Management
    UFUNCTION(BlueprintCallable, Category = "Profiler Metrics")
    void RegisterCustomMetric(const FString& MetricName, EPCGProfilerCategory Category, const FString& Unit = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "Profiler Metrics")
    void UpdateCustomMetric(const FString& MetricName, float Value);

    UFUNCTION(BlueprintCallable, Category = "Profiler Metrics")
    void RemoveCustomMetric(const FString& MetricName);

    UFUNCTION(BlueprintCallable, Category = "Profiler Metrics")
    FPCGPerformanceMetric GetMetric(const FString& MetricName) const;

    UFUNCTION(BlueprintCallable, Category = "Profiler Metrics")
    TArray<FPCGPerformanceMetric> GetAllMetrics() const;

    UFUNCTION(BlueprintCallable, Category = "Profiler Metrics")
    TArray<FPCGPerformanceMetric> GetMetricsByCategory(EPCGProfilerCategory Category) const;

    // Performance Data
    UFUNCTION(BlueprintCallable, Category = "Profiler Data")
    FPCGSystemPerformance GetCurrentPerformance() const;

    UFUNCTION(BlueprintCallable, Category = "Profiler Data")
    FPCGSystemPerformance GetAveragePerformance() const;

    UFUNCTION(BlueprintCallable, Category = "Profiler Data")
    FPCGSystemPerformance GetPeakPerformance() const;

    UFUNCTION(BlueprintCallable, Category = "Profiler Data")
    TArray<FPCGProfilerAlert> GetActiveAlerts() const;

    UFUNCTION(BlueprintCallable, Category = "Profiler Data")
    TArray<FPCGProfilerAlert> GetAlertHistory() const;

    // Reporting
    UFUNCTION(BlueprintCallable, Category = "Profiler Reports")
    FPCGProfilerReport GenerateReport() const;

    UFUNCTION(BlueprintCallable, Category = "Profiler Reports")
    void ExportReportToFile(const FString& FilePath) const;

    UFUNCTION(BlueprintCallable, Category = "Profiler Reports")
    void ExportMetricsToCSV(const FString& FilePath) const;

    UFUNCTION(BlueprintCallable, Category = "Profiler Reports")
    FString GetPerformanceSummary() const;

    UFUNCTION(BlueprintCallable, Category = "Profiler Reports")
    TArray<FString> GetOptimizationRecommendations() const;

    // Thresholds and Alerts
    UFUNCTION(BlueprintCallable, Category = "Profiler Thresholds")
    void SetMetricThreshold(const FString& MetricName, float WarningThreshold, float ErrorThreshold);

    UFUNCTION(BlueprintCallable, Category = "Profiler Thresholds")
    void ClearAlert(const FString& MetricName);

    UFUNCTION(BlueprintCallable, Category = "Profiler Thresholds")
    void ClearAllAlerts();

    UFUNCTION(BlueprintCallable, Category = "Profiler Thresholds")
    bool HasActiveAlerts() const;

    // Integration with PCG Systems
    UFUNCTION(BlueprintCallable, Category = "Profiler Integration")
    void IntegrateWithWorldPartition(APCGWorldPartitionManager* WorldPartitionManager);

    UFUNCTION(BlueprintCallable, Category = "Profiler Integration")
    void IntegrateWithNanite(APCGNaniteOptimizer* NaniteOptimizer);

    UFUNCTION(BlueprintCallable, Category = "Profiler Integration")
    void IntegrateWithLumen(APCGLumenIntegrator* LumenIntegrator);

    UFUNCTION(BlueprintCallable, Category = "Profiler Integration")
    void IntegrateWithStreaming(APCGStreamingManager* StreamingManager);

    // Benchmarking
    UFUNCTION(BlueprintCallable, Category = "Profiler Benchmark")
    void StartBenchmark(const FString& BenchmarkName);

    UFUNCTION(BlueprintCallable, Category = "Profiler Benchmark")
    void EndBenchmark(const FString& BenchmarkName);

    UFUNCTION(BlueprintCallable, Category = "Profiler Benchmark")
    float GetBenchmarkResult(const FString& BenchmarkName) const;

    UFUNCTION(BlueprintCallable, Category = "Profiler Benchmark")
    TMap<FString, float> GetAllBenchmarkResults() const;

    // Memory Profiling
    UFUNCTION(BlueprintCallable, Category = "Profiler Memory")
    void StartMemoryProfiling();

    UFUNCTION(BlueprintCallable, Category = "Profiler Memory")
    void StopMemoryProfiling();

    UFUNCTION(BlueprintCallable, Category = "Profiler Memory")
    void TakeMemorySnapshot(const FString& SnapshotName);

    UFUNCTION(BlueprintCallable, Category = "Profiler Memory")
    int32 GetMemoryUsage(const FString& Category = TEXT("")) const;

    UFUNCTION(BlueprintCallable, Category = "Profiler Performance")
    float GetAverageFrameTime() const;

    UFUNCTION(BlueprintCallable, Category = "Profiler Performance")
    int32 GetTriangleCount() const;

    // GPU Profiling
    UFUNCTION(BlueprintCallable, Category = "Profiler GPU")
    void StartGPUProfiling();

    UFUNCTION(BlueprintCallable, Category = "Profiler GPU")
    void StopGPUProfiling();

    UFUNCTION(BlueprintCallable, Category = "Profiler GPU")
    float GetGPUFrameTime() const;

    UFUNCTION(BlueprintCallable, Category = "Profiler GPU")
    int32 GetDrawCallCount() const;

    // Network Profiling
    UFUNCTION(BlueprintCallable, Category = "Profiler Network")
    void StartNetworkProfiling();

    UFUNCTION(BlueprintCallable, Category = "Profiler Network")
    void StopNetworkProfiling();

    UFUNCTION(BlueprintCallable, Category = "Profiler Network")
    void StreamDataToEndpoint(const FString& Endpoint);

    // Visualization
    UFUNCTION(BlueprintCallable, Category = "Profiler Visualization")
    void DrawProfilerOverlay(UCanvas* Canvas);

    UFUNCTION(BlueprintCallable, Category = "Profiler Visualization")
    void SetOverlayPosition(const FVector2D& Position);

    UFUNCTION(BlueprintCallable, Category = "Profiler Visualization")
    void SetOverlayScale(float Scale);

    UFUNCTION(BlueprintCallable, Category = "Profiler Visualization")
    void ToggleOverlayVisibility();

private:
    // Internal data
    UPROPERTY()
    TMap<FString, FPCGPerformanceMetric> RegisteredMetrics;

    UPROPERTY()
    TArray<FPCGProfilerAlert> ActiveAlerts;

    UPROPERTY()
    TArray<FPCGProfilerAlert> AlertHistory;

    UPROPERTY()
    FPCGSystemPerformance CurrentPerformance;

    UPROPERTY()
    FPCGSystemPerformance AveragePerformance;

    UPROPERTY()
    FPCGSystemPerformance PeakPerformance;

    // Internal state
    FThreadSafeBool bIsActive;
    FThreadSafeBool bIsPaused;
    FThreadSafeBool bIsInitialized;
    float LastUpdateTime;
    float AccumulatedTime;
    int32 SampleCount;
    
    // Timing
    TMap<FString, FDateTime> BenchmarkStartTimes;
    TMap<FString, float> BenchmarkResults;
    
    // Integration references
    UPROPERTY()
    TWeakObjectPtr<APCGWorldPartitionManager> WorldPartitionManagerRef;

    UPROPERTY()
    TWeakObjectPtr<APCGNaniteOptimizer> NaniteOptimizerRef;

    UPROPERTY()
    TWeakObjectPtr<APCGLumenIntegrator> LumenIntegratorRef;

    UPROPERTY()
    TWeakObjectPtr<APCGStreamingManager> StreamingManagerRef;

    // Profiling data
    TCircularBuffer<FPCGSystemPerformance> PerformanceHistory{1000}; // Buffer for 1000 performance samples
    TMap<EPCGProfilerCategory, bool> CategoryStates;
    
    // Memory tracking
    TMap<FString, int32> MemorySnapshots;
    bool bMemoryProfilingActive;
    
    // GPU tracking
    bool bGPUProfilingActive;
    TArray<float> GPUFrameTimes;
    
    // Network streaming
    bool bNetworkStreamingActive;
    FString CurrentNetworkEndpoint;
    
    // Overlay rendering
    FVector2D OverlayPosition;
    float OverlayScale;
    bool bOverlayVisible;
    
    // Internal functions
    void UpdateProfiler(float DeltaTime);
    void CollectSystemMetrics();
    void CollectCPUMetrics();
    void CollectMemoryMetrics();
    void CollectGPUMetrics();
    void CollectPCGMetrics();
    void CollectNaniteMetrics();
    void CollectLumenMetrics();
    void CollectStreamingMetrics();
    
    void UpdateMetric(const FString& MetricName, float Value);
    void CheckThresholds();
    void ProcessAlerts();
    void UpdateAverages();
    void UpdatePeakValues();
    
    void LogToFile(const FString& Message);
    void StreamToNetwork(const FString& Data);
    
    // Utility functions
    float GetSamplingInterval() const;
    FColor GetSeverityColor(EPCGProfilerSeverity Severity) const;
    FString FormatMetricValue(float Value, const FString& Unit) const;
    FString GenerateOptimizationRecommendation(const FPCGPerformanceMetric& Metric) const;
    
    // Async operations
    void StartAsyncProfiling();
    void StopAsyncProfiling();
    
    // Event handlers
    void OnMetricThresholdExceeded(const FString& MetricName, float Value, float Threshold);
    void OnSystemPerformanceChanged(const FPCGSystemPerformance& NewPerformance);
};