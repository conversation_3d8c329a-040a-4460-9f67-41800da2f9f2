"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.10.cpp"
@"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Aura.Shared.rsp"
/FI"C:/Aura/Intermediate/Build/Win64/x64/AuraEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.RTTI.ValApi.ValExpApi.Cpp20.h"
/FI"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Definitions.Aura.h"
/Yu"C:/Aura/Intermediate/Build/Win64/x64/AuraEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.RTTI.ValApi.ValExpApi.Cpp20.h"
/Fp"C:/Aura/Intermediate/Build/Win64/x64/AuraEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.RTTI.ValApi.ValExpApi.Cpp20.h.pch"
/Fo"C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.10.cpp.obj"
/experimental:log "C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.10.cpp.sarif"
/sourceDependencies "C:/Aura/Intermediate/Build/Win64/x64/UnrealEditor/Development/Aura/Module.Aura.gen.10.cpp.dep.json"
/Zc:inline
/nologo
/Oi
/FC
/diagnostics:caret
/c
/Gw
/Gy
/utf-8
/wd4819
/DSAL_NO_ATTRIBUTE_DECLARATIONS=1
/permissive-
/Zc:strictStrings-
/Zc:__cplusplus
/D_CRT_STDIO_LEGACY_WIDE_SPECIFIERS=1
/D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS=1
/D_WINDLL
/D_DISABLE_EXTENDED_ALIGNED_STORAGE
/Ob2
/d2ExtendedWarningInfo
/Ox
/Ot
/GF
/errorReport:prompt
/EHsc
/DPLATFORM_EXCEPTIONS_DISABLED=0
/Z7
/MD
/bigobj
/fp:fast
/Zo
/Zp8
/W4
/we4456
/we4458
/we4459
/wd4244
/wd4838
/TP
/GR
/std:c++20
/Zc:preprocessor
/wd5054