#include "ALaneManager.h"
#include "AMinionWaveManager.h"
#include "Components/SplineComponent.h"
#include "Components/SceneComponent.h"
#include "Math/UnrealMathUtility.h"
#include "DrawDebugHelpers.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/Actor.h"
#include "Engine/OverlapResult.h"
#include "Engine/CollisionProfile.h"
#include "Components/PrimitiveComponent.h"
#include "Logging/LogMacros.h"
#include "HAL/IConsoleManager.h"
#include "Containers/Queue.h"
#include "Algo/Reverse.h"
#include "ARiverPrismalManager.h"

// Define logging categories
DEFINE_LOG_CATEGORY_STATIC(LogLaneManager, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogLanePathfinding, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogLaneTowers, Log, All);
DEFINE_LOG_CATEGORY_STATIC(LogLaneValidation, Log, All);

// Console variables for debug control
static TAutoConsoleVariable<bool> CVarShowLaneDebug(
    TEXT("Aura.ShowLaneDebug"),
    false,
    TEXT("Show lane debug lines"),
    ECVF_Default
);

static TAutoConsoleVariable<float> CVarDebugLineThickness(
    TEXT("Aura.DebugLineThickness"),
    2.0f,
    TEXT("Thickness of debug lines"),
    ECVF_Default
);

ALaneManager::ALaneManager()
{
    PrimaryActorTick.bCanEverTick = true;

    // Criar componente raiz
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    if (!RootSceneComponent)
    {
        UE_LOG(LogLaneManager, Error, TEXT("Failed to create RootSceneComponent"));
        return;
    }
    RootComponent = RootSceneComponent;

    // Criar componentes spline para cada lane com validação
    SuperiorLaneSpline = CreateDefaultSubobject<USplineComponent>(TEXT("SuperiorLaneSpline"));
    if (SuperiorLaneSpline)
    {
        SuperiorLaneSpline->SetupAttachment(RootComponent);
        SuperiorLaneSpline->SetClosedLoop(false);
        SuperiorLaneSpline->SetUnselectedSplineSegmentColor(FLinearColor::Red);
    }
    else
    {
        UE_LOG(LogLaneManager, Error, TEXT("Failed to create SuperiorLaneSpline"));
    }

    CentralLaneSpline = CreateDefaultSubobject<USplineComponent>(TEXT("CentralLaneSpline"));
    if (CentralLaneSpline)
    {
        CentralLaneSpline->SetupAttachment(RootComponent);
        CentralLaneSpline->SetClosedLoop(false);
        CentralLaneSpline->SetUnselectedSplineSegmentColor(FLinearColor::Blue);
    }
    else
    {
        UE_LOG(LogLaneManager, Error, TEXT("Failed to create CentralLaneSpline"));
    }

    InferiorLaneSpline = CreateDefaultSubobject<USplineComponent>(TEXT("InferiorLaneSpline"));
    if (InferiorLaneSpline)
    {
        InferiorLaneSpline->SetupAttachment(RootComponent);
        InferiorLaneSpline->SetClosedLoop(false);
        InferiorLaneSpline->SetUnselectedSplineSegmentColor(FLinearColor::Green);
    }
    else
    {
        UE_LOG(LogLaneManager, Error, TEXT("Failed to create InferiorLaneSpline"));
    }

    // Inicializar dados das lanes com posições e equações matemáticas validadas conforme documento
    LanesData.SetNum(3);
    
    // Lane Superior: Y = -0.577X + 6928 (±300 UU largura) - Conforme mapaimplementacao.md
    LanesData[0].LaneType = ELaneManagerType::Superior;
    LanesData[0].LaneWidth = SUPERIOR_LANE_WIDTH; // 300 UU
    LanesData[0].StartPosition = FVector(-8000.0f, 5533.6f, 0.0f); // Calculado: y = -0.577*(-8000) + 6928
    LanesData[0].EndPosition = FVector(8000.0f, 2312.0f, 0.0f);   // Calculado: y = -0.577*(8000) + 6928

    // Lane Central: Y = 0 (±400 UU largura) - Conforme mapaimplementacao.md
    LanesData[1].LaneType = ELaneManagerType::Central;
    LanesData[1].LaneWidth = CENTRAL_LANE_WIDTH; // 400 UU
    LanesData[1].StartPosition = FVector(-4800.0f, 0.0f, 0.0f); // X limitado pela área do rio
    LanesData[1].EndPosition = FVector(4800.0f, 0.0f, 0.0f);    // X limitado pela área do rio

    // Lane Inferior: Y = 0.577X - 6928 (±300 UU largura) - Conforme mapaimplementacao.md
    LanesData[2].LaneType = ELaneManagerType::Inferior;
    LanesData[2].LaneWidth = INFERIOR_LANE_WIDTH; // 300 UU
    LanesData[2].StartPosition = FVector(-8000.0f, -11544.0f, 0.0f); // Calculado: y = 0.577*(-8000) - 6928
    LanesData[2].EndPosition = FVector(8000.0f, -2312.0f, 0.0f);     // Calculado: y = 0.577*(8000) - 6928
    
    // Validar configurações matemáticas
    ValidateMathematicalConstants();
    
    // Inicializar cache de pathfinding
    PathfindingCache.Empty();
    LastCacheUpdate = 0.0f;
    
    // Integração com sistema de rio
    RiverManager = nullptr;
    WaterSpeedMultiplier = 0.5f;
    bEnableWaterDetection = true;
    
    UE_LOG(LogLaneManager, Log, TEXT("ALaneManager initialized with %d lanes"), LanesData.Num());
}

void ALaneManager::BeginPlay()
{
    Super::BeginPlay();
    
    UE_LOG(LogLaneManager, Log, TEXT("ALaneManager BeginPlay started"));
    
    // Validar mundo e componentes essenciais
    if (!GetWorld())
    {
        UE_LOG(LogLaneManager, Error, TEXT("World is null in BeginPlay"));
        return;
    }
    
    if (!RootSceneComponent)
    {
        UE_LOG(LogLaneManager, Error, TEXT("RootSceneComponent is null in BeginPlay"));
        return;
    }
    
    // Inicializar todas as lanes com validação
    if (!InitializeLanes())
    {
        UE_LOG(LogLaneManager, Error, TEXT("Failed to initialize lanes"));
        return;
    }
    
    // Inicializar torres com validação
    if (!InitializeTowers())
    {
        UE_LOG(LogLaneManager, Error, TEXT("Failed to initialize towers"));
        return;
    }
    
    // Gerar waypoints para pathfinding com validação
    if (!GenerateWaypoints())
    {
        UE_LOG(LogLaneManager, Error, TEXT("Failed to generate waypoints"));
        return;
    }
    
    // Validar geometria das lanes
    if (!ValidateLaneGeometry())
    {
        UE_LOG(LogLaneValidation, Error, TEXT("Lane geometry validation failed!"));
        return;
    }
    
    // Configurar splines após inicialização
    SetupLaneSplines();
    
    // Buscar referência do RiverManager
    FindRiverManager();
    
    UE_LOG(LogLaneManager, Log, TEXT("ALaneManager BeginPlay completed successfully"));
}

void ALaneManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Atualizar cache de pathfinding periodicamente
    LastCacheUpdate += DeltaTime;
    if (LastCacheUpdate >= PATHFINDING_UPDATE_INTERVAL)
    {
        UpdatePathfindingCache();
        LastCacheUpdate = 0.0f;
    }
    
    // Controle de performance para debug drawing
    static float DebugDrawTimer = 0.0f;
    const float DebugDrawInterval = 0.1f; // Atualizar debug a cada 100ms para performance
    
    DebugDrawTimer += DeltaTime;
    
    // Desenhar debug lines apenas se habilitado via console variable e com controle de performance
    if (CVarShowLaneDebug.GetValueOnGameThread() && GetWorld() && DebugDrawTimer >= DebugDrawInterval)
    {
        DebugDrawTimer = 0.0f;
        
        const float LineThickness = CVarDebugLineThickness.GetValueOnGameThread();
        const float DebugLifetime = 0.15f; // Lifetime otimizado para performance
        const uint8 DepthPriority = 0;
        
        // Validar dados das lanes antes de desenhar
        if (LanesData.Num() >= 3)
        {
            // Desenhar lanes com cores identificáveis e validação
            for (int32 LaneIndex = 0; LaneIndex < FMath::Min(LanesData.Num(), 3); ++LaneIndex)
            {
                const FLaneData& Lane = LanesData[LaneIndex];
                
                // Validar posições antes de desenhar
                if (Lane.StartPosition.ContainsNaN() || Lane.EndPosition.ContainsNaN())
                {
                    UE_LOG(LogLaneManager, Warning, TEXT("Skipping debug draw for lane %d: invalid positions"), LaneIndex);
                    continue;
                }
                
                FColor LaneColor = FColor::White;
                switch (LaneIndex)
                {
                    case 0: LaneColor = FColor::Red; break;    // Superior
                    case 1: LaneColor = FColor::Blue; break;   // Central
                    case 2: LaneColor = FColor::Green; break;  // Inferior
                }
                
                DrawDebugLine(GetWorld(), Lane.StartPosition, Lane.EndPosition, 
                             LaneColor, false, DebugLifetime, DepthPriority, LineThickness);
                
                // Desenhar waypoints com otimização
                for (int32 WaypointIndex = 0; WaypointIndex < Lane.Waypoints.Num(); ++WaypointIndex)
                {
                    const FVector& Waypoint = Lane.Waypoints[WaypointIndex];
                    if (!Waypoint.IsZero() && !Waypoint.ContainsNaN())
                    {
                        DrawDebugSphere(GetWorld(), Waypoint, 50.0f, 8, FColor::Yellow, 
                                      false, DebugLifetime, DepthPriority, 1.0f);
                        
                        // Desenhar número do waypoint para debug
                        FString WaypointText = FString::Printf(TEXT("W%d-%d"), LaneIndex, WaypointIndex);
                        DrawDebugString(GetWorld(), Waypoint + FVector(0, 0, 75.0f), 
                                      WaypointText, nullptr, FColor::White, DebugLifetime);
                    }
                }
            }
            
            // Desenhar torres com informações detalhadas e otimização
            for (int32 TowerIndex = 0; TowerIndex < TowersData.Num(); ++TowerIndex)
            {
                const FTowerData& Tower = TowersData[TowerIndex];
                
                if (Tower.Position.IsZero() || Tower.Position.ContainsNaN())
                {
                    continue;
                }
                
                // Cor baseada no tipo e estado da torre
                FColor TowerColor = FColor::White;
                switch (Tower.TowerType)
                {
                    case ETowerType::Externa: 
                        TowerColor = Tower.bIsDestroyed ? FColor::Black : FColor::Orange; 
                        break;
                    case ETowerType::Interna: 
                        TowerColor = Tower.bIsDestroyed ? FColor::Black : FColor::Purple; 
                        break;
                    case ETowerType::Inibidor: 
                        TowerColor = Tower.bIsDestroyed ? FColor::Black : FColor::Magenta; 
                        break;
                }
                
                // Desenhar torre com validação de dimensões
                float SafeHeight = FMath::Max(Tower.Height, 100.0f);
                float SafeRadius = FMath::Max(Tower.BaseRadius, 50.0f);
                
                DrawDebugCylinder(GetWorld(), Tower.Position, 
                                Tower.Position + FVector(0, 0, SafeHeight),
                                SafeRadius, 8, TowerColor, false, DebugLifetime, DepthPriority, 2.0f);
                
                // Desenhar range de ataque apenas se a torre estiver ativa
                if (!Tower.bIsDestroyed && Tower.AttackRange > 0.0f)
                {
                    FColor RangeColor = TowerColor;
                    RangeColor.A = 64; // Semi-transparente
                    DrawDebugCircle(GetWorld(), Tower.Position, Tower.AttackRange, 16, 
                                  RangeColor, false, DebugLifetime, DepthPriority, 1.0f, 
                                  FVector(1, 0, 0), FVector(0, 1, 0));
                }
                
                // Informações da torre
                FVector TextPosition = Tower.Position + FVector(0, 0, SafeHeight + 50.0f);
                FString TowerInfo = FString::Printf(TEXT("T%d: %d/%d HP\nDmg: %.0f"), 
                                                  TowerIndex, Tower.Health, Tower.MaxHealth, Tower.AttackDamage);
                DrawDebugString(GetWorld(), TextPosition, TowerInfo, nullptr, FColor::White, DebugLifetime);
            }
        }
    }
    
    // Limpeza periódica do cache de pathfinding para evitar vazamentos de memória
    static float CacheCleanupTimer = 0.0f;
    const float CacheCleanupInterval = 30.0f; // Limpeza a cada 30 segundos
    
    CacheCleanupTimer += DeltaTime;
    if (CacheCleanupTimer >= CacheCleanupInterval)
    {
        CacheCleanupTimer = 0.0f;
        CleanupPathfindingCache();
    }
    
    // Atualizar integração com sistema de rio
    UpdateRiverIntegration();
}

FVector ALaneManager::CalculateSuperiorLanePosition(float X) const
{
    // Lane Superior: Y = -0.577X + 6928
    float Y = SUPERIOR_LANE_SLOPE * X + SUPERIOR_LANE_INTERCEPT;
    return FVector(X, Y, 0.0f);
}

FVector ALaneManager::CalculateCentralLanePosition(float X) const
{
    // Lane Central: Y = 0
    return FVector(X, 0.0f, 0.0f);
}

FVector ALaneManager::CalculateInferiorLanePosition(float X) const
{
    // Lane Inferior: Y = 0.577X - 6928
    float Y = INFERIOR_LANE_SLOPE * X + INFERIOR_LANE_INTERCEPT;
    return FVector(X, Y, 0.0f);
}

bool ALaneManager::InitializeLanes()
{
    UE_LOG(LogLaneManager, Log, TEXT("Initializing lanes..."));
    
    if (LanesData.Num() < 3)
    {
        UE_LOG(LogLaneManager, Error, TEXT("Insufficient lane data. Expected 3, got %d"), LanesData.Num());
        return false;
    }
    
    // Configurar splines para cada lane com validação robusta
    if (SuperiorLaneSpline && IsValid(SuperiorLaneSpline))
    {
        SuperiorLaneSpline->ClearSplinePoints();
        SuperiorLaneSpline->AddSplinePoint(LanesData[0].StartPosition, ESplineCoordinateSpace::World);
        
        // Adicionar pontos intermediários para melhor curvatura
        FVector MidPoint = (LanesData[0].StartPosition + LanesData[0].EndPosition) * 0.5f;
        SuperiorLaneSpline->AddSplinePoint(MidPoint, ESplineCoordinateSpace::World);
        
        SuperiorLaneSpline->AddSplinePoint(LanesData[0].EndPosition, ESplineCoordinateSpace::World);
        SuperiorLaneSpline->UpdateSpline();
        
        UE_LOG(LogLaneManager, Log, TEXT("Superior lane spline initialized successfully"));
        
        // Broadcast evento de lane inicializada
        BroadcastLaneInitialized(ELaneManagerType::Superior);
    }
    else
    {
        UE_LOG(LogLaneManager, Error, TEXT("SuperiorLaneSpline is invalid"));
        return false;
    }
    
    if (CentralLaneSpline && IsValid(CentralLaneSpline))
    {
        CentralLaneSpline->ClearSplinePoints();
        CentralLaneSpline->AddSplinePoint(LanesData[1].StartPosition, ESplineCoordinateSpace::World);
        
        // Lane central é reta, mas adicionar ponto médio para consistência
        FVector MidPoint = (LanesData[1].StartPosition + LanesData[1].EndPosition) * 0.5f;
        CentralLaneSpline->AddSplinePoint(MidPoint, ESplineCoordinateSpace::World);
        
        CentralLaneSpline->AddSplinePoint(LanesData[1].EndPosition, ESplineCoordinateSpace::World);
        CentralLaneSpline->UpdateSpline();
        
        UE_LOG(LogLaneManager, Log, TEXT("Central lane spline initialized successfully"));
        
        // Broadcast evento de lane inicializada
        BroadcastLaneInitialized(ELaneManagerType::Central);
    }
    else
    {
        UE_LOG(LogLaneManager, Error, TEXT("CentralLaneSpline is invalid"));
        return false;
    }
    
    if (InferiorLaneSpline && IsValid(InferiorLaneSpline))
    {
        InferiorLaneSpline->ClearSplinePoints();
        InferiorLaneSpline->AddSplinePoint(LanesData[2].StartPosition, ESplineCoordinateSpace::World);
        
        // Adicionar pontos intermediários para melhor curvatura
        FVector MidPoint = (LanesData[2].StartPosition + LanesData[2].EndPosition) * 0.5f;
        InferiorLaneSpline->AddSplinePoint(MidPoint, ESplineCoordinateSpace::World);
        
        InferiorLaneSpline->AddSplinePoint(LanesData[2].EndPosition, ESplineCoordinateSpace::World);
        InferiorLaneSpline->UpdateSpline();
        
        UE_LOG(LogLaneManager, Log, TEXT("Inferior lane spline initialized successfully"));
        
        // Broadcast evento de lane inicializada
        BroadcastLaneInitialized(ELaneManagerType::Inferior);
    }
    else
    {
        UE_LOG(LogLaneManager, Error, TEXT("InferiorLaneSpline is invalid"));
        return false;
    }
    
    UE_LOG(LogLaneManager, Log, TEXT("All lanes initialized successfully"));
    return true;
}

bool ALaneManager::InitializeTowers()
{
    UE_LOG(LogLaneTowers, Log, TEXT("Initializing towers system..."));
    
    // Limpar dados existentes
    TowersData.Empty();
    
    // Configurar torres externas (6 torres por time) - Geometria cilíndrica precisa
    SetupExternalTowers();
    
    // Configurar torres internas (3 torres por time) - Geometria octogonal precisa
    SetupInternalTowers();
    
    // Configurar torres inibidoras (3 torres por time) - Torres duplas
    SetupInhibitorTowers();
    // Time azul (lado esquerdo) - Torres externas
    if (!CreateTowerSet(-6000.0f, ETowerType::Externa, TEXT("Blue External")))
    {
        UE_LOG(LogLaneTowers, Error, TEXT("Failed to create blue external towers"));
        return false;
    }
    
    // Torres internas (3 torres por time)
    if (!CreateTowerSet(-3000.0f, ETowerType::Interna, TEXT("Blue Internal")))
    {
        UE_LOG(LogLaneTowers, Error, TEXT("Failed to create blue internal towers"));
        return false;
    }
    
    // Torres inibidoras (2 torres por time)
    if (!CreateInhibitorTowers(-1000.0f, TEXT("Blue Inhibitor")))
    {
        UE_LOG(LogLaneTowers, Error, TEXT("Failed to create blue inhibitor towers"));
        return false;
    }
    
    // Time vermelho (lado direito) - Torres externas
    if (!CreateTowerSet(6000.0f, ETowerType::Externa, TEXT("Red External")))
    {
        UE_LOG(LogLaneTowers, Error, TEXT("Failed to create red external towers"));
        return false;
    }
    
    // Torres internas (3 torres por time)
    if (!CreateTowerSet(3000.0f, ETowerType::Interna, TEXT("Red Internal")))
    {
        UE_LOG(LogLaneTowers, Error, TEXT("Failed to create red internal towers"));
        return false;
    }
    
    // Torres inibidoras (2 torres por time)
    if (!CreateInhibitorTowers(1000.0f, TEXT("Red Inhibitor")))
    {
        UE_LOG(LogLaneTowers, Error, TEXT("Failed to create red inhibitor towers"));
        return false;
    }
    
    UE_LOG(LogLaneTowers, Log, TEXT("Towers system initialized successfully. Total towers: %d"), TowersData.Num());
    return true;
}

bool ALaneManager::GenerateWaypoints()
{
    UE_LOG(LogLanePathfinding, Log, TEXT("Generating waypoints for all lanes..."));
    
    if (LanesData.Num() < 3)
    {
        UE_LOG(LogLanePathfinding, Error, TEXT("Insufficient lane data for waypoint generation"));
        return false;
    }
    
    int32 TotalWaypoints = 0;
    
    // Gerar waypoints para cada lane com validação
    for (FLaneData& Lane : LanesData)
    {
        Lane.Waypoints.Empty();
        
        // Calcular número de waypoints baseado na distância e especificações do documento
        float LaneLength = FVector::Dist(Lane.StartPosition, Lane.EndPosition);
        
        // Usar distâncias especificadas no documento (800-1200 UU)
        float WaypointDistance = FMath::RandRange(MIN_WAYPOINT_DISTANCE, MAX_WAYPOINT_DISTANCE);
        int32 NumWaypoints = FMath::Max(1, FMath::CeilToInt(LaneLength / WaypointDistance));
        
        UE_LOG(LogLanePathfinding, Log, TEXT("Generating %d waypoints for lane %d (Length: %.2f)"), 
               NumWaypoints, (int32)Lane.LaneType, LaneLength);
        
        // Gerar waypoints ao longo da lane
        for (int32 i = 0; i <= NumWaypoints; i++)
        {
            float Alpha = (NumWaypoints > 0) ? (float)i / (float)NumWaypoints : 0.0f;
            FVector WaypointPos = FMath::Lerp(Lane.StartPosition, Lane.EndPosition, Alpha);
            
            // Ajustar posição baseada no tipo de lane com validação matemática
            switch (Lane.LaneType)
            {
                case ELaneManagerType::Superior:
                    WaypointPos = CalculateSuperiorLanePosition(WaypointPos.X);
                    break;
                case ELaneManagerType::Central:
                    WaypointPos = CalculateCentralLanePosition(WaypointPos.X);
                    break;
                case ELaneManagerType::Inferior:
                    WaypointPos = CalculateInferiorLanePosition(WaypointPos.X);
                    break;
                default:
                    UE_LOG(LogLanePathfinding, Warning, TEXT("Unknown lane type encountered"));
                    continue;
            }
            
            // Validar posição do waypoint
            if (IsValidWaypointPosition(WaypointPos))
            {
                Lane.Waypoints.Add(WaypointPos);
                TotalWaypoints++;
            }
            else
            {
                UE_LOG(LogLanePathfinding, Warning, TEXT("Invalid waypoint position: %s"), *WaypointPos.ToString());
            }
        }
        
        UE_LOG(LogLanePathfinding, Log, TEXT("Lane %d: Generated %d valid waypoints"), 
               (int32)Lane.LaneType, Lane.Waypoints.Num());
    }
    
    UE_LOG(LogLanePathfinding, Log, TEXT("Waypoint generation completed. Total waypoints: %d"), TotalWaypoints);
    return TotalWaypoints > 0;
}

TArray<FVector> ALaneManager::FindPath(const FVector& StartPos, const FVector& EndPos, ELaneManagerType LaneType) const
{
    SCOPE_CYCLE_COUNTER(STAT_LaneManager_FindPath);
    
    TArray<FVector> Path;
    
    // Validação de entrada
    if (!IsValidWaypointPosition(StartPos) || !IsValidWaypointPosition(EndPos))
    {
        UE_LOG(LogLaneManager, Warning, TEXT("Invalid start or end position for pathfinding"));
        return Path;
    }
    
    // Verificar cache primeiro usando StartPos como chave
    FVector CacheKey = StartPos;

    if (PathfindingCache.Contains(CacheKey))
    {
        const FCachedPath& CachedPath = PathfindingCache[CacheKey];
        if (FDateTime::Now() - CachedPath.Timestamp < FTimespan::FromSeconds(PATHFINDING_CACHE_DURATION))
        {
            return CachedPath.Path;
        }
        else
        {
            // Cache expirado, remover
            const_cast<ALaneManager*>(this)->PathfindingCache.Remove(CacheKey);
        }
    }
    
    // Obter waypoints da lane específica
    TArray<FVector> LaneWaypoints;
    if (LaneType == ELaneManagerType::Superior && LanesData.IsValidIndex(0))
    {
        LaneWaypoints = LanesData[0].Waypoints;
    }
    else if (LaneType == ELaneManagerType::Central && LanesData.IsValidIndex(1))
    {
        LaneWaypoints = LanesData[1].Waypoints;
    }
    else if (LaneType == ELaneManagerType::Inferior && LanesData.IsValidIndex(2))
    {
        LaneWaypoints = LanesData[2].Waypoints;
    }
    
    if (LaneWaypoints.Num() == 0)
    {
        // Fallback: linha direta se não há waypoints
        Path.Add(StartPos);
        Path.Add(EndPos);
        UE_LOG(LogLaneManager, Warning, TEXT("No waypoints available for lane type %d, using direct path"), (int32)LaneType);
        return Path;
    }
    
    // Implementação completa do A* com heap binário
    TArray<FPathNode> OpenSet;
    TMap<FVector, FPathNode> AllNodes;
    TSet<FVector> ClosedSet;
    
    // Adicionar posições de início e fim aos waypoints para pathfinding
    TArray<FVector> AllPositions = LaneWaypoints;
    AllPositions.Insert(StartPos, 0);
    AllPositions.Add(EndPos);
    
    // Inicializar nó de início
    FPathNode StartNode;
    StartNode.Position = StartPos;
    StartNode.GCost = 0.0f;
    StartNode.HCost = CalculateHeuristic(StartPos, EndPos);
    StartNode.FCost = StartNode.GCost + StartNode.HCost;
    StartNode.Parent = FVector::ZeroVector;
    
    OpenSet.Add(StartNode);
    AllNodes.Add(StartPos, StartNode);
    
    // Heap binário para OpenSet (min-heap baseado em FCost)
    auto HeapPredicate = [](const FPathNode& A, const FPathNode& B) {
        return A.FCost < B.FCost;
    };
    
    int32 MaxIterations = 1000; // Prevenir loops infinitos
    int32 Iterations = 0;
    
    while (OpenSet.Num() > 0 && Iterations < MaxIterations)
    {
        Iterations++;
        
        // Reorganizar heap e pegar o nó com menor FCost
        OpenSet.HeapSort(HeapPredicate);
        FPathNode CurrentNode = OpenSet[0];
        OpenSet.RemoveAt(0);
        
        // Adicionar ao conjunto fechado
        ClosedSet.Add(CurrentNode.Position);
        
        // Verificar se chegamos ao destino
        if (FVector::Dist(CurrentNode.Position, EndPos) < WAYPOINT_SNAP_DISTANCE)
        {
            // Reconstruir caminho
            Path = ReconstructPath(CurrentNode, AllNodes);
            break;
        }
        
        // Examinar vizinhos
        TArray<FVector> Neighbors = GetNeighbors(CurrentNode.Position);
        
        for (const FVector& NeighborPos : Neighbors)
        {
            // Pular se já está no conjunto fechado
            if (ClosedSet.Contains(NeighborPos))
            {
                continue;
            }
            
            // Verificar se é uma posição válida para pathfinding
            if (!IsValidPathPosition(NeighborPos))
            {
                continue;
            }
            
            // Calcular custos
            float TentativeGCost = CurrentNode.GCost + FVector::Dist(CurrentNode.Position, NeighborPos);
            
            // Verificar se este caminho para o vizinho é melhor
            bool bInOpenSet = false;
            FPathNode* ExistingNode = AllNodes.Find(NeighborPos);
            
            if (ExistingNode)
            {
                bInOpenSet = OpenSet.ContainsByPredicate([NeighborPos](const FPathNode& Node) {
                    return Node.Position.Equals(NeighborPos, 1.0f);
                });
                
                if (bInOpenSet && TentativeGCost >= ExistingNode->GCost)
                {
                    continue; // Este não é um caminho melhor
                }
            }
            
            // Criar ou atualizar nó vizinho
            FPathNode NeighborNode;
            NeighborNode.Position = NeighborPos;
            NeighborNode.GCost = TentativeGCost;
            NeighborNode.HCost = CalculateHeuristic(NeighborPos, EndPos);
            NeighborNode.FCost = NeighborNode.GCost + NeighborNode.HCost;
            NeighborNode.Parent = CurrentNode.Position;
            
            AllNodes.Add(NeighborPos, NeighborNode);
            
            if (!bInOpenSet)
            {
                OpenSet.Add(NeighborNode);
            }
            else
            {
                // Atualizar nó existente no OpenSet
                for (FPathNode& Node : OpenSet)
                {
                    if (Node.Position.Equals(NeighborPos, 1.0f))
                    {
                        Node = NeighborNode;
                        break;
                    }
                }
            }
        }
    }
    
    // Se não encontrou caminho, usar fallback
    if (Path.Num() == 0)
    {
        UE_LOG(LogLaneManager, Warning, TEXT("A* pathfinding failed after %d iterations, using waypoint fallback"), Iterations);
        
        // Fallback: caminho através dos waypoints mais próximos
        int32 StartWaypointIndex = 0;
        float MinStartDistance = FVector::Dist(StartPos, LaneWaypoints[0]);
        for (int32 i = 1; i < LaneWaypoints.Num(); i++)
        {
            float Distance = FVector::Dist(StartPos, LaneWaypoints[i]);
            if (Distance < MinStartDistance)
            {
                MinStartDistance = Distance;
                StartWaypointIndex = i;
            }
        }
        
        int32 EndWaypointIndex = 0;
        float MinEndDistance = FVector::Dist(EndPos, LaneWaypoints[0]);
        for (int32 i = 1; i < LaneWaypoints.Num(); i++)
        {
            float Distance = FVector::Dist(EndPos, LaneWaypoints[i]);
            if (Distance < MinEndDistance)
            {
                MinEndDistance = Distance;
                EndWaypointIndex = i;
            }
        }
        
        Path.Add(StartPos);
        if (StartWaypointIndex < EndWaypointIndex)
        {
            for (int32 i = StartWaypointIndex; i <= EndWaypointIndex; i++)
            {
                Path.Add(LaneWaypoints[i]);
            }
        }
        else
        {
            for (int32 i = StartWaypointIndex; i >= EndWaypointIndex; i--)
            {
                Path.Add(LaneWaypoints[i]);
            }
        }
        Path.Add(EndPos);
    }
    
    // Armazenar no cache
    if (Path.Num() > 0)
    {
        FCachedPath CachedPath;
        CachedPath.Path = Path;
        CachedPath.Timestamp = FDateTime::Now();
        
        // Limpar cache se estiver muito cheio
        if (PathfindingCache.Num() >= MAX_CACHE_ENTRIES)
        {
            const_cast<ALaneManager*>(this)->CleanupPathfindingCache();
        }
        
        const_cast<ALaneManager*>(this)->PathfindingCache.Add(CacheKey, CachedPath);
        
        UE_LOG(LogLaneManager, VeryVerbose, TEXT("Path found with %d nodes, cached with key: %s"), Path.Num(), *CacheKey.ToString());
    }
    
    return Path;
}

float ALaneManager::CalculateHeuristic(const FVector& Start, const FVector& End) const
{
    // Distância euclidiana
    return FVector::Dist(Start, End);
}

TArray<FVector> ALaneManager::GetNearbyWaypoints(const FVector& Position, ELaneManagerType LaneType, float Radius) const
{
    TArray<FVector> NearbyWaypoints;
    
    // Obter waypoints da lane específica
    TArray<FVector> LaneWaypoints;
    if (LaneType == ELaneManagerType::Superior && LanesData.IsValidIndex(0))
    {
        LaneWaypoints = LanesData[0].Waypoints;
    }
    else if (LaneType == ELaneManagerType::Central && LanesData.IsValidIndex(1))
    {
        LaneWaypoints = LanesData[1].Waypoints;
    }
    else if (LaneType == ELaneManagerType::Inferior && LanesData.IsValidIndex(2))
    {
        LaneWaypoints = LanesData[2].Waypoints;
    }
    
    // Filtrar waypoints dentro do raio
    for (const FVector& Waypoint : LaneWaypoints)
    {
        if (FVector::Dist(Position, Waypoint) <= Radius)
        {
            NearbyWaypoints.Add(Waypoint);
        }
    }
    
    return NearbyWaypoints;
}

void ALaneManager::SpawnTower(const FTowerData& TowerData)
{
    // Implementação usando SpawnTowerAtPosition
    SpawnTowerAtPosition(TowerData.Position, TowerData.TowerType, ETeam::Azul);
}

bool ALaneManager::SpawnTowerAtPosition(const FVector& Position, ETowerType TowerType, ETeam Team)
{
    if (!IsValidTowerPosition(Position))
    {
        UE_LOG(LogLaneManager, Warning, TEXT("Invalid tower position: %.2f, %.2f, %.2f"), 
               Position.X, Position.Y, Position.Z);
        return false;
    }

    // Verificar se já existe uma torre nesta posição
    for (const FTowerData& ExistingTower : TowersData)
    {
        if (FVector::Dist(ExistingTower.Position, Position) < 200.0f) // Distância mínima entre torres
        {
            UE_LOG(LogLaneManager, Warning, TEXT("Tower too close to existing tower at position: %.2f, %.2f"), 
                   Position.X, Position.Y);
            return false;
        }
    }

    // Obter classe da torre baseada no tipo
    TSubclassOf<AActor> TowerClass = GetTowerClassForType(TowerType);
    if (!TowerClass)
    {
        UE_LOG(LogLaneManager, Error, TEXT("Failed to get tower class for type: %d"), (int32)TowerType);
        return false;
    }

    // Verificar se o mundo é válido
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogLaneManager, Error, TEXT("World is null, cannot spawn tower"));
        return false;
    }

    // Configurar parâmetros de spawn
    FActorSpawnParameters SpawnParams;
    SpawnParams.Owner = this;
    SpawnParams.Instigator = GetInstigator();
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bNoFail = true;

    // Ajustar posição Z para o chão
    FVector AdjustedPosition = Position;
    FHitResult GroundHit;
    FVector TraceStart = Position + FVector(0, 0, 1000.0f);
    FVector TraceEnd = Position - FVector(0, 0, 1000.0f);
    
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.AddIgnoredActor(this);
    
    if (World->LineTraceSingleByChannel(GroundHit, TraceStart, TraceEnd, ECC_WorldStatic, QueryParams))
    {
        AdjustedPosition.Z = GroundHit.Location.Z;
    }

    // Spawnar o ator da torre
    AActor* SpawnedTower = World->SpawnActor<AActor>(TowerClass, AdjustedPosition, FRotator::ZeroRotator, SpawnParams);
    
    if (!SpawnedTower)
    {
        UE_LOG(LogLaneManager, Error, TEXT("Failed to spawn tower actor at position: %.2f, %.2f, %.2f"), 
               AdjustedPosition.X, AdjustedPosition.Y, AdjustedPosition.Z);
        return false;
    }

    // Criar dados da torre
    FTowerData NewTowerData;
    NewTowerData.TowerType = TowerType;
    NewTowerData.Position = AdjustedPosition;
    NewTowerData.HP = 100;
    NewTowerData.MaxHP = 100;
    NewTowerData.AttackRange = 800.0f;
    NewTowerData.AttackDamage = 50.0f;
    NewTowerData.bIsActive = true;

    // Configurar propriedades específicas da torre
    switch (TowerType)
    {
        case ETowerType::Externa:
            NewTowerData.BaseRadius = 120.0f;
            NewTowerData.Height = 600.0f;
            NewTowerData.HP = 2500;
            NewTowerData.MaxHP = 2500;
            break;
        case ETowerType::Interna:
            NewTowerData.BaseRadius = 100.0f;
            NewTowerData.Height = 800.0f;
            NewTowerData.HP = 3000;
            NewTowerData.MaxHP = 3000;
            break;
        case ETowerType::Inibidor:
            NewTowerData.BaseRadius = 150.0f;
            NewTowerData.Height = 1000.0f;
            NewTowerData.HP = 4000;
            NewTowerData.MaxHP = 4000;
            break;
    }

    // Configurar propriedades do ator spawnado
    if (SpawnedTower)
    {
        // Definir tags para identificação
        SpawnedTower->Tags.Add(FName("Tower"));
        
        // Configurar nome do ator
        FString TowerName = FString::Printf(TEXT("Tower_%s_%d"), 
                                          TowerType == ETowerType::Inibidor ? TEXT("Inhibitor") : TEXT("Turret"),
                                          TowersData.Num());
        SpawnedTower->SetActorLabel(TowerName);
        
        // Configurar colisão se necessário
        UPrimitiveComponent* RootPrimitive = Cast<UPrimitiveComponent>(SpawnedTower->GetRootComponent());
        if (RootPrimitive)
        {
            RootPrimitive->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            RootPrimitive->SetCollisionObjectType(ECC_WorldStatic);
            RootPrimitive->SetCollisionResponseToAllChannels(ECR_Block);
        }
    }

    // Adicionar aos dados
    TowersData.Add(NewTowerData);

    // Adicionar à lista de torres ativas
    ActiveTowers.Add(SpawnedTower);

    UE_LOG(LogLaneManager, Log, TEXT("Tower spawned successfully: %s at position: %.2f, %.2f, %.2f"), 
           SpawnedTower ? *SpawnedTower->GetName() : TEXT("Unknown"),
           AdjustedPosition.X, AdjustedPosition.Y, AdjustedPosition.Z);

    // Broadcast evento de torre spawnada
    OnTowerSpawned.Broadcast(NewTowerData, SpawnedTower);

    return true;
}

TArray<AActor*> ALaneManager::FindTargetsInRange(const FVector& TowerPosition, float Range) const
{
    TArray<AActor*> TargetsInRange;
    
    if (!GetWorld())
    {
        return TargetsInRange;
    }
    
    // Buscar todos os atores em um raio
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    
    bool bHit = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        TowerPosition,
        FQuat::Identity,
        ECollisionChannel::ECC_Pawn,
        FCollisionShape::MakeSphere(Range),
        QueryParams
    );
    
    if (bHit)
    {
        for (const FOverlapResult& Result : OverlapResults)
        {
            if (Result.GetActor() && Result.GetActor()->IsValidLowLevel())
            {
                TargetsInRange.Add(Result.GetActor());
            }
        }
    }
    
    return TargetsInRange;
}

AActor* ALaneManager::GetBestTarget(const FVector& TowerPosition, const TArray<AActor*>& PotentialTargets) const
{
    if (PotentialTargets.Num() == 0)
    {
        return nullptr;
    }
    
    // Prioridade: Torre > Minion > Campeão
    // Por enquanto, retornar o mais próximo
    AActor* BestTarget = PotentialTargets[0];
    float MinDistance = FVector::Dist(TowerPosition, BestTarget->GetActorLocation());
    
    for (AActor* Target : PotentialTargets)
    {
        float Distance = FVector::Dist(TowerPosition, Target->GetActorLocation());
        if (Distance < MinDistance)
        {
            MinDistance = Distance;
            BestTarget = Target;
        }
    }
    
    return BestTarget;
}

bool ALaneManager::ValidateLaneGeometry() const
{
    // Validar constantes matemáticas conforme mapaimplementacao.md
    const float ExpectedSuperiorSlope = -0.577f;  // -tan(30°)
    const float ExpectedInferiorSlope = 0.577f;   // tan(30°)
    const float ExpectedSuperiorIntercept = 6928.0f;
    const float ExpectedInferiorIntercept = -6928.0f;

    bool bValidationPassed = true;

    // Validar inclinações das lanes
    if (!FMath::IsNearlyEqual(SUPERIOR_LANE_SLOPE, ExpectedSuperiorSlope, 0.001f))
    {
        UE_LOG(LogLaneManager, Error, TEXT("Superior lane slope validation failed: Expected %.3f, Got %.3f"), 
               ExpectedSuperiorSlope, SUPERIOR_LANE_SLOPE);
        bValidationPassed = false;
    }

    if (!FMath::IsNearlyEqual(INFERIOR_LANE_SLOPE, ExpectedInferiorSlope, 0.001f))
    {
        UE_LOG(LogLaneManager, Error, TEXT("Inferior lane slope validation failed: Expected %.3f, Got %.3f"), 
               ExpectedInferiorSlope, INFERIOR_LANE_SLOPE);
        bValidationPassed = false;
    }

    // Validar interceptos das lanes
    if (!FMath::IsNearlyEqual(SUPERIOR_LANE_INTERCEPT, ExpectedSuperiorIntercept, 1.0f))
    {
        UE_LOG(LogLaneManager, Error, TEXT("Superior lane intercept validation failed: Expected %.1f, Got %.1f"), 
               ExpectedSuperiorIntercept, SUPERIOR_LANE_INTERCEPT);
        bValidationPassed = false;
    }

    if (!FMath::IsNearlyEqual(INFERIOR_LANE_INTERCEPT, ExpectedInferiorIntercept, 1.0f))
    {
        UE_LOG(LogLaneManager, Error, TEXT("Inferior lane intercept validation failed: Expected %.1f, Got %.1f"), 
               ExpectedInferiorIntercept, INFERIOR_LANE_INTERCEPT);
        bValidationPassed = false;
    }

    // Validar larguras das lanes conforme documento
    if (SUPERIOR_LANE_WIDTH != 300.0f || INFERIOR_LANE_WIDTH != 300.0f)
    {
        UE_LOG(LogLaneManager, Error, TEXT("Lane width validation failed: Superior/Inferior should be 300 UU"));
        bValidationPassed = false;
    }

    if (CENTRAL_LANE_WIDTH != 400.0f)
    {
        UE_LOG(LogLaneManager, Error, TEXT("Central lane width validation failed: Should be 400 UU"));
        bValidationPassed = false;
    }

    // Validar distâncias entre waypoints (800-1200 UU conforme documento)
    if (MIN_WAYPOINT_DISTANCE != 800.0f || MAX_WAYPOINT_DISTANCE != 1200.0f)
    {
        UE_LOG(LogLaneManager, Error, TEXT("Waypoint distance validation failed: Should be 800-1200 UU"));
        bValidationPassed = false;
    }

    if (bValidationPassed)
    {
        UE_LOG(LogLaneManager, Log, TEXT("Lane geometry validation passed - All constants match mapaimplementacao.md"));
    }
    else
    {
        UE_LOG(LogLaneManager, Error, TEXT("Lane geometry validation FAILED - Constants do not match document specifications"));
    }

    return bValidationPassed;
}

bool ALaneManager::IsPositionInLane(const FVector& Position, ELaneManagerType LaneType, float Tolerance) const
{
    // Verificar se a posição está dentro da área jogável
    if (!IsValidWaypointPosition(Position))
    {
        return false;
    }
    
    FVector LaneCenter;
    float LaneWidth;
    
    switch (LaneType)
    {
        case ELaneManagerType::Superior:
            LaneCenter = CalculateSuperiorLanePosition(Position.X);
            LaneWidth = SUPERIOR_LANE_WIDTH;
            break;
        case ELaneManagerType::Central:
            LaneCenter = CalculateCentralLanePosition(Position.X);
            LaneWidth = CENTRAL_LANE_WIDTH;
            break;
        case ELaneManagerType::Inferior:
            LaneCenter = CalculateInferiorLanePosition(Position.X);
            LaneWidth = INFERIOR_LANE_WIDTH;
            break;
        default:
            UE_LOG(LogLaneManager, Warning, TEXT("Unknown lane type: %d"), (int32)LaneType);
            return false;
    }
    
    float DistanceFromCenter = FMath::Abs(Position.Y - LaneCenter.Y);
    return DistanceFromCenter <= (LaneWidth / 2.0f) + Tolerance;
}

ELaneManagerType ALaneManager::GetClosestLane(const FVector& Position) const
{
    float DistToSuperior = FMath::Abs(Position.Y - CalculateSuperiorLanePosition(Position.X).Y);
    float DistToCentral = FMath::Abs(Position.Y - CalculateCentralLanePosition(Position.X).Y);
    float DistToInferior = FMath::Abs(Position.Y - CalculateInferiorLanePosition(Position.X).Y);
    
    if (DistToSuperior <= DistToCentral && DistToSuperior <= DistToInferior)
    {
        return ELaneManagerType::Superior;
    }
    else if (DistToCentral <= DistToInferior)
    {
        return ELaneManagerType::Central;
    }
    else
    {
        return ELaneManagerType::Inferior;
    }
}

bool ALaneManager::IsPositionInRiver(const FVector& Position) const
{
    // Integração com o sistema de rio (será implementado no ARiverPrismalManager)
    // Por enquanto, assumir que o rio está na região central
    return FMath::Abs(Position.Y) <= 600.0f && FMath::Abs(Position.X) <= 4800.0f;
}

FVector ALaneManager::GetBridgePosition(ELaneManagerType LaneType) const
{
    // Pontes da lane central em X = ±600 UU
    if (LaneType == ELaneManagerType::Central)
    {
        // Retornar ponte mais próxima baseada na posição atual
        return FVector(0.0f, 0.0f, 0.0f); // Centro do mapa
    }
    
    return FVector::ZeroVector;
}

float ALaneManager::GetMovementSpeedModifier(const FVector& Position) const
{
    // Redução de velocidade no rio: 50%
    if (IsPositionInRiver(Position))
    {
        return 0.5f;
    }
    
    return 1.0f; // Velocidade normal
}

void ALaneManager::SetupLaneSplines()
{
    // Configurar spline da lane superior
    if (SuperiorLaneSpline)
    {
        SuperiorLaneSpline->ClearSplinePoints();
        for (float X = -7200.0f; X <= 7200.0f; X += 600.0f)
        {
            FVector Point = CalculateSuperiorLanePosition(X);
            SuperiorLaneSpline->AddSplinePoint(Point, ESplineCoordinateSpace::World);
        }
        SuperiorLaneSpline->UpdateSpline();
    }
    
    // Configurar spline da lane central
    if (CentralLaneSpline)
    {
        CentralLaneSpline->ClearSplinePoints();
        for (float X = -7200.0f; X <= 7200.0f; X += 600.0f)
        {
            FVector Point = CalculateCentralLanePosition(X);
            CentralLaneSpline->AddSplinePoint(Point, ESplineCoordinateSpace::World);
        }
        CentralLaneSpline->UpdateSpline();
    }
    
    // Configurar spline da lane inferior
    if (InferiorLaneSpline)
    {
        InferiorLaneSpline->ClearSplinePoints();
        for (float X = -7200.0f; X <= 7200.0f; X += 600.0f)
        {
            FVector Point = CalculateInferiorLanePosition(X);
            InferiorLaneSpline->AddSplinePoint(Point, ESplineCoordinateSpace::World);
        }
        InferiorLaneSpline->UpdateSpline();
    }
}

void ALaneManager::CalculateWaypointsForLane(ELaneManagerType LaneType)
{
    TArray<FVector> Waypoints;
    
    // Gerar 12 waypoints por lane com distâncias de 800-1200 UU
    for (int32 i = 0; i < 12; i++)
    {
        float Progress = static_cast<float>(i) / 11.0f; // 0.0 a 1.0
        float X = FMath::Lerp(-7200.0f, 7200.0f, Progress);
        
        FVector Waypoint;
        switch (LaneType)
        {
            case ELaneManagerType::Superior:
                Waypoint = CalculateSuperiorLanePosition(X);
                break;
            case ELaneManagerType::Central:
                Waypoint = CalculateCentralLanePosition(X);
                break;
            case ELaneManagerType::Inferior:
                Waypoint = CalculateInferiorLanePosition(X);
                break;
        }
        
        if (IsValidWaypointPosition(Waypoint))
        {
            Waypoints.Add(Waypoint);
        }
    }
    
    // Atualizar dados da lane
    int32 LaneIndex = static_cast<int32>(LaneType);
    if (LanesData.IsValidIndex(LaneIndex))
    {
        LanesData[LaneIndex].Waypoints = Waypoints;
    }
}

FVector ALaneManager::GetLaneDirection(ELaneManagerType LaneType, const FVector& Position) const
{
    switch (LaneType)
    {
        case ELaneManagerType::Superior:
            // Direção da lane superior (inclinação negativa)
            return FVector(1.0f, SUPERIOR_LANE_SLOPE, 0.0f).GetSafeNormal();
        case ELaneManagerType::Central:
            // Direção da lane central (horizontal)
            return FVector(1.0f, 0.0f, 0.0f);
        case ELaneManagerType::Inferior:
            // Direção da lane inferior (inclinação positiva)
            return FVector(1.0f, INFERIOR_LANE_SLOPE, 0.0f).GetSafeNormal();
        default:
            return FVector::ForwardVector;
    }
}

void ALaneManager::InvalidatePathfindingCache(const FVector& Position, float Radius)
{
    // Validar parâmetros de entrada
    if (Radius <= 0.0f)
    {
        UE_LOG(LogLaneManager, Warning, TEXT("Invalid radius for cache invalidation: %.2f"), Radius);
        return;
    }
    
    if (Position.ContainsNaN())
    {
        UE_LOG(LogLaneManager, Error, TEXT("Invalid position for cache invalidation: contains NaN values"));
        return;
    }
    
    // Remove entradas do cache que estão dentro do raio especificado
    
    TArray<FVector> KeysToRemove;
    KeysToRemove.Reserve(PathfindingCache.Num() / 4); // Estimativa para otimização
    
    for (const auto& CacheEntry : PathfindingCache)
    {
        if (!CacheEntry.Key.ContainsNaN() && FVector::Dist(CacheEntry.Key, Position) <= Radius)
        {
            KeysToRemove.Add(CacheEntry.Key);
        }
    }
    
    // Remover entradas invalidadas
    for (const FVector& Key : KeysToRemove)
    {
        PathfindingCache.Remove(Key);
    }
    
    UE_LOG(LogLaneManager, Log, TEXT("Invalidated %d pathfinding cache entries near position %.2f, %.2f (radius: %.2f)"), 
           KeysToRemove.Num(), Position.X, Position.Y, Radius);
}

void ALaneManager::ConfigureTowerProperties(FTowerData& TowerData) const
{
    // Validar dados da torre antes da configuração
    if (!IsValid(this))
    {
        UE_LOG(LogLaneManager, Error, TEXT("ALaneManager is not valid during tower configuration"));
        return;
    }
    
    // Configurar propriedades baseadas no tipo de torre conforme documento
    switch (TowerData.TowerType)
    {
        case ETowerType::Externa:
            TowerData.HP = 2500;
            TowerData.MaxHP = 2500;
            TowerData.AttackRange = 775.0f;
            TowerData.AttackDamage = 152.0f;
            TowerData.BaseRadius = 120.0f;
            TowerData.Height = 600.0f;
            break;
            
        case ETowerType::Interna:
            TowerData.HP = 3000;
            TowerData.MaxHP = 3000;
            TowerData.AttackRange = 850.0f;
            TowerData.AttackDamage = 175.0f;
            TowerData.BaseRadius = 100.0f;
            TowerData.Height = 800.0f;
            break;
            
        case ETowerType::Inibidor:
            TowerData.HP = 4000;
            TowerData.MaxHP = 4000;
            TowerData.AttackRange = 1075.0f;
            TowerData.AttackDamage = 190.0f;
            TowerData.BaseRadius = 150.0f;
            TowerData.Height = 1000.0f;
            break;
            
        default:
            UE_LOG(LogLaneManager, Error, TEXT("Unknown tower type for configuration: %d"), (int32)TowerData.TowerType);
            // Configurar valores padrão seguros
            TowerData.HP = 1000;
            TowerData.MaxHP = 1000;
            TowerData.AttackRange = 500.0f;
            TowerData.AttackDamage = 100.0f;
            TowerData.BaseRadius = 80.0f;
            TowerData.Height = 400.0f;
            break;
    }
    
    // Validar valores configurados
    if (TowerData.HP <= 0 || TowerData.MaxHP <= 0)
    {
        UE_LOG(LogLaneManager, Error, TEXT("Invalid health values configured for tower type %d"), (int32)TowerData.TowerType);
        TowerData.HP = FMath::Max(TowerData.HP, 1);
        TowerData.MaxHP = FMath::Max(TowerData.MaxHP, 1);
    }
    
    if (TowerData.AttackRange <= 0.0f || TowerData.AttackDamage <= 0.0f)
    {
        UE_LOG(LogLaneManager, Error, TEXT("Invalid combat values configured for tower type %d"), (int32)TowerData.TowerType);
        TowerData.AttackRange = FMath::Max(TowerData.AttackRange, 100.0f);
        TowerData.AttackDamage = FMath::Max(TowerData.AttackDamage, 10.0f);
    }
    
    // Marcar torre como ativa
    TowerData.bIsActive = true;
    
    UE_LOG(LogLaneManager, Log, TEXT("Configured tower properties: Type=%d, Health=%d/%d, Range=%.1f, Damage=%.1f, Radius=%.1f, Height=%.1f"), 
           (int32)TowerData.TowerType, TowerData.HP, TowerData.MaxHP, TowerData.AttackRange, 
           TowerData.AttackDamage, TowerData.BaseRadius, TowerData.Height);
}

bool ALaneManager::IsValidWaypointPosition(const FVector& Position) const
{
    // Validar parâmetros de entrada
    if (Position.ContainsNaN())
    {
        UE_LOG(LogLaneManager, Error, TEXT("Invalid waypoint position: contains NaN values"));
        return false;
    }
    
    // Verificar se está dentro da área jogável
    if (!IsPositionInPlayableArea(Position))
    {
        UE_LOG(LogLaneManager, VeryVerbose, TEXT("Waypoint position outside playable area: %.2f, %.2f"), Position.X, Position.Y);
        return false;
    }
    
    // Verificar proximidade com torres existentes (validação robusta)
    const float MinDistanceFromTowers = 300.0f;
    for (const FTowerData& Tower : TowersData)
    {
        if (!Tower.Position.ContainsNaN())
        {
            float DistanceToTower = FVector::Dist(Position, Tower.Position);
            if (DistanceToTower < MinDistanceFromTowers)
            {
                UE_LOG(LogLaneManager, VeryVerbose, TEXT("Waypoint too close to tower at %.2f, %.2f (distance: %.2f)"), 
                       Tower.Position.X, Tower.Position.Y, DistanceToTower);
                return false;
            }
        }
    }
    
    // Verificar se não está no rio (área central) - conforme especificações do documento
    const float RiverHalfWidth = 2400.0f;  // Largura do rio: 4800 UU
    const float RiverHalfHeight = 800.0f;   // Altura navegável do rio
    
    if (FMath::Abs(Position.X) < RiverHalfWidth && FMath::Abs(Position.Y) < RiverHalfHeight)
    {
        UE_LOG(LogLaneManager, VeryVerbose, TEXT("Waypoint position inside river area: %.2f, %.2f"), Position.X, Position.Y);
        return false; // Área do rio
    }
    
    // Verificar se está em uma lane válida (opcional - pode ser removido se waypoints podem estar fora das lanes)
    bool bInValidLane = false;
    for (const FLaneData& Lane : LanesData)
    {
        if (IsPositionInLane(Position, Lane.LaneType))
        {
            bInValidLane = true;
            break;
        }
    }
    
    if (!bInValidLane)
    {
        UE_LOG(LogLaneManager, VeryVerbose, TEXT("Waypoint position not in any valid lane: %.2f, %.2f"), Position.X, Position.Y);
        // Não retornar false aqui - waypoints podem estar entre lanes para pathfinding
    }
    
    return true;
}

// Implementações das funções faltantes
void ALaneManager::ValidateMathematicalConstants()
{
    UE_LOG(LogLaneManager, Log, TEXT("Validating mathematical constants..."));

    // Validar constantes das lanes
    const float ExpectedSuperiorSlope = -0.577f;
    const float ExpectedInferiorSlope = 0.577f;

    if (!FMath::IsNearlyEqual(SUPERIOR_LANE_SLOPE, ExpectedSuperiorSlope, 0.001f))
    {
        UE_LOG(LogLaneManager, Error, TEXT("Superior lane slope validation failed"));
    }

    if (!FMath::IsNearlyEqual(INFERIOR_LANE_SLOPE, ExpectedInferiorSlope, 0.001f))
    {
        UE_LOG(LogLaneManager, Error, TEXT("Inferior lane slope validation failed"));
    }

    UE_LOG(LogLaneManager, Log, TEXT("Mathematical constants validation completed"));
}

void ALaneManager::UpdatePathfindingCache()
{
    UE_LOG(LogLanePathfinding, VeryVerbose, TEXT("Updating pathfinding cache..."));

    // Limpar cache antigo
    PathfindingCache.Empty();

    // Recalcular caminhos mais utilizados
    for (const FLaneData& Lane : LanesData)
    {
        for (int32 i = 0; i < Lane.Waypoints.Num() - 1; i++)
        {
            FVector Start = Lane.Waypoints[i];
            FVector End = Lane.Waypoints[i + 1];

            TArray<FVector> Path = FindPath(Start, End, Lane.LaneType);
            // CORRIGIDO: Criar FCachedPath corretamente
            FCachedPath CachedPath;
            CachedPath.Path = Path;
            CachedPath.CreationTime = GetWorld()->GetTimeSeconds();
            CachedPath.bIsValid = true;
            PathfindingCache.Add(Start, CachedPath);
        }
    }

    UE_LOG(LogLanePathfinding, VeryVerbose, TEXT("Pathfinding cache updated with %d entries"), PathfindingCache.Num());
}

bool ALaneManager::CreateTowerSet(float XPosition, ETowerType TowerType, const FString& TowerSetName)
{
    UE_LOG(LogLaneTowers, Log, TEXT("Creating tower set: %s at X=%.2f"), *TowerSetName, XPosition);

    // Configurações baseadas no tipo de torre
    float BaseRadius, Height, AttackRange;
    int32 HP;

    switch (TowerType)
    {
        case ETowerType::Externa:
            BaseRadius = 120.0f;
            Height = 600.0f;
            AttackRange = 800.0f;
            HP = 2500;
            break;
        case ETowerType::Interna:
            BaseRadius = 100.0f;
            Height = 800.0f;
            AttackRange = 900.0f;
            HP = 3000;
            break;
        case ETowerType::Inibidor:
            BaseRadius = 150.0f;
            Height = 1000.0f;
            AttackRange = 1000.0f;
            HP = 4000;
            break;
        default:
            return false;
    }

    // Criar torres para cada lane
    TArray<FVector> TowerPositions = {
        FVector(XPosition, CalculateSuperiorLanePosition(XPosition).Y, 0.0f),
        FVector(XPosition, CalculateCentralLanePosition(XPosition).Y, 0.0f),
        FVector(XPosition, CalculateInferiorLanePosition(XPosition).Y, 0.0f)
    };

    for (const FVector& Position : TowerPositions)
    {
        FTowerData NewTower;
        NewTower.TowerType = TowerType;
        NewTower.Position = Position;
        NewTower.BaseRadius = BaseRadius;
        NewTower.Height = Height;
        NewTower.AttackRange = AttackRange;
        NewTower.HP = HP;
        NewTower.MaxHP = HP;
        NewTower.AttackDamage = 150.0f;
        NewTower.AttackSpeed = 1.0f;
        NewTower.bIsActive = true;

        TowersData.Add(NewTower);
    }

    UE_LOG(LogLaneTowers, Log, TEXT("Successfully created %d towers for set: %s"), TowerPositions.Num(), *TowerSetName);
    return true;
}

bool ALaneManager::CreateInhibitorTowers(float XPosition, const FString& TowerSetName)
{
    UE_LOG(LogLaneTowers, Log, TEXT("Creating inhibitor towers: %s at X=%.2f"), *TowerSetName, XPosition);

    // Torres inibidoras apenas nas lanes superior e inferior
    TArray<FVector> InhibitorPositions = {
        FVector(XPosition, CalculateSuperiorLanePosition(XPosition).Y, 0.0f),
        FVector(XPosition, CalculateInferiorLanePosition(XPosition).Y, 0.0f)
    };

    for (const FVector& Position : InhibitorPositions)
    {
        FTowerData NewTower;
        NewTower.TowerType = ETowerType::Inibidor;
        NewTower.Position = Position;
        NewTower.BaseRadius = 150.0f;
        NewTower.Height = 1000.0f;
        NewTower.AttackRange = 1000.0f;
        NewTower.HP = 4000;
        NewTower.MaxHP = 4000;
        NewTower.AttackDamage = 200.0f;
        NewTower.AttackSpeed = 0.8f;
        NewTower.bIsActive = true;

        TowersData.Add(NewTower);
    }

    UE_LOG(LogLaneTowers, Log, TEXT("Successfully created %d inhibitor towers for set: %s"), InhibitorPositions.Num(), *TowerSetName);
    return true;
}

bool ALaneManager::IsPositionInPlayableArea(const FVector& Position) const
{
    // Verificar limites da área jogável conforme especificações
    return (FMath::Abs(Position.X) <= PlayableArea / 2.0f && 
            FMath::Abs(Position.Y) <= PlayableArea / 2.0f);
}

bool ALaneManager::IsValidTowerPosition(const FVector& Position) const
{
    // Verificar se está dentro da área jogável
    if (!IsPositionInPlayableArea(Position))
    {
        return false;
    }
    
    // Verificar se não há sobreposição com outras torres
    for (const FTowerData& ExistingTower : TowersData)
    {
        float Distance = FVector::Dist(Position, ExistingTower.Position);
        if (Distance < (ExistingTower.BaseRadius + 200.0f)) // Buffer mínimo
        {
            return false;
        }
    }
    
    return true;
}

TSubclassOf<AActor> ALaneManager::GetTowerClassForType(ETowerType TowerType) const
{
    // Retornar classe da torre baseada no tipo configurado no Blueprint
    switch (TowerType)
    {
        case ETowerType::Externa:
            if (TurretClass)
            {
                return TurretClass;
            }
            UE_LOG(LogLaneManager, Warning, TEXT("TurretClass not configured in Blueprint"));
            break;
            
        case ETowerType::Interna:
            if (InternalTowerClass)
            {
                return InternalTowerClass;
            }
            UE_LOG(LogLaneManager, Warning, TEXT("InternalTowerClass not configured in Blueprint"));
            break;
            
        case ETowerType::Inibidor:
            if (InhibitorClass)
            {
                return InhibitorClass;
            }
            UE_LOG(LogLaneManager, Warning, TEXT("InhibitorClass not configured in Blueprint"));
            break;
            
        default:
            UE_LOG(LogLaneManager, Error, TEXT("Unknown tower type: %d"), (int32)TowerType);
            break;
    }
    
    // Fallback: tentar usar uma classe padrão se disponível
    if (DefaultTowerClass)
    {
        UE_LOG(LogLaneManager, Warning, TEXT("Using DefaultTowerClass as fallback for tower type: %d"), (int32)TowerType);
        return DefaultTowerClass;
    }
    
    UE_LOG(LogLaneManager, Error, TEXT("No tower class available for type: %d. Configure tower classes in Blueprint."), (int32)TowerType);
    return nullptr;
}

TArray<FVector> ALaneManager::GetNeighbors(const FVector& Position) const
{
    TArray<FVector> Neighbors;
    
    // Gerar vizinhos em grid 3D
    const float StepSize = 200.0f; // Tamanho do passo para pathfinding
    
    TArray<FVector> Directions = {
        FVector(StepSize, 0, 0),      // Frente
        FVector(-StepSize, 0, 0),     // Trás
        FVector(0, StepSize, 0),      // Direita
        FVector(0, -StepSize, 0),     // Esquerda
        FVector(StepSize, StepSize, 0),   // Diagonal frente-direita
        FVector(StepSize, -StepSize, 0),  // Diagonal frente-esquerda
        FVector(-StepSize, StepSize, 0),  // Diagonal trás-direita
        FVector(-StepSize, -StepSize, 0)  // Diagonal trás-esquerda
    };
    
    for (const FVector& Direction : Directions)
    {
        FVector NeighborPos = Position + Direction;
        if (IsValidPathPosition(NeighborPos))
        {
            Neighbors.Add(NeighborPos);
        }
    }
    
    return Neighbors;
}

TArray<FVector> ALaneManager::ReconstructPath(const FPathNode& EndNode, const TMap<FVector, FPathNode>& AllNodes) const
{
    TArray<FVector> Path;
    FVector CurrentPos = EndNode.Position;
    
    // Reconstruir caminho seguindo os pais
    while (CurrentPos != FVector::ZeroVector)
    {
        Path.Insert(CurrentPos, 0);
        
        const FPathNode* CurrentNode = AllNodes.Find(CurrentPos);
        if (CurrentNode && CurrentNode->Parent != FVector::ZeroVector)
        {
            CurrentPos = CurrentNode->Parent;
        }
        else
        {
            break;
        }
    }
    
    return Path;
}

bool ALaneManager::IsValidPathPosition(const FVector& Position) const
{
    // Verificar se está dentro da área jogável
    if (!IsPositionInPlayableArea(Position))
    {
        return false;
    }
    
    // Verificar colisões com torres
    for (const FTowerData& Tower : TowersData)
    {
        float Distance = FVector::Dist(Position, Tower.Position);
        if (Distance < Tower.BaseRadius)
        {
            return false;
        }
    }
    
    return true;
}

void ALaneManager::ConfigureTowerProperties(AActor* Tower, const FTowerData& TowerData)
{
    if (!Tower || !IsValid(Tower))
    {
        UE_LOG(LogLaneTowers, Warning, TEXT("Invalid tower actor provided for configuration"));
        return;
    }
    
    // Configurar propriedades básicas com validação
    Tower->SetActorLocation(TowerData.Position);
    
    // Calcular escala baseada nas dimensões da torre
    float ScaleX = FMath::Max(TowerData.BaseRadius / 100.0f, 0.1f);
    float ScaleY = ScaleX;
    float ScaleZ = FMath::Max(TowerData.Height / 100.0f, 0.1f);
    Tower->SetActorScale3D(FVector(ScaleX, ScaleY, ScaleZ));
    
    // Configurar componentes específicos da torre se disponíveis
    // Tentar encontrar componente de saúde
    if (UActorComponent* HealthComponent = Tower->GetComponentByClass(UActorComponent::StaticClass()))
    {
        // Configurar saúde se o componente suportar
        UE_LOG(LogLaneTowers, Verbose, TEXT("Health component found for tower"));
    }
    
    // Configurar tags para identificação
    FString TowerTag = FString::Printf(TEXT("Tower_%s"), 
        TowerData.TowerType == ETowerType::Externa ? TEXT("External") :
        TowerData.TowerType == ETowerType::Interna ? TEXT("Internal") : TEXT("Inhibitor"));
    Tower->Tags.AddUnique(FName(*TowerTag));
    
    UE_LOG(LogLaneTowers, Log, TEXT("Tower configured successfully: Type=%d, Health=%d, Range=%.2f, Position=%s"), 
           (int32)TowerData.TowerType, TowerData.Health, TowerData.AttackRange, *TowerData.Position.ToString());
}

void ALaneManager::CleanupPathfindingCache()
{
    if (PathfindingCache.Num() == 0)
    {
        return;
    }
    
    UE_LOG(LogLanePathfinding, Log, TEXT("Cleaning up pathfinding cache. Current size: %d entries"), PathfindingCache.Num());
    
    // Remover entradas antigas do cache (mais de 5 minutos)
    const float MaxCacheAge = 300.0f; // 5 minutos
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    TArray<FVector> KeysToRemove;
    
    for (const auto& CacheEntry : PathfindingCache)
    {
        // Assumindo que temos um timestamp no cache (seria necessário adicionar à estrutura)
        // Por enquanto, limpar cache baseado no tamanho
        if (PathfindingCache.Num() > MAX_CACHE_ENTRIES)
        {
            KeysToRemove.Add(CacheEntry.Key);
        }
    }
    
    // Remover entradas mais antigas primeiro (FIFO)
    int32 EntriesToRemove = FMath::Max(0, PathfindingCache.Num() - MAX_CACHE_ENTRIES);
    int32 RemovedCount = 0;
    
    for (const FVector& Key : KeysToRemove)
    {
        if (RemovedCount >= EntriesToRemove)
        {
            break;
        }
        
        PathfindingCache.Remove(Key);
        RemovedCount++;
    }
    
    UE_LOG(LogLanePathfinding, Log, TEXT("Pathfinding cache cleanup completed. Removed %d entries. New size: %d"), 
           RemovedCount, PathfindingCache.Num());
}



void ALaneManager::FindRiverManager()
{
    if (!GetWorld())
    {
        UE_LOG(LogLaneManager, Warning, TEXT("Cannot find RiverManager: World is null"));
        return;
    }
    
    // Buscar ARiverPrismalManager no mundo
    RiverManager = Cast<ARiverPrismalManager>(UGameplayStatics::GetActorOfClass(GetWorld(), ARiverPrismalManager::StaticClass()));
    
    if (RiverManager)
    {
        UE_LOG(LogLaneManager, Log, TEXT("RiverManager found and connected successfully"));
        
        // Configurar parâmetros de integração
        bEnableWaterDetection = true;
        WaterSpeedMultiplier = 0.5f; // Velocidade reduzida na água
        
        // Validar se o RiverManager está funcionando corretamente
        if (RiverManager->IsValidLowLevel())
        {
            UE_LOG(LogLaneManager, Log, TEXT("RiverManager validation successful"));
            
            // Broadcast evento de conexão com rio
            BroadcastRiverIntegrationStatusChanged(true);
        }
        else
        {
            UE_LOG(LogLaneManager, Warning, TEXT("RiverManager found but validation failed"));
            RiverManager = nullptr;
            
            // Broadcast evento de falha na conexão
            BroadcastRiverIntegrationStatusChanged(false);
        }
    }
    else
    {
        UE_LOG(LogLaneManager, Warning, TEXT("RiverManager not found in world. Water integration disabled."));
        bEnableWaterDetection = false;
        
        // Broadcast evento de desconexão
        BroadcastRiverIntegrationStatusChanged(false);
    }
}

void ALaneManager::UpdateRiverIntegration()
{
    if (!RiverManager || !bEnableWaterDetection)
    {
        return;
    }
    
    // Verificar se o RiverManager ainda é válido
    if (!IsValid(RiverManager))
    {
        UE_LOG(LogLaneManager, Warning, TEXT("RiverManager became invalid, attempting to reconnect"));
        FindRiverManager();
        return;
    }
    
    // Atualizar informações de água para pathfinding
    UpdateWaterPathfinding();
    
    // Log periódico para debug (a cada 10 segundos)
    static float LastRiverLogTime = 0.0f;
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    if (CurrentTime - LastRiverLogTime > 10.0f)
    {
        UE_LOG(LogLaneManager, Verbose, TEXT("River integration active. Water speed multiplier: %.2f"), WaterSpeedMultiplier);
        LastRiverLogTime = CurrentTime;
    }
}

void ALaneManager::UpdateWaterPathfinding()
{
    if (!RiverManager)
    {
        return;
    }
    
    // Verificar waypoints que estão na água e ajustar pathfinding
    for (int32 LaneIndex = 0; LaneIndex < LanesData.Num(); LaneIndex++)
    {
        FLaneData& Lane = LanesData[LaneIndex];
        
        for (int32 WaypointIndex = 0; WaypointIndex < Lane.Waypoints.Num(); WaypointIndex++)
        {
            FVector& Waypoint = Lane.Waypoints[WaypointIndex];
            
            // Verificar se o waypoint está na água
            if (RiverManager->IsPositionInWater(Waypoint))
            {
                // Marcar waypoint como aquático para pathfinding especial
                // Isso pode ser usado para aplicar velocidade reduzida
                UE_LOG(LogLanePathfinding, Verbose, TEXT("Waypoint in water detected: Lane %d, Waypoint %d"), LaneIndex, WaypointIndex);
                
                // Verificar se há ponte disponível
                FVector BridgePosition = RiverManager->GetNearestBridgePosition(Waypoint);
                if (!BridgePosition.IsZero())
                {
                    float BridgeDistance = FVector::Dist(Waypoint, BridgePosition);
                    if (BridgeDistance < 500.0f) // 5 metros de tolerância
                    {
                        // Ajustar waypoint para usar a ponte
                        Waypoint = BridgePosition;
                        UE_LOG(LogLanePathfinding, Log, TEXT("Waypoint adjusted to use bridge: %s"), *BridgePosition.ToString());
                    }
                }
            }
        }
    }
}

bool ALaneManager::IsPositionInWater(const FVector& Position) const
{
    if (!RiverManager || !bEnableWaterDetection)
    {
        return false;
    }
    
    return RiverManager->IsPositionInWater(Position);
}

float ALaneManager::GetWaterSpeedMultiplier(const FVector& Position) const
{
    if (!IsPositionInWater(Position))
    {
        return 1.0f; // Velocidade normal fora da água
    }
    
    return WaterSpeedMultiplier; // Velocidade reduzida na água
}

FVector ALaneManager::GetNearestBridgePosition(const FVector& Position) const
{
    if (!RiverManager)
    {
        return FVector::ZeroVector;
    }
    
    return RiverManager->GetNearestBridgePosition(Position);
}

TArray<FVector> ALaneManager::FindWaterPath(const FVector& Start, const FVector& End) const
{
    if (!RiverManager)
    {
        // Fallback para pathfinding normal
        return FindPath(Start, End, ELaneManagerType::Central);
    }
    
    // Usar pathfinding aquático do RiverManager
    TArray<FVector> WaterPath = RiverManager->FindWaterPath(Start, End);
    
    if (WaterPath.Num() > 0)
    {
        UE_LOG(LogLanePathfinding, Log, TEXT("Water path found with %d waypoints"), WaterPath.Num());
        return WaterPath;
    }

    // Se não encontrou caminho aquático, usar pathfinding normal
    UE_LOG(LogLanePathfinding, Warning, TEXT("Water path not found, using normal pathfinding"));
    return FindPath(Start, End, ELaneManagerType::Central);
}

// Implementação do sistema de eventos
void ALaneManager::BroadcastTowerDestroyed(const FTowerData& DestroyedTower)
{
    if (OnTowerDestroyed.IsBound())
    {
        OnTowerDestroyed.Broadcast(DestroyedTower);
        UE_LOG(LogLaneManager, Log, TEXT("Tower destroyed event broadcasted for tower at position: %s"), 
               *DestroyedTower.Position.ToString());
    }
}

void ALaneManager::BroadcastTowerSpawned(const FTowerData& SpawnedTower, AActor* TowerActor)
{
    if (OnTowerSpawned.IsBound() && IsValid(TowerActor))
    {
        OnTowerSpawned.Broadcast(SpawnedTower, TowerActor);
        UE_LOG(LogLaneManager, Log, TEXT("Tower spawned event broadcasted for tower at position: %s"), 
               *SpawnedTower.Position.ToString());
    }
}

void ALaneManager::BroadcastPathfindingCacheUpdated(int32 CacheSize, float UpdateTime)
{
    if (OnPathfindingCacheUpdated.IsBound())
    {
        OnPathfindingCacheUpdated.Broadcast(CacheSize, UpdateTime);
        UE_LOG(LogLaneManager, VeryVerbose, TEXT("Pathfinding cache updated: Size=%d, Time=%.3fs"), 
               CacheSize, UpdateTime);
    }
}

void ALaneManager::BroadcastLaneInitialized(ELaneManagerType LaneType)
{
    if (OnLaneInitialized.IsBound())
    {
        OnLaneInitialized.Broadcast(LaneType);
        UE_LOG(LogLaneManager, Log, TEXT("Lane initialized: %s"), 
               *UEnum::GetValueAsString(LaneType));
    }
}

void ALaneManager::BroadcastWaypointReached(const FVector& WaypointPosition, ELaneManagerType LaneType)
{
    if (OnWaypointReached.IsBound())
    {
        OnWaypointReached.Broadcast(WaypointPosition, LaneType);
        UE_LOG(LogLaneManager, VeryVerbose, TEXT("Waypoint reached: %s on lane %s"), 
               *WaypointPosition.ToString(), *UEnum::GetValueAsString(LaneType));
    }
}

void ALaneManager::BroadcastRiverIntegrationStatusChanged(bool bIsConnected)
{
    if (OnRiverIntegrationStatusChanged.IsBound())
    {
        OnRiverIntegrationStatusChanged.Broadcast(bIsConnected);
        UE_LOG(LogLaneManager, Log, TEXT("River integration status changed: %s"), 
               bIsConnected ? TEXT("Connected") : TEXT("Disconnected"));
    }
}

// Implementações das funções de setup de torres
void ALaneManager::SetupExternalTowers()
{
    if (!GetWorld())
    {
        UE_LOG(LogLaneManager, Error, TEXT("SetupExternalTowers: World is null"));
        return;
    }

    UE_LOG(LogLaneManager, Log, TEXT("Setting up external towers with cylindrical geometry"));

    // Torres externas do time azul (lado esquerdo)
    const float BlueXPosition = -6000.0f;
    const TArray<FVector> BlueExternalPositions = {
        FVector(BlueXPosition, -4000.0f, 0.0f), // Superior
        FVector(BlueXPosition, -2000.0f, 0.0f), // Central Superior
        FVector(BlueXPosition, 0.0f, 0.0f),     // Central
        FVector(BlueXPosition, 2000.0f, 0.0f),  // Central Inferior
        FVector(BlueXPosition, 4000.0f, 0.0f),  // Inferior
        FVector(BlueXPosition, 6000.0f, 0.0f)   // Extremo Inferior
    };

    // Torres externas do time vermelho (lado direito)
    const float RedXPosition = 6000.0f;
    const TArray<FVector> RedExternalPositions = {
        FVector(RedXPosition, -6000.0f, 0.0f),  // Extremo Superior
        FVector(RedXPosition, -4000.0f, 0.0f),  // Superior
        FVector(RedXPosition, -2000.0f, 0.0f),  // Central Superior
        FVector(RedXPosition, 0.0f, 0.0f),      // Central
        FVector(RedXPosition, 2000.0f, 0.0f),   // Central Inferior
        FVector(RedXPosition, 4000.0f, 0.0f)    // Inferior
    };

    // Criar torres externas azuis
    for (int32 i = 0; i < BlueExternalPositions.Num(); i++)
    {
        FTowerData TowerData;
        TowerData.Position = BlueExternalPositions[i];
        TowerData.TowerType = ETowerType::Externa;
        TowerData.Team = ETeam::Azul;
        TowerData.Health = 2500.0f;
        TowerData.MaxHealth = 2500.0f;
        TowerData.AttackRange = 775.0f;
        TowerData.AttackDamage = 152.0f;
        TowerData.AttackSpeed = 0.83f;
        TowerData.bIsActive = true;
        TowerData.Radius = 88.0f; // Geometria cilíndrica
        TowerData.Height = 1056.0f;
        
        ConfigureTowerProperties(TowerData);
        TowersData.Add(TowerData);
        
        // Spawn do ator da torre
        if (TurretClass)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            AActor* TowerActor = GetWorld()->SpawnActor<AActor>(TurretClass, TowerData.Position, FRotator::ZeroRotator, SpawnParams);
            if (TowerActor)
            {
                ConfigureTowerProperties(TowerActor, TowerData);
                ActiveTowers.Add(TowerActor);
                BroadcastTowerSpawned(TowerData, TowerActor);
                UE_LOG(LogLaneManager, Log, TEXT("Blue external tower %d spawned at %s"), i + 1, *TowerData.Position.ToString());
            }
        }
    }

    // Criar torres externas vermelhas
    for (int32 i = 0; i < RedExternalPositions.Num(); i++)
    {
        FTowerData TowerData;
        TowerData.Position = RedExternalPositions[i];
        TowerData.TowerType = ETowerType::Externa;
        TowerData.Team = ETeam::Vermelho;
        TowerData.Health = 2500.0f;
        TowerData.MaxHealth = 2500.0f;
        TowerData.AttackRange = 775.0f;
        TowerData.AttackDamage = 152.0f;
        TowerData.AttackSpeed = 0.83f;
        TowerData.bIsActive = true;
        TowerData.Radius = 88.0f; // Geometria cilíndrica
        TowerData.Height = 1056.0f;
        
        ConfigureTowerProperties(TowerData);
        TowersData.Add(TowerData);
        
        // Spawn do ator da torre
        if (TurretClass)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            AActor* TowerActor = GetWorld()->SpawnActor<AActor>(TurretClass, TowerData.Position, FRotator::ZeroRotator, SpawnParams);
            if (TowerActor)
            {
                ConfigureTowerProperties(TowerActor, TowerData);
                ActiveTowers.Add(TowerActor);
                BroadcastTowerSpawned(TowerData, TowerActor);
                UE_LOG(LogLaneManager, Log, TEXT("Red external tower %d spawned at %s"), i + 1, *TowerData.Position.ToString());
            }
        }
    }

    UE_LOG(LogLaneManager, Log, TEXT("External towers setup completed: %d towers created"), BlueExternalPositions.Num() + RedExternalPositions.Num());
}

void ALaneManager::SetupInternalTowers()
{
    if (!GetWorld())
    {
        UE_LOG(LogLaneManager, Error, TEXT("SetupInternalTowers: World is null"));
        return;
    }

    UE_LOG(LogLaneManager, Log, TEXT("Setting up internal towers with octagonal geometry"));

    // Torres internas do time azul (lado esquerdo)
    const float BlueXPosition = -3000.0f;
    const TArray<FVector> BlueInternalPositions = {
        FVector(BlueXPosition, -2309.4f, 0.0f), // Superior (Y = -0.577 * -3000 + 1577.4)
        FVector(BlueXPosition, 0.0f, 0.0f),     // Central
        FVector(BlueXPosition, 2309.4f, 0.0f)   // Inferior (Y = 0.577 * -3000 + 4040.6)
    };

    // Torres internas do time vermelho (lado direito)
    const float RedXPosition = 3000.0f;
    const TArray<FVector> RedInternalPositions = {
        FVector(RedXPosition, -2309.4f, 0.0f), // Superior (Y = -0.577 * 3000 + 5196.6)
        FVector(RedXPosition, 0.0f, 0.0f),     // Central
        FVector(RedXPosition, 2309.4f, 0.0f)   // Inferior (Y = 0.577 * 3000 - 5196.6)
    };

    // Criar torres internas azuis
    for (int32 i = 0; i < BlueInternalPositions.Num(); i++)
    {
        FTowerData TowerData;
        TowerData.Position = BlueInternalPositions[i];
        TowerData.TowerType = ETowerType::Interna;
        TowerData.Team = ETeam::Azul;
        TowerData.Health = 4000.0f;
        TowerData.MaxHealth = 4000.0f;
        TowerData.AttackRange = 775.0f;
        TowerData.AttackDamage = 170.0f;
        TowerData.AttackSpeed = 0.83f;
        TowerData.bIsActive = true;
        TowerData.Radius = 88.0f; // Geometria octogonal (raio inscrito)
        TowerData.Height = 1200.0f;
        
        ConfigureTowerProperties(TowerData);
        TowersData.Add(TowerData);
        
        // Spawn do ator da torre
        if (InternalTowerClass)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            AActor* TowerActor = GetWorld()->SpawnActor<AActor>(InternalTowerClass, TowerData.Position, FRotator::ZeroRotator, SpawnParams);
            if (TowerActor)
            {
                ConfigureTowerProperties(TowerActor, TowerData);
                ActiveTowers.Add(TowerActor);
                BroadcastTowerSpawned(TowerData, TowerActor);
                UE_LOG(LogLaneManager, Log, TEXT("Blue internal tower %d spawned at %s"), i + 1, *TowerData.Position.ToString());
            }
        }
        else if (DefaultTowerClass)
        {
            // Fallback para classe padrão
            FActorSpawnParameters SpawnParams;
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            AActor* TowerActor = GetWorld()->SpawnActor<AActor>(DefaultTowerClass, TowerData.Position, FRotator::ZeroRotator, SpawnParams);
            if (TowerActor)
            {
                ConfigureTowerProperties(TowerActor, TowerData);
                ActiveTowers.Add(TowerActor);
                BroadcastTowerSpawned(TowerData, TowerActor);
                UE_LOG(LogLaneManager, Warning, TEXT("Blue internal tower %d spawned with default class at %s"), i + 1, *TowerData.Position.ToString());
            }
        }
    }

    // Criar torres internas vermelhas
    for (int32 i = 0; i < RedInternalPositions.Num(); i++)
    {
        FTowerData TowerData;
        TowerData.Position = RedInternalPositions[i];
        TowerData.TowerType = ETowerType::Interna;
        TowerData.Team = ETeam::Vermelho;
        TowerData.Health = 4000.0f;
        TowerData.MaxHealth = 4000.0f;
        TowerData.AttackRange = 775.0f;
        TowerData.AttackDamage = 170.0f;
        TowerData.AttackSpeed = 0.83f;
        TowerData.bIsActive = true;
        TowerData.Radius = 88.0f; // Geometria octogonal (raio inscrito)
        TowerData.Height = 1200.0f;
        
        ConfigureTowerProperties(TowerData);
        TowersData.Add(TowerData);
        
        // Spawn do ator da torre
        if (InternalTowerClass)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            AActor* TowerActor = GetWorld()->SpawnActor<AActor>(InternalTowerClass, TowerData.Position, FRotator::ZeroRotator, SpawnParams);
            if (TowerActor)
            {
                ConfigureTowerProperties(TowerActor, TowerData);
                ActiveTowers.Add(TowerActor);
                BroadcastTowerSpawned(TowerData, TowerActor);
                UE_LOG(LogLaneManager, Log, TEXT("Red internal tower %d spawned at %s"), i + 1, *TowerData.Position.ToString());
            }
        }
        else if (DefaultTowerClass)
        {
            // Fallback para classe padrão
            FActorSpawnParameters SpawnParams;
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            AActor* TowerActor = GetWorld()->SpawnActor<AActor>(DefaultTowerClass, TowerData.Position, FRotator::ZeroRotator, SpawnParams);
            if (TowerActor)
            {
                ConfigureTowerProperties(TowerActor, TowerData);
                ActiveTowers.Add(TowerActor);
                BroadcastTowerSpawned(TowerData, TowerActor);
                UE_LOG(LogLaneManager, Warning, TEXT("Red internal tower %d spawned with default class at %s"), i + 1, *TowerData.Position.ToString());
            }
        }
    }

    UE_LOG(LogLaneManager, Log, TEXT("Internal towers setup completed: %d towers created"), BlueInternalPositions.Num() + RedInternalPositions.Num());
}

void ALaneManager::SetupInhibitorTowers()
{
    if (!GetWorld())
    {
        UE_LOG(LogLaneManager, Error, TEXT("SetupInhibitorTowers: World is null"));
        return;
    }

    UE_LOG(LogLaneManager, Log, TEXT("Setting up inhibitor towers (double towers)"));

    // Torres inibidoras do time azul (lado esquerdo) - 3 conjuntos de torres duplas
    const float BlueXPosition = -1000.0f;
    const TArray<FVector> BlueInhibitorPositions = {
        FVector(BlueXPosition, -577.0f, 0.0f),  // Superior (Y = -0.577 * -1000 + 0)
        FVector(BlueXPosition, 0.0f, 0.0f),     // Central
        FVector(BlueXPosition, 577.0f, 0.0f)    // Inferior (Y = 0.577 * -1000 + 1154)
    };

    // Torres inibidoras do time vermelho (lado direito) - 3 conjuntos de torres duplas
    const float RedXPosition = 1000.0f;
    const TArray<FVector> RedInhibitorPositions = {
        FVector(RedXPosition, -577.0f, 0.0f),   // Superior (Y = -0.577 * 1000 + 1154)
        FVector(RedXPosition, 0.0f, 0.0f),      // Central
        FVector(RedXPosition, 577.0f, 0.0f)     // Inferior (Y = 0.577 * 1000 - 1154)
    };

    // Distância entre torres duplas
    const float TowerPairDistance = 200.0f;

    // Criar torres inibidoras azuis (duplas)
    for (int32 i = 0; i < BlueInhibitorPositions.Num(); i++)
    {
        FVector BasePosition = BlueInhibitorPositions[i];
        
        // Primeira torre do par
        FTowerData TowerData1;
        TowerData1.Position = BasePosition + FVector(0.0f, -TowerPairDistance / 2.0f, 0.0f);
        TowerData1.TowerType = ETowerType::Inibidor;
        TowerData1.Team = ETeam::Azul;
        TowerData1.Health = 4000.0f;
        TowerData1.MaxHealth = 4000.0f;
        TowerData1.AttackRange = 775.0f;
        TowerData1.AttackDamage = 170.0f;
        TowerData1.AttackSpeed = 0.83f;
        TowerData1.bIsActive = true;
        TowerData1.Radius = 88.0f;
        TowerData1.Height = 1200.0f;
        
        ConfigureTowerProperties(TowerData1);
        TowersData.Add(TowerData1);
        
        // Segunda torre do par
        FTowerData TowerData2;
        TowerData2.Position = BasePosition + FVector(0.0f, TowerPairDistance / 2.0f, 0.0f);
        TowerData2.TowerType = ETowerType::Inibidor;
        TowerData2.Team = ETeam::Azul;
        TowerData2.Health = 4000.0f;
        TowerData2.MaxHealth = 4000.0f;
        TowerData2.AttackRange = 775.0f;
        TowerData2.AttackDamage = 170.0f;
        TowerData2.AttackSpeed = 0.83f;
        TowerData2.bIsActive = true;
        TowerData2.Radius = 88.0f;
        TowerData2.Height = 1200.0f;
        
        ConfigureTowerProperties(TowerData2);
        TowersData.Add(TowerData2);
        
        // Spawn das torres
        TSubclassOf<AActor> TowerClassToUse = InhibitorClass ? InhibitorClass : DefaultTowerClass;
        
        if (TowerClassToUse)
        {
            // Spawn primeira torre
            FActorSpawnParameters SpawnParams1;
            SpawnParams1.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            AActor* TowerActor1 = GetWorld()->SpawnActor<AActor>(TowerClassToUse, TowerData1.Position, FRotator::ZeroRotator, SpawnParams1);
            if (TowerActor1)
            {
                ConfigureTowerProperties(TowerActor1, TowerData1);
                ActiveTowers.Add(TowerActor1);
                BroadcastTowerSpawned(TowerData1, TowerActor1);
                UE_LOG(LogLaneManager, Log, TEXT("Blue inhibitor tower %d-A spawned at %s"), i + 1, *TowerData1.Position.ToString());
            }
            
            // Spawn segunda torre
            FActorSpawnParameters SpawnParams2;
            SpawnParams2.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            AActor* TowerActor2 = GetWorld()->SpawnActor<AActor>(TowerClassToUse, TowerData2.Position, FRotator::ZeroRotator, SpawnParams2);
            if (TowerActor2)
            {
                ConfigureTowerProperties(TowerActor2, TowerData2);
                ActiveTowers.Add(TowerActor2);
                BroadcastTowerSpawned(TowerData2, TowerActor2);
                UE_LOG(LogLaneManager, Log, TEXT("Blue inhibitor tower %d-B spawned at %s"), i + 1, *TowerData2.Position.ToString());
            }
        }
    }

    // Criar torres inibidoras vermelhas (duplas)
    for (int32 i = 0; i < RedInhibitorPositions.Num(); i++)
    {
        FVector BasePosition = RedInhibitorPositions[i];
        
        // Primeira torre do par
        FTowerData TowerData1;
        TowerData1.Position = BasePosition + FVector(0.0f, -TowerPairDistance / 2.0f, 0.0f);
        TowerData1.TowerType = ETowerType::Inibidor;
        TowerData1.Team = ETeam::Vermelho;
        TowerData1.Health = 4000.0f;
        TowerData1.MaxHealth = 4000.0f;
        TowerData1.AttackRange = 775.0f;
        TowerData1.AttackDamage = 170.0f;
        TowerData1.AttackSpeed = 0.83f;
        TowerData1.bIsActive = true;
        TowerData1.Radius = 88.0f;
        TowerData1.Height = 1200.0f;
        
        ConfigureTowerProperties(TowerData1);
        TowersData.Add(TowerData1);
        
        // Segunda torre do par
        FTowerData TowerData2;
        TowerData2.Position = BasePosition + FVector(0.0f, TowerPairDistance / 2.0f, 0.0f);
        TowerData2.TowerType = ETowerType::Inibidor;
        TowerData2.Team = ETeam::Vermelho;
        TowerData2.Health = 4000.0f;
        TowerData2.MaxHealth = 4000.0f;
        TowerData2.AttackRange = 775.0f;
        TowerData2.AttackDamage = 170.0f;
        TowerData2.AttackSpeed = 0.83f;
        TowerData2.bIsActive = true;
        TowerData2.Radius = 88.0f;
        TowerData2.Height = 1200.0f;
        
        ConfigureTowerProperties(TowerData2);
        TowersData.Add(TowerData2);
        
        // Spawn das torres
        TSubclassOf<AActor> TowerClassToUse = InhibitorClass ? InhibitorClass : DefaultTowerClass;
        
        if (TowerClassToUse)
        {
            // Spawn primeira torre
            FActorSpawnParameters SpawnParams1;
            SpawnParams1.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            AActor* TowerActor1 = GetWorld()->SpawnActor<AActor>(TowerClassToUse, TowerData1.Position, FRotator::ZeroRotator, SpawnParams1);
            if (TowerActor1)
            {
                ConfigureTowerProperties(TowerActor1, TowerData1);
                ActiveTowers.Add(TowerActor1);
                BroadcastTowerSpawned(TowerData1, TowerActor1);
                UE_LOG(LogLaneManager, Log, TEXT("Red inhibitor tower %d-A spawned at %s"), i + 1, *TowerData1.Position.ToString());
            }
            
            // Spawn segunda torre
            FActorSpawnParameters SpawnParams2;
            SpawnParams2.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            AActor* TowerActor2 = GetWorld()->SpawnActor<AActor>(TowerClassToUse, TowerData2.Position, FRotator::ZeroRotator, SpawnParams2);
            if (TowerActor2)
            {
                ConfigureTowerProperties(TowerActor2, TowerData2);
                ActiveTowers.Add(TowerActor2);
                BroadcastTowerSpawned(TowerData2, TowerActor2);
                UE_LOG(LogLaneManager, Log, TEXT("Red inhibitor tower %d-B spawned at %s"), i + 1, *TowerData2.Position.ToString());
            }
        }
    }

    int32 TotalInhibitorTowers = (BlueInhibitorPositions.Num() + RedInhibitorPositions.Num()) * 2; // Duplas
    UE_LOG(LogLaneManager, Log, TEXT("Inhibitor towers setup completed: %d towers created (double towers)"), TotalInhibitorTowers);
}

// Função duplicada removida - usar a implementação anterior
// Resto da função duplicada removido
