// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "UPCGPerformanceProfiler.h"

#ifdef AURA_UPCGPerformanceProfiler_generated_h
#error "UPCGPerformanceProfiler.generated.h already included, missing '#pragma once' in UPCGPerformanceProfiler.h"
#endif
#define AURA_UPCGPerformanceProfiler_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class APCGLumenIntegrator;
class APCGNaniteOptimizer;
class APCGStreamingManager;
class APCGWorldPartitionManager;
class UCanvas;
enum class EPCGProfilerCategory : uint8;
enum class EPCGProfilerSamplingRate : uint8;
struct FPCGPerformanceMetric;
struct FPCGProfilerAlert;
struct FPCGProfilerReport;
struct FPCGSystemPerformance;

// ********** Begin ScriptStruct FPCGProfilerConfig ************************************************
#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_91_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGProfilerConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGProfilerConfig;
// ********** End ScriptStruct FPCGProfilerConfig **************************************************

// ********** Begin ScriptStruct FPCGPerformanceMetric *********************************************
#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_171_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGPerformanceMetric_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGPerformanceMetric;
// ********** End ScriptStruct FPCGPerformanceMetric ***********************************************

// ********** Begin ScriptStruct FPCGSystemPerformance *********************************************
#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_228_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGSystemPerformance_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGSystemPerformance;
// ********** End ScriptStruct FPCGSystemPerformance ***********************************************

// ********** Begin ScriptStruct FPCGProfilerAlert *************************************************
#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_336_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGProfilerAlert_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGProfilerAlert;
// ********** End ScriptStruct FPCGProfilerAlert ***************************************************

// ********** Begin ScriptStruct FPCGProfilerReport ************************************************
#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_374_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGProfilerReport_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGProfilerReport;
// ********** End ScriptStruct FPCGProfilerReport **************************************************

// ********** Begin Delegate FOnPerformanceAlert ***************************************************
#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_411_DELEGATE \
AURA_API void FOnPerformanceAlert_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceAlert, FPCGProfilerAlert const& Alert);


// ********** End Delegate FOnPerformanceAlert *****************************************************

// ********** Begin Delegate FOnMetricUpdated ******************************************************
#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_412_DELEGATE \
AURA_API void FOnMetricUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnMetricUpdated, FPCGPerformanceMetric const& Metric);


// ********** End Delegate FOnMetricUpdated ********************************************************

// ********** Begin Delegate FOnProfilerReportGenerated ********************************************
#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_413_DELEGATE \
AURA_API void FOnProfilerReportGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnProfilerReportGenerated, FPCGProfilerReport const& Report);


// ********** End Delegate FOnProfilerReportGenerated **********************************************

// ********** Begin Delegate FOnThresholdExceeded **************************************************
#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_414_DELEGATE \
AURA_API void FOnThresholdExceeded_DelegateWrapper(const FMulticastScriptDelegate& OnThresholdExceeded, const FString& MetricName, float Value);


// ********** End Delegate FOnThresholdExceeded ****************************************************

// ********** Begin Class UPCGPerformanceProfiler **************************************************
#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_424_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execToggleOverlayVisibility); \
	DECLARE_FUNCTION(execSetOverlayScale); \
	DECLARE_FUNCTION(execSetOverlayPosition); \
	DECLARE_FUNCTION(execDrawProfilerOverlay); \
	DECLARE_FUNCTION(execStreamDataToEndpoint); \
	DECLARE_FUNCTION(execStopNetworkProfiling); \
	DECLARE_FUNCTION(execStartNetworkProfiling); \
	DECLARE_FUNCTION(execGetDrawCallCount); \
	DECLARE_FUNCTION(execGetGPUFrameTime); \
	DECLARE_FUNCTION(execStopGPUProfiling); \
	DECLARE_FUNCTION(execStartGPUProfiling); \
	DECLARE_FUNCTION(execGetTriangleCount); \
	DECLARE_FUNCTION(execGetAverageFrameTime); \
	DECLARE_FUNCTION(execGetMemoryUsage); \
	DECLARE_FUNCTION(execTakeMemorySnapshot); \
	DECLARE_FUNCTION(execStopMemoryProfiling); \
	DECLARE_FUNCTION(execStartMemoryProfiling); \
	DECLARE_FUNCTION(execGetAllBenchmarkResults); \
	DECLARE_FUNCTION(execGetBenchmarkResult); \
	DECLARE_FUNCTION(execEndBenchmark); \
	DECLARE_FUNCTION(execStartBenchmark); \
	DECLARE_FUNCTION(execIntegrateWithStreaming); \
	DECLARE_FUNCTION(execIntegrateWithLumen); \
	DECLARE_FUNCTION(execIntegrateWithNanite); \
	DECLARE_FUNCTION(execIntegrateWithWorldPartition); \
	DECLARE_FUNCTION(execHasActiveAlerts); \
	DECLARE_FUNCTION(execClearAllAlerts); \
	DECLARE_FUNCTION(execClearAlert); \
	DECLARE_FUNCTION(execSetMetricThreshold); \
	DECLARE_FUNCTION(execGetOptimizationRecommendations); \
	DECLARE_FUNCTION(execGetPerformanceSummary); \
	DECLARE_FUNCTION(execExportMetricsToCSV); \
	DECLARE_FUNCTION(execExportReportToFile); \
	DECLARE_FUNCTION(execGenerateReport); \
	DECLARE_FUNCTION(execGetAlertHistory); \
	DECLARE_FUNCTION(execGetActiveAlerts); \
	DECLARE_FUNCTION(execGetPeakPerformance); \
	DECLARE_FUNCTION(execGetAveragePerformance); \
	DECLARE_FUNCTION(execGetCurrentPerformance); \
	DECLARE_FUNCTION(execGetMetricsByCategory); \
	DECLARE_FUNCTION(execGetAllMetrics); \
	DECLARE_FUNCTION(execGetMetric); \
	DECLARE_FUNCTION(execRemoveCustomMetric); \
	DECLARE_FUNCTION(execUpdateCustomMetric); \
	DECLARE_FUNCTION(execRegisterCustomMetric); \
	DECLARE_FUNCTION(execEnableCategory); \
	DECLARE_FUNCTION(execSetSamplingRate); \
	DECLARE_FUNCTION(execResetProfiler); \
	DECLARE_FUNCTION(execPauseProfiling); \
	DECLARE_FUNCTION(execStopProfiling); \
	DECLARE_FUNCTION(execStartProfiling); \
	DECLARE_FUNCTION(execIsProfilerActive); \
	DECLARE_FUNCTION(execShutdownProfiler); \
	DECLARE_FUNCTION(execInitializeProfiler);


AURA_API UClass* Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister();

#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_424_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPCGPerformanceProfiler(); \
	friend struct Z_Construct_UClass_UPCGPerformanceProfiler_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURA_API UClass* Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister(); \
public: \
	DECLARE_CLASS2(UPCGPerformanceProfiler, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/Aura"), Z_Construct_UClass_UPCGPerformanceProfiler_NoRegister) \
	DECLARE_SERIALIZER(UPCGPerformanceProfiler)


#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_424_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPCGPerformanceProfiler(UPCGPerformanceProfiler&&) = delete; \
	UPCGPerformanceProfiler(const UPCGPerformanceProfiler&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPCGPerformanceProfiler); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPCGPerformanceProfiler); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UPCGPerformanceProfiler) \
	NO_API virtual ~UPCGPerformanceProfiler();


#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_421_PROLOG
#define FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_424_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_424_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_424_INCLASS_NO_PURE_DECLS \
	FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h_424_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPCGPerformanceProfiler;

// ********** End Class UPCGPerformanceProfiler ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_Source_Aura_Public_UPCGPerformanceProfiler_h

// ********** Begin Enum EPCGProfilerCategory ******************************************************
#define FOREACH_ENUM_EPCGPROFILERCATEGORY(op) \
	op(EPCGProfilerCategory::CPU) \
	op(EPCGProfilerCategory::Memory) \
	op(EPCGProfilerCategory::GPU) \
	op(EPCGProfilerCategory::Rendering) \
	op(EPCGProfilerCategory::Streaming) \
	op(EPCGProfilerCategory::PCG) \
	op(EPCGProfilerCategory::WorldPartition) \
	op(EPCGProfilerCategory::Nanite) \
	op(EPCGProfilerCategory::Lumen) \
	op(EPCGProfilerCategory::Network) \
	op(EPCGProfilerCategory::Audio) \
	op(EPCGProfilerCategory::Physics) \
	op(EPCGProfilerCategory::Animation) \
	op(EPCGProfilerCategory::Blueprint) \
	op(EPCGProfilerCategory::Custom) 

enum class EPCGProfilerCategory : uint8;
template<> struct TIsUEnumClass<EPCGProfilerCategory> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGProfilerCategory>();
// ********** End Enum EPCGProfilerCategory ********************************************************

// ********** Begin Enum EPCGProfilerSeverity ******************************************************
#define FOREACH_ENUM_EPCGPROFILERSEVERITY(op) \
	op(EPCGProfilerSeverity::Info) \
	op(EPCGProfilerSeverity::Warning) \
	op(EPCGProfilerSeverity::Error) \
	op(EPCGProfilerSeverity::Critical) 

enum class EPCGProfilerSeverity : uint8;
template<> struct TIsUEnumClass<EPCGProfilerSeverity> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGProfilerSeverity>();
// ********** End Enum EPCGProfilerSeverity ********************************************************

// ********** Begin Enum EPCGProfilerDisplayMode ***************************************************
#define FOREACH_ENUM_EPCGPROFILERDISPLAYMODE(op) \
	op(EPCGProfilerDisplayMode::Overlay) \
	op(EPCGProfilerDisplayMode::Console) \
	op(EPCGProfilerDisplayMode::File) \
	op(EPCGProfilerDisplayMode::Network) \
	op(EPCGProfilerDisplayMode::All) 

enum class EPCGProfilerDisplayMode : uint8;
template<> struct TIsUEnumClass<EPCGProfilerDisplayMode> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGProfilerDisplayMode>();
// ********** End Enum EPCGProfilerDisplayMode *****************************************************

// ********** Begin Enum EPCGProfilerSamplingRate **************************************************
#define FOREACH_ENUM_EPCGPROFILERSAMPLINGRATE(op) \
	op(EPCGProfilerSamplingRate::VeryLow) \
	op(EPCGProfilerSamplingRate::Low) \
	op(EPCGProfilerSamplingRate::Medium) \
	op(EPCGProfilerSamplingRate::High) \
	op(EPCGProfilerSamplingRate::VeryHigh) \
	op(EPCGProfilerSamplingRate::Realtime) 

enum class EPCGProfilerSamplingRate : uint8;
template<> struct TIsUEnumClass<EPCGProfilerSamplingRate> { enum { Value = true }; };
template<> AURA_API UEnum* StaticEnum<EPCGProfilerSamplingRate>();
// ********** End Enum EPCGProfilerSamplingRate ****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
